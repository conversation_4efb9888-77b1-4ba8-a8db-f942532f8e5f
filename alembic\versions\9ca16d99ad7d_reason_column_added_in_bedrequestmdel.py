"""reason column added in bedrequestmdel

Revision ID: 9ca16d99ad7d
Revises: bac1b8e7421a
Create Date: 2023-11-24 05:32:23.583774

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9ca16d99ad7d'
down_revision = 'bac1b8e7421a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bed_request', sa.Column('reason', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('bed_request', 'reason')
    # ### end Alembic commands ###
