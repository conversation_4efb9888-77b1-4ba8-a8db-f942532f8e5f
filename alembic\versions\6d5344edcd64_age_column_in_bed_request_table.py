"""age column in bed request table

Revision ID: 6d5344edcd64
Revises: a67d55df9fcd
Create Date: 2024-12-26 13:06:15.886552

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6d5344edcd64'
down_revision = 'a67d55df9fcd'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bed_request', sa.Column('patient_age', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('bed_request', 'patient_age')
    # ### end Alembic commands ###
