from fastapi import APIRouter, Depends, HTTPException, Request, Form
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
import json
import base64
import os
import hashlib
from database.db_conf import SessionLocal
import logging
from pathlib import Path
from user.resolvers import resend_otp_msg
logger=logging.getLogger()

router = APIRouter(
    prefix="/sms",
    tags=["sms"],
    responses={404: {"description": "Not found"}},
)


@router.post("/sms-webhook")
async def hello(request: Request):
    body=await request.body()
    logger.info(body)
    try:
        db= SessionLocal()
        json_string = body.decode("utf-8")
        data= json.loads(json_string)
        resend_otp_msg(db,data)
    except Exception as e:
        logger.info(e)
        return e
    finally:
        db.close()

    