"""column added in config table

Revision ID: 4b93ebb59812
Revises: 1fcc4d1da4f5
Create Date: 2024-01-24 08:58:36.788842

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '4b93ebb59812'
down_revision = '1fcc4d1da4f5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('config', sa.Column('data1', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('config', 'data1')
    # ### end Alembic commands ###
