"""remarks orderby date cols added

Revision ID: 9b27dd1e11de
Revises: 8bbd5e96f73c
Create Date: 2025-07-11 13:05:16.257032

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9b27dd1e11de'
down_revision = '8bbd5e96f73c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_queue', sa.Column('order_by_date', sa.DateTime(timezone=True), nullable=True), schema='queue')
    op.add_column('user_queue', sa.Column('remarks', sa.Text(), nullable=True), schema='queue')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_queue', 'remarks', schema='queue')
    op.drop_column('user_queue', 'order_by_date', schema='queue')
    # ### end Alembic commands ###
