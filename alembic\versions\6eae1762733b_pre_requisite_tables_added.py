"""pre requisite tables added

Revision ID: 6eae1762733b
Revises: aa28051f0bc1
Create Date: 2023-11-23 07:43:39.402766

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6eae1762733b'
down_revision = 'aa28051f0bc1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('service_prerequisite',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('service_id', sa.Integer(), nullable=True),
    sa.Column('pre_req_service_id', sa.Integer(), nullable=True),
    sa.Column('time_in_min', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['pre_req_service_id'], ['service.id'], name='service_prerequisite_pre_req_service_id_fk'),
    sa.ForeignKeyConstraint(['service_id'], ['service.id'], name='service_prerequisite_service_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user_service_prerequisite',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('user_service_id', sa.Integer(), nullable=True),
    sa.Column('pre_req_user_service_id', sa.Integer(), nullable=True),
    sa.Column('status', sa.Enum('HOLD', 'WAITING', 'IGNORE', name='userprereqstatusenum'), nullable=False),
    sa.Column('un_hold_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['pre_req_user_service_id'], ['user_service.id'], name='user_service_prerequisite_pre_req_user_service_id_fk'),
    sa.ForeignKeyConstraint(['user_service_id'], ['user_service.id'], name='user_service_prerequisite_user_service_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_service_prerequisite')
    op.drop_table('service_prerequisite')
    # ### end Alembic commands ###
