import enum
from sqlalchemy import <PERSON><PERSON><PERSON>, BigInteger, Column, Text,DateTime, Enum, ForeignKey, Integer, String, Numeric, UniqueConstraint, text, JSON
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import JSONB
import strawberry
from sqlalchemy.orm import relationship

from database.db_conf import Base
from user.models import StatusEnum

@strawberry.enum
class VitalUserStatusEnum(enum.Enum):
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"

class AssessmentCategory(Base):
    __tablename__ = "assessment_category"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    code = Column(String , nullable = False)
    domain_code = Column(String, nullable=True)
    category_conditions = Column(JSONB)
    multiple = Column(Boolean)
    name= Column(String, nullable = True)
    description = Column(String)
    config_id =Column(Integer)
    status = Column(Enum(StatusEnum), default=StatusEnum.ACTIVE )
    priority = Column(Numeric, server_default=text("1"),nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    assessment_questions= relationship("AssessmentQuestion",back_populates="assessment_category", lazy='dynamic',order_by="AssessmentQuestion.priority")

    def __repr__(self) -> str:
        return "<AssessmentCategory %r>" % self.id

class AssessmentQuestion(Base):
    __tablename__ = "assessment_question"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    assessment_category_id =Column(Integer, ForeignKey(
        "assessment_category.id", name="assessment_category_assessment_category_id_fk"))
    code= Column(String, nullable = True)
    name= Column(String, nullable = True)
    type= Column(String, nullable=True)
    question_options = Column(JSONB)
    question_validations = Column(JSONB)
    results = Column(JSONB)
    status = Column(Enum(StatusEnum), default=StatusEnum.ACTIVE )
    priority = Column(Numeric, server_default=text("1"),nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    question_conditions = Column(JSONB)
    formula =  Column(Text)
    formula_variables =  Column(JSONB)

    assessment_category= relationship("AssessmentCategory")
    def __repr__(self) -> str:
        return "<AssessmentQuestion %r>" % self.id

class UserAssessmentDetail(Base):
    __tablename__ = "user_assessment_detail"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id =Column(Integer, ForeignKey(
        "user.id", name="user_assessment_detail_user_id_fk"))    
    assessment_category_id =Column(Integer, ForeignKey(
        "assessment_category.id", name="user_assessment_detail_assessment_category_id_fk"))
    assessment_question_id =Column(Integer, ForeignKey(
        "assessment_question.id", name="user_assessment_detail_assessment_question_id_fk"))
    index = Column(Integer)
    value = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self) -> str:
        return "<UserAssessmentDetail %r>" % self.id

class UserVitalDetail(Base):
    __tablename__ = "user_vital_detail"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id =Column(Integer, ForeignKey(
        "user.id", name="user_assessment_detail_user_id_fk"))    
    vital_code = Column(String)
    value = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self) -> str:
        return "<UserAssessmentDetail %r>" % self.id
    
class VitalUser(Base):
    __tablename__ = "vital_user"
    id = Column(Integer, primary_key=True, autoincrement=True)
    patient_name=Column(String)
    uhid=Column(String)
    created_at=Column(DateTime(timezone=True), server_default=func.now())
    updated_at=Column(DateTime(timezone=True), onupdate=func.now())
    encounter_id=Column(String,nullable=True)
    failure_reason=Column(String,nullable=True)
    device_code = Column(String,nullable=True)
    status = Column(Enum(VitalUserStatusEnum), default=VitalUserStatusEnum.IN_PROGRESS)
    
class AllergyMaster(Base):
    __tablename__ = "allergy_master"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    domain_code = Column(String , nullable = False)
    master_code = Column(String, nullable=False)
    short_desc = Column(String, nullable=False)

    def __repr__(self) -> str:
        return "<AllergyMaster %r>" % self.id
    
class VitalAccuracyModule(Base):
    __tablename__ = "vital_accuracy"
    id = Column(Integer, primary_key=True, autoincrement=True)
    device_id = Column(Integer,ForeignKey("device.id",name="rel_device_module_device_id_fk"))
    data = Column(String)

    def __repr__(self) -> str:
        return "<VitalAccuracyModule %r>" % self.id