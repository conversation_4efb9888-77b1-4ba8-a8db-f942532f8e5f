"""models created for visa letter generation

Revision ID: 31b11835c0e3
Revises: a5ca64e3fd26
Create Date: 2023-11-21 13:51:28.028074

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '31b11835c0e3'
down_revision = 'a5ca64e3fd26'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('authorized_doctor',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('doctor_name', sa.String(), nullable=True),
    sa.Column('doctor_position', sa.Text(), nullable=True),
    sa.Column('mobile_no', sa.String(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('signature_path', sa.String(), nullable=True),
    sa.Column('stamp_path', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('country',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('country_name', sa.String(), nullable=True),
    sa.Column('country_code', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user_visa_data',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('patient_name', sa.String(), nullable=True),
    sa.Column('passport_no', sa.String(), nullable=True),
    sa.Column('provisional_diagnosis', sa.String(), nullable=True),
    sa.Column('treatment_duration', sa.String(), nullable=True),
    sa.Column('attendant1', sa.String(), nullable=True),
    sa.Column('attendant1_passport_no', sa.String(), nullable=True),
    sa.Column('attendant2', sa.String(), nullable=True),
    sa.Column('attendant2_passport_no', sa.String(), nullable=True),
    sa.Column('attendant3', sa.String(), nullable=True),
    sa.Column('attendant3_passport_no', sa.String(), nullable=True),
    sa.Column('hospital_signatory', sa.String(), nullable=True),
    sa.Column('contact_details', sa.String(), nullable=True),
    sa.Column('doctor_name', sa.String(), nullable=True),
    sa.Column('doctor_specialization', sa.String(), nullable=True),
    sa.Column('country', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('country_embassy',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('country_id', sa.Integer(), nullable=True),
    sa.Column('embassy', sa.String(), nullable=True),
    sa.Column('embassy_address', sa.Text(), nullable=True),
    sa.Column('pdf_format', sa.Enum('TABLE', 'NORMAL', name='pdfformatenum'), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['country_id'], ['country.id'], name='country_embassy_country_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('country_embassy')
    op.drop_table('user_visa_data')
    op.drop_table('country')
    op.drop_table('authorized_doctor')
    # ### end Alembic commands ###
