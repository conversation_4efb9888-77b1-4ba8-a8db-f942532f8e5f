"""Column created in location table

Revision ID: d0ca18e03898
Revises: eb0464d21448
Create Date: 2024-03-04 13:53:36.302398

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd0ca18e03898'
down_revision = 'eb0464d21448'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_queue_step', sa.Column('created_location', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_queue_step', 'created_location')
    # ### end Alembic commands ###
