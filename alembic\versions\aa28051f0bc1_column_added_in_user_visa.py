"""column added in user_visa

Revision ID: aa28051f0bc1
Revises: 8301169894a9
Create Date: 2023-11-22 09:43:52.933993

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'aa28051f0bc1'
down_revision = '8301169894a9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_visa_data', sa.Column('pdf_file_path', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_visa_data', 'pdf_file_path')
    # ### end Alembic commands ###
