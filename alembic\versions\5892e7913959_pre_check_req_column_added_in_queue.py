"""pre check req column added in queue

Revision ID: 5892e7913959
Revises: 511a1b939eb4
Create Date: 2024-04-22 06:13:04.764907

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5892e7913959'
down_revision = '511a1b939eb4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('queue', sa.Column('pre_check_req', sa.Bo<PERSON>an(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('queue', 'pre_check_req')
    # ### end Alembic commands ###
