"""user assessment, user vital tables added

Revision ID: e80530077563
Revises: 81124ceacaa1
Create Date: 2024-02-14 11:21:46.463123

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e80530077563'
down_revision = '81124ceacaa1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_vital_detail',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('vital_code', sa.String(), nullable=True),
    sa.Column('value', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], name='user_assessment_detail_user_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user_assessment_detail',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('assessment_category_id', sa.Integer(), nullable=True),
    sa.Column('assessment_question_id', sa.Integer(), nullable=True),
    sa.Column('value', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['assessment_category_id'], ['assessment_category.id'], name='user_assessment_detail_assessment_category_id_fk'),
    sa.ForeignKeyConstraint(['assessment_question_id'], ['assessment_question.id'], name='user_assessment_detail_assessment_question_id_fk'),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], name='user_assessment_detail_user_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.execute("ALTER TYPE registrationtypeenum ADD VALUE 'VITAL'")

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_assessment_detail')
    op.drop_table('user_vital_detail')
    # ### end Alembic commands ###
