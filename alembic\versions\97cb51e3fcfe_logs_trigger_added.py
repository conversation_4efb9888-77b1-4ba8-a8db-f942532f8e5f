"""logs trigger added

Revision ID: 97cb51e3fcfe
Revises: 85512adafa60
Create Date: 2024-02-09 12:41:40.821346

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '97cb51e3fcfe'
down_revision = '85512adafa60'
branch_labels = None
depends_on = None
create_function = """CREATE OR REPLACE FUNCTION queue.log_user_queue()
  RETURNS TRIGGER 
  LANGUAGE PLPGSQL
  AS
$$
BEGIN
	if old.id is null
	then INSERT  INTO queue.user_queue_logs( user_queue_id,status,pre_check_status,weightage_id,counter,created_by,created_at)
	 values(new.id,new.status,new.pre_check_status,new.weightage_id,new.counter,new.created_by,now());
	end if;
	if new.id!= old.id or new.status!=old.status or new.pre_check_status!=old.pre_check_status or new.weightage_id!=old.weightage_id or new.counter!=old.counter
	then
	INSERT  INTO queue.user_queue_logs( user_queue_id,status,pre_check_status,weightage_id,counter,created_by,created_at)
	 values(new.id,new.status,new.pre_check_status,new.weightage_id,new.counter,new.created_by,now());
	END IF;
	RETURN NEW;
END;
$$"""

create_trigger="""
CREATE TRIGGER log_user_queue
    AFTER INSERT OR UPDATE 
    ON queue.user_queue
    FOR EACH ROW
    EXECUTE FUNCTION queue.log_user_queue();
"""
drop_trigger = """DROP TRIGGER log_user_queue
ON user_tokens;"""
drop_function = """DROP FUNCTION IF EXISTS log_user_queue();"""
def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(create_function)
    op.execute(create_trigger)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(drop_trigger)
    op.execute(drop_function)
    # ### end Alembic commands ###
