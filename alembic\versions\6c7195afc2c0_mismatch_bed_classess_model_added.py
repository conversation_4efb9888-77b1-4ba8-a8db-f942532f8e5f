"""mismatch bed classess model added

Revision ID: 6c7195afc2c0
Revises: f03e480ca02c
Create Date: 2023-11-29 06:02:41.093328

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6c7195afc2c0'
down_revision = 'f03e480ca02c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('mismatch_bed_classes',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('requested_bed_class', sa.String(), nullable=True),
    sa.Column('alloted_bed_class', sa.String(), nullable=True),
    sa.Column('billable_bed_class', sa.String(), nullable=True),
    sa.Column('tat', sa.String(), nullable=True),
    sa.Column('uhid', sa.String(), nullable=True),
    sa.Column('patient_name', sa.String(), nullable=True),
    sa.Column('contact_no', sa.String(), nullable=True),
    sa.Column('date', sa.Date(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('bed_request', sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('bed_request', 'completed_at')
    op.drop_table('mismatch_bed_classes')
    # ### end Alembic commands ###
