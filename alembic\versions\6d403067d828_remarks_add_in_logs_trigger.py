"""remarks add in logs trigger

Revision ID: 6d403067d828
Revises: 9b27dd1e11de
Create Date: 2025-07-11 16:23:43.703076

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6d403067d828'
down_revision = '9b27dd1e11de'
branch_labels = None
depends_on = None
create_function = """CREATE OR REPLACE FUNCTION queue.log_user_queue()
  RETURNS TRIGGER 
  LANGUAGE PLPGSQL
  AS
$$
BEGIN
	if old.id is null
	then INSERT  INTO queue.user_queue_logs( user_queue_id,status,pre_check_status,weightage_id,counter,created_by,created_at, remarks)
	 values(new.id,new.status,new.pre_check_status,new.weightage_id,new.counter,new.created_by,new.created_at,remarks);
	end if;
	if new.id!= old.id or new.status!=old.status or new.pre_check_status!=old.pre_check_status or new.weightage_id!=old.weightage_id or new.counter!=old.counter or new.remarks!=old.remarks
	then
	INSERT  INTO queue.user_queue_logs( user_queue_id,status,pre_check_status,weightage_id,counter,created_by,created_at,remarks)
	 values(new.id,new.status,new.pre_check_status,new.weightage_id,new.counter,new.updated_by,new.updated_at,new.remarks);
	END IF;
	RETURN NEW;
END;
$$"""

drop_function = """DROP FUNCTION IF EXISTS log_user_queue();"""
def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_queue_logs', sa.Column('remarks', sa.Text(), nullable=True), schema='queue')
    op.execute(create_function)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(drop_function)
    op.drop_column('user_queue_logs', 'remarks', schema='queue')
    # ### end Alembic commands ###



