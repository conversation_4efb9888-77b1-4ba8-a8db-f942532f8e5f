"""column added in user queue

Revision ID: da664c845d5d
Revises: 4852d42e0657
Create Date: 2023-11-08 09:56:48.774398

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'da664c845d5d'
down_revision = '4852d42e0657'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_queue', sa.Column('force_exit', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_queue', 'force_exit')
    # ### end Alembic commands ###
