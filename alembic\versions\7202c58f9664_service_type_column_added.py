"""service type column added

Revision ID: 7202c58f9664
Revises: 5668073c5bcf
Create Date: 2024-04-23 10:53:58.961721

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7202c58f9664'
down_revision = '5668073c5bcf'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('service', sa.Column('service_type', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('service', 'service_type')
    # ### end Alembic commands ###
