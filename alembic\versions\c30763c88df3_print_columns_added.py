"""print columns added

Revision ID: c30763c88df3
Revises: 6fcf217489e5
Create Date: 2023-12-08 09:56:25.072713

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c30763c88df3'
down_revision = '6fcf217489e5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('printer', sa.Column('job_media_sheets_completed', sa.Integer(), nullable=True))
    op.add_column('printer', sa.Column('request_id', sa.String(), nullable=True))
    op.add_column('printer', sa.Column('group_id', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('printer', 'group_id')
    op.drop_column('printer', 'request_id')
    op.drop_column('printer', 'job_media_sheets_completed')
    # ### end Alembic commands ###
