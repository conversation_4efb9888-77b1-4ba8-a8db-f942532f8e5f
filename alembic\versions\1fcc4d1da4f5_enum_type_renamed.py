"""enum type renamed

Revision ID: 1fcc4d1da4f5
Revises: 16bcf5cc3944
Create Date: 2024-01-19 09:00:58.803501

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1fcc4d1da4f5'
down_revision = '16bcf5cc3944'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TYPE requeststatus RENAME VALUE 'DISCHARGED' TO 'VACATED'")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TYPE requeststatus RENAME VALUE 'VACATED' TO 'DISCHARGED'")
    # ### end Alembic commands ###
