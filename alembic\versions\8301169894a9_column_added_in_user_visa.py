"""column added in user_visa

Revision ID: 8301169894a9
Revises: 31b11835c0e3
Create Date: 2023-11-21 14:49:09.585569

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8301169894a9'
down_revision = '31b11835c0e3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_visa_data', sa.Column('reference_id', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_visa_data', 'reference_id')
    # ### end Alembic commands ###
