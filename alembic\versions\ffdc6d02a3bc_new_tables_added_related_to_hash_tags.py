"""new tables added related to hash tags

Revision ID: ffdc6d02a3bc
Revises: 77a325914b4e
Create Date: 2024-05-03 05:06:25.336394

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql



# revision identifiers, used by Alembic.
revision = 'ffdc6d02a3bc'
down_revision = '77a325914b4e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('master_hash_tags',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('code', sa.String(), nullable=True),
    sa.Column('status', postgresql.ENUM('ACTIVE', 'INACTIVE', name='statusenum',create_type=False), nullable=True),
    sa.Column('type', sa.Enum('BED360', name='hashtagtypeenum'), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code')
    )
    op.create_table('rel_bed_request_hash_tag',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('hash_tag_id', sa.Integer(), nullable=True),
    sa.Column('bed_request_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['bed_request_id'], ['bed_request.id'], name='rel_bed_request_hash_tag_bed_request_id_fk'),
    sa.ForeignKeyConstraint(['hash_tag_id'], ['master_hash_tags.id'], name='rel_bed_request_hash_tag_hash_tag_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('rel_bed_request_hash_tag')
    op.drop_table('master_hash_tags')
    # ### end Alembic commands ###
