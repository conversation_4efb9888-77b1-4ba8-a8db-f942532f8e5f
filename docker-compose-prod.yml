version: "3.8"

services:

  # aig_opd_db:
  #   container_name: aig_opd_db
  #   image: postgres
  #   restart: always
  #   ports:
  #     - 5190:5432
  #   environment:
  #     - POSTGRES_USER=${DB_USER}
  #     - POSTGRES_PASSWORD=${DB_PASSWORD}
  #     - POSTGRES_DB=${DB_NAME}
  #   env_file:
  #     - env-prod
  #   volumes:
  #     - ../data/aig_opd_db:/var/lib/postgresql/data
  opd_redis:
    container_name: opd_redis
    image: redis:6.2-alpine
    network_mode: "host"
    env_file:
      - .env-prod
    volumes:
      - ${DOCKER_VOLUME}/volumes/opd_redis:/data
    command: ["redis-server", "--requirepass", "${REDIS_PASSWORD}", "--appendonly", "yes"]
      
  opd_app:
    container_name: opd_app
    network_mode: "host"
    build: .
    privileged: true
    command: bash -c "hypercorn main:app --bind 0.0.0.0:8001 --workers 6"
    env_file:
      - .env-prod
    restart: always
    depends_on:
      - opd_redis

  opd_celery_worker:
    container_name: opd_celery_worker
    build: 
      context: .
    network_mode: "host"
    command: celery -A celery_worker worker --loglevel=info
    restart: always
    environment:
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
    env_file:
      - .env-prod
    depends_on:
      - opd_app
      - opd_redis
  # opd_flower:
  #   container_name: opd_flower
  #   restart: always
  #   command: celery -A celery_worker flower --port=5557 --basic_auth=admin:admin --persistent=True
  #   volumes:
  #     - ../data/opd_app/flower:/data
  #   ports:
  #     - 5558:5557
  #   environment:
  #     - CELERY_BROKER_URL=${CELERY_BROKER_URL}
  #     - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
  #   env_file:
  #     - env-prod
  #   depends_on:
  #     - opd_celery_worker
    
  opd_celery_beat:
    container_name: opd_celery_beat
    restart: always
    network_mode: "host"
    build: 
      context: .
    depends_on:
      - opd_celery_worker
    environment:
        - CELERY_BROKER_URL=${CELERY_BROKER_URL}
        - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
    env_file:
      - .env-prod
    command: celery -A celery_worker beat -l INFO --scheduler celery_sqlalchemy_scheduler.schedulers:DatabaseScheduler
    