"""assessment questions table columnschanged

Revision ID: f78ec778b714
Revises: 855b3e193d0d
Create Date: 2024-03-05 07:44:05.052231

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'f78ec778b714'
down_revision = '855b3e193d0d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('assessment_question', sa.Column('question_options', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('assessment_question', sa.Column('question_validations', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('assessment_question', sa.Column('question_conditions', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('assessment_question', sa.Column('formula', sa.Text(), nullable=True))
    op.add_column('assessment_question', sa.Column('formula_variables', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.drop_column('assessment_question', 'validations')
    op.drop_column('assessment_question', 'options')
    op.drop_column('assessment_question', 'condition')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_queue_step', sa.Column('description', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('assessment_question', sa.Column('condition', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.add_column('assessment_question', sa.Column('options', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True))
    op.add_column('assessment_question', sa.Column('validations', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True))
    op.drop_column('assessment_question', 'formula_variables')
    op.drop_column('assessment_question', 'formula')
    op.drop_column('assessment_question', 'question_conditions')
    op.drop_column('assessment_question', 'question_validations')
    op.drop_column('assessment_question', 'question_options')
    # ### end Alembic commands ###
