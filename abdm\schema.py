import logging,pytz
import os
from typing import List, Optional
import strawberry
from abdm.resolvers import create_health_id_from_aadhar, generate_otp_aadhar_health_id, resend_aadhar_otp, resend_mobile_otp, verify_aadhar_link_mobile_otp, verify_otp_aadhar_health_id, surepass_generate_otp, surepass_submit_otp, surepass_resend_otp
from exceptions.exceptions import MutationError
from jose import jws
from datetime import datetime, time, timedelta

from graphql_types import MutationResponse
from user.schema import PatientLogin
logger = logging.getLogger()


@strawberry.type
class AadharOTP:
    txn_id: Optional[str]
    mobile_number: Optional[str]
    status: Optional[str]


@strawberry.type
class AadharData:
    first_name: Optional[str]
    phone_number: Optional[str]
    address:Optional[str]
    age:Optional[str]
    status:Optional[str]
    gender:Optional[str]
    access_token:Optional[str]
    health_id:Optional[str]
    aadhar:Optional[str]
    txn_id:Optional[str]
    qr_code:Optional[str]
    date_of_birth : Optional[str]
    photo:Optional[str]
    phr_address : Optional[str]
    health_card: Optional[str]
    mobile_linked : Optional[str]
    state: Optional[str]
    city : Optional[str]
    pincode: Optional[str]
    can_proceed: Optional[bool]
    his_city: Optional[str]
    last_name: Optional[str]
    locality: Optional[str]

@strawberry.type
class Mutation:

    @strawberry.mutation
    def generate_otp_aadhar_health_id(self, info, ref_no: str, phone_number:str) -> MutationResponse[AadharOTP]:
        try:
            db = info.context["db"]
            if os.environ["USE_SUREPASS"] == str(1):
                obj=surepass_generate_otp(db, ref_no,phone_number)
            else:
                obj=generate_otp_aadhar_health_id(db, ref_no,phone_number)

            return MutationResponse.from_status_flag(True, "OTP generated successfully", AadharOTP(txn_id=obj[0], mobile_number=obj[1],status=obj[2]))
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.mutation
    def verify_otp_aadhar_health_id(self, info, txn_id: str, otp: str,ref_no:str, phone_number:str,flow: str) -> MutationResponse[PatientLogin]:
        try:
            db = info.context["db"]
            his_db=info.context["db_aig_his"]
            obj = None
            if(flow == "MOBILE_OTP"):
                obj = verify_aadhar_link_mobile_otp(db, txn_id,otp,ref_no,phone_number)
            elif(flow == "AADHAR_OTP"):
                obj=verify_otp_aadhar_health_id(db,his_db, txn_id,otp,ref_no,phone_number,flow)
            elif(flow == "AADHAR_OTP_ONLY_VERIFY"):
                if os.environ["USE_SUREPASS"] == str(1):
                    obj=surepass_submit_otp(db,his_db, txn_id,otp,ref_no,phone_number,flow)
                else:
                    obj=verify_otp_aadhar_health_id(db,his_db, txn_id,otp,ref_no,phone_number,flow)
            dt = datetime.now() + \
            timedelta(minutes=float(
                os.environ["ACCESS_EXPIRE_MINUTES"]))
            access_token = jws.sign({"sub": {"user_id": phone_number ,"user_sub_type": "PATIENT"}, "user_type": "PATIENT", "exp": dt.isoformat(
            ), "device_id": info.context["device_id"]}, os.environ["SECRET_KEY"], algorithm=os.environ["ALGORITHM"])
            # return MutationResponse.from_status_flag(True, "Verification successfully done", )
            users=list(filter(lambda x: x.get("patientName").split(" ",1)[1]==obj[0]+" "+obj[19],obj[16]))
            status= "REGISTER" if len(users) == 0 else "ALREADY_REGISTERED"
            return MutationResponse.from_status_flag(True, "Login Successfull", PatientLogin.from_instance(obj[16], phone_number,access_token, None, status, None, db,AadharData(health_card=obj[11],phr_address = None,photo = obj[10],date_of_birth = obj[9],first_name=obj[0], phone_number=phone_number, address=obj[1],age=obj[2],access_token=None,status=obj[3],gender=obj[4],aadhar=obj[5],health_id=obj[6],txn_id=obj[7],qr_code=obj[8],mobile_linked = obj[12], state = obj[13], city= obj[14], pincode= obj[15], can_proceed= obj[17], his_city= obj[18],last_name=obj[19],locality=obj[20])) if obj[16] is not None else None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.mutation
    def create_health_id_by_aadhar(self, info, txn_id: str, phone_number:str,ref_id:str) -> MutationResponse[AadharData]:
        try:
            db = info.context["db"]
            obj=create_health_id_from_aadhar(db, txn_id,phone_number,ref_id)
            return MutationResponse.from_status_flag(True, "Verification successfully done", AadharData(health_card=obj[11],phr_address = None,photo = obj[10],date_of_birth = obj[9],first_name=obj[0], phone_number=phone_number, address=obj[1],age=obj[2],access_token=None,status=obj[3],gender=obj[4],aadhar=obj[5],health_id=obj[6],txn_id=obj[7],qr_code=obj[8],mobile_linked=obj[12]))
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.mutation
    def resend_aadhar_otp(self, info, txn_id: str) -> MutationResponse[AadharOTP]:
        try:
            db = info.context["db"]
            if os.environ["USE_SUREPASS"] == str(1):
                obj=surepass_resend_otp(db, txn_id)
            else:
                obj=resend_aadhar_otp(db, txn_id)
            return MutationResponse.from_status_flag(True, "OTP sent successfully", AadharOTP(txn_id=obj, mobile_number=None,status=None))
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.mutation
    def resend_mobile_otp(self, info, txn_id: str) -> MutationResponse[AadharOTP]:
        try:
            db = info.context["db"]
            obj=resend_mobile_otp(db, txn_id)
            return MutationResponse.from_status_flag(True, "OTP sent successfully", AadharOTP(txn_id=obj, mobile_number=None,status=None))
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
