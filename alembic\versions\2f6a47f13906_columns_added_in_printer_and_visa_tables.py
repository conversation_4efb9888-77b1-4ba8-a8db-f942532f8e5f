"""columns added in printer and visa tables

Revision ID: 2f6a47f13906
Revises: 284a0edf8af6
Create Date: 2023-12-04 10:43:24.964493

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2f6a47f13906'
down_revision = '284a0edf8af6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('printer', sa.Column('device_id', sa.String(), nullable=True))
    op.add_column('user_visa_data', sa.Column('passport_image', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_visa_data', 'passport_image')
    op.drop_column('printer', 'device_id')
    # ### end Alembic commands ###
