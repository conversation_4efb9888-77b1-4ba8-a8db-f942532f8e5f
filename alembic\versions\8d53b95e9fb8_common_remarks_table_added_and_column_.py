"""common_remarks table added and column added in bed request

Revision ID: 8d53b95e9fb8
Revises: e6db8d8e173f
Create Date: 2024-01-02 10:23:23.438777

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8d53b95e9fb8'
down_revision = 'e6db8d8e173f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('common_remarks',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('code', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('type', sa.Enum('CANCELLATION', name='remarkstypeenum'), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('bed_request', sa.Column('cancellation_remarks', sa.String(), nullable=True))
    op.execute("ALTER TYPE requeststatus ADD VALUE 'HOLD'")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('bed_request', 'cancellation_remarks')
    op.drop_table('common_remarks')
    op.execute("ALTER TYPE requeststatus DROP VALUE 'HOLD'")

    # ### end Alembic commands ###
