"""operation menu resource relation tables

Revision ID: e9b0b02759ef
Revises: 106d80f3b9ba
Create Date: 2023-11-29 13:32:44.591920

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e9b0b02759ef'
down_revision = '106d80f3b9ba'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('rel_menu_operation',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('menu_id', sa.Integer(), nullable=True),
    sa.Column('operation_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['menu_id'], ['menu.id'], name='rel_menu_operation_menu_id_fk'),
    sa.ForeignKeyConstraint(['operation_id'], ['operation.id'], name='rel_menu_operation_operation_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('rel_resource_operation',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('resource_id', sa.Integer(), nullable=True),
    sa.Column('operation_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['operation_id'], ['operation.id'], name='rel_resource_operation_operation_id_fk'),
    sa.ForeignKeyConstraint(['resource_id'], ['resource.id'], name='rel_resource_operation_module_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('rel_resource_operation')
    op.drop_table('rel_menu_operation')
    # ### end Alembic commands ###
