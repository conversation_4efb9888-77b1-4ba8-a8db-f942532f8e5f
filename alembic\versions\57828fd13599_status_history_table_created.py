"""status history table created

Revision ID: 57828fd13599
Revises: e47d050de03b
Create Date: 2024-03-29 10:22:47.993619

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '57828fd13599'
down_revision = 'e47d050de03b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('bed_status_history',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('his_id', sa.Integer(), nullable=True),
    sa.Column('bed_no', sa.String(), nullable=True),
    sa.Column('bed_class', sa.String(), nullable=True),
    sa.Column('bed_status', sa.String(), nullable=True),
    sa.Column('nurse_station', sa.String(), nullable=True),
    sa.Column('uhid', sa.String(), nullable=True),
    sa.Column('ip_number', sa.Integer(), nullable=True),
    sa.Column('patient_name', sa.String(), nullable=True),
    sa.Column('speciality', sa.String(), nullable=True),
    sa.Column('primary_consultant', sa.String(), nullable=True),
    sa.Column('secondary_consultant', sa.String(), nullable=True),
    sa.Column('contact_number', sa.String(), nullable=True),
    sa.Column('req_bed_type', sa.String(), nullable=True),
    sa.Column('billable_bed_type', sa.String(), nullable=True),
    sa.Column('date', sa.Date(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('discharge_history', sa.Column('completion_status', sa.Boolean(), nullable=True))
    op.add_column('discharge_history', sa.Column('discharge_time', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('discharge_history', 'discharge_time')
    op.drop_column('discharge_history', 'completion_status')
    op.drop_table('bed_status_history')
    # ### end Alembic commands ###
