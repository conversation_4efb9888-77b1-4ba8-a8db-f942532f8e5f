from typing import List, Optional
from menu.resolvers import get_all_module_menus_resources, get_modules_data, get_module_menus_resources
import strawberry
from graphql_types import MutationResponse, OperationTypes, QueryResponse
from exceptions.exceptions import MutationError
from menu.models import Menu as MenuModel
import logging
logger = logging.getLogger()
from resources.models import Resource as ResourceModel
from user.models import Module as ModuleModel, UserType as UserTypeModel

@strawberry.type
class Resource:
    id: int
    name: str
    code: str
    parent_resource_id: Optional[int] = None
    instance = strawberry.Private[ResourceModel]
    @classmethod
    def from_instance(cls, instance: ResourceModel):
        return cls(id=instance.id, name=instance.name, code=instance.code, parent_resource_id= instance.parent_resource_id)
    
@strawberry.type
class Menu:
    id: int
    name: str
    code: str
    icon: str
    parent_menu_id: Optional[int] = None
    resources: Optional[List[Resource]] = None
    instance = strawberry.Private[MenuModel]
    @classmethod
    def from_instance(cls, instance: MenuModel):
         return cls(id=instance.id, 
                    name=instance.name, 
                    code=instance.code,
                    parent_menu_id=instance.parent_menu_id,
                    icon = instance.icon,
                    resources = [Resource.from_instance(obj) for obj in instance.resources])
@strawberry.type
class ModuleMenuResource:
    menus: Optional[List[Menu]] = None
    @classmethod
    def from_instance(cls, menus):
         return cls(
            menus = [Menu.from_instance(obj.menu) for obj in menus])
            
@strawberry.type
class AllModuleMenuResource:
    id:int
    name:str
    code : str
    menus: Optional[List[Menu]] = None
    instance = strawberry.Private[ModuleModel]
    @classmethod
    def from_instance(cls,instance:ModuleModel, menus):
         return cls(
            id = instance.id,
            name = instance.module_name,
            code = instance.module_code,
            menus = [Menu.from_instance(obj.menu) for obj in menus])
@strawberry.type
class Module:
    id: Optional[int] = None
    name: Optional[str] = None
    code: Optional[str] = None

    # instance = strawberry.Private[ModuleModel]
    @classmethod
    def from_instance(cls, id:int, name: str, code:str):
        return cls(id=id, name=name, code=code)

@strawberry.type
class UseRoleMenu:
    id: int
    name: str
    code: str
    icon: str
    parent_menu_id: Optional[int] = None
    resources: Optional[List[Resource]] = None
    instance = strawberry.Private[MenuModel]
    @classmethod
    def from_instance(cls, instance: MenuModel, resources : Optional[List[int]] = None):
         return cls(id=instance.id, 
                    name=instance.name, 
                    code=instance.code,
                    parent_menu_id=instance.parent_menu_id,
                    icon = instance.icon,
                    resources = [Resource.from_instance(obj) for obj in instance.resources if obj.id in resources])


@strawberry.type
class Query:
    
    @strawberry.field
    def get_module_menus_resources(self, info, module_id: Optional[int] = None) -> QueryResponse[ModuleMenuResource]:
        try:
            db = info.context["db"]
            menus = get_module_menus_resources(db,module_id)
            return QueryResponse.from_status_flag(True, "list fetched successfully",ModuleMenuResource.from_instance(menus))
        except MutationError as ex:
                return QueryResponse.from_status_flag(False, ex.message, None)

    @strawberry.field
    def get_modules_data(self, info) -> QueryResponse[List[Module]]:
        try:
            db = info.context["db"]
            data = get_modules_data(db)
            return QueryResponse.from_status_flag(True, "list fetched successfully",[Module.from_instance(obj.id,obj.module_name,obj.module_code) for obj in data])
        except MutationError as ex:
                return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_all_module_menus_resources(self, info, module_id: Optional[int] = None) -> QueryResponse[List[AllModuleMenuResource]]:
        try:
            db = info.context["db"]
            data = get_all_module_menus_resources(db,module_id)
            return QueryResponse.from_status_flag(True, "list fetched successfully",[AllModuleMenuResource.from_instance(module,menus) for module,menus in data.items()])
        except MutationError as ex:
                return QueryResponse.from_status_flag(False, ex.message, None)