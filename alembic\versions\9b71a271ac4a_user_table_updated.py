"""user table updated

Revision ID: 9b71a271ac4a
Revises: 014ff37d23ce
Create Date: 2025-07-29 19:00:39.322779

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9b71a271ac4a'
down_revision = '014ff37d23ce'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user', sa.Column('age', sa.String(), nullable=True))
    op.add_column('user', sa.Column('gender', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'gender')
    op.drop_column('user', 'age')
    # ### end Alembic commands ###
