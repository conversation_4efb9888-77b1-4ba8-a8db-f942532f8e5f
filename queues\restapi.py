from http.client import H<PERSON>P<PERSON>x<PERSON>
from typing import Optional
from database.db_conf import SessionLocal
from fastapi import APIRouter, Depends
import logging
# from gtts import gTTS
import io
from fastapi.responses import StreamingResponse
from queues.resolvers import update_loc_1
logger = logging.getLogger()

router = APIRouter(
    prefix="/queue",
    tags=["Queue"],
    responses={404: {"description": "Not found"}},
)
async def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
        
@router.get("/update_loc")
def update_loc(tag_id: str,location_id, db=Depends(get_db)):
    try:
        logger.info("loc_start_time")
        logger.info(tag_id)
        logger.info(location_id)
        data = update_loc_1(db,tag_id,location_id)
        logger.info("loc_end_time")
        if data:
            return "success"
        else:
            logger.info(f"user not found tag id: {tag_id} location id :{location_id}")
            return f"user not found tag id: {tag_id} location id :{location_id}"
    except Exception as e:
        logger.exception(e)
        raise HTTPException(status_code=500, detail="failure")


# @router.get("/get_blob")
# def get_blob_from_text(text: str):
#     tts = gTTS(text)
#     buffer = io.BytesIO()
#     tts.write_to_fp(buffer)
#     buffer.seek(0)
#     return StreamingResponse(buffer, media_type="audio/mpeg")
    