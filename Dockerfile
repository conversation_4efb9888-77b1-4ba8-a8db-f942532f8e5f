FROM python:3.10
ENV TZ=Asia/Kolkata
ENV PYTHONUNBUFFERED=1
WORKDIR /opd_app
COPY requirements.txt requirements.txt
RUN pip3 install --upgrade pip
RUN curl https://packages.microsoft.com/keys/microsoft.asc | apt-key add - \
    && curl https://packages.microsoft.com/config/debian/11/prod.list > /etc/apt/sources.list.d/mssql-release.list \
    && apt-get update && ACCEPT_EULA=Y apt-get install -y msodbcsql17
RUN apt-get update -y && \
    apt-get install -y cups libcups2-dev
# RUN apt-get update && apt-get install -y cups cups-libs cups-devel && rm -rf /var/lib/apt/lists/*
RUN pip3 install -r requirements.txt
COPY . /opd_app
EXPOSE 8001 6379