"""column added

Revision ID: da0dff1c64af
Revises: 27dea1ea9553
Create Date: 2023-12-06 14:21:24.864301

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'da0dff1c64af'
down_revision = '27dea1ea9553'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('printer', sa.Column('print_start_time', sa.DateTime(timezone=True), nullable=True))
    op.add_column('printer', sa.Column('print_end_time', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('printer', 'print_end_time')
    op.drop_column('printer', 'print_start_time')
    # ### end Alembic commands ###
