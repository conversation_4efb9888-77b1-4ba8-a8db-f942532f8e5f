"""bill no unique removed

Revision ID: 387091fbdc64
Revises: e80530077563
Create Date: 2024-02-15 09:14:53.754709

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '387091fbdc64'
down_revision = 'e80530077563'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('user_service_bill_no_key', 'user_service', type_='unique')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint('user_service_bill_no_key', 'user_service', ['bill_no'])
    # ### end Alembic commands ###
