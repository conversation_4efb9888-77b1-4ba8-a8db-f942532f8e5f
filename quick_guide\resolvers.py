from sqlalchemy.orm import Session

from quick_guide.models import MedicalServices as MedicalServicesModel, KeyLocations as KeyLocationsModel, Specialtity as SpecialtityModel

def list_medical_services(db: Session):
    return db.query(MedicalServicesModel).filter(MedicalServicesModel.is_active == True).order_by(MedicalServicesModel.id).all()


def list_key_locations(db: Session):
    return db.query(KeyLocationsModel).order_by(KeyLocationsModel.priority).all()

def list_specialties(db: Session):
    return db.query(SpecialtityModel).filter(SpecialtityModel.is_active==True).order_by(SpecialtityModel.name).all()


# def list_locations(db:Session):
#     return db.query(LocationModel).order_by(LocationModel.priority).all()

# def get_location_info(db:Session, location_id:int):
#     return db.query(LocationModel).filter(LocationModel.id == location_id).order_by(LocationModel.priority).one_or_none()
