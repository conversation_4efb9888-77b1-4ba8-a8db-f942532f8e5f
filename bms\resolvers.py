import base64
from collections import defaultdict
from itertools import product
import os
from typing import List, Optional
import pytz
from sqlalchemy import desc, distinct, func, or_, and_,case, Integer
from constants import ONE_AIG_SEND_WHATSAPP_MSG, GET_PATIENT_DETAILS_BY_UHID
from database.db_conf import SessionLocal
from sqlalchemy.orm import Session, aliased
import logging
# from ai4bharat.transliteration import XlitEngine
from graphql_types import BedAllotRequest, AssignBed
from user.resolvers import get_access_token
from util.globals import handle_request, handle_request1
logger = logging.getLogger()
from bms.models import BedRequest as BedRequestModel, DischargeHistory as DischargeHistoryModel, HashTagTypeENum, MasterHashTags as MasterHashTagsModel, RelBedRequestHashTag as RelBedRequestHashTagModel, RemarksTypeEnum, BedRemarks as BedRemarksModel,ExcluedBeds as ExcludeBedsModel, CommonRemarks as CommonRemarksModel, RemarksTypeEnum, BedRemarks as BedRemarksModel, BedStatusHistory as BedStatusHistoryModel, SupportinCareService as SupportinCareServiceModel
from menu.models import Menu as MenuModel
from resources.models import RelResourceOperation as RelResourceOperationModel, RelUserTypeResource as RelUserTypeResourceModel, Resource as ResourceModel, RelStaffUserResource as RelStaffUserResourceModel
from exceptions.exceptions import MutationError
from bms.models import BedRequestType, RequestStatus , CaseTypeEnum, PriorityEnum
from user.models import RegistrationTypeEnum, StatusEnum, User as UserModel, UserType as UserTypeModel, Module as ModuleModel
from staff_user.models import RelStaffUserNurseStation as RelStaffUserNurseStationModel, StaffUser as StaffUserModel
from datetime import datetime, date, timedelta
from sqlalchemy.exc import DataError, NoResultFound
import openpyxl
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment
import python_lang as lang
import io
from redis_lock import RedisLock
from util.globals import client_lock
import pandas as pd
from openpyxl.styles import Border, Side


def get_bed_requests(db: Session, status:str, type:str, staff_user:int):
    query = bed_request_query(db,staff_user)
    staff_user : StaffUserModel = get_staff_user(db,staff_user)
    if status is not None and status != "":
        query = query.filter(BedRequestModel.status == status)
    else:
        query = query.filter(BedRequestModel.completed_status!=True).filter(or_(BedRequestModel.status == RequestStatus.REQUESTED, BedRequestModel.status == RequestStatus.ALLOTED, BedRequestModel.status == RequestStatus.HOLD))
    if type is not None and type != "":
        query = query.filter(BedRequestModel.type== type)
    else:
        resources = (
            db.scalars(db.query(ResourceModel.code)
            .join(RelStaffUserResourceModel, ResourceModel.id == RelStaffUserResourceModel.resource_id)
            .join(ModuleModel, RelStaffUserResourceModel.module_id == ModuleModel.id)
            .filter(ResourceModel.code.in_(["ADMISSION","TRANSFER"]))
            .filter(RelStaffUserResourceModel.staff_user_id == staff_user.id, ResourceModel.status == StatusEnum.ACTIVE)
            .order_by(ResourceModel.priority.desc())).all()
        )
        query = query.filter(BedRequestModel.type.in_(resources))
    # query = query.order_by(desc(BedRequestModel.priority))
    data=query.all()
    # logger.info(data)
    return data

def initiate_bed_request(db: Session, staff_user: int, request_type: str, data: BedAllotRequest, his_db: Session):
    with RedisLock(client_lock, "initiate_bed_request", blocking_timeout=60):
        try:
            # if not (request_type in BedRequestType.__members__.values()):
            msg=None
            if data.uhid is not None and data.uhid != "":
                uhid = get_patient_details(data.uhid)
            if data.id is None or data.id==0:
                bed_request = db.query(BedRequestModel).filter(BedRequestModel.uhid== data.uhid,BedRequestModel.completed_status == False).filter(BedRequestModel.status!=RequestStatus.ADMITTED,BedRequestModel.status!=RequestStatus.TRANSFERED).one_or_none()
                if bed_request is not None:
                    raise MutationError(f"user has already requested a bed with WL.No: {bed_request.wl_no}")
                today = datetime.now(pytz.timezone('Asia/Kolkata'))
                date_format = today.strftime("%d%m")
                previous_wl_no = db.query(BedRequestModel.wl_no).filter(func.date(BedRequestModel.created_at) == func.date(func.now())).order_by(desc(BedRequestModel.created_at)).first()
                logger.info(previous_wl_no)
                if previous_wl_no is None:
                    logger.info('first wl')
                    wl_no =f"WL{date_format}001"
                else:
                    logger.info(func.now())
                    logger.info(previous_wl_no.wl_no)
                    wl_no_numeric_part = int(previous_wl_no.wl_no[len(f"WL{date_format}"):]) + 1
                    wl_no = f"WL{date_format}{wl_no_numeric_part:03d}" if wl_no_numeric_part < 1000 else f"WL{date_format}{wl_no_numeric_part}"
                logger.info(wl_no)
            staff_user = get_staff_user(db,staff_user)
            if request_type == BedRequestType.ADMISSION.name:
                if data.case_type is None or data.case_type == "":
                    raise MutationError("Please specify the case type.")
                if data.case_type == CaseTypeEnum.INSURANCE.name  and (data.rate_of_contract is None or data.rate_of_contract == ""):
                    raise MutationError("Please specify the rate of contract for insurance.")
                if data.patient_category is None or data.patient_category == "":
                    raise MutationError("Please choose a patient category.")
                if data.estimation is None or data.estimation == "":
                    raise MutationError("Please provide an estimation value.")
                if data.estimated_by is None or data.estimated_by == "":
                    raise MutationError("Please provide estimated by.")
                try:
                    if int(data.estimation) < 0:
                        raise MutationError("Invalid estimation value")
                except Exception as e:
                    logger.exception(e)
                    raise MutationError("Provide valid estimation value in digits")
                if data.id is not None and data.id!=0:
                    bed_request = db.query(BedRequestModel).filter(BedRequestModel.id == data.id).one_or_none()
                    if bed_request is None:
                        raise MutationError("Bed request not found")
                    if (bed_request.status == RequestStatus.ALLOTED) or (bed_request.status == RequestStatus.CANCELLED):
                        raise MutationError(f"You cannot edit a request which is already {bed_request.status.name}")
                    bed_request,msg=edit_bed_request(db, request_type, data,staff_user,bed_request)
                else:
                    status = get_tentative_bed_status(data.tentative_admission_date,today)
                    bed_request= BedRequestModel(
                        type = request_type,
                        wl_no = wl_no,
                        uhid = data.uhid,
                        user_contact_no = data.user_contact_no,
                        patient_category = data.patient_category,
                        patient_name = data.patient_name,
                        department = data.department,
                        doctor_name = data.doctor_name,
                        requested_bed_class =data.requested_bed_class,
                        case_type = data.case_type,
                        estimation = data.estimation,
                        created_by = staff_user.name if staff_user is not None else " ",
                        referred_by = data.referred_by if (data.referred_by is not None or data.referred_by !="") else None,
                        priority = PriorityEnum.REGULAR if data.priority is None or data.priority == "" else data.priority,
                        rate_of_contract = data.rate_of_contract if (data.rate_of_contract is not None and data.rate_of_contract !="") else None,
                        estimated_by = data.estimated_by,
                        status = status,
                        tentative_admission_date = data.tentative_admission_date if (data.tentative_admission_date is not None and data.tentative_admission_date != '') else None,
                        supporting_care_services = (data.supporting_care_services).split(",") if data.supporting_care_services else None,
                        patient_age= uhid.get("age")
                    )
                    db.add(bed_request)
            elif request_type == BedRequestType.TRANSFER.name:
                if (data.admission_no == None or data.admission_no == ""):
                    raise MutationError("Please provide the Admission Number")
                sql_query = f"EXEC Achala_GetBedStatusListforKiosk @InterfaceID= 1100, @IPNumber = '{data.admission_no}'"
                bed_data = his_db.execute(sql_query).first()
                if bed_data is None:
                    raise MutationError("Please Provide valid Admission Number")
                if (data.req_from == None or data.req_from == ""):
                    raise MutationError("Please provide where the request is from")
                if (data.source == None or data.source == ""):
                    raise MutationError("Please provide the source to transfer")
                if data.id is not None and data.id!=0:
                    bed_request = db.query(BedRequestModel).filter(BedRequestModel.id == data.id).one_or_none()
                    if bed_request is None:
                        raise MutationError("Bed request not found")
                    if (bed_request.status == RequestStatus.ALLOTED) or (bed_request.status == RequestStatus.CANCELLED):
                        raise MutationError(f"You cannot edit a request which is already {bed_request.status.name}")
                    bed_request,msg=edit_bed_request(db, request_type, data, staff_user,bed_request)
                else:
                    status = get_tentative_bed_status(data.tentative_admission_date,today)
                    bed_request=  BedRequestModel(
                        type = request_type,
                        wl_no = wl_no,
                        uhid = data.uhid,
                        user_contact_no = data.user_contact_no,
                        patient_name = data.patient_name,
                        department = data.department,
                        doctor_name = data.doctor_name,
                        requested_bed_class = data.requested_bed_class,
                        req_from = data.req_from,
                        created_by = staff_user.name if staff_user is not None else " ",
                        priority = data.priority if (data.priority is not None and data.priority !="") else PriorityEnum.REGULAR.name,
                        admission_no = data.admission_no,
                        source = data.source,
                        tentative_admission_date = data.tentative_admission_date if (data.tentative_admission_date is not None and data.tentative_admission_date != '') else None,
                        status = status,
                        supporting_care_services = (data.supporting_care_services).split(",") if data.supporting_care_services else None,
                        patient_age= uhid.get("age")
                    )
                    db.add(bed_request)
            else:
                raise MutationError("Provide a valid Request Type")
            db.flush()
            if (data.id is None or data.id ==0) and request_type == BedRequestType.ADMISSION.name:
                from celery_worker import send_bed_request_msg
                send_bed_request_msg.apply_async(args=[bed_request.user_contact_no,"BED_ADMISSION_REQUEST",bed_request.created_at.strftime("%d/%m/%Y"),bed_request.wl_no], countdown=0)
            add_bed_remarks(db, bed_request.id, data.remarks, staff_user,bed_request.status.name)
            update_bed_request_hash_tags(db,bed_request.id,data.hash_ids)
            db.commit()
            msg = "Request Added Successfully" if msg is None else msg
            return get_bed_requests(db,None,None,staff_user.id),msg
        except MutationError as ex:
            logger.exception(ex)
            raise MutationError(ex.message)
        except Exception as e:
            logger.exception(e)
            raise MutationError("Failed to save the request")
    
def get_patient_details(uhid: str):
    try:
        if uhid is not None and uhid != "":
            try:
                res = get_access_token()
                token = "Bearer " + res["access_token"]
                headers = {"Authorization": token}
                logger.info(os.environ["AIG_BASE_URL"])
                res1 = handle_request(
                    os.environ["AIG_BASE_URL"] + GET_PATIENT_DETAILS_BY_UHID,
                    headers,{"patientID": uhid})
            except MutationError as ex:
               raise MutationError(ex.message)
            if res1.json().get('response') is not None:
                return res1.json()['response']
            else:   
                raise MutationError("User not registered")
    except MutationError as ex:
        raise MutationError(ex.message)
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while getting patient details")

def record_patient(db, uhid:str, registration_type):
    details= get_patient_details(uhid)
    user= db.query(UserModel).filter(UserModel.umr_no==uhid).one_or_none()
    if user is None:
        user= UserModel(
            name= details["patientName"],
            umr_no=details["patientID"],
            registration_type= registration_type
        )
        db.add(user)
        db.commit()
    return user.id,details
    
def assign_bed(his_db: Session, db: Session, staff_user: int, request_id: int, data: AssignBed):
    try:
        status_list = [RequestStatus.CANCELLED.name,RequestStatus.ADMITTED.name,RequestStatus.TRANSFERED.name,RequestStatus.VACATED.name]
        bed_request: BedRequestModel = db.query(BedRequestModel).filter(BedRequestModel.id == request_id).first()
        if bed_request is None:
            raise MutationError("The bed request is invalid")
        elif bed_request.status.name in status_list:
            raise MutationError("Invalid request")
        staff_user = get_staff_user(db,staff_user)
        alloted_bed= db.query(BedRequestModel.id).filter(BedRequestModel.bed_no==data.bed_no).filter(BedRequestModel.status==RequestStatus.ALLOTED,BedRequestModel.id!= bed_request.id ).first()
        if alloted_bed is not None:
            raise MutationError("Bed is already assigned")
        initial_alloted_by = bed_request.alloted_by
        # shiffting_time = data.shifting_time if (data.shifting_time is not None and data.shifting_time !="") else None
        # if bed_request.type == BedRequestType.ADMISSION:
        #     if (data.admission_status == None or data.admission_status == ""):
        #         raise MutationError("Please provide all the required fields")
        #     bed_request.admission_status = data.admission_status
        # if shiffting_time is not None:
            # current_date_time = datetime.now(pytz.timezone('Asia/Kolkata'))
            # if shiffting_time > current_date_time:
            #     raise MutationError("Invalid Shifting time")
        sql_query = f"EXEC Achala_GetBedStatusListforKiosk @InterfaceID= 1100, @BedNo = '{data.bed_no}'"
        bed = his_db.execute(sql_query).first()
        if bed is None:
            raise MutationError("Invalid Bed Number")
        bed_request.floor = bed["Nurse Station"].split('-')[1].strip() if bed["Nurse Station"] is not None else None
        bed_request.bed_no = data.bed_no
        bed_request.alloted_bed_class = bed["Bed Class"]
        bed_request.bed_status = bed["Bed Status"]
        bed_request.billing_bed_class = data.billing_bed_class if (data.billing_bed_class is not None and data.billing_bed_class !="") else data.alloted_bed_class
        bed_request.alloted_time = func.now()
        bed_request.informed_to = data.informed_to
        # bed_request.shifting_time = shiffting_time
        bed_request.status = RequestStatus.ALLOTED
        bed_request.nurse_station = bed["Nurse Station"]
        bed_request.alloted_by = staff_user.name if staff_user is not None else " "
        # if bed_request.requested_bed_class == data.alloted_bed_class == data.billing_bed_class:
        #     bed_request.reason = "UP SCALE"
        # else:
        if bed_request.requested_bed_class != data.alloted_bed_class and data.alloted_bed_class == data.billing_bed_class:
            bed_request.reason = "UP SCALE"
        elif bed_request.requested_bed_class != data.alloted_bed_class and data.alloted_bed_class != data.billing_bed_class:
            if data.reason is None or data.reason =="":
                raise MutationError("Provide the reason for changing billing bed class")
            bed_request.reason = data.reason
        db.flush()
        if bed_request.type == BedRequestType.ADMISSION and initial_alloted_by is None:
            from celery_worker import send_bed_request_msg
            send_bed_request_msg.apply_async(args=[bed_request.user_contact_no,"BED_ALLOTMENT_REQUEST",bed_request.patient_name,bed_request.wl_no], countdown=0)
        add_bed_remarks(db, bed_request.id, data.bed_ops_remarks, staff_user,bed_request.status.name)
        update_bed_request_hash_tags(db,bed_request.id,data.hash_ids)
        db.commit()
        return get_bed_requests(db,None,None,staff_user.id)
    except MutationError as ex:
        logger.exception(ex)
        raise MutationError(ex.message)
    except DataError as ex:
        logger.exception(ex)
        raise MutationError("You have provided invalid data")
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error while updating bed request")

def get_bed_status(his_db: Session, db: Session, staff_user: int):
    excluded_beds = get_excluded_beds(db)
    nurse_stations = get_staff_user_nurse_stations(db,staff_user)
    nurse_station_condition = f"'{','.join(f'{station}' for station in nurse_stations)}'" if len(nurse_stations) > 0 else ""
    if nurse_station_condition !="":
        sql_query = f"EXEC Achala_GetBedStatusListforKiosk @InterfaceID= 1100, @NurseStation = {nurse_station_condition}"
        if excluded_beds != "":
            sql_query +=f" ,@ExcludeBedNo = {excluded_beds}"
        data  = his_db.execute(sql_query).all()
        return data
    else:
        return []

def get_departments(his_db: Session):
    sql_query = f"EXEC PR_GetEmployeeMasterforKiosk"
    departments  = his_db.execute(sql_query).all()
    return sorted(list(set(map(lambda x: x.Speciality.strip(), departments))))

def get_doctor_by_department(his_db: Session, dep_name: str):
    sql_query = f"EXEC PR_GetEmployeeMasterforKiosk "
    if dep_name is not None and dep_name != "":
        sql_query = sql_query + f" @Speciality = '{dep_name}'"
    doctors  = his_db.execute(sql_query).all()
    return list(map(lambda x: x["Consultant Name"].strip(), doctors))

    
def get_case_type(db: Session):
    types = [case_type.value for case_type in CaseTypeEnum]
    return types

def get_patient_category(db: Session):
    categories = ["NORMAL","VIP","VULNERABLE"]
    return categories

def get_bed_classes(his_db: Session, uhid: str):
    try:
        sql_Query = "EXEC Achala_GetBedStatusListforKiosk @InterfaceID= 1100"
        if uhid is not None and uhid != "":
            sql_Query += f", @UHID = '{uhid}'"
        res_data = his_db.execute(sql_Query).fetchall()
        data = sorted(list(set(map(lambda x: x["Bed Class"], res_data))))
        return data
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occurred while retrieving data")
    
def bed_status_priority(bed_status):
    priority_mapping = {
        'Vacant': 1,
        'HouseKeeping': 2,
        'Discharged but not vacated': 3,
        'Bill Ready': 4,
        'Transferred To OT': 5
    }
    return priority_mapping.get(bed_status, 6)
    
def get_bed_numbers(db: Session,his_db: Session,bedClass:str):
    try:
        assigned_beds = db.query(BedRequestModel.bed_no).filter(BedRequestModel.status == RequestStatus.ALLOTED).all()
        alloted_beds = list(map(lambda x: x.bed_no,assigned_beds))
        exclude_beds_data = db.query(ExcludeBedsModel.bed_no).all()
        excluded_beds = list(map(lambda x: x.bed_no,exclude_beds_data))    
        total_beds_to_exclude = excluded_beds+alloted_beds
        exclude_beds_condition = f" , @ExcludeBedNo = '{','.join(f'{bed}' for bed in total_beds_to_exclude)}'" if(len(total_beds_to_exclude)) > 0 else ''
        bed_class_condition = f", @BedClass = '{bedClass}'" if (bedClass is not None and bedClass != "") else ""
        discharge_query = f"EXEC PR_GetBedAndDischargeStatus @InterfaceID= 1100"
        res_data = his_db.execute(discharge_query).all()
        discarged = list(set(list(map(lambda x: x["Bed"],res_data)))-set(total_beds_to_exclude))
        discharged_beds_condition = f" ,@IncludeBedNo= '{','.join(f'{bed}' for bed in discarged)}'" if len(discarged) > 0 else ""
        query1 = f"""EXEC Achala_GetBedStatusListforKiosk @InterfaceID= 1100,
                    @BedStatus ='Vacant,HouseKeeping,Discharged but not vacated,Bill Ready,Transferred To OT'
                    {exclude_beds_condition}
                    {bed_class_condition}
                    """
        res_data1 = his_db.execute(query1).all()
        discharge_bed_class_condition = bed_class_condition if discharged_beds_condition != "" else ",@BedClass = '{bedClass}'" if (bedClass is not None and bedClass != "") else ""
        query2 = f"""EXEC Achala_GetBedStatusListforKiosk @InterfaceID= 1100
                   {discharged_beds_condition}
                    {discharge_bed_class_condition}
                    """
        # logger.info(query2)
        res_data2 = his_db.execute(query2).all()
        data = res_data1+res_data2
        res_data = sorted(data, key=lambda x: bed_status_priority(x["Bed Status"]))
        return res_data
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while retriving data")

def get_request_status(db: Session):
    status = [status.value for status in RequestStatus]
    return status

def get_user_discharges(his_db: Session, db: Session,staff_user:int):
    excluded_beds = get_excluded_beds(db)
    nurse_stations = get_staff_user_nurse_stations(db,staff_user)
    linked_nurse_stations = ",".join(f"{station}" for station in nurse_stations) if len(nurse_stations)> 0 else ""
    excluded_beds_condition = f",@Bed_Not_In = {excluded_beds}" if excluded_beds != "" else ""
    nurse_station_condition = (f" @Nurse_Station ='{linked_nurse_stations}'" if linked_nurse_stations != "" else "")
    comma_condition = "," if (excluded_beds_condition.strip() != "") and (nurse_station_condition.strip()!="") else ""
    
    if nurse_station_condition.strip() !="":
        sql_query = f"""EXEC PR_GetBedAndDischargeStatus @InterfaceID= 1100 {excluded_beds_condition} {comma_condition} {nurse_station_condition}"""
        logger.info(sql_query)
        res_data = his_db.execute(sql_query).all()
        return res_data
    else:
        return []

def download_excel_file(db: Session):
    try:
        data = db.query(BedRequestModel).all()
        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.append(["Type", "Wl No","UHID","Case Type","Patient Category",
                          "User Contact No","Patient Name","Department","Doctor Name",
                          "Bed Class","Request From","Estimation","Remarks","Bed No","Bed Status",
                          "Alloted Time","Admission Status","Shifting Time","Informed To","Bed Ops Remarks"
                          ,"Status","Requested By","Requested at"])
        for item in data:
            worksheet.append([item.type.name, item.wl_no, item.uhid,item.case_type,item.patient_category,
                              item.user_contact_no, item.patient_name, item.department, item.doctor_name, item.bed_class,
                              item.req_from, item.estimation, item.remarks, item.bed_no, item.bed_status,
                              item.alloted_time.replace(tzinfo=None) if item.alloted_time is not None else None, 
                              item.admission_status, item.shifting_time.replace(tzinfo=None) if item.shifting_time is not None else None,
                              item.informed_to, item.bed_ops_remarks,
                              item.status.name, item.created_by, 
                              item.created_at.replace(tzinfo=None) if item.created_at is not None else None])
        excel_data = io.BytesIO()
        workbook.save(excel_data)
        excel_data.seek(0)
        return excel_data
    except Exception as e:
        logger.exception(e)
        
def withdraw_allocation(db: Session, request_id: int, remarks: str, staff_user: int,hash_ids:Optional[List[int]]=None):
    try:
        staff_user = get_staff_user(db,staff_user)
        bed_request = db.query(BedRequestModel).filter(BedRequestModel.id == request_id).first()
        if bed_request is None:
            raise MutationError("The bed request is invalid")
        if bed_request.status != RequestStatus.REQUESTED and bed_request.status != RequestStatus.ALLOTED:
            raise MutationError("You can't withdraw a request who is already Admitted or Cancelled")
        bed_request.bed_no = None
        bed_request.alloted_bed_class = None
        bed_request.billing_bed_class = None
        bed_request.status = RequestStatus.REQUESTED
        bed_request.bed_status = None
        bed_request.alloted_time = None
        db.flush()
        add_bed_remarks(db, bed_request.id, remarks, staff_user,bed_request.status.name)
        update_bed_request_hash_tags(db,bed_request.id,hash_ids)
        db.commit()
        return get_bed_requests(db,None,None,staff_user.id)
    except MutationError as ex:
        logger.exception(ex)
        raise MutationError(ex.message)
    
def cancel_request(db: Session, request_id: int, remarks: str, staff_user: int,cancellation_remarks:str, status: Optional[str] = None,hash_ids:Optional[List[int]]=None):
    try:
        staff_user = get_staff_user(db,staff_user)
        bed_request = db.query(BedRequestModel).filter(BedRequestModel.id == request_id).first()
        if bed_request is None:
            raise MutationError("The bed request is invalid")
        if bed_request.status == RequestStatus.ADMITTED or bed_request.status == RequestStatus.TRANSFERED:
            raise MutationError("You can't cancel a request who is already Admitted")
        bed_request.bed_no = None
        bed_request.alloted_bed_class = None
        bed_request.billing_bed_class = None
        bed_request.bed_status = None
        bed_request.status = RequestStatus.CANCELLED
        bed_request.completed_at = func.now()
        bed_request.completed_status = True
        bed_request.cancellation_remarks = cancellation_remarks
        db.flush()
        add_bed_remarks(db, bed_request.id, remarks, staff_user,bed_request.status.name)
        update_bed_request_hash_tags(db,bed_request.id,hash_ids)
        db.commit()
        return get_bed_requests(db,status,None,staff_user.id)
    except MutationError as ex:
        logger.exception(ex)
        raise MutationError(ex.message)
    
def get_bed_details(his_db: Session, admission_no: str):
    try:
        sql_query = f"EXEC Achala_GetBedStatusListforKiosk @InterfaceID= 1100, @IPNumber='{admission_no}'"
        data = his_db.execute(sql_query).one()
        return data
    except NoResultFound as e:
        logger.exception(e)
        raise MutationError("Invalid Admission Number")
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to get data ")

def get_priority(db: Session):
    status = [priority.value for priority in PriorityEnum]
    return status

def get_modules(db: Session):
    data = db.query(ModuleModel.module_code,ModuleModel.base_url).all()
    return [{"name": module_code, "url": base_url} for module_code, base_url in data]

def add_bed_remarks(db: Session, bed_request_id, remarks, staff_user, status):
    try:
        bed_remarks = BedRemarksModel(
            bed_request_id = bed_request_id,
            remarks = remarks,
            user_role = staff_user.user_type.code if staff_user is not None else None,
            created_by = staff_user.name if staff_user is not None else " "  ,
            status = status
        )
        db.add(bed_remarks)
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while storing remarks")

def process_allocated_beds(db:Session, his_db:Session):
    try:
        alloted_requested_data = db.query(BedRequestModel).filter(or_(BedRequestModel.status == RequestStatus.ALLOTED,BedRequestModel.status == RequestStatus.REQUESTED)).all()
        if len(alloted_requested_data)> 0:
            for obj in alloted_requested_data:
                sql_query = f"EXEC Achala_GetBedStatusListforKiosk @InterfaceID= 1100, @UHID ='{obj.uhid}' "
                if obj.type == BedRequestType.ADMISSION:
                    ip_no = his_db.execute(sql_query).first()
                    if ip_no is not None:
                        obj.admission_no = ip_no["IP Number"]
                        obj.status = RequestStatus.ADMITTED
                        obj.completed_at= func.now()
                        obj.bed_no = ip_no["Bed No"]
                        obj.bed_status = ip_no["Bed Status"]
                        obj.alloted_bed_class = ip_no["Bed Class"]
                        obj.billing_bed_class = ip_no["Billable BedType"]
                        bed_remarks = BedRemarksModel(
                                bed_request_id = obj.id,
                                remarks = f"Already Admitted in - {ip_no['Bed Class']} [{ip_no['Bed No']}]",
                                created_by = "SERVER", 
                                status = "ADMITTED"
                            )
                        db.add(bed_remarks)
                else:
                    alloted_bed_class = obj.alloted_bed_class if (obj.alloted_bed_class is not None  and  obj.alloted_bed_class  != "")else ""
                    if (obj.alloted_bed_class is not None and obj.alloted_bed_class  != ""):
                        sql_query = sql_query+  f" , @BedClass ='{alloted_bed_class}'"
                    if (obj.req_from == obj.alloted_bed_class):
                        sql_query = sql_query+  f" , @BedNo ='{obj.bed_no}'"
                    ip_no = his_db.execute(sql_query).first()
                    if ip_no is not None :
                        if obj.req_from != ip_no['Bed Class'] or (obj.bed_no is not None and obj.bed_no != ''):
                            obj.admission_no = ip_no["IP Number"]
                            obj.status = RequestStatus.TRANSFERED 
                            obj.completed_at= func.now()
                            bed_remarks = BedRemarksModel(
                                    bed_request_id = obj.id,
                                    remarks = f"Already Transferred in - {ip_no['Bed Class']} [{ip_no['Bed No']}]",
                                    created_by = "SERVER", 
                                    status = "TRANSFERED"
                                )
                            db.add(bed_remarks)
                # if obj.status == RequestStatus.REQUESTED:
                #     sql_query = f"SELECT [Bed Status] FROM GetBedStatusListforKiosk WHERE [Bed No] ='{obj.bed_no}'"
                #     bed_status = his_db.execute(sql_query).first()
                #     if bed_status is not None:
                #         obj.bed_status = bed_status["Bed Status"]
        # checking discharged beds
        admitted_beds = db.query(BedRequestModel.id,BedRequestModel.admission_no, BedRequestModel.alloted_bed_class).filter(or_(BedRequestModel.status == RequestStatus.ADMITTED, BedRequestModel.status == RequestStatus.TRANSFERED)).all()
        for requests in admitted_beds:
            sql_query = f"EXEC Achala_GetBedStatusListforKiosk @InterfaceID= 1100, @IPNumber ='{requests.admission_no}' , @BedClass ='{requests.alloted_bed_class}'"
            discharged_beds = his_db.execute(sql_query).one_or_none()
            if discharged_beds is None :
                db.query(BedRequestModel).filter(BedRequestModel.id == requests.id,BedRequestModel.admission_no==requests.admission_no).update({
                    BedRequestModel.completed_status : True,BedRequestModel.status: RequestStatus.VACATED,BedRequestModel.discharged_time: func.now()},)
        db.commit()
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while fetching data")
    
def mismatch_data(db: Session, his_db: Session):
    try:
        sql_query = f"EXEC Achala_GetBedStatusListforKiosk @InterfaceID= 1100, @CheckIPNotEmpty =1,@CheckBillableBedTypeNotEmpty = 1, @CheckBedClassNotEqualBillable=1"
        his_data = his_db.execute(sql_query).all()
        his_data = list(map(lambda x:x["IP Number"],his_data))
        if len(his_data) > 0:
            for ip_number in his_data:
                res_data = db.query(BedRequestModel).filter(and_(BedRequestModel.admission_no == str(ip_number),BedRequestModel.reason =='Not Available',
                        or_(BedRequestModel.status == RequestStatus.ADMITTED, BedRequestModel.status == RequestStatus.TRANSFERED))).order_by(BedRequestModel.created_at).first()
                if res_data is not None:
                    time = datetime.now(pytz.timezone('Asia/Kolkata'))
                    difference_time = (time - res_data.completed_at.astimezone(pytz.timezone('Asia/Kolkata')))
                    total_time =  difference_time.total_seconds() / 60
                    res_data.mismatch_duration = total_time
        db.commit()
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while fetching data")

def get_mismatch_bed_classes(db: Session,staff_user : int):
        exclude_data = db.query(ExcludeBedsModel.bed_no).all()
        excluded_beds = list(map(lambda x: x.bed_no,exclude_data))
        query = bed_request_query(db,staff_user)
        data = query.filter(BedRequestModel.bed_no.notin_(excluded_beds)).filter(BedRequestModel.alloted_bed_class != BedRequestModel.billing_bed_class).order_by(BedRequestModel.completed_status).all()
        return data

def get_bed_requests_in_date_range(db, from_date:str, to_date:str,staff_user: int):
    try:
        query = bed_request_query(db,staff_user)
        return  query.filter(func.date(BedRequestModel.created_at).between(from_date, to_date)).all()
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while fetching data")

def get_excluded_beds(db: Session):
    try:
        exclude_data = db.query(ExcludeBedsModel.bed_no).all()
        exclude_beds = list(map(lambda x: x.bed_no,exclude_data))
        beds = ""
        if len(exclude_beds) > 0:
            beds = f"'{','.join(f'{bed}' for bed in exclude_beds)}'"
        return beds
    except Exception as e:
        logger.exception(e)
        return ""

def change_bed_request_priority(db: Session, request_id: int, remarks: str,priority: str, staff_user: int):
    try:
        staff_user = get_staff_user(db,staff_user)
        bed_request = db.query(BedRequestModel).filter(BedRequestModel.id == request_id).first()
        if bed_request is None:
            raise MutationError("The bed request is invalid")
        if bed_request.status != RequestStatus.REQUESTED and bed_request.status != RequestStatus.ALLOTED:
            raise MutationError("You can't edit a user who is already Admitted")
        bed_request.priority = PriorityEnum(priority)
        add_bed_remarks(db, bed_request.id, remarks, staff_user,bed_request.status.name)
        db.commit()        
        return get_bed_requests(db,None,None,staff_user.id)
    except MutationError as ex:
        logger.exception(ex)
        raise MutationError(ex.message)

def update_bed_request_status(db: Session, request_id: int, remarks: str, staff_user: int,status:str,hash_ids:Optional[List[int]]=None):
    try:
        staff_user = get_staff_user(db,staff_user)
        bed_request = db.query(BedRequestModel).filter(BedRequestModel.id == request_id).first()
        if bed_request is None:
            raise MutationError("The bed request is invalid")
        logger.info(bed_request.status)
        if status == 'HOLD':
            if (bed_request.status == RequestStatus.ADMITTED or bed_request.status == RequestStatus.TRANSFERED or bed_request.status == RequestStatus.ALLOTED):
                raise MutationError(f"You cannot hold a request that has already been {bed_request.status.name.capitalize()}")
            bed_request.status = RequestStatus.HOLD
        elif status == "UNHOLD":
            bed_request.status = RequestStatus.REQUESTED
        
        db.flush()
        add_bed_remarks(db, bed_request.id, remarks, staff_user,bed_request.status.name)
        update_bed_request_hash_tags(db,bed_request.id,hash_ids)
        db.commit()
        return get_bed_requests(db,None,None,staff_user.id)
    except MutationError as ex:
        logger.exception(ex)
        raise MutationError(ex.message)

def get_common_remarks(db: Session, type: Optional[str]= None):
    try:
        query = db.query(CommonRemarksModel.code)
        if type is not None and type != "":
            query = query.filter(CommonRemarksModel.type == type)
        else:
            query = query.filter(CommonRemarksModel.type == RemarksTypeEnum.CANCELLATION)
        data = query.all()
        remarks = list(map(lambda x: x.code, data))
        return remarks
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while retriving data")
    
def get_er_beds(db:Session,his_db:Session):
    excluded_beds = get_excluded_beds(db)
    sql_qurery = f"EXEC Achala_GetBedStatusListforKiosk @InterfaceID= 1100, @BedClass='ER' ,@ExcludeBedNo = {excluded_beds}"
    data  = his_db.execute(sql_qurery).all()
    res=[]
    for obj in data:
        admitted_time=None
        transfer_request = None
        if obj["IP Number"] is not None and obj["IP Number"]!=0:
            bed_request=db.query(BedRequestModel).filter(BedRequestModel.bed_no==obj["Bed No"]).filter(BedRequestModel.admission_no==str(obj["IP Number"])).order_by(desc(BedRequestModel.created_at)).first()
            transfer_request=(db.query(BedRequestModel).filter(and_(BedRequestModel.admission_no==str(obj["IP Number"]),BedRequestModel.type == BedRequestType.TRANSFER))
                              .filter(and_(BedRequestModel.completed_status == False,BedRequestModel.status != RequestStatus.ADMITTED,BedRequestModel.status != RequestStatus.TRANSFERED)).order_by(desc(BedRequestModel.created_at)).first())
            admitted_time=bed_request.completed_at if bed_request is not None else None
        res.append({
            "bed_no":obj["Bed No"],
            "bed_status":obj["Bed Status"],
            "user_name":obj["Patient Name"],
            "umr_no":obj["UHID"],
            "ip_no":obj["IP Number"],
            "admitted_time":admitted_time,
            "transfer_request":transfer_request
        })
    return res

def get_staff_user(db: Session, staff_user: int):
    staff_user = db.query(StaffUserModel).filter(StaffUserModel.id == staff_user).first()
    if staff_user is None:
        raise MutationError("Staff User Not Found")
    return staff_user

def get_avg_time_of_er_beds(db:Session,his_db: Session):
    today=date.today()
    day_result = (
            db.query(
                func.avg(func.extract('epoch', BedRequestModel.discharged_time - BedRequestModel.completed_at)).label('avg_time'),
                BedRequestModel.floor,func.date(BedRequestModel.created_at).label('date'),func.count(BedRequestModel.id).label('count')
            )
            .filter(BedRequestModel.status == RequestStatus.VACATED,BedRequestModel.alloted_bed_class=='ER')
            .filter(and_(func.date(BedRequestModel.created_at) <= today,func.date(BedRequestModel.created_at) >=today-timedelta(days=6)))
            .group_by(BedRequestModel.floor,func.date(BedRequestModel.created_at)).order_by(func.date(BedRequestModel.created_at).desc())
            .all()
        )
    first_day_of_current_month = today.replace(day=1)
    first_day_of_last_month = (first_day_of_current_month - timedelta(days=1)).replace(day=1)
    month_result= (
                db.query(
                    func.avg(func.extract('epoch', BedRequestModel.discharged_time - BedRequestModel.completed_at)).label('avg_time'),
                    BedRequestModel.floor,func.extract('month', BedRequestModel.created_at).label('month'),func.count(BedRequestModel.id).label('count')
                )
                .filter(BedRequestModel.status == RequestStatus.VACATED,BedRequestModel.alloted_bed_class=='ER')
                .filter(
                    or_(
                        and_(
                            func.date(BedRequestModel.created_at) >= first_day_of_last_month,
                            func.date(BedRequestModel.created_at) < first_day_of_current_month
                        ),
                        and_(
                            func.date(BedRequestModel.created_at) >= first_day_of_current_month,
                            func.date(BedRequestModel.created_at) <= today
                        )
                    )
                )
                .group_by('month',BedRequestModel.floor)
                .all()
                )
    return (day_result,month_result)
    
def get_er_bed_details(db:Session,date:str,floor:str):
    try:
        er_beds=   (
            db.query(BedRequestModel.uhid,BedRequestModel.completed_at,BedRequestModel.bed_no,BedRequestModel.discharged_time,BedRequestModel.patient_name,BedRequestModel.id)
            .filter(BedRequestModel.status == RequestStatus.VACATED,BedRequestModel.alloted_bed_class=='ER')
            .filter(and_(func.date(BedRequestModel.created_at)==date,BedRequestModel.floor==floor))
            .all()
        )
        return er_beds
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while retriving data")
    
def update_pending_bed_request_status(db: Session):
    try:
        thershold_date = datetime.now(pytz.timezone('Asia/Kolkata')).date() + timedelta(days=int(os.environ["THERSHOLD_DAYS"]))
        future_requests = (
            db.query(BedRequestModel.id, BedRequestModel.tentative_admission_date).filter(BedRequestModel.status == RequestStatus.PENDING).filter(BedRequestModel.tentative_admission_date<= thershold_date).all()
        )
        for request in future_requests:
            db.query(BedRequestModel).filter(BedRequestModel.id == request.id).update({BedRequestModel.status: RequestStatus.REQUESTED})
            bed_remarks = BedRemarksModel(
                                bed_request_id = request.id,
                                remarks = f"Tentative Admission Date - {request.tentative_admission_date}",
                                created_by = "SERVER", 
                                status = "REQUESTED"
                            )
            db.add(bed_remarks)
        db.commit()
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while Updating data")

def update_admission_date(db: Session, request_id: int, remarks: str, staff_user: int,admission_date: str):
    try:
        staff_user = get_staff_user(db,staff_user)
        bed_request = db.query(BedRequestModel).filter(BedRequestModel.id == request_id).first()
        if bed_request is None:
            raise MutationError("The bed request is invalid")
        status = get_tentative_bed_status(admission_date,datetime.now(pytz.timezone('Asia/Kolkata')))
        bed_request.status = status
        bed_request.tentative_admission_date = admission_date
        db.flush()
        add_bed_remarks(db, bed_request.id, remarks, staff_user,bed_request.status.name)
        db.commit()
        return get_bed_requests(db,'PENDING',None,staff_user.id)
    except MutationError as ex:
        logger.exception(ex)
        raise MutationError(ex.message)

def get_tentative_bed_status(admission_date,today):
    status = None
    if admission_date is not None and admission_date != "":
        if datetime.strptime(admission_date, "%Y-%m-%d").date() > today.date()+timedelta(days=int(os.environ["THERSHOLD_DAYS"])):
            status = RequestStatus.PENDING
        else:
            status = RequestStatus.REQUESTED
    else:
        status = RequestStatus.REQUESTED
    return status

def edit_bed_request(db: Session, request_type:str, data: BedAllotRequest,staff_user: StaffUserModel,bed_request: BedRequestModel):
    try:
        bed_request.user_contact_no = data.user_contact_no
        bed_request.department = data.department
        bed_request.doctor_name = data.doctor_name
        bed_request.requested_bed_class =data.requested_bed_class
        bed_request.supporting_care_services = (data.supporting_care_services).split(",") if data.supporting_care_services else None
        bed_request.priority = PriorityEnum.REGULAR if data.priority is None or data.priority == "" else data.priority
        today = datetime.now(pytz.timezone('Asia/Kolkata'))
        status = get_tentative_bed_status(data.tentative_admission_date,today)
        if request_type == BedRequestType.ADMISSION.name:
            bed_request.status = status
            bed_request.case_type = data.case_type
            bed_request.rate_of_contract = data.rate_of_contract if (data.rate_of_contract is not None and data.rate_of_contract !="") else None
            bed_request.tentative_admission_date = data.tentative_admission_date 
            bed_request.estimation = data.estimation
            bed_request.estimated_by = data.estimated_by
            bed_request.patient_category = data.patient_category
            bed_request.referred_by = data.referred_by if (data.referred_by is not None or data.referred_by !="") else None
        else:
            bed_request.tentative_admission_date = data.tentative_admission_date 
            bed_request.status = status
            bed_request.req_from = data.req_from
            bed_request.source = data.source
        db.flush()
        return bed_request,"Request Updated Successfully"
    except MutationError as ex:
        logger.exception(ex)
        raise MutationError(ex.message)
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to edit the request")
def get_nurse_stations(his_db: Session):
    try:
        sql_query = "EXEC Achala_GetBedStatusListforKiosk @InterfaceID= 1100"
        data = his_db.execute(sql_query).all()
        nurse_stations = list(set(map(lambda x: x["Nurse Station"],data)))
        return nurse_stations
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while retriving data")

def get_staff_user_nurse_stations(db: Session, staff_user_id: int):
    try:
        stations_data = db.query(RelStaffUserNurseStationModel.nurse_station).filter(RelStaffUserNurseStationModel.staff_user_id == staff_user_id).all()
        nurse_stations = list(map(lambda x: x.nurse_station,stations_data)) 
        return nurse_stations
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while retriving data")

def bed_request_query(db:Session,staff_user:int):
    staff_user : StaffUserModel = get_staff_user(db,staff_user)
    nurse_stations = get_staff_user_nurse_stations(db,staff_user.id)
    allocation_count = db.query(RelStaffUserResourceModel).join(ResourceModel).filter(RelStaffUserResourceModel.staff_user_id == staff_user.id, ResourceModel.code == 'INCLUDE_UNASSIGNED_REQUESTS').count()
    if allocation_count > 0:
        query =  db.query(BedRequestModel).filter(or_(BedRequestModel.nurse_station.in_(nurse_stations),BedRequestModel.nurse_station.is_(None),BedRequestModel.created_by == staff_user.name))
    else:
        query =  db.query(BedRequestModel).filter(or_(BedRequestModel.nurse_station.in_(nurse_stations),BedRequestModel.created_by == staff_user.name))
    return query

def update_discharged_beds(db: Session, his_db: Session):
    sql_query = f"EXEC PR_GetBedAndDischargeStatus @InterfaceID=1100"
    res_data = his_db.execute(sql_query).all()
    his_ip_numbers = list(map(lambda x: x["IP Number"],res_data))
    present_ip_numbers = db.scalars(db.query(DischargeHistoryModel.ip_number).filter(DischargeHistoryModel.ip_number.in_(his_ip_numbers)).filter(DischargeHistoryModel.completion_status==False)).all()
    discharged_ip_numbers = db.scalars(db.query(DischargeHistoryModel.ip_number).filter(DischargeHistoryModel.ip_number.notin_(his_ip_numbers)).filter(DischargeHistoryModel.completion_status==False)).all()
    if len(discharged_ip_numbers) > 0:
        db.query(DischargeHistoryModel).filter(DischargeHistoryModel.completion_status==False).filter(DischargeHistoryModel.ip_number.in_(discharged_ip_numbers)).update({DischargeHistoryModel.completion_status: True,DischargeHistoryModel.discharge_time: func.now()})
    ip_numbers_not_in_db = list(set(his_ip_numbers)-set(present_ip_numbers))
    discharge_data = list(
                        map(
                            lambda data: {
                                'uhid': data["UHID"],
                                'ip_number': data["IP Number"],
                                'patient_name': data["Patient Name"],
                                'bed': data["Bed"],
                                'admission_date_time': data["Admission DateTime"],
                                'requested_discharge_date': data["Requested Discharge Date"],
                                'nurse_clearance_date_time': data["Nurse Clearance DateTime"],
                                'blood_bank_clearance_date_time': data["BloodBank Clearance DateTime"],
                                'discharge_summary_date_time': data["Discharge Summary DateTime"],
                                'fnb_clearance_date_time': data["FNB Clearance DateTime"],
                                'pharmacy_clearance_date_time': data["Pharmacy Clearance DateTime"],
                                'audit_clrsave_date_time': data["Audit Clearance DateTime"],
                                'billing_ack_date_time': data["Billing Ack DateTime"],
                                'bill_ready_date_time': data["Bill Ready DateTime"],
                                'clinical_discharge': data["Clinical Discharge"],
                                'otc_clearance_date_time': data["OT Clearance DateTime"]
                            },
                            filter(lambda data: data["IP Number"] in ip_numbers_not_in_db, res_data)
                        )
                    )
    db.bulk_insert_mappings(DischargeHistoryModel,discharge_data)
    existing_ip_data = list(filter(lambda data: data["IP Number"] in present_ip_numbers, res_data))
    if len(existing_ip_data)>0:
        for data in existing_ip_data:
            updated_data = {
                'patient_name': data["Patient Name"],
                'bed': data["Bed"],
                'admission_date_time': data["Admission DateTime"],
                'requested_discharge_date': data["Requested Discharge Date"],
                'nurse_clearance_date_time': data["Nurse Clearance DateTime"],
                'blood_bank_clearance_date_time': data["BloodBank Clearance DateTime"],
                'discharge_summary_date_time': data["Discharge Summary DateTime"],
                'fnb_clearance_date_time': data["FNB Clearance DateTime"],
                'pharmacy_clearance_date_time': data["pharmaPharmacy Clearance DateTimecyclearancedatetime"],
                'audit_clrsave_date_time': data["Audit Clearance DateTime"],
                'billing_ack_date_time': data["Billing Ack DateTime"],
                'bill_ready_date_time': data["Bill Ready DateTime"],
                'clinical_discharge': data["Clinical Discharge"],
                'otc_clearance_date_time': data["OT Clearance DateTime"]
            }
            db.query(DischargeHistoryModel).filter(DischargeHistoryModel.completion_status==False).filter(DischargeHistoryModel.ip_number == data["IP Number"]).update(updated_data)
    db.commit()

def store_bed_status_history(db: Session, his_db: Session):
    sql_query = f"EXEC Achala_GetBedStatusListforKiosk @InterfaceID= 1100"
    res_data = his_db.execute(sql_query).all()
    history_date = date.today() - timedelta(days=1)
    mapped_list = list(
        map(
            lambda data: {
                'his_id': data["Id"],
                'bed_no': data["Bed No"],
                'bed_class': data["Bed Class"],
                'bed_status': data["Bed Status"],
                'nurse_station': data["Nurse Station"],
                'uhid': data["UHID"],
                'ip_number': data["IP Number"],
                'patient_name': data["Patient Name"],
                'speciality': data["Speciality"],
                'primary_consultant': data["Primary Consultant"],
                'secondary_consultant': data["Secondary Consultant"],
                'contact_number': data["Contact number"],
                'req_bed_type': data["Req BedType"],
                'billable_bed_type': data["Billable BedType"],
                'date': history_date
            },
            res_data
        )
    )
    db.bulk_insert_mappings(BedStatusHistoryModel,mapped_list)
    db.commit()

def generate_case(type,condition,status):
    if condition is None:
        return func.count(
                    case([
                        (
                            BedRequestModel.type == type,
                            BedRequestModel.id
                        )
                    ])
                )
    elif condition is not None:
        return and_(
                    BedRequestModel.type ==type,
                    BedRequestModel.status.in_(status),
                )

def get_bed_360_reports(db: Session, start_date: str, end_date:str):
    try:
        created_admissions_count = generate_case(BedRequestType.ADMISSION,None,None).label('CreatedAdmissionsCount')
        created_transfers_count = generate_case(BedRequestType.TRANSFER,None,None).label('CreatedTransfersCount')

        allotted_admissions_count = generate_case(BedRequestType.ADMISSION,None,None).label('AllotedAdmissionsCount')
        allotted_transfers_count = generate_case(BedRequestType.TRANSFER,None,None).label('AllotedTransfersCount')

        admitted_admissions_count = func.count(case([(generate_case(BedRequestType.ADMISSION,'completed',["ADMITTED",'VACATED']), BedRequestModel.id)])).label('CompletedAdmissionsCount')
        admitted_transfers_count = func.count(case([(generate_case(BedRequestType.TRANSFER,'completed',["TRANSFERED",'VACATED']), BedRequestModel.id)])).label('CompletedTransfersCount')

        cancelled_admissions_count = func.count(case([(generate_case(BedRequestType.ADMISSION,'cancelled',['CANCELLED']), BedRequestModel.id)])).label('CancelledAdmissionsCount')
        cancelled_transfers_count = func.count(case([(generate_case(BedRequestType.TRANSFER,'cancelled',['CANCELLED']), BedRequestModel.id)])).label('CancelledTransfersCount')

        total_created_counts = (
            db.query(
            BedRequestModel.requested_bed_class,
            created_admissions_count,
            created_transfers_count
        ).group_by(BedRequestModel.requested_bed_class)
        .filter(func.date(BedRequestModel.created_at)>=start_date,func.date(BedRequestModel.created_at)<=end_date)
        .order_by(
                case([
                    (BedRequestModel.requested_bed_class=='General',0),
                    (BedRequestModel.requested_bed_class=='Sharing',1),
                    (BedRequestModel.requested_bed_class=='Single',2),
                    (BedRequestModel.requested_bed_class=='Deluxe',3),
                    (BedRequestModel.requested_bed_class=='Suite',4),
                ],else_=5)
            )
        .all()
        )
        total_alloted_counts = (
            db.query(
            BedRequestModel.alloted_bed_class,
            allotted_admissions_count,
            allotted_transfers_count
        ).group_by(BedRequestModel.alloted_bed_class)
        .filter(func.date(BedRequestModel.alloted_time)>=start_date,func.date(BedRequestModel.alloted_time)<=end_date)
        .order_by(
                case([
                    (BedRequestModel.alloted_bed_class=='General',0),
                    (BedRequestModel.alloted_bed_class=='Sharing',1),
                    (BedRequestModel.alloted_bed_class=='Single',2),
                    (BedRequestModel.alloted_bed_class=='Deluxe',3),
                    (BedRequestModel.alloted_bed_class=='Suite',4),
                ],else_=5)
            )
        .all()
        )
        total_admitted_cancelled_counts =(
            db.query(
            admitted_admissions_count,
            admitted_transfers_count,
            cancelled_admissions_count,
            cancelled_transfers_count
        )
        .filter(func.date(BedRequestModel.completed_at)>=start_date,func.date(BedRequestModel.completed_at)<=end_date)
        .all()
        )
        avg_times_query = (
            db.query(
                func.date(BedRequestModel.created_at).label('date'),
                BedRequestModel.floor,
                func.avg(
            case(
                [
                    (BedRequestModel.type == BedRequestType.ADMISSION,
                     func.extract('epoch', BedRequestModel.alloted_time - BedRequestModel.created_at))
                ], else_=None
            )
        ).label('AvgAdmissionAllotedTime'),
        func.avg(
            case(
                [
                    (BedRequestModel.type == BedRequestType.TRANSFER,
                     func.extract('epoch', BedRequestModel.alloted_time - BedRequestModel.created_at))
                ], else_=None
            )
        ).label('AvgTransferAllotedTime'),
        func.avg(
            case(
                [
                    (BedRequestModel.type == BedRequestType.ADMISSION,
                     func.extract('epoch', BedRequestModel.completed_at - BedRequestModel.alloted_time))
                ], else_=None
            )
        ).label('AvgAdmissionAdmittedTime'),
        func.avg(
            case(
                [
                    (BedRequestModel.type == BedRequestType.TRANSFER,
                     func.extract('epoch', BedRequestModel.completed_at - BedRequestModel.alloted_time))
                ], else_=None
            )
        ).label('AvgTransferAdmittedTime'),
            )
            .filter(BedRequestModel.status !=RequestStatus.CANCELLED,BedRequestModel.status !=RequestStatus.REQUESTED,BedRequestModel.status !=RequestStatus.PENDING)
            .filter(func.date(BedRequestModel.created_at)>=start_date,func.date(BedRequestModel.created_at)<=end_date)
            .filter(BedRequestModel.floor.isnot(None))
            .order_by(func.date(BedRequestModel.created_at).asc(),case(
                [
                    (BedRequestModel.floor == 'B1', 0),
                    (BedRequestModel.floor == 'GF', 1),
                    (func.cast(BedRequestModel.floor, Integer) != None, func.cast(BedRequestModel.floor, Integer)),
                ],
                else_=3,
            ))
            .group_by(func.date(BedRequestModel.created_at),BedRequestModel.floor).all()
        )

        time_range = (
            db.query(
                func.date(BedRequestModel.created_at).label('date'),
                func.count(case([(and_(BedRequestModel.type==BedRequestType.ADMISSION,func.extract('hour', BedRequestModel.created_at) >= 0,func.extract('hour', BedRequestModel.created_at) < 6),BedRequestModel.id)],else_=None)).label('AdmisionFirstQuarter'),
                func.count(case([(and_(BedRequestModel.type==BedRequestType.ADMISSION,func.extract('hour', BedRequestModel.created_at) >= 6,func.extract('hour', BedRequestModel.created_at) < 12),BedRequestModel.id)],else_=None)).label('AdmisionSecondQuarter'),
                func.count(case([(and_(BedRequestModel.type==BedRequestType.ADMISSION,func.extract('hour', BedRequestModel.created_at) >= 12,func.extract('hour', BedRequestModel.created_at) < 18),BedRequestModel.id)],else_=None)).label('AdmisionThirdQuarter'),
                func.count(case([(and_(BedRequestModel.type==BedRequestType.ADMISSION,func.extract('hour', BedRequestModel.created_at) >= 18,func.extract('hour', BedRequestModel.created_at) < 23),BedRequestModel.id)],else_=None)).label('AdmisionFourthQuarter'),
                func.count(case([(and_(BedRequestModel.type==BedRequestType.TRANSFER,func.extract('hour', BedRequestModel.created_at) >= 0,func.extract('hour', BedRequestModel.created_at) < 6),BedRequestModel.id)],else_=None)).label('TransferFirstQuarter'),
                func.count(case([(and_(BedRequestModel.type==BedRequestType.TRANSFER,func.extract('hour', BedRequestModel.created_at) >= 6,func.extract('hour', BedRequestModel.created_at) < 12),BedRequestModel.id)],else_=None)).label('TransferSecondQuarter'),
                func.count(case([(and_(BedRequestModel.type==BedRequestType.TRANSFER,func.extract('hour', BedRequestModel.created_at) >= 12,func.extract('hour', BedRequestModel.created_at) < 18),BedRequestModel.id)],else_=None)).label('TransferThirdQuarter'),
                func.count(case([(and_(BedRequestModel.type==BedRequestType.TRANSFER,func.extract('hour', BedRequestModel.created_at) >= 18,func.extract('hour', BedRequestModel.created_at) < 23),BedRequestModel.id)],else_=None)).label('TransferFourthQuarter'),
                
            )
            .filter(func.date(BedRequestModel.created_at)>=start_date,func.date(BedRequestModel.created_at)<=end_date)
            .group_by(func.date(BedRequestModel.created_at)).all()
        )
        time_range_bed_location = (
            db.query(
                func.date(BedRequestModel.created_at).label('date'),
                BedRequestModel.req_from,
                func.count(case([(and_(BedRequestModel.type==BedRequestType.TRANSFER,func.extract('hour', BedRequestModel.created_at) >= 0,func.extract('hour', BedRequestModel.created_at) < 6),BedRequestModel.id)],else_=None)).label('TransferFirstQuarter'),
                func.count(case([(and_(BedRequestModel.type==BedRequestType.TRANSFER,func.extract('hour', BedRequestModel.created_at) >= 6,func.extract('hour', BedRequestModel.created_at) < 12),BedRequestModel.id)],else_=None)).label('TransferSecondQuarter'),
                func.count(case([(and_(BedRequestModel.type==BedRequestType.TRANSFER,func.extract('hour', BedRequestModel.created_at) >= 12,func.extract('hour', BedRequestModel.created_at) < 18),BedRequestModel.id)],else_=None)).label('TransferThirdQuarter'),
                func.count(case([(and_(BedRequestModel.type==BedRequestType.TRANSFER,func.extract('hour', BedRequestModel.created_at) >= 18,func.extract('hour', BedRequestModel.created_at) < 23),BedRequestModel.id)],else_=None)).label('TransferFourthQuarter'), 
            )
            .filter(func.date(BedRequestModel.created_at)>=start_date,func.date(BedRequestModel.created_at)<=end_date)
            .filter(BedRequestModel.req_from.isnot(None),BedRequestModel.alloted_time.isnot(None))
            .group_by(func.date(BedRequestModel.created_at),BedRequestModel.req_from).all()
        )

        return total_created_counts, total_alloted_counts,total_admitted_cancelled_counts,avg_times_query,time_range,time_range_bed_location
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured in fetching data")

def create_header(ws,start_cell,cells_to_merge,header_name,header_type):
    ws[start_cell] = header_name
    ws.merge_cells(cells_to_merge)
    name_cell = ws[start_cell]
    name_cell.alignment = Alignment(horizontal='center')
    name_cell.font = Font(bold=True)
    if header_type =='MAIN_HEADER':
        name_cell.font = Font(bold=True,color="0977AF", size=13)

def append_data(ws,start_row,start_col,data):
    for obj in data:
        ws.cell(row=start_row, column=start_col, value=obj)
        start_col += 1

def apply_border(ws,start_row,start_col,end_row,end_col):
    thin_border = Border(left=Side(style='thin'), 
                     right=Side(style='thin'), 
                     top=Side(style='thin'), 
                     bottom=Side(style='thin'))

    for row in ws.iter_rows(min_row=start_row, max_row=end_row, min_col=start_col, max_col=end_col):
        for cell in row:
            cell.border = thin_border

def download_bed360_reports(db,start_date,end_date):
    try:
        total_created_counts, total_alloted_counts,total_admitted_cancelled_counts,avg_times,time_ranges,time_range_bed_location=get_bed_360_reports(db,start_date,end_date)

        header_fields=['Created Requests','Alloted Requests','Admitted Requests','Cancelled Requests']
        bed_classes = ['General', 'Sharing', 'Single', 'Deluxe', 'Suite', 'Others']

        total_admission_requests=dict.fromkeys(header_fields, 0)
        total_transfer_requests=dict.fromkeys(header_fields, 0)
        admission_created_bed_class_count=dict.fromkeys(bed_classes, 0)
        admission_alloted_bed_class_count=dict.fromkeys(bed_classes, 0)
        transfer_created_bed_class_count=dict.fromkeys(bed_classes, 0)
        transfer_alloted_bed_class_count=dict.fromkeys(bed_classes, 0)

        for bed_class,admission_count,transfer_count in total_created_counts:
            if bed_class not in bed_classes:
                admission_created_bed_class_count['Others'] +=admission_count
                transfer_created_bed_class_count['Others'] +=transfer_count
            else:
                admission_created_bed_class_count[bed_class]=admission_count
                transfer_created_bed_class_count[bed_class]=transfer_count
            total_admission_requests['Created Requests'] +=admission_count
            total_transfer_requests['Created Requests'] +=transfer_count
        
        for bed_class,admission_count,transfer_count in total_alloted_counts:
            if bed_class not in bed_classes:
                admission_alloted_bed_class_count['Others'] +=admission_count
                transfer_alloted_bed_class_count['Others'] +=transfer_count
            else:
                admission_alloted_bed_class_count[bed_class]=admission_count
                transfer_alloted_bed_class_count[bed_class]=transfer_count
            total_admission_requests['Alloted Requests'] +=admission_count
            total_transfer_requests['Alloted Requests'] +=transfer_count

        for data in total_admitted_cancelled_counts:
            total_admission_requests['Admitted Requests'] = data.CompletedAdmissionsCount
            total_admission_requests['Cancelled Requests'] = data.CancelledAdmissionsCount
            total_transfer_requests['Admitted Requests'] = data.CompletedTransfersCount
            total_transfer_requests['Cancelled Requests'] = data.CancelledTransfersCount
          
        logger.info(total_transfer_requests)
        logger.info(transfer_created_bed_class_count)
        wb = Workbook()
        ws = wb.active
        ws.title = "Reports"
        #Main Header
        create_header(ws,'A1','A1:T1','Bed 360 Reports','MAIN_HEADER')
        ws['A2']='Date Range'
        ws['B2']=f'{start_date}/{end_date}'
        ws.merge_cells(start_row=2, start_column=2, end_row=2, end_column=6)
        name_cell = ws['B2']
        name_cell.alignment = Alignment(horizontal='center')
        create_header(ws,'A3','A3:D3','Admission Requests In Date Range',None)
        ws.append(header_fields)
        ws.append(list(total_admission_requests.values()))
        apply_border(ws,3,1,5,len(header_fields))
        
        create_header(ws,'F3','F3:K3','Admission Created Requests',None)
        append_data(ws,4,6,bed_classes)
        append_data(ws,5,6,list(admission_created_bed_class_count.values()))
        apply_border(ws,3,6,5,6+len(bed_classes)-1)

        create_header(ws,'M3','M3:R3','Admission Alloted Requests',None)
        append_data(ws,4,13,bed_classes)
        append_data(ws,5,13,list(admission_alloted_bed_class_count.values()))
        apply_border(ws,3,13,5,13+len(bed_classes)-1)

        create_header(ws,'A7','A7:D7','Transfer Requests In Date Range',None)
        ws.append(header_fields)
        ws.append(list(total_transfer_requests.values()))
        apply_border(ws,7,1,9,len(header_fields))
        
        create_header(ws,'F7','F7:K7','Transfer Created Requests',None)
        append_data(ws,8,6,bed_classes)
        append_data(ws,9,6,list(transfer_created_bed_class_count.values()))
        apply_border(ws,7,6,9,6+len(bed_classes)-1)

        create_header(ws,'M7','M7:R7','Transfer Alloted Requests',None)
        append_data(ws,8,13,bed_classes)
        append_data(ws,9,13,list(transfer_alloted_bed_class_count.values()))
        apply_border(ws,7,13,9,13+len(bed_classes)-1)

        table_start_row = 11
        table_columns = ['Date','Floor','Admissions','Transfers']
        alloted_start_col = 1
        create_header(ws,f'A{table_start_row}',f'A{table_start_row}:D{table_start_row}','TAT Report Requested to Alloted Time',None)
        append_data(ws,table_start_row+1,alloted_start_col,table_columns)
        for values in avg_times:
            values=list(values)
            values[0] = values[0].strftime('%Y-%m-%d')
            values[2]=timedelta(seconds=float(values[2])) if values[2] is not None else 0
            values[3]=timedelta(seconds=float(values[3])) if values[3] is not None else 0
            ws.append(values[:4])
        create_header(ws,f'G{table_start_row}',f'G{table_start_row}:J{table_start_row}','TAT Report Alloted to Admitted Time',None)
        admitted_start_col = 7
        append_data(ws,table_start_row+1,admitted_start_col,table_columns)
        for row_index, row_data in enumerate(avg_times, start=table_start_row+2):
            date = row_data.date.strftime("%Y-%m-%d")
            ws.cell(row=row_index, column=admitted_start_col, value=date)
            ws.cell(row=row_index, column=admitted_start_col+1, value=row_data.floor)
            ws.cell(row=row_index, column=admitted_start_col+2, value=timedelta(seconds=float(row_data.AvgAdmissionAdmittedTime)) if row_data.AvgAdmissionAdmittedTime is not None else 0) 
            ws.cell(row=row_index, column=admitted_start_col+3, value=timedelta(seconds=float(row_data.AvgTransferAdmittedTime)) if row_data.AvgTransferAdmittedTime is not None else 0)
        
        apply_border(ws,table_start_row,alloted_start_col,table_start_row + len(avg_times)+1,alloted_start_col + len(table_columns)-1)
        apply_border(ws,table_start_row,admitted_start_col,table_start_row + len(avg_times)+1,admitted_start_col + len(table_columns)-1)

        time_columns=['Q1/00:01 AM- 5:59AM','Q2/6:00 AM - 11:59 AM','Q3/12:00 Noon - 17:59 PM','Q4/18:00 PM - 23:59 PM']
        create_header(ws,f'L{table_start_row}',f'L{table_start_row}:P{table_start_row}','Admission Requests in Time Range',None)
        append_data(ws,table_start_row+1,12,['Date']+time_columns)
        time_start_col_1=12
        for row_index,row_data in enumerate(time_ranges,start=table_start_row+2):
            date = row_data.date.strftime("%Y-%m-%d")
            ws.cell(row=row_index, column=time_start_col_1, value=date)
            ws.cell(row=row_index, column=time_start_col_1+1, value=row_data.AdmisionFirstQuarter)
            ws.cell(row=row_index, column=time_start_col_1+2, value=row_data.AdmisionSecondQuarter)
            ws.cell(row=row_index, column=time_start_col_1+3, value=row_data.AdmisionThirdQuarter)
            ws.cell(row=row_index, column=time_start_col_1+4, value=row_data.AdmisionFourthQuarter)
        apply_border(ws,table_start_row,time_start_col_1,table_start_row + len(time_ranges)+1,time_start_col_1 + len(time_columns))
        
        create_header(ws,f'S{table_start_row}',f'S{table_start_row}:W{table_start_row}','Transfer Requests in Time Range',None)
        append_data(ws,table_start_row+1,19,['Date']+time_columns)
        time_start_col_2=19
        for row_index,row_data in enumerate(time_ranges,start=table_start_row+2):
            date = row_data.date.strftime("%Y-%m-%d")
            ws.cell(row=row_index, column=time_start_col_2, value=date)
            ws.cell(row=row_index, column=time_start_col_2+1, value=row_data.TransferFirstQuarter)
            ws.cell(row=row_index, column=time_start_col_2+2, value=row_data.TransferSecondQuarter)
            ws.cell(row=row_index, column=time_start_col_2+3, value=row_data.TransferThirdQuarter)
            ws.cell(row=row_index, column=time_start_col_2+4, value=row_data.TransferFourthQuarter)
        apply_border(ws,table_start_row,time_start_col_2,table_start_row + len(time_ranges)+1,time_start_col_2 + len(time_columns))

        table_start_row= table_start_row+2+len(time_ranges)+1
        create_header(ws,f'S{table_start_row}',f'S{table_start_row}:X{table_start_row}','Transfer Requests - Requested From - Bed Location Category',None)
        append_data(ws,table_start_row+1,19,['Date','Bed Location']+time_columns)
        time_start_col_2=19
        for row_index,row_data in enumerate(time_range_bed_location,start=table_start_row+2):
            date = row_data.date.strftime("%Y-%m-%d")
            ws.cell(row=row_index, column=time_start_col_2, value=date)
            ws.cell(row=row_index, column=time_start_col_2+1, value=row_data.req_from)
            ws.cell(row=row_index, column=time_start_col_2+2, value=row_data.TransferFirstQuarter)
            ws.cell(row=row_index, column=time_start_col_2+3, value=row_data.TransferSecondQuarter)
            ws.cell(row=row_index, column=time_start_col_2+4, value=row_data.TransferThirdQuarter)
            ws.cell(row=row_index, column=time_start_col_2+5, value=row_data.TransferFourthQuarter)
        apply_border(ws,table_start_row,time_start_col_2,table_start_row + len(time_range_bed_location)+1,time_start_col_2 + len(time_columns)+1)
            
        file_path = f"data/bed_reports.xlsx"
        wb.save(file_path)
        data = open(file_path, 'rb').read()
        base64_encoded = base64.b64encode(data).decode('UTF-8')
        return base64_encoded
    except MutationError as e:
        logger.exception(e)
        raise MutationError(e.message)
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while creating file")
    
def get_hash_tags(db:Session):
    return db.query(MasterHashTagsModel).filter(MasterHashTagsModel.type == HashTagTypeENum.BED360).filter(MasterHashTagsModel.status == StatusEnum.ACTIVE).all()

def update_bed_request_hash_tags(db,bed_request_id,hash_ids):
    db.query(RelBedRequestHashTagModel).filter(
                        RelBedRequestHashTagModel.bed_request_id==bed_request_id).delete()
    bed_request_hash_tags = []
    if hash_ids is not None and len(hash_ids) > 0:
        bed_request_hash_tags = list(map(lambda pair: {"bed_request_id": pair[0], "hash_tag_id": pair[1]}, product([bed_request_id], hash_ids)))
    db.bulk_insert_mappings(RelBedRequestHashTagModel, bed_request_hash_tags)


def update_hash_tags(db: Session, request_id: int, remarks: str, hash_ids, staff_user,status):
    try:
        staff_user = get_staff_user(db,staff_user)
        bed_request = db.query(BedRequestModel).filter(BedRequestModel.id == request_id).first()
        if bed_request is None:
            raise MutationError("The bed request is invalid")
        update_bed_request_hash_tags(db,bed_request.id,hash_ids)
        if remarks is not None and remarks!='':
            add_bed_remarks(db, bed_request.id, remarks, staff_user,bed_request.status.name)
        db.commit()
        return get_bed_requests(db,status,None,staff_user.id)
    except MutationError as ex:
        logger.exception(ex)
        raise MutationError(ex.message)

def get_supporting_care_services(db: Session):
    data = db.query(SupportinCareServiceModel.name).all()
    return data

def send_bed_request_whatsapp_msg(phone_number, event_code,var1,var2):
    try:
        res1=handle_request1(os.environ["ONE_AIG_BASE_URL"]+ONE_AIG_SEND_WHATSAPP_MSG,None,
                    {
                        "event": event_code,
                        "phoneNumber": phone_number,
                        "var1": var1,
                        "var2": var2
                    }
            )
        logger.info(res1)
        logger.info(res1.json())
    except Exception as ex:
        logger.exception(ex)