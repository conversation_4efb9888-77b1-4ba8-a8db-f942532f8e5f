"""page number columns added

Revision ID: f1e5951d7fc6
Revises: 06117c3e7767
Create Date: 2023-12-13 11:28:25.944021

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f1e5951d7fc6'
down_revision = '06117c3e7767'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('printer', sa.Column('total_pages', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('printer', 'total_pages')
    # ### end Alembic commands ###
