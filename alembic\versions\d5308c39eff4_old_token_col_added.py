"""old token col added

Revision ID: d5308c39eff4
Revises: 85726da17e66
Create Date: 2025-04-09 16:56:48.596539

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd5308c39eff4'
down_revision = '85726da17e66'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_service', sa.Column('old_token_id', sa.Integer(), nullable=True), schema='queue')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_service', 'old_token_id', schema='queue')
    # ### end Alembic commands ###
