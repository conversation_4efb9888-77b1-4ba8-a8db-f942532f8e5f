from datetime import datetime
import json
import os
from fastapi import APIRouter, Request
import logging

from jose import JWSError

from operation.resolvers import check_user_type_has_access
logger=logging.getLogger()
from fastapi import File, UploadFile
from util.globals import handle_request, handle_request1
from jose import jws

router = APIRouter(
    prefix="/pacs-report",
    tags=["pacs-report"],
    responses={404: {"description": "Not found"}},
)

@router.post("/upload")
def upload(request: Request,file: UploadFile = File(...)):
    try:
        try:
            token=request.headers["Authorization"]
            payload = jws.verify(token.split("Bearer ")[1], key=os.environ["SECRET_KEY"], algorithms=[os.environ["ALGORITHM"]])
            content=json.loads(payload)
            if content["exp"]< datetime.now().isoformat() :
                return {"status_code":"500","message": "token expired"}
            if content["sub"]["user_id"]!='emr_user':
                return {"status_code":"500","message": "un-authorized"}
        except Exception as ex:
            logger.exception("error")
            return {"status_code":"500","message": "un-authorized"}
        contents = file.file.read()
        with open('pacs-files/'+file.filename, 'wb') as f:
            f.write(contents)
    except Exception:
        return {"status_code":"500","message": "There was an error uploading the file"}
    finally:
        file.file.close()

    return {"status_code":"200","message": f"Successfully uploaded"}

@router.post("/print-ris-report")
def print_ris_report(token: str, accessionNo:str):
    try:
        logger.info(f'{token}{accessionNo}')
        return get_ris_report(token,accessionNo)
    except Exception:
        logger.exception("exception")
        return {"message": "There was an error uploading the file"}

def get_ris_report(token,accessionNo):
    body={
        "mode": "21",
        "dsData": {
            "dtCriteria": [
                {
                    "AccessionNo": accessionNo,
                    "Token": token
                }
            ]
        }
    }
    res=handle_request1("http://10.10.100.131:84/YASASII-RIS_API/API/Reporting",None,body)
    logger.info(res)
    if res.status_code == 200:
        data= res.json()
        if data["StatusCode"]==200:
            body={
                "mode": "39",
                "dsData": {
                    "dtCriteria": [
                        {
                            "RisOrderId": data["Result"][0]["RisOrderId"]
                        }
                    ]
                }
            }
            res1=handle_request1("http://10.10.100.131:84/YASASII-RIS_API/API/RisReporting",None,body)
            logger.info(res1)
            if res1.status_code == 200:
                data1= res1.json()
                if data1["StatusCode"]==200:
                   return data1["Result"][0]["ImageUrlpath"]
                else:
                    return data1["Message"]
            else:
                return "error calling his api"        
        else:
            return data["Message"]
    else:
        return "error calling his api"