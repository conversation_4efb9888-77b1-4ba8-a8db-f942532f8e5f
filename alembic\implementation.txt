
INSERT INTO public.operation (name, type, status, created_at, updated_at) VALUES ('deviceLogin', 'MUTATION', 'ACTIVE', '2023-09-27 14:54:22.812783+00', NULL);

# have to add the above operation id in the below query 
INSERT INTO public.rel_operation_user_type (operation_id, entity_type, user_type_id, auth_type, created_at, updated_at) VALUES (1, 'PATIENT', NULL, 'NO_AUTH', '2023-09-27 15:13:41.61192+00', NULL);

INSERT INTO public.screen (screen_code, initial_action, is_final, created_at, updated_at, screen_name) VALUES (38, 'AADHAAR_CITY_SCREEN', 'AADHAR_REGISTRATION_ACTION', 0, '2023-09-29 05:10:49.972617+00', NULL, 'Aadhar city screen');
INSERT INTO public.screen (screen_code, initial_action, is_final, created_at, updated_at, screen_name) VALUES (39, '<PERSON>D<PERSON><PERSON>_TITLE_SCREEN', 'AADHAR_REGISTRATION_ACTION', 0, '2023-09-29 05:12:06.021718+00', NULL, 'Aadhar title screen');

#changed text in data column of config data and for testing report print count is set to 2 change aacordingly in PROD.
UPDATE public.config SET data = '{"ms_age":18,"max_report_count":2,"session_expiry_count":120,"session_popup_count":10,"success_session_expiry_count":30,"success_session_popup_count":10}' WHERE id = 1;

#add login pin to the linked devices 
