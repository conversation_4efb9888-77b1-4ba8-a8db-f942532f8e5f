"""queue and user management changes added

Revision ID: f88e77a150bb
Revises: c826449be568, d2153cef7d0d
Create Date: 2024-02-29 09:42:14.536201

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f88e77a150bb'
down_revision = ('c826449be568', 'd2153cef7d0d')
branch_labels = None
depends_on = None


def upgrade():
    pass


def downgrade():
    pass
