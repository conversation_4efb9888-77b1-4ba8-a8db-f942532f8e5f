from datetime import datetime, timedelta
import os
from fastapi import APIRouter
import logging
from jose import JWSError

from pydantic import BaseModel
logger=logging.getLogger()
from fastapi import File, UploadFile
from util.globals import handle_request
from jose import jws

router = APIRouter(
    prefix="/auth",
    tags=["auth"],
    responses={404: {"description": "Not found"}},
)
class AuthBody(BaseModel):
    user_name: str
    password: str
    
@router.post("/login")
def login(body:AuthBody):
    try:
      dt = datetime.now() + \
            timedelta(minutes=float(
                os.environ["ACCESS_EXPIRE_MINUTES"]))
      if body.user_name=='emr_user' and body.password=='Admin@Emr2023':
            access_token = jws.sign({"sub": {"user_id": body.user_name ,"user_sub_type": "EMR"}, "user_type": "THIRD_PARTY", "exp": dt.isoformat(
            ), "device_id": ""}, os.environ["SECRET_KEY"], algorithm=os.environ["ALGORITHM"])
      return {"status_code":"200","message": f"file uploaded successfully","data":access_token}
    except Exception:
        logger.exception("error")
        return {"status_code":"500","message": "Error generating token"}
