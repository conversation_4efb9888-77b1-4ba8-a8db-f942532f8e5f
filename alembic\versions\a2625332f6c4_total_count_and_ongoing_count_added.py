"""total count and ongoing count added

Revision ID: a2625332f6c4
Revises: ba5043bfe852
Create Date: 2023-10-11 14:45:31.998608

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a2625332f6c4'
down_revision = 'ba5043bfe852'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('queue', sa.Column('total_count', sa.Integer(), server_default=sa.text('0'), nullable=True))
    op.add_column('queue', sa.Column('ongoing_count', sa.Integer(), server_default=sa.text('0'), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('queue', 'ongoing_count')
    op.drop_column('queue', 'total_count')
    # ### end Alembic commands ###
