"""status col added

Revision ID: eca33d1cdc68
Revises: 6d403067d828
Create Date: 2025-07-11 16:43:02.488399

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'eca33d1cdc68'
down_revision = '6d403067d828'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('queue_weightage_action', sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='statusenum'), nullable=True), schema='queue')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('queue_weightage_action', 'status', schema='queue')
    # ### end Alembic commands ###
