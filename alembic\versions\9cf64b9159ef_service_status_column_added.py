"""service status column added

Revision ID: 9cf64b9159ef
Revises: b275754c29d2
Create Date: 2025-03-24 16:55:54.119840

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9cf64b9159ef'
down_revision = 'b275754c29d2'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('service', sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='statusenum')))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('service', 'status')
    # ### end Alembic commands ###
