"""remove updated by and updated at columns

Revision ID: cc27cc63459c
Revises: a2625332f6c4
Create Date: 2023-10-11 15:31:01.751572

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'cc27cc63459c'
down_revision = 'a2625332f6c4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('rel_staff_user_queue_created_by_fk', 'rel_staff_user_queue', type_='foreignkey')
    op.drop_column('rel_staff_user_queue', 'created_by')
    op.drop_column('rel_staff_user_queue', 'created_at')
    op.drop_column('rel_staff_user_queue', 'updated_at')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('rel_staff_user_queue', sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True))
    op.add_column('rel_staff_user_queue', sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True))
    op.add_column('rel_staff_user_queue', sa.Column('created_by', sa.INTEGER(), autoincrement=False, nullable=True))
    op.create_foreign_key('rel_staff_user_queue_created_by_fk', 'rel_staff_user_queue', 'staff_user', ['created_by'], ['id'])
    # ### end Alembic commands ###
