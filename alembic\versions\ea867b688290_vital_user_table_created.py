"""vital user table created

Revision ID: ea867b688290
Revises: a6099f711666
Create Date: 2024-08-20 12:36:50.999004

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ea867b688290'
down_revision = 'a6099f711666'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
