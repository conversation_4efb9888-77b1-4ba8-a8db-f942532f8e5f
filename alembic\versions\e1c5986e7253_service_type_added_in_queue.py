"""service type added in queue

Revision ID: e1c5986e7253
Revises: 7202c58f9664
Create Date: 2024-04-23 14:02:46.492975

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e1c5986e7253'
down_revision = '7202c58f9664'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('queue', sa.Column('service_type', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('queue', 'service_type')
    # ### end Alembic commands ###
