"""device_code col added

Revision ID: 7f9efe1ed68f
Revises: aa3a14df2b89
Create Date: 2024-10-24 19:02:22.294985

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7f9efe1ed68f'
down_revision = 'aa3a14df2b89'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('device', sa.Column('printer_ip', sa.String(), nullable=True))
    op.add_column('vital_user', sa.Column('device_code', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('vital_user', 'device_code')
    op.drop_column('device', 'printer_ip')
    # ### end Alembic commands ###
