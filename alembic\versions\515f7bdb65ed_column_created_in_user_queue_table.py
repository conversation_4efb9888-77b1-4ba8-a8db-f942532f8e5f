"""Column created in user_queue table

Revision ID: 515f7bdb65ed
Revises: 6693c5718a07
Create Date: 2024-03-08 06:05:55.293385

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '515f7bdb65ed'
down_revision = '6693c5718a07'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(None, 'location', ['code'])
    op.add_column('user_queue', sa.Column('next_location_id', sa.Integer(), nullable=True))
    op.create_foreign_key('user_queue_next_location_id_fk', 'user_queue', 'location', ['next_location_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('user_queue_next_location_id_fk', 'user_queue', type_='foreignkey')
    op.drop_column('user_queue', 'next_location_id')
    op.drop_constraint(None, 'location', type_='unique')
    # ### end Alembic commands ###
