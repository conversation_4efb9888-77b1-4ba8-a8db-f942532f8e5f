"""rfid tag code added

Revision ID: d16f9adca583
Revises: 48c8dac3edc8
Create Date: 2024-07-22 12:45:09.416010

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd16f9adca583'
down_revision = '48c8dac3edc8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('tag', sa.Column('rfid_code', sa.String(), nullable=True), schema='queue')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('tag', 'rfid_code', schema='queue')
    # ### end Alembic commands ###
