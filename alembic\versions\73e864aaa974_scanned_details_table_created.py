"""scanned details table created

Revision ID: 73e864aaa974
Revises: 3b2f715d4a30
Create Date: 2023-12-19 13:04:50.619158

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '73e864aaa974'
down_revision = '3b2f715d4a30'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_visa_scanned_details',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('reference_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('scanned_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_visa_scanned_details')
    # ### end Alembic commands ###
