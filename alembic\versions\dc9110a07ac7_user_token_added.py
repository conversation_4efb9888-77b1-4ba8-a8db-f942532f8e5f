"""user token added

Revision ID: dc9110a07ac7
Revises: 0f716e64129a
Create Date: 2024-07-18 09:46:18.145786

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'dc9110a07ac7'
down_revision = '0f716e64129a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_token',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('token_no', sa.String(), nullable=False),
    sa.Column('bill_no', sa.String(), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('date', sa.Date(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], name='user_service_user_id_fk'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('bill_no'),
    schema='queue'
    )
    op.create_index('idx_user_token_created_at', 'user_token', ['created_at'], unique=False, schema='queue', postgresql_using='btree')
    op.drop_index('idx_user_token_created_at', table_name='user_token')
    op.drop_table('user_token')
    op.add_column('user_queue', sa.Column('token_id', sa.Integer(), nullable=True), schema='queue')
    op.create_foreign_key('user_queue_user_token_id_fk', 'user_queue', 'user_token', ['token_id'], ['id'], source_schema='queue', referent_schema='queue')
    op.add_column('user_service', sa.Column('token_id', sa.Integer(), nullable=True), schema='queue')
    op.create_foreign_key('user_service_user_token_id_fk', 'user_service', 'user_token', ['token_id'], ['id'], source_schema='queue', referent_schema='queue')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('user_service_user_token_id_fk', 'user_service', schema='queue', type_='foreignkey')
    op.drop_column('user_service', 'token_id', schema='queue')
    op.drop_constraint('user_queue_user_token_id_fk', 'user_queue', schema='queue', type_='foreignkey')
    op.drop_column('user_queue', 'token_id', schema='queue')
    op.create_table('user_token',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('token_no', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('bill_no', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('date', sa.DATE(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], name='user_service_user_id_fk'),
    sa.PrimaryKeyConstraint('id', name='user_token_pkey'),
    sa.UniqueConstraint('bill_no', name='user_token_bill_no_key')
    )
    op.create_index('idx_user_token_created_at', 'user_token', ['created_at'], unique=False)
    op.drop_index('idx_user_token_created_at', table_name='user_token', schema='queue', postgresql_using='btree')
    op.drop_table('user_token', schema='queue')
    # ### end Alembic commands ###
