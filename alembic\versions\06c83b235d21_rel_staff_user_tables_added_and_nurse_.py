"""rel staff user tables added and nurse station column in bed request

Revision ID: 06c83b235d21
Revises: 4c80b4e93587
Create Date: 2024-01-03 09:31:31.499806

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '06c83b235d21'
down_revision = '4c80b4e93587'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('rel_module_menu',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('module_id', sa.Integer(), nullable=True),
    sa.Column('menu_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['menu_id'], ['menu.id'], name='rel_module_menu_menu_id_fk'),
    sa.ForeignKeyConstraint(['module_id'], ['module.id'], name='rel_module_menu_module_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('rel_module_resource',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('module_id', sa.Integer(), nullable=True),
    sa.Column('resource_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['module_id'], ['module.id'], name='rel_module_resource_module_id_fk'),
    sa.ForeignKeyConstraint(['resource_id'], ['resource.id'], name='rel_module_resource_resource_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('rel_staff_user_menu',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('module_id', sa.Integer(), nullable=True),
    sa.Column('staff_user_id', sa.Integer(), nullable=True),
    sa.Column('menu_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['menu_id'], ['menu.id'], name='rel_staff_user_menu_menu_id_fk'),
    sa.ForeignKeyConstraint(['module_id'], ['module.id'], name='rel_staff_user_menu_module_id_fk'),
    sa.ForeignKeyConstraint(['staff_user_id'], ['staff_user.id'], name='rel_staff_user_menu_staff_user_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('rel_staff_user_nurse_station',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('staff_user_id', sa.Integer(), nullable=True),
    sa.Column('nurse_station', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['staff_user_id'], ['staff_user.id'], name='rel_staff_user_nurse_station_staff_user_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('rel_staff_user_resource',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('module_id', sa.Integer(), nullable=True),
    sa.Column('staff_user_id', sa.Integer(), nullable=True),
    sa.Column('resource_id', sa.Integer(), nullable=True),
    sa.Column('menu_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['menu_id'], ['menu.id'], name='rel_staff_user_resource_menu_id_fk'),
    sa.ForeignKeyConstraint(['module_id'], ['module.id'], name='rel_staff_user_resource_module_id_fk'),
    sa.ForeignKeyConstraint(['resource_id'], ['resource.id'], name='rel_staff_user_resource_resource_id_fk'),
    sa.ForeignKeyConstraint(['staff_user_id'], ['staff_user.id'], name='rel_staff_user_resource_staff_user_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('bed_request', sa.Column('nurse_station', sa.String(), nullable=True))
    op.add_column('rel_user_type_resource', sa.Column('menu_id', sa.Integer(), nullable=True))
    op.create_foreign_key('rel_menu_user_type_resource_menu_id_fk', 'rel_user_type_resource', 'menu', ['menu_id'], ['id'])
    op.add_column('resource', sa.Column('menu_id', sa.Integer(), nullable=True))
    op.create_foreign_key('resource_menu_id_fk', 'resource', 'menu', ['menu_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('resource_menu_id_fk', 'resource', type_='foreignkey')
    op.drop_column('resource', 'menu_id')
    op.drop_constraint('rel_menu_user_type_resource_menu_id_fk', 'rel_user_type_resource', type_='foreignkey')
    op.drop_column('rel_user_type_resource', 'menu_id')
    op.drop_column('bed_request', 'nurse_station')
    op.drop_table('rel_staff_user_resource')
    op.drop_table('rel_staff_user_nurse_station')
    op.drop_table('rel_staff_user_menu')
    op.drop_table('rel_module_resource')
    op.drop_table('rel_module_menu')
    # ### end Alembic commands ###
