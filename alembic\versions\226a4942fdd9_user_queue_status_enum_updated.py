"""user queue status enum updated

Revision ID: 226a4942fdd9
Revises: ac3f00f8fa99
Create Date: 2024-08-14 13:58:40.313397

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '226a4942fdd9'
down_revision = 'ac3f00f8fa99'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TYPE userqueuestatusenum ADD VALUE 'PAUSED'")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
