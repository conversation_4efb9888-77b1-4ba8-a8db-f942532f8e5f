from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.text import MI<PERSON><PERSON>ext
import os
import smtplib
import logging
from exceptions.exceptions import MutationError


logger = logging.getLogger()

def send_email(to, subject,text,sender_email,sender_password):
    try:
        gmail_user = sender_email
        gmail_pwd = sender_password
        msg = MIMEMultipart()
        msg['From'] = gmail_user
        msg['To'] = ', '.join(to)
        msg['Subject'] = subject
        msg.attach(MIMEText(text,'html'))
        mailServer = smtplib.SMTP(os.environ["MAIL_SERVER"])
        mailServer.ehlo()
        mailServer.starttls()
        mailServer.ehlo()
        mailServer.login(gmail_user, gmail_pwd)
        mailServer.sendmail(gmail_user, to, msg.as_string())
        mailServer.close()
        logger.info("Sent email successfully ")
    except smtplib.SMTPException as error:
        logger.exception("Failed to send email")
        raise MutationError(f"Failed to send email")