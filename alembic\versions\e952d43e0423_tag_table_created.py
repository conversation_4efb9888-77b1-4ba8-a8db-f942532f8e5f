"""tag table created

Revision ID: e952d43e0423
Revises: e774e688cf61
Create Date: 2024-03-20 09:34:04.160759

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = 'e952d43e0423'
down_revision = 'e774e688cf61'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('tag',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('code', sa.String(), nullable=True),
    sa.Column('status', postgresql.ENUM('ACTIVE', 'INACTIVE', name='statusenum',create_type=False), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code')
    )
    op.execute("ALTER TABLE user_queue ALTER COLUMN tag_id TYPE INTEGER USING tag_id::integer")
    op.create_foreign_key('user_queue_tag_id_fk', 'user_queue', 'tag', ['tag_id'], ['id'])
    op.add_column('user_queue_step', sa.Column('tag_id', sa.Integer(), nullable=True))
    op.create_foreign_key('user_queue_tag_id_fk', 'user_queue_step', 'tag', ['tag_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('user_queue_tag_id_fk', 'user_queue_step', type_='foreignkey')
    op.drop_column('user_queue_step', 'tag_id')
    op.drop_constraint('user_queue_tag_id_fk', 'user_queue', type_='foreignkey')
    op.drop_table('tag')
    # ### end Alembic commands ###
