from operator import or_
from typing import List, Optional
from sqlalchemy import func
from sqlalchemy import desc
from menu.models import RelMenuOperation as RelMenuOperation
from resources.models import RelResourceOperation as RelResourceOperationModel, RelUserTypeResource as RelUserTypeResourceModel
from graphql_types import OperationTypes
from user.models import StatusEnum, UserType as UserTypemodel
from operation.models import AuthenticationTypeEnum, Operation as OperationModel, OperationRelUserType as OperationRelUserTypeModel
from sqlalchemy.orm import Session
import logging
logger = logging.getLogger()
from user.models import UserType as UserTypeModel

def check_user_type_has_access(db: Session,operations:List[str], user_type:str,entity_type:str):
    query=db.query(OperationRelUserTypeModel).join(OperationRelUserTypeModel.operation).join(OperationRelUserTypeModel.user_type).filter(UserTypemodel.code==user_type, OperationModel.status==StatusEnum.ACTIVE).filter(OperationModel.name.in_(operations)).order_by(desc(OperationRelUserTypeModel.auth_type))
    accessCount=query.count()
    # logger.info(query)
    # logger.info(operations)
    logger.info(accessCount)
    if operations.__len__()!=accessCount:
        return check_is_login_requied(db,operations, entity_type)
    elif operations.__len__()<=0:
        return False
    return True  

def check_is_login_requied(db: Session,operations:List[str], entity_type:str):
    try:
        query=db.query(OperationRelUserTypeModel).join(OperationRelUserTypeModel.operation).filter(OperationRelUserTypeModel.user_type_id==None,OperationRelUserTypeModel.entity_type==entity_type,OperationModel.status==StatusEnum.ACTIVE).filter(OperationModel.name.in_(operations)).order_by(desc(OperationRelUserTypeModel.auth_type))
        obj=query.first()
        if obj is not None:
            if obj.auth_type==AuthenticationTypeEnum.NO_AUTH:
                return True
        return False
    except Exception as e:
        logger.info(e)
        
def saveOperation(db: Session,operation_name:str,operation_type:str,type_list:List[OperationTypes],operation_id:Optional[int]):
    exists=db.query(OperationModel).filter(OperationModel.id==operation_id).one_or_none()
    if(exists is not None):
        created_at=exists.created_at
        db.query(OperationModel).filter(OperationModel.id==operation_id).update({"name":operation_name,"type":operation_type})
        db.query(OperationRelUserTypeModel).filter(OperationRelUserTypeModel.operation_id==operation_id).delete()
        db.commit()
        operationId=operation_id
        updated_at=func.now()
    else:
        operation_obj=OperationModel(
        name = operation_name,
        type = operation_type
        )
        db.add(operation_obj),
        db.commit()
        updated_at=None
        created_at=func.now()
        operationId=operation_obj.id
    for list in type_list:
        if(list.user_type is not None and len(list.user_type)>0):
            for userType in list.user_type:
                user_type=db.query(UserTypeModel).filter(UserTypeModel.entity_type==list.entity_type,UserTypeModel.name==userType).one_or_none()
                if(user_type is not None):
                    operation_rel_obj=OperationRelUserTypeModel(
                    entity_type = list.entity_type,
                    user_type_id = user_type.id,
                    operation_id=operationId,
                    auth_type=list.auth_type,
                    updated_at=updated_at,
                    created_at=created_at
                    )
                db.add(operation_rel_obj)
                db.commit()
        else:
            operation_rel_obj=OperationRelUserTypeModel(
            operation_id=operationId,
            auth_type=list.auth_type,
            entity_type=list.entity_type,
            updated_at=updated_at,
            created_at=created_at
            )
            db.add(operation_rel_obj),
            db.commit()
    

# def activate_trigger(db: Session,type:SubscriptionTypeEnum,staff_user_id:int=None,nursing_station_id: int=None, user_id: int=None, call_to: CalltoEnum=None):
#     query=db.query(SubscriptionTriggerModel.id).filter(SubscriptionTriggerModel.type==type)
#     if type==SubscriptionTypeEnum.GET_ROOMS:
#         query.filter(SubscriptionTriggerModel.call_to==call_to)
#         if call_to==CalltoEnum.NURSE.name:
#             query.filter(SubscriptionTriggerModel.nursing_station_id==nursing_station_id)
#     elif type==SubscriptionTypeEnum.GET_PATIENTS:
#         query.filter(or_(SubscriptionTriggerModel.staff_user_id==staff_user_id,SubscriptionTriggerModel.staff_user_id.is_(None)))
#     elif type==SubscriptionTypeEnum.GET_USER_REQUESTED_CALL_BELLS:
#         query.filter(SubscriptionTriggerModel.user_id==user_id)
#     elif type==SubscriptionTypeEnum.GET_CALL_BELLS_BY_STAFF:
#         query.filter(or_(SubscriptionTriggerModel.staff_user_id==staff_user_id,SubscriptionTriggerModel.staff_user_id.is_(None)))
#     return query.all()

# def flush_subscription_triggers(db:Session,ids:List[int]):
#     db.query(SubscriptionTriggerModel).filter(SubscriptionTriggerModel.id.in_(ids)).delete()
#     db.commit()


def update_operations(db:Session, user_type_id:int):
    db.query(OperationRelUserTypeModel).filter(OperationRelUserTypeModel.user_type_id==user_type_id).delete()
    user_type= db.query(UserTypeModel).filter(UserTypeModel.id==user_type_id).one()
    menu_operations= db.scalars(db.query(RelMenuOperation.operation_id).filter(RelMenuOperation.menu_id.in_(db.query(RelUserTypeResourceModel.menu_id).filter(RelUserTypeResourceModel.user_type_id==user_type_id).distinct()))).all()
    res_operations= db.scalars(db.query(RelResourceOperationModel.operation_id).filter(RelResourceOperationModel.resource_id.in_(db.query(RelUserTypeResourceModel.resource_id).filter(RelUserTypeResourceModel.user_type_id==user_type_id)))).all()
    rows= db.scalars(db.query(OperationModel.id).filter(OperationModel.status==StatusEnum.ACTIVE).filter(OperationModel.id.in_(menu_operations+res_operations))).all()
    oper_rel_user_type_list=[]
    for row in rows:
        oper_rel_user_type= OperationRelUserTypeModel(
            operation_id=row,
            auth_type="BEARER",
            entity_type=user_type.entity_type,
            user_type_id = user_type_id
        )
        oper_rel_user_type_list.append(oper_rel_user_type)
    db.add_all(oper_rel_user_type_list)
    db.commit()