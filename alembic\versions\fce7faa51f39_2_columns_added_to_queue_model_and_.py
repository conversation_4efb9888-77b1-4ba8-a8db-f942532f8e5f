"""2 columns added to queue model and removed 1 column in user queue

Revision ID: fce7faa51f39
Revises: 69a21e1f3088
Create Date: 2023-10-13 05:29:03.301957

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fce7faa51f39'
down_revision = '69a21e1f3088'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('queue', sa.Column('completed_count', sa.Integer(), server_default=sa.text('0'), nullable=True))
    op.add_column('queue', sa.Column('cancelled_count', sa.Integer(), server_default=sa.text('0'), nullable=True))
    op.drop_column('user_queue', 'count')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_queue', sa.Column('count', sa.INTEGER(), autoincrement=False, nullable=True))
    op.drop_column('queue', 'cancelled_count')
    op.drop_column('queue', 'completed_count')
    # ### end Alembic commands ###
