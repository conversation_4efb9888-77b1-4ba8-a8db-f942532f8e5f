"""bed remarks table added

Revision ID: 57a1f9eaa1c4
Revises: 7164906be003
Create Date: 2023-11-02 06:40:03.826044

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '57a1f9eaa1c4'
down_revision = '7164906be003'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('bed_remarks',
    sa.<PERSON>umn('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('bed_request_id', sa.BigInteger(), nullable=True),
    sa.Column('remarks', sa.Text(), nullable=True),
    sa.Column('user_role', sa.String(), nullable=True),
    sa.Column('created_by', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['bed_request_id'], ['bed_request.id'], name='bed_remarks_bed_request_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_column('bed_request', 'remarks')
    op.drop_column('bed_request', 'bed_ops_remarks')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bed_request', sa.Column('bed_ops_remarks', sa.TEXT(), autoincrement=False, nullable=True))
    op.add_column('bed_request', sa.Column('remarks', sa.TEXT(), autoincrement=False, nullable=True))
    op.drop_table('bed_remarks')
    # ### end Alembic commands ###
