"""Column added in bedrequest

Revision ID: 615573df01c6
Revises: 12d39b0457f7
Create Date: 2023-11-30 06:58:07.221010

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '615573df01c6'
down_revision = '12d39b0457f7'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bed_request', sa.Column('completed_status', sa.<PERSON>(), nullable=True))
    op.create_index(op.f('ix_bed_request_completed_status'), 'bed_request', ['completed_status'], unique=False)
    op.execute("ALTER TABLE bed_request ALTER COLUMN mismatch_duration TYPE INTEGER USING mismatch_duration::integer")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_bed_request_completed_status'), table_name='bed_request')
    op.drop_column('bed_request', 'completed_status')
    op.execute("ALTER TABLE bed_request ALTER COLUMN mismatch_duration TYPE VARCHAR USING CAST(mismatch_duration AS VARCHAR)")    # ### end Alembic commands ###
