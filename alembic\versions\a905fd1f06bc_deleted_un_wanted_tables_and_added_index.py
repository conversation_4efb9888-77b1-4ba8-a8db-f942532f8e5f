"""deleted un wanted tables and added index

Revision ID: a905fd1f06bc
Revises: 0cf0ef434963
Create Date: 2024-04-01 06:40:16.271466

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'a905fd1f06bc'
down_revision = '0cf0ef434963'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('bed_status')
    op.drop_table('rel_menu_user_type')
    op.drop_table('rel_staff_user_menu')
    op.create_index(op.f('ix_bed_status_history_date'), 'bed_status_history', ['date'], unique=False)
    op.create_index(op.f('ix_discharge_history_completion_status'), 'discharge_history', ['completion_status'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_discharge_history_completion_status'), table_name='discharge_history')
    op.drop_index(op.f('ix_bed_status_history_date'), table_name='bed_status_history')
    op.create_table('rel_staff_user_menu',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('module_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('staff_user_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('menu_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['menu_id'], ['menu.id'], name='rel_staff_user_menu_menu_id_fk'),
    sa.ForeignKeyConstraint(['module_id'], ['module.id'], name='rel_staff_user_menu_module_id_fk'),
    sa.ForeignKeyConstraint(['staff_user_id'], ['staff_user.id'], name='rel_staff_user_menu_staff_user_id_fk'),
    sa.PrimaryKeyConstraint('id', name='rel_staff_user_menu_pkey')
    )
    op.create_table('rel_menu_user_type',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('module_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('user_type_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('menu_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['menu_id'], ['menu.id'], name='rel_menu_user_type_menu_id_fk'),
    sa.ForeignKeyConstraint(['module_id'], ['module.id'], name='rel_menu_user_type_module_id_fk'),
    sa.ForeignKeyConstraint(['user_type_id'], ['user_type.id'], name='rel_menu_user_type_user_type_id_fk'),
    sa.PrimaryKeyConstraint('id', name='rel_menu_user_type_pkey')
    )
    op.create_table('bed_status',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('ward_name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('admission_type', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('bed_status', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('patient_name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('admission_number', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('bed_no', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('bed_class', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('floor', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('uhid', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('tower', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='bed_status_pkey'),
    sa.UniqueConstraint('admission_number', name='bed_status_admission_number_key')
    )
    # ### end Alembic commands ###
