"""lang column added

Revision ID: 07c5a5d1fd3e
Revises: 1563339aabc9
Create Date: 2023-10-09 11:32:20.631121

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '07c5a5d1fd3e'
down_revision = '1563339aabc9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('track_screen', sa.Column('lang', sa.String(), server_default='en', nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('track_screen', 'lang')
    # ### end Alembic commands ###
