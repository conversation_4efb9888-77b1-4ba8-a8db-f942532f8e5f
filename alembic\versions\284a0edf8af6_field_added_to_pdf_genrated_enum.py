"""field added to pdf genrated enum

Revision ID: 284a0edf8af6
Revises: 6eb26c2f0b16
Create Date: 2023-12-01 07:17:17.553181

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '284a0edf8af6'
down_revision = '6eb26c2f0b16'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TYPE pdfgenerationenum ADD VALUE 'CANCELLED'")

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TYPE pdfgenerationenum DROP VALUE 'CANCELLED'")

    # ### end Alembic commands ###
