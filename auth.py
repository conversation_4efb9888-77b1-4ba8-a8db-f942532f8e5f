from datetime import datetime
import json
import logging
from user.models import EntityTypeEnum
# from user.resolvers import get_staff_user, get_user
# from user.schema import StaffUser
from user.resolvers import get_devices, get_user_by_user_id, get_hospital_devices
from visa_letter.resolvers import get_visa_config_data
from operation.resolvers import check_is_login_requied, check_user_type_has_access
from typing import Optional
from fastapi import Depends, HTTPException, WebSocket, status, Request
from fastapi.security import HTTPBearer
from graphql import parse
logger = logging.getLogger()
from database.db_conf import SessionLocal
from jose import jws
from jose.jwt import J<PERSON><PERSON>laimsError, JWTError, ExpiredSignatureError

import os
token_auth_schema = HTTPBearer()

class AuthUser:
    id: Optional[int]

    def __init__(self, id: int) -> None:
        self.id = id

class DeviceDetail:
    device_code: str
    hospital_code: str
    printer_name: Optional[str]
    max_report_count: int
    pacs_base_url: Optional[str]
    max_report_select_count: Optional[str]
    print_wait_time: Optional[str]
    reg_match_perc: Optional[int]
        
    def __init__(self, device_code: str,hospital_code: str,printer_name:str, max_report_count:int, pacs_base_url: str, max_report_select_count: str,print_wait_time: str,reg_match_perc: int) -> None:
        self.device_code = device_code
        self.hospital_code = hospital_code
        self.printer_name = printer_name
        self.max_report_count=max_report_count
        self.pacs_base_url = pacs_base_url
        self.max_report_select_count = max_report_select_count
        self.print_wait_time = print_wait_time
        self.reg_match_perc = reg_match_perc
        
async def custom_context_dependency(request: Request =None, webSocket: WebSocket= None) -> str:
    admission_ip_no = None
    token=None
    device_id = None
    name = None
    device_type =None
    lang=None
    query=[]
    operation_list=[]
    if request is not None:
        try:
            if request is not None:
                item=request
                try:
                    data = await request.json()
                except:
                    return None
                query=parse(data["query"]).definitions[0].selection_set.selections
                operation_list=list(map(lambda x: x.name.value, list(filter(lambda x: x.name.value !='__typename', query))))
                logger.info(operation_list)
                if operation_list.__contains__("__schema") and operation_list.__len__()==1:
                    return None
                # logger.info(request.headers)
                header_keys = request.headers.keys()
            else:
                item=webSocket
                pass
            header_keys = item.headers.keys()
            # logger.info(header_keys)
            if "device_id" in header_keys:
                device_id = item.headers["device_id"]
                logger.info("Device Id:"+item.headers["device_id"])
            if "device_type" in header_keys:
                device_type = item.headers["device_type"]
            if "lang" in header_keys:
                lang=item.headers["lang"]
            if "authorization" in header_keys:
                auth=item.headers["authorization"].split(" ")
                if(auth[0]=='Bearer'):
                    token = auth[1]
        except Exception as e:
            logger.info(e)
            return (None,device_id,operation_list,device_type,token,lang)
    else:
        try:
            print("**connection params**")
            connection_params = webSocket.query_params
            token = connection_params.get("authorization", "")
            print(token)
        except Exception as e:
            logger.info(e)
        return ('WEB_SOCKET',device_id,operation_list,device_type,token,lang)
    return (None, device_id,operation_list, device_type,token,lang)

async def get_auth_user_context(auth_header: str = Depends(custom_context_dependency)):
    staff_user=None
    token=None
    lang=None
    validation_failure = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Required headers not found",
        headers={"WWW-Authenticate": "Bearer"},
    )
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    access_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Access Denied",
        headers={"WWW-Authenticate": "Bearer"},
    )
    device_unautorised = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Device is not authorised to access this resource",
        headers={"WWW-Authenticate": "Bearer"},
    )
    db = SessionLocal()
    user = None
    device = None
    device_detail = None
    is_authenticated = False
    is_authentication_required = False
    device_id = None
    visa_expiry_days= None
    entity_type=None
    patient_id = None
    login_module=None
    user_sub_type= None
    if auth_header is None:
        logger.info(os.environ["ENABLE_GRAPHQL"])
        if int(os.environ["ENABLE_GRAPHQL"])==1:
            pass
        else:
            raise access_exception
    elif auth_header[0]=='WEB_SOCKET':
        token = auth_header[4]
        try:
            payload = jws.verify(token, key=os.environ["SECRET_KEY"], algorithms=[os.environ["ALGORITHM"]])
            content=json.loads(payload)
            if content["exp"]< datetime.now().isoformat() :
                raise credentials_exception
            elif (content["user_type"] == "PATIENT" and content["device_id"]!=auth_header[1]) or (content["user_type"] == "HOSPITAL" and content["device_id"]!=auth_header[1]):
                raise credentials_exception
            if content["user_type"] == "STAFF":
                flag=check_user_type_has_access(db,auth_header[2],content["sub"]["user_sub_type"],auth_header[3])
                if(auth_header[2].__len__()>0 and not flag):
                    if db is not None: db.close()
                    logger.info("access denied with auth token")
                    raise access_exception
            staff_user = content["sub"]["user_id"] if content["sub"]["user_id"] is not None else None
        except Exception as e:
            logger.exception(e)
    else:
        if auth_header is not None and (auth_header[3]==None or auth_header[3]==''):
            raise validation_failure
        elif auth_header is not None :
            token = auth_header[4]
            lang = auth_header[5]
            device_id=auth_header[1]
            logger.info(auth_header[3])
            entity_type=auth_header[3]
            if auth_header[3]=="STAFF":
                visa_config_data= get_visa_config_data(db)
                visa_expiry_days = visa_config_data.get('visa_expiry_days')
            elif auth_header[3]=="HOSPITAL":
                device = get_hospital_devices(db,device_id)
                if device is None:
                    if db is not None: db.close()
                    logger.exception("device unautorised")
                    raise device_unautorised
            else:
                device,config_data=get_devices(db,device_id)
                if device is None:
                    if db is not None: db.close()
                    logger.exception("device unautorised")
                    raise device_unautorised
                else:
                    if auth_header[3] == "PATIENT":
                        user = "PATIENT"
                    device_detail=DeviceDetail(device_code=device.device_code, hospital_code=device.hospital_code, printer_name=device.printer_name, max_report_count=config_data.get('max_report_count'), pacs_base_url= config_data.get('his_pacs_base_url'),max_report_select_count=config_data.get('max_report_select_count'),print_wait_time=config_data.get('print_wait_time'),reg_match_perc=config_data.get('reg_match_perc'))

            if token is None or token=='':
                if(auth_header[2].__len__()>0 and not check_is_login_requied(db,auth_header[2],auth_header[3])):
                    if db is not None: db.close()
                    logger.exception("access denied")
                    raise access_exception
            else:
                is_authentication_required=False
                try:
                    payload = jws.verify(token, key=os.environ["SECRET_KEY"], algorithms=[os.environ["ALGORITHM"]])
                    content=json.loads(payload)
                    if content["exp"]< datetime.now().isoformat() :
                        raise credentials_exception
                    elif (content["user_type"] == "PATIENT" and content["device_id"]!=auth_header[1]) or (content["user_type"] == "HOSPITAL" and content["device_id"]!=auth_header[1]):
                        raise credentials_exception
                    if content["user_type"] == "PATIENT" or content["user_type"] == "STAFF" or content["user_type"] == "HOSPITAL":
                        flag=check_user_type_has_access(db,auth_header[2],content["sub"]["user_sub_type"],auth_header[3])
                        if(auth_header[2].__len__()>0 and not flag):
                            if db is not None: db.close()
                            logger.info("access denied with auth token")
                            raise access_exception
                    if(content["user_type"]==EntityTypeEnum.HOSPITAL.name):
                        patient_id = content["sub"]["patient_id"] if content["sub"].get("patient_id") is not None else None
                    if(content["user_type"]==EntityTypeEnum.STAFF.name):
                        staff_user = content["sub"]["user_id"] if content["sub"]["user_id"] is not None else None
                        login_module = content["sub"]["login_module"] if content["sub"].get("login_module") is not None else None
                        user_sub_type= content["sub"]["user_sub_type"]
                    if(content["user_type"]==EntityTypeEnum.PATIENT.name):
                        if content["sub"]["user_id"] is None:
                            if db is not None: db.close()
                            raise validation_failure
                        try:
                            if content["sub"]["user_sub_type"]=='VITAL':
                                user = get_user_by_user_id(db,content["sub"]["user_id"])
                            # logger.info(user)
                                if user is not None:
                                    user = AuthUser(id=user.id)
                            is_authenticated = True
                            # else:
                            #     if db is not None: db.close()
                            #     raise credentials_exception
                        except HTTPException as e:
                            if db is not None: db.close()
                            raise e
                        except Exception as ex:
                            logger.exception(ex)
                            if db is not None: db.close()   
                            raise credentials_exception
                    # elif(content["entity_type"]==EntityTypeEnum.STAFF.name):
                    #     user_staff = get_staff_user(db,content["sub"]["id"])
                    #     # logger.info(user_staff)
                    #     if user_staff is None:
                    #         if db is not None: db.close()
                    #         raise credentials_exception
                    #     staff_user=StaffUser.from_instance(user_staff)

                except (JWTError, JWTClaimsError,ExpiredSignatureError) as ex:
                    logger.info(ex.message)
                    if db is not None: db.close()
                    raise credentials_exception
                except HTTPException as e:
                    if db is not None: db.close()
                    logger.info(e.detail)
                    raise e
                except Exception as ex:
                    logger.exception(ex)
                    if db is not None: db.close()
                    raise credentials_exception
            if db is not None: db.close()        
    return {
        "is_authentication_required" : is_authentication_required,
        "is_authenticated" : is_authenticated,
        "auth_user" : user,
        "device_id" : auth_header[1] if auth_header is not None else None,
        "staff_user" : staff_user,
        "role": auth_header[3] if auth_header is not None else None,
        "lang":"en" if lang==None or lang=='' else lang,
        "device_detail": device_detail,
        "visa_expiry_days":visa_expiry_days,
        "entity_type":entity_type,
        "patient_id":patient_id,
        "login_module":login_module,
        "user_sub_type":user_sub_type
    }
