"""vitals datetime column added

Revision ID: f8d2eb9974a8
Revises: 9cf64b9159ef
Create Date: 2025-03-24 17:08:58.441302

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f8d2eb9974a8'
down_revision = '9cf64b9159ef'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_queue', sa.Column('vitals_datetime', sa.DateTime(timezone=True), nullable=True), schema='queue')
    op.add_column('user_queue', sa.Column('phy_ass_datetime', sa.DateTime(timezone=True), nullable=True), schema='queue')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_queue', 'phy_ass_datetime', schema='queue')
    op.drop_column('user_queue', 'vitals_datetime', schema='queue')
    # ### end Alembic commands ###
