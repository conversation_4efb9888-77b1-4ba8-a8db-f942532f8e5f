"""column added in user login table

Revision ID: 1ad66cd34438
Revises: e86e241db8d2
Create Date: 2024-02-08 06:41:08.876620

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1ad66cd34438'
down_revision = 'e86e241db8d2'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('staff_user_login_details', sa.Column('entity_type', sa.Enum('HOSPITAL', 'PATIENT', 'STAFF', name='entitytypeenum'), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('staff_user_login_details', 'entity_type')
    # ### end Alembic commands ###
