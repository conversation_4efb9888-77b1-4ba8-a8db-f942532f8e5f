
from datetime import date, datetime, time
from typing import Generic, List, Optional, Text, TypeVar, Union
from typing_extensions import Annotated
import strawberry
from strawberry.file_uploads.scalars import Upload

T = TypeVar('T')

@strawberry.type
class MutationResponse(Generic[T]):
    success: bool
    message: str
    data: Optional[T] = strawberry.field

    @classmethod
    def from_status_flag(cls, success: bool, message: str, data: Generic[T]):
        return cls(success=success, message=message, data=data)

@strawberry.type
class QueryResponse(Generic[T]):
    success: bool
    message: str
    data: Optional[T] = strawberry.field

    @classmethod
    def from_status_flag(cls, success: bool, message: str, data: Generic[T]):
        return cls(success=success, message=message, data=data)

@strawberry.type
class QueryErrorResponse:
    status: bool
    msg: str

    def from_status_flag(status, msg):
        return {
            "status": status,
            "message": msg
        }


@strawberry.input
class PatientResponse:
    question_id: int
    response: str


@strawberry.input
class PatientVitalDetail:
    vital_detail_id: int = None
    value: Optional[str] = None


@strawberry.input
class PatientVital:
    vital_id: int
    details: List[PatientVitalDetail]


@strawberry.input
class UserInputOutputDetailsInput:
    input_output_detail_id: int = None
    value: Optional[str] = None


@strawberry.input
class UserInputOutputInput:
    input_output_id: int
    details: List[UserInputOutputDetailsInput]


@strawberry.input
class CreateStaffUser:
    user_type_id: int
    user_name: str
    first_name: str
    last_name: str
    gender: str
    phone_number: str
    email: str
    age: Optional[int] = None
    id: Optional[int] = None


@strawberry.input
class OperationTypes:
    entity_type: str
    auth_type: str
    user_type: Optional[List[str]] = None


@strawberry.input
class UserAssessmentDetailInput:
    assessment_category_id: int
    value: Optional[str] = None
    assessment_question_id: int
    index: Optional[int] = 0

@strawberry.input
class UserVitalInput:
    vital_code: str
    value: Optional[str] = None

@strawberry.input
class UserAssessmentInput:
    assessment_id: int
    details: List[UserAssessmentDetailInput]

@strawberry.input
class VitalDetailCode:
    code : str
    value : Optional[str] = None

@strawberry.input
class DoctorEducationDetails:
    doctor_id : Optional[int] = None
    collage : Optional[str] = None
    university : Optional[str] = None
    course_name :  Optional[str] = None
    completion_year : Optional[int] = None

@strawberry.input
class TimeSlots:
    start_time : time
    end_time : time
    status : Optional[str] =  None

@strawberry.input
class DoctorAvailableTimeSLots:
    availability_slot_duration : int
    availability_slot_days : List[str]
    time_slots_list : List[TimeSlots]

@strawberry.input
class DoctorRegistration:
    user_name : str
    password : str
    registration_no : str
    phone_number : str
    gender : str
    specialization : List[int]
    speciality : List[int]
    biography : Optional[Text] = None
    education_details :Optional[List[DoctorEducationDetails]] = None
    available_time_slots : Optional[List[DoctorAvailableTimeSLots]] = None
    first_name : Optional[str] = None
    last_name : Optional[str] = None
    age : Optional[int] = None
    birth_date : Optional[datetime] = None
    email : Optional[str] = None
    status : Optional[str] = None
    city : Optional[str] = None
    district : Optional[str] = None
    state : Optional[str] = None
    postal_code : Optional[int] = None
    registration_date : Optional[datetime] = None
    registered_at : Optional[str] = None
    signature : Optional[str] = None
    doctor_type : Optional[str] = None
    fee_amount : Optional[int] = None
    experience : Optional[int] = None

@strawberry.input
class UserRegister:
    phone_number : str
    gender : str
    name : Optional[str] = None
    age :   Optional[int] = None
    date_of_birth : str
    address : Text
    aadhar_number : Optional[str] = None
    health_id : Optional[str] = None
    firstName : str
    lastName : str
    locality : str
    pincode : str
    stateName : str
    cityName : str
    title: str
    maritalStatus: Optional[str]="Unknown"
    idCardType:Optional[str] = ""
    idCardNo:Optional[str] = ""
    middleName:Optional[str]=""
    registration_type:Optional[str]="MOBILE_REGISTRATION"
    accept_terms: Optional[bool]
    accepted_by: Optional[str]
    accepted_rel: Optional[str]
    

@strawberry.input
class DoctorAvailableDay:
    doctor_id : int
    doctor_name : str
    duration : int
    start_time : time
    end_time : time
    available_date : datetime
    status : Optional[str] = None

   
   
#Appointments Types and inputs

@strawberry.input
class AppointmentDetailsInput:
    doctor_id: int
    user_id: int
    appointment_datetime: datetime
    appointment_date: date
    appointment_time: time

#Prescription Types

@strawberry.input
class PrescriptionAdviceDetails:
    doctor_id: int
    advice_name: str
    advice_text: str

@strawberry.input
class PrescriptionComplaintDetails:
    doctor_id: int
    complaint_name: str
    complaint_text: str

@strawberry.input
class PrescriptionDiagnosisDetails:
    doctor_id: int
    diagnosis_name: str
    diagnosis_text: str

@strawberry.input
class PrescriptionTestRequiredDetails:
    doctor_id: int
    test_required_name: str
    test_required_text: str

@strawberry.input
class PrescriptionDietExerciseDetails:
    doctor_id: int
    user_id: int
    appointment_datetime: datetime

@strawberry.input
class PaymentDetails :
    ref_id : str
    service : str
    amount : float
    description : Optional[str] = None
    payment_id : Optional[str] = None
    status : Optional[str] = None



@strawberry.input
class FeedbackDetails:
    response: str
    code: Optional[str] = None
    question_id: Optional[int] = None


@strawberry.input
class Questions:
    type: str
    response: Optional[str] = None
    options: Optional[List[int]] = None
    question_id: Optional[int]=None
    question_code: Optional[str]=None


@strawberry.input
class UserFeedbackDetails:
    type: str
    response: str
    txn_id: Optional[str] = None
    questions: Optional[List[Questions]] = None
    category_id: Optional[int]= None
    category_code:Optional[str]= None
    
@strawberry.input
class ReportDetail :
    order_id:int
    pat_type:int
    test_id:int
    type: str
    order_number: str
    order_date: str

@strawberry.input
class Counter:
    number: int
    code: str
    status: str
    id: Optional[int] = None
    doctor_image: Optional[Upload] = None
    queue_counter_name: Optional[str] = None
    image_name: Optional[str] = None

@strawberry.input
class QueueInput:
    queue_code:str
    queue_name:str
    service_type: str
    queue_type: str
    cluster_id:int
    avg_procedure_time:float
    upcoming_patients:int
    test_ids: List[int]
    buffer_time: Optional[int] = None
    staff_user_ids:Optional[List[int]] = None
    queue_id:Optional[int] = None
    counters: Optional[List[Counter]] = None
    waiting_capacity: Optional[int] = None
    show_patient_name: Optional[bool] = None
    assignment_options: Optional[List[str]] = None
    
@strawberry.input
class StaffUserInput:
    emp_id:str
    name:str
    email:str
    phone_number:str
    user_role_id:int
    nurse_stations: Optional[List[str]] = None
    queues:Optional[List[int]] = None
    user_id:Optional[int] = None
    modules: Optional[List[str]] = None

@strawberry.input
class QRDetail:
    uhid:Optional[str] = None
    token_id:Optional[str] = None
    user_queue_id:Optional[str] = None
    prerequisites_conditions: Optional[List[str]] = None

@strawberry.input
class BillDetail:
    umr_no:str
    bill_no:str
    name:str
    phone_number: str
    service_codes: List[str]
    
@strawberry.input
class BillDetail1:
    bill_no:Optional[str]
    detail_bill_id: Optional[int]= None
    billed_at: Optional[str]= None
    package_id: Optional[int]= 0
    service_code: str
    appointment_date_time: Optional[str]=None
    bill: Optional[str]= None
    test_id: Optional[str] = None
    
@strawberry.input
class UserDetail:
    uhid: Optional[str]
    name: Optional[str]
    phone_number: Optional[str]
    bill_detail: List[BillDetail1]
    weightage_id: Optional[int]
    doctor_name: Optional[str]
    patient_type: Optional[str]
    remarks: Optional[str]= None
    is_platinum: Optional[bool]=False
    age: Optional[int]=None
    gender: Optional[str]=None
    
@strawberry.input
class BedAllotRequest:
    uhid :str
    user_contact_no:str
    patient_name :str
    department :str
    doctor_name :str
    requested_bed_class:str
    remarks:str
    referred_by: Optional[str] = None
    priority: Optional[str] = None
    source: Optional[str] = None
    rate_of_contract: Optional[str] = None
    admission_no: Optional[str] = None
    case_type :Optional[str] = None
    patient_category:Optional[str] = None
    req_from:Optional[str] = None
    estimation :Optional[str] = None
    estimated_by: Optional[str] = None
    tentative_admission_date: Optional[str] = None
    id: Optional[int] = None
    hash_ids: Optional[List[int]]=None
    supporting_care_services: Optional[str] = None
    
@strawberry.input
class AssignBed:
    bed_no:str
    informed_to: str
    bed_ops_remarks: str
    alloted_bed_class:Optional[str] = None
    billing_bed_class:Optional[str] = None
    reason : Optional[str] = None
    hash_ids:Optional[List[int]]=None
    # shifting_time: Optional[str] = None

@strawberry.input
class Attendants:
    name: str
    passport: str

@strawberry.input
class VisaDoctors:
    name: str
    specialization: str

@strawberry.input
class VisaData:
    patient_name: str
    patient_passport: str
    provisional_diagnosis: Optional[str] =None
    treatment_duration: Optional[str] =None
    country: int
    embassy: int
    patient_passport_file1: Optional[Upload] = None
    patient_passport_file2: Optional[Upload] = None
    patient_passport_file1_name: Optional[str] = None
    patient_passport_file2_name: Optional[str] = None
    donors: Optional[List[Attendants]] = None
    user_visa_id : Optional[int] = None
    doctors: Optional[List[VisaDoctors]] = None
    attendants: Optional[List[Attendants]] = None
    appointment_schedule_date: Optional[str] = None
    appointment_start_time: Optional[str] = None
    appointment_end_time: Optional[str] = None
    additions : Optional[str] = None

@strawberry.input
class ModuleInput:
    module_id:Optional[int] = None
    menu_ids:Optional[List[int]] = None
    resource_ids:Optional[List[int]] = None

@strawberry.input
class UserRoleInput:
    name:str
    code:str
    modules: Optional[List[str]] = None
    user_role_id:Optional[int] = None
    status: Optional[str] = None
    assign_to_all_users:Optional[bool]= None

@strawberry.input
class VitalData:
    name:Optional[str] = None
    value:Optional[str] = None

@strawberry.input
class TagInput:
    id : Optional[int] =None
    name : str
    code : str
    rfid_code : Optional[str] = None
    status : Optional[str]=None

@strawberry.input
class WhatsappData:
    patient_id: str
    type: str
    order_id: int
    test_id: int 
    pat_type:int
    order_date: str
    order_no:str
    request_id:str

@strawberry.input
class BarCodeDetails:
    uhid:str
    bill_no:str
    test_id:str
    order_id:str

@strawberry.input
class SampleCollectionInput(BarCodeDetails):
    sample_no: str
    sample_collection_date_time: str

@strawberry.input
class AcknowledgeDetailsInput(BarCodeDetails):
    ack_date_time: str