"""queue step locations added

Revision ID: ba32e137771c
Revises: 18742d2a6464
Create Date: 2024-02-26 14:08:07.296669

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ba32e137771c'
down_revision = '18742d2a6464'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('queue_step_location',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('code', sa.String(), nullable=True),
    sa.Column('priority', sa.Integer(), nullable=True),
    sa.Column('queue_step_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['queue_step_id'], ['queue_step.id'], name='queue_step_location_queue_step_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('user_queue_step', sa.Column('queue_step_location_id', sa.Integer(), nullable=True))
    op.create_foreign_key('user_queue_step_queue_step_location_id_fk', 'user_queue_step', 'queue_step_location', ['queue_step_location_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('user_queue_step_queue_step_location_id_fk', 'user_queue_step', type_='foreignkey')
    op.drop_column('user_queue_step', 'queue_step_location_id')
    op.drop_table('queue_step_location')
    # ### end Alembic commands ###
