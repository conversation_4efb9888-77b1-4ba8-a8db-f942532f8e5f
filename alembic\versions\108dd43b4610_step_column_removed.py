"""step column removed

Revision ID: 108dd43b4610
Revises: b1975ee56619
Create Date: 2023-08-31 09:47:27.140663

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '108dd43b4610'
down_revision = 'b1975ee56619'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('screen', 'step')
    op.drop_column('track_screen', 'step')
    op.drop_column('track_screen_logs', 'step')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('track_screen_logs', sa.Column('step', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('track_screen', sa.Column('step', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('screen', sa.Column('step', sa.VARCHAR(), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
