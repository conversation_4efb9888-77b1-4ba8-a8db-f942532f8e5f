import enum
from sqlalchemy import <PERSON><PERSON><PERSON>, Index, BigInteger, Column, DateTime, Enum, Foreign<PERSON>ey, Integer, String, Numeric, UniqueConstraint, text, Date
from sqlalchemy.sql import func
import strawberry
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB

from database.db_conf import Base

@strawberry.enum
class ServiceStatusEnum(enum.Enum):
    PENDING = "PENDING"
    ON_PROGRESS = "ON_PROGRESS"
    COMPLETED = "COMPLETED"
    CANCELLED = "CANCELLED"

class UserToken(Base):
    __tablename__ = "user_token"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    token_no = Column(String , nullable = False, unique= False)
    bill_no= Column(String, nullable = True, unique= True)
    user_id =Column(Integer, ForeignKey(
        "user.id", name="user_service_user_id_fk"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    date = Column(Date)

    user_services= relationship("UserService",back_populates="user_token")
    user= relationship("User")
    __table_args__ =(
        Index('idx_user_token_created_at', "created_at", postgresql_using='btree'),
        {"schema": "queue"}
    )
    
    def __repr__(self) -> str:
        return "<UserToken %r>" % self.id

class UserService(Base):
    __tablename__ = "user_service"
    __table_args__ = {'schema': "queue"}

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id =Column(Integer, ForeignKey(
        "user.id", name="user_service_user_id_fk"))
    service_id = Column(Integer, ForeignKey(
        "service.id", name="user_service_service_id_fk"))
    token_id = Column(Integer, ForeignKey(
        "queue.user_token.id", name="user_service_user_token_id_fk"))
    bill_no= Column(String, nullable = True, unique= False)
    detail_bill_id= Column(String, nullable = True)
    billed_at=  Column(DateTime(timezone=True))
    package_id = Column(Integer, default=False)
    old_token_id = Column(Integer)
    bill= Column(String)
    test_id= Column(String)
    sample_no= Column(String)
    order_details= Column(JSONB)
    priority = Column(Numeric, server_default=text("1"),nullable=False)
    status = Column(Enum(ServiceStatusEnum), default=ServiceStatusEnum.PENDING,nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    appointment_date_time= Column(DateTime(timezone=True))

    user_token= relationship("UserToken",back_populates="user_services")
    user = relationship("User")
    service= relationship("Service")
    def __repr__(self) -> str:
        return "<UserService %r>" % self.id
