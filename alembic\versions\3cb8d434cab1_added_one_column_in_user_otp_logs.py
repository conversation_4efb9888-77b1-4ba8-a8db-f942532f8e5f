"""added one column in user_otp_logs

Revision ID: 3cb8d434cab1
Revises: bc8985a6d2f8
Create Date: 2023-08-02 09:29:04.477079

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3cb8d434cab1'
down_revision = 'bc8985a6d2f8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_otp_logs', sa.Column('request_id', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_otp_logs', 'request_id')
    # ### end Alembic commands ###
