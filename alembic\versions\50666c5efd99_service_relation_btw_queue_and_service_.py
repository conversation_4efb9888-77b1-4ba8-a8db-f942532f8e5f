"""service,relation btw queue and service tables added

Revision ID: 50666c5efd99
Revises: 87cd37ebf916
Create Date: 2023-10-11 09:46:25.550860

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '50666c5efd99'
down_revision = '87cd37ebf916'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('service',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('code', sa.String(), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('procedure_time', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code')
    )
    op.create_table('rel_queue_test',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('test_id', sa.Integer(), nullable=True),
    sa.Column('queue_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['queue_id'], ['queue.id'], name='rel_queue_test_queue_id_fk'),
    sa.ForeignKeyConstraint(['test_id'], ['service.id'], name='rel_queue_test_test_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('rel_queue_test')
    op.drop_table('service')
    # ### end Alembic commands ###
