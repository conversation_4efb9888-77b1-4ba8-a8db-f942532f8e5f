"""track screen logs table changed and column names changed

Revision ID: 3453f45123f5
Revises: 584ddb9f9c40
Create Date: 2023-08-07 04:44:51.758240

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3453f45123f5'
down_revision = '584ddb9f9c40'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('sms_send_detail', sa.Column('sent_to', sa.String(), nullable=True))
    op.add_column('sms_send_detail', sa.Column('count', sa.Integer(), nullable=True))
    op.drop_column('sms_send_detail', 'to')
    op.drop_column('sms_send_detail', 'resend_count')
    op.add_column('track_screen', sa.Column('device_id', sa.String(), nullable=True))
    op.add_column('track_screen_logs', sa.Column('track_screen_id', sa.BigInteger(), nullable=True))
    op.create_foreign_key('track_screen_logs_track_screen_id_fk', 'track_screen_logs', 'track_screen', ['track_screen_id'], ['id'])
    op.drop_column('track_screen_logs', 'initial_action')
    op.create_unique_constraint(None, 'user_otp', ['request_id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'user_otp', type_='unique')
    op.add_column('track_screen_logs', sa.Column('initial_action', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_constraint('track_screen_logs_track_screen_id_fk', 'track_screen_logs', type_='foreignkey')
    op.drop_column('track_screen_logs', 'track_screen_id')
    op.drop_column('track_screen', 'device_id')
    op.add_column('sms_send_detail', sa.Column('resend_count', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('sms_send_detail', sa.Column('to', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_column('sms_send_detail', 'count')
    op.drop_column('sms_send_detail', 'sent_to')
    # ### end Alembic commands ###
