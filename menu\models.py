import enum
from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Column, DateTime, Enum, ForeignKey, String, Numeric, Text, Integer, Date
from sqlalchemy.sql import func
import strawberry
from database.db_conf import Base
from user.models import StatusEnum
from sqlalchemy.orm import relationship
from operation.models import Operation


class Menu(Base):
    __tablename__ = "menu"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String)
    code = Column(String)
    icon_type = Column(String)
    icon= Column(String)
    status = Column(Enum(StatusEnum))
    priority = Column(Integer)
    link = Column(String)
    target= Column(String)
    parent_menu_id = Column(Integer, ForeignKey("menu.id",name="menu_parent_menu_id_fk"))

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    child_menus = relationship("Menu", remote_side=[id])
    resources = relationship("Resource", back_populates='menu')


    def __repr__(self) -> str:
        return "<Menu %r>" % self.id

class RelMenuOperation(Base):
    __tablename__ = "rel_menu_operation"

    id = Column(BigInteger, primary_key=True, autoincrement =True)
    menu_id = Column(Integer,ForeignKey("menu.id",name="rel_menu_operation_menu_id_fk"))
    operation_id = Column(Integer, ForeignKey(
        "operation.id", name="rel_menu_operation_operation_id_fk"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # menu = relationship("Menu")
    # operation = relationship("Operation")

    def __repr__(self) -> str:
        return "<RelMenuOperation %r>" % self.id
    
class RelModuleMenu(Base):
    __tablename__ = "rel_module_menu"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    module_id = Column(Integer,ForeignKey("module.id",name="rel_module_menu_module_id_fk"))
    menu_id = Column(Integer, ForeignKey(
        "menu.id", name="rel_module_menu_menu_id_fk"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    menu = relationship("Menu")


    def __repr__(self) -> str:
        return "<RelModuleMenu %r>" % self.id