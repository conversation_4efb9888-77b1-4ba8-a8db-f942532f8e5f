"""freezed at column added in user queue

Revision ID: f4f6f1f6c113
Revises: 49ab5d3d468e
Create Date: 2023-10-13 09:01:33.701137

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f4f6f1f6c113'
down_revision = '49ab5d3d468e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_queue', sa.Column('freezed_at', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_queue', 'freezed_at')
    # ### end Alembic commands ###
