import time
from typing import Optional
import uuid
import unicodedata
import re
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.lib import colors
import os, logging
from reportlab.lib import colors
from reportlab.platypus import Table, TableStyle
from sqlalchemy import asc, distinct, func
from exceptions.exceptions import MutationError
from graphql_types import VisaData
from util.globals import generate_visa_qr
logger = logging.getLogger()
from datetime import datetime , date, timedelta
from sqlalchemy.orm import Session
from visa_letter.models import AttendantTypeEnum, Country as CountryModel, CountryEmbassy as CountryEmbassyModel, Doctor as DoctorModel, PdfFormatEnum, PdfGenerationEnum, UserVisaData as UserVisaDataModel, AuthorizedDoctor as AuthorizedDoctorModel, UserVisaAttendant as UserVisaAttendantModel, UserVisaDoctor as UserVisaDoctorModel, UserVisaScannedDetails as UserVisaScannedDetailsModel
from fpdf import FPDF
import pytz,json
from config.models import Config as ConfigModel
from staff_user.models import StaffUser as StaffUserModel


def clean_unicode_text(text):
    """Remove or replace Unicode characters that are not supported by Latin-1 encoding"""
    if not text:
        return text

    # Remove invisible Unicode characters like Left-to-Right Mark (U+200E)
    text = re.sub(r'[\u200e\u200f\u202a-\u202e]', '', text)

    # Replace common Unicode characters with ASCII equivalents
    replacements = {
        '\u2013': '-',  # En dash
        '\u2014': '--', # Em dash
        '\u2018': "'",  # Left single quotation mark
        '\u2019': "'",  # Right single quotation mark
        '\u201c': '"',  # Left double quotation mark
        '\u201d': '"',  # Right double quotation mark
        '\u2026': '...' # Horizontal ellipsis
    }

    for unicode_char, replacement in replacements.items():
        text = text.replace(unicode_char, replacement)

    # Normalize Unicode characters to their closest ASCII equivalent
    text = unicodedata.normalize('NFKD', text)

    # Remove any remaining non-Latin-1 characters
    text = text.encode('latin-1', errors='ignore').decode('latin-1')

    return text

async def generate_visa_user_data(db: Session, visa_data : VisaData, staff_user):
    try:
        staff_user= db.query(StaffUserModel).filter(StaffUserModel.id==staff_user).one_or_none()
        appointment_slot=""
        embassy = db.query(CountryEmbassyModel.pdf_format).filter(CountryEmbassyModel.id == visa_data.embassy).first()
        if visa_data.doctors is None or len(visa_data.doctors) < 0:
            raise MutationError("Please provide the consultant doctor's details.")
        if len(visa_data.doctors) > 2:
            raise MutationError("The number of doctors should not exceed 2")
        if visa_data.attendants is not None and len(visa_data.attendants) > 3:
            raise MutationError("The number of attendants should not exceed 3")
        if embassy.pdf_format == PdfFormatEnum.NORMAL:
            if visa_data.appointment_schedule_date is None or visa_data.appointment_schedule_date == "":
                raise MutationError("Please provide appointment scheduled date.")
            if (visa_data.appointment_start_time is None or visa_data.appointment_start_time is None) or  (visa_data.appointment_start_time == "" or visa_data.appointment_start_time == ""):
                raise MutationError("Please provide appointment time slot.")
            start_time = datetime.strptime(visa_data.appointment_start_time.split()[0], "%H:%M:%S").time()
            end_time = datetime.strptime(visa_data.appointment_end_time.split()[0], "%H:%M:%S").time()
            current_time = datetime.now(pytz.timezone('Asia/Kolkata')).strftime("%Y-%m-%d %H:%M:%S")
            current_time = datetime.strptime(current_time, "%Y-%m-%d %H:%M:%S")
            op_start_time = datetime.strptime(f"{visa_data.appointment_schedule_date} {start_time}", "%Y-%m-%d %H:%M:%S")
            if current_time > op_start_time:
                raise MutationError("Start time has already passed. Please choose a future date or start time.")
            elif start_time > end_time:
                raise MutationError("Invalid time slots")
            appointment_slot = f"{start_time} to {end_time}"
        file1 = ""
        file2 = ""
        
        if visa_data.user_visa_id is None:
            reference_id = str(uuid.uuid4())
            if visa_data.patient_passport_file1 is not None:
                file1_extn = visa_data.patient_passport_file1.filename.split('.')[-1]
                with open(f"data/visa_letter/passport_image/{reference_id}_1.{file1_extn}", 'wb') as new_file:
                        new_file.write(await visa_data.patient_passport_file1.read())
                file1 = f"{reference_id}_1.{file1_extn}",

            if visa_data.patient_passport_file2 is not None:
                file2_extn = visa_data.patient_passport_file2.filename.split('.')[-1]
                with open(f"data/visa_letter/passport_image/{reference_id}_2.{file2_extn}", 'wb') as new_file:
                        new_file.write(await visa_data.patient_passport_file2.read())
                file2 = f"{reference_id}_2.{file2_extn}",

            user_visa_data= UserVisaDataModel(
                patient_name = visa_data.patient_name,
                passport_no = visa_data.patient_passport.upper(),
                provisional_diagnosis = visa_data.provisional_diagnosis,
                treatment_duration = visa_data.treatment_duration,
                country_id = visa_data.country,
                reference_id = reference_id,
                embassy_id = visa_data.embassy,
                status = PdfGenerationEnum.PENDING,
                appointment_date = visa_data.appointment_schedule_date,
                appointment_slot = appointment_slot,
                additions = visa_data.additions,
                patient_passport_file1 = file1 if file1 !="" else None,
                patient_passport_file2 = file2 if file2 != "" else None,
                created_by = staff_user.name if staff_user is not None else " "
            )
            db.add(user_visa_data)
            db.flush()
            if visa_data.attendants is not None and len(visa_data.attendants) > 0:
                for attendant in visa_data.attendants:
                    attendant_data = UserVisaAttendantModel(
                        user_visa_id = user_visa_data.id,
                        attendant = attendant.name,
                        attendant_passport = attendant.passport.upper(),
                        type = AttendantTypeEnum.ATTENDANT
                    )
                    db.add(attendant_data)
            if visa_data.doctors is not None and len(visa_data.doctors) > 0:
                for doctor in visa_data.doctors:
                    doctor_data = UserVisaDoctorModel(
                        user_visa_id = user_visa_data.id,
                        doctor_name = "Dr. "+doctor.name if "Dr." not in doctor.name else doctor.name,
                        doctor_specalization = doctor.specialization
                    )
                    db.add(doctor_data)
            if visa_data.donors is not None and len(visa_data.donors) > 0:
                for donor in visa_data.donors:
                    donor_data = UserVisaAttendantModel(
                        user_visa_id = user_visa_data.id,
                        attendant = donor.name,
                        attendant_passport = donor.passport.upper(),
                        type = AttendantTypeEnum.DONOR
                    )
                    db.add(donor_data)
        else:
            user_visa_data = db.query(UserVisaDataModel).filter(UserVisaDataModel.id == visa_data.user_visa_id).first()
            if user_visa_data is None:
                raise MutationError("Data not found for the requested user")
            user_visa_data.patient_name = visa_data.patient_name,
            user_visa_data.passport_no = visa_data.patient_passport.upper(),
            user_visa_data.provisional_diagnosis = visa_data.provisional_diagnosis,
            user_visa_data.treatment_duration = visa_data.treatment_duration,
            user_visa_data.country_id = visa_data.country,
            user_visa_data.embassy_id = visa_data.embassy,
            user_visa_data.appointment_date = visa_data.appointment_schedule_date,
            user_visa_data.appointment_slot = appointment_slot
            user_visa_data.additions = visa_data.additions
            user_visa_data.last_updated_by = staff_user.name if staff_user is not None else " "
            if visa_data.patient_passport_file1 is None and (visa_data.patient_passport_file1_name is None or visa_data.patient_passport_file1_name == ""):
                user_visa_data.patient_passport_file1 = None
            elif visa_data.patient_passport_file1 is not None:
                file1_extn = visa_data.patient_passport_file1.filename.split('.')[-1]
                with open(f"data/visa_letter/passport_image/{user_visa_data.reference_id}_1.{file1_extn}", 'wb') as new_file:
                        new_file.write(await visa_data.patient_passport_file1.read())
                user_visa_data.patient_passport_file1=f"{user_visa_data.reference_id}_1.{file1_extn}"
            if visa_data.patient_passport_file2 is None and (visa_data.patient_passport_file2_name is None or visa_data.patient_passport_file2_name == ""):
                user_visa_data.patient_passport_file2 = None
            elif visa_data.patient_passport_file2 is not None:
                file2_extn = visa_data.patient_passport_file2.filename.split('.')[-1]
                with open(f"data/visa_letter/passport_image/{user_visa_data.reference_id}_2.{file2_extn}", 'wb') as new_file:
                        new_file.write(await visa_data.patient_passport_file2.read())
                user_visa_data.patient_passport_file2 = f"{user_visa_data.reference_id}_2.{file1_extn}"
            db.query(UserVisaAttendantModel).filter(UserVisaAttendantModel.user_visa_id == visa_data.user_visa_id).delete()
            db.query(UserVisaDoctorModel).filter(UserVisaDoctorModel.user_visa_id == visa_data.user_visa_id).delete()
            if visa_data.attendants is not None and len(visa_data.attendants) > 0:
                for attendant in visa_data.attendants:
                    attendant_data = UserVisaAttendantModel(
                        user_visa_id = user_visa_data.id,
                        attendant = attendant.name,
                        attendant_passport = attendant.passport.upper(),
                        type = AttendantTypeEnum.ATTENDANT
                    )
                    db.add(attendant_data)
            if visa_data.doctors is not None and len(visa_data.doctors) > 0:
                for doctor in visa_data.doctors:
                    doctor_data = UserVisaDoctorModel(
                        user_visa_id = user_visa_data.id,
                        doctor_name = doctor.name,
                        doctor_specalization = doctor.specialization
                    )
                    db.add(doctor_data)
            if visa_data.donors is not None and len(visa_data.donors) > 0:
                for donor in visa_data.donors:
                    donor_data = UserVisaAttendantModel(
                        user_visa_id = user_visa_data.id,
                        attendant = donor.name,
                        attendant_passport = donor.passport.upper(),
                        type = AttendantTypeEnum.DONOR
                    )
                    db.add(donor_data)
            
        db.commit()
        return get_all_user_visa_data(db)
    except MutationError as ex:
        raise MutationError(ex.message)
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to store data")
class FPDF(FPDF):
    def header(self):
        self.set_font("times", size=10)
        self.set_text_color(0, 0, 0)

        self.image("assets/aig_logo.png", x=10, y=8,w=75)
        self.set_xy(70, 8)
        self.multi_cell(0,4,"""Mindspace Road, Gachibowli,
                  Hyderabad, Telangana - 500032,
                  Tel: +91 40 4244 4222,
                  <EMAIL>
                  www.aighospitals.com
        """, align='R')
        self.line(7, 30,203 ,30)
        self.ln(4)
        with self.local_context(fill_opacity=0.25):
            page_width = self.w
            page_height = self.h
            middle_x = page_width / 3
            middle_y = page_height / 2
            self.image("assets/water_mark.png", middle_x, middle_y, 70)
    

def generate_visa_invitation_letter(db: Session, user_visa_id:int, doctor_signatory: int,visa_expiry_days: int,staff_user: int):
    try:
        staff_user= db.query(StaffUserModel).filter(StaffUserModel.id==staff_user).one_or_none()
        user_data = db.query(UserVisaDataModel).filter(UserVisaDataModel.id == user_visa_id).first()
        if user_data is None:
            raise MutationError("User Data not Found")
        doctor_signature = db.query(AuthorizedDoctorModel).filter(AuthorizedDoctorModel.id == doctor_signatory).first()
        if doctor_signature is None:
            raise MutationError("Doctor Data not Found")
        pdf_path = f"data/visa_letter/pdfs/{user_data.reference_id}.pdf"
        today_date = date.today()
        t_date = today_date.strftime("%dth %B, %Y")
        date_format = today_date.strftime("%d%m%y")
        to_address = db.query(CountryEmbassyModel).filter(CountryEmbassyModel.id == user_data.embassy_id).first()
        pdf_file_path = f"{user_data.reference_id}.pdf"
        qr_img = generate_visa_qr(user_data.reference_id)
        pdf = FPDF(format="A4")
        pdf.add_page()
        # pdf.add_font("dejavu-sans", style="", fname="DejaVuSans.ttf")
        # pdf.set_font('dejavu-sans', size=14)
        pdf.set_font("times",size=10)                
        user_id = f"{user_data.id:03d}" if user_data.id < 1000 else f"{user_data.id}"
        ref_number= f"AIG/{to_address.country.country_code}/{date_format}/{user_id}"
        pdf.set_font(style="B")
        pdf.cell(0,5,txt=f"Ref No: {ref_number}",ln=True, align='R')
        pdf.cell(0,5,txt=f"Date: {t_date}", ln=True, align='R')
        pdf.set_font(style="B")
        pdf.cell(txt='To,',ln=True)
        pdf.ln(2)
        pdf.set_font(style="")
        pdf.multi_cell(0,txt=to_address.embassy_address)
        pdf.ln(6)
        pdf.set_font(style="U")
        pdf.cell(txt="MOST URGENT:", ln=True)
        pdf.ln(1)
        pdf.cell(txt="Request for help in issuing visa for medical treatment at AIG Hospitals, Hyderabad, India Reg.", ln=True)
        pdf.set_font(style="")
        pdf.ln(4)
        sub_body = to_address.country.sub_body_part_1
        pdf.cell(txt=to_address.country.subject, ln=True)
        pdf.ln(4)
        user_attendants = user_data.attendant
        user_treated_doctors = user_data.doctor
        treated_doctors=""
        doctors_for_bangla =[]
        doctors_spec_for_bangla =[]
        if len(user_treated_doctors) > 0:
            for doctor in user_treated_doctors:
                doctors_for_bangla.append(doctor.doctor_name)
                doctors_spec_for_bangla.append(doctor.doctor_specalization)
                treated_doctors = treated_doctors + f" {doctor.doctor_name}"
                treated_doctors = treated_doctors  + f"\n \t\t{doctor.doctor_specalization}\n"
        total_attendants =[]
        if len(user_attendants) > 0:
            count=0
            for atndt in user_attendants:
                if atndt.type == AttendantTypeEnum.DONOR:
                    total_attendants.append((f"Donor",f"{atndt.attendant}"))
                    total_attendants.append((f"Passport No of the Donor",f"{atndt.attendant_passport}"))
                else:
                    count += 1
                    total_attendants.append((f"Accompanying Attendant {count}",f"{atndt.attendant}"))
                    total_attendants.append((f"Passport No of the Attendant {count}",f"{atndt.attendant_passport}"))
        total_attendants = tuple(total_attendants)
        if to_address.pdf_format == PdfFormatEnum.NORMAL:
            appointment_date = user_data.appointment_date.strftime("%dth %B, %Y")
            sub_body = to_address.country.sub_body_part_1.replace("<$1>", (user_data.patient_name).upper()).replace(
                "<$2>", user_data.passport_no).replace("<$3>", ' & '.join(doctors_for_bangla)).replace(
                    "<$4>", ' & '.join(doctors_spec_for_bangla)).replace("<$5>", appointment_date).replace(
                        "<$6>", user_data.appointment_slot)
        pdf.multi_cell(0,4,txt=sub_body)
        if to_address.country.sub_body_part_3 is not None and to_address.country.sub_body_part_3 != "":
            pdf.ln(1)
            pdf.multi_cell(0,4,txt=to_address.country.sub_body_part_2)          
            pdf.ln(4)
            pdf.cell(txt=to_address.country.sub_body_part_3,ln=True)
            pdf.ln(2)
        elif to_address.pdf_format == PdfFormatEnum.NORMAL and len(user_attendants) > 0:
            pdf.ln(4)
            pdf.cell(txt=to_address.country.sub_body_part_2)          
            pdf.ln(8)
        table_data1=(("Name and Location of Medical Facility", "AIG Hospitals, Hyderabad, India"),
                ("Patient Name ", f"{user_data.patient_name}"),
                ("Passport No. ", f"{user_data.passport_no}"),
                ("Provisional Diagnosis", f"{user_data.provisional_diagnosis}"),
                ("Duration of Treatment",	f"{user_data.treatment_duration}"),
                ("Treating Hospital / Doctor", f"{treated_doctors}"))
        table_data2= total_attendants
        table_data3= (("Hospital Signatory", f"{doctor_signature.doctor_name}\n{doctor_signature.doctor_position}"),
                ("Contact Detail", f"{doctor_signature.mobile_no}")
                )
        if to_address.pdf_format != PdfFormatEnum.NORMAL:
            TABLE_DATA = (table_data1+table_data2+table_data3)
            with pdf.table(line_height=pdf.font_size+1,padding=0.7) as table:
                for data_row in TABLE_DATA:
                    if (data_row[1] == "None" or data_row[1] is None or data_row[1] == ""):
                        continue
                    row = table.row()
                    for datum in data_row:
                        row.cell(clean_unicode_text(str(datum)) if datum else "", padding=0)
        else:
            pdf.set_font(style="B")
            atndt_type=""
            if len(user_attendants) > 0:
                for i, atndt in enumerate(user_attendants, 1):
                    if atndt.type == AttendantTypeEnum.ATTENDANT:
                        atndt_type = 'Attendant'
                    else:
                        atndt_type = 'Donor'
                    attendant_text = f"{i}. {atndt.attendant} ({atndt_type}) - Passport No - {atndt.attendant_passport}"
                    pdf.cell(txt=clean_unicode_text(attendant_text), ln=True)
        pdf.set_font(style="")
        pdf.ln(3)
        pdf.multi_cell(0,4,txt=clean_unicode_text(to_address.country.conclusion))
        if user_data.additions is not None and user_data.additions != "":
            pdf.ln(2)
            pdf.multi_cell(0,4,txt=clean_unicode_text(user_data.additions))
        pdf.ln(4)
        pdf.cell(txt="With Kind Regards, ", ln=True)
        pdf.ln(1)
        pdf.cell(txt="For AIG Hospitals", ln=True)
        pdf.ln(1)
        pdf.image(f"{qr_img}",keep_aspect_ratio=True,w=30,h=15)
        pdf.cell(txt="Scan here to validate authenticity",ln=True)
        pdf.image(f"{doctor_signature.signature_path}",keep_aspect_ratio=True,x=10, y=pdf.get_y(), w=35,h=15)
        pdf.image(f"{doctor_signature.stamp_path}", x=pdf.get_x() + 35, y=pdf.get_y(), w=15, h=15)
        pdf.ln(14)
        pdf.set_text_color(255,69,0)
        pdf.cell(txt=clean_unicode_text(doctor_signature.doctor_name), ln=True)
        pdf.set_text_color(0,0,139)
        pdf.cell(txt=clean_unicode_text(doctor_signature.doctor_position), ln=True)
        pdf.cell(txt=f"Mob: {doctor_signature.mobile_no}", ln=True)
        pdf.set_text_color(30,144,255)
        pdf.set_font(style="U")
        pdf.cell(txt=clean_unicode_text(doctor_signature.email), ln=True)
        pdf.set_font(style="")
        pdf.set_text_color(0, 0, 0)
        if to_address.country.note is not None and to_address.country.note != "":
            pdf.ln(14)
            pdf.multi_cell(0,4,txt=clean_unicode_text(f"Note:- {to_address.country.note}"))
        pdf.output(pdf_path)
        # Save the PDF
        current_date = date.today()

        user_data.expiry_date = current_date + timedelta(days=int(visa_expiry_days))
        user_data.pdf_file_path = pdf_file_path
        user_data.hospital_signatory = doctor_signature.doctor_name
        user_data.contact_details = doctor_signature.mobile_no
        user_data.status = PdfGenerationEnum.GENERATED
        user_data.ref_no = ref_number
        user_data.generated_by = staff_user.name if staff_user is not None else " "
        db.commit()
        return get_all_user_visa_data(db)
    except MutationError as ex:
        raise MutationError(ex.message)
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured")


def get_visa_countries(db: Session):
    return db.query(CountryModel.id,CountryModel.country_name).order_by(asc(CountryModel.country_name)).all()

def get_authorized_doctors(db: Session):
    return db.query(AuthorizedDoctorModel.id,AuthorizedDoctorModel.doctor_name).all()

def get_all_user_visa_data(db:Session, from_date: Optional[str] = None, to_date: Optional[str] = None):
    try:
        query = db.query(UserVisaDataModel).order_by(UserVisaDataModel.created_at.desc(),UserVisaDataModel.status == PdfGenerationEnum.PENDING)
        if (from_date is not None and from_date != "") and (to_date is not None and to_date!=""):
            query = query.filter(func.date(UserVisaDataModel.created_at).between(from_date, to_date))
        return query.all()
    except Exception as  e:
        logger.exception(e)

def get_country_embassy(db:Session, country_id : int):
    query = db.query(CountryEmbassyModel.id, CountryEmbassyModel.embassy, CountryEmbassyModel.pdf_format).order_by(asc(CountryEmbassyModel.embassy))
    if country_id is not None:
        query = query.filter(CountryEmbassyModel.country_id == country_id)
    data = query.all()
    return data

def get_user_visa_info(db: Session, reference_id: str):
    data =  db.query(UserVisaDataModel).filter(UserVisaDataModel.reference_id == reference_id).first()
    return data

def delete_user_visa_data(db: Session, user_visa_id: int):
    try:
        user_visa_data = db.query(UserVisaDataModel).filter(UserVisaDataModel.id == user_visa_id).first()
        if user_visa_data is None:
            raise MutationError("Data not found for the requested user")
        db.delete(user_visa_data)
        db.query(UserVisaAttendantModel).filter(UserVisaAttendantModel.user_visa_id == user_visa_id).delete()
        db.query(UserVisaDoctorModel).filter(UserVisaDoctorModel.user_visa_id == user_visa_id).delete()
        db.commit()
        return get_all_user_visa_data(db)
    except MutationError as ex:
        raise MutationError(ex.message)
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to delete the data")
    
def cancel_user_visa_data(db: Session, user_visa_id: int, remarks: str):
    try:
        user_visa_data = db.query(UserVisaDataModel).filter(UserVisaDataModel.id == user_visa_id).first()
        if user_visa_data is None:
            raise MutationError("Data not found for the requested user")
        user_visa_data.status = PdfGenerationEnum.CANCELLED
        user_visa_data.remarks = remarks
        db.commit()
        return get_all_user_visa_data(db)
    except MutationError as ex:
        raise MutationError(ex.message)
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to delete the data")
    
def get_visa_doctors(db: Session):
    data=  db.query(distinct(DoctorModel.name).label('name')).order_by(asc(DoctorModel.name)).all()
    doctors = list(map(lambda x: x.name, data))
    return doctors

def get_visa_doctor_specality(db: Session, doctor_name: str):
    data =  db.query(DoctorModel.speciality).filter(DoctorModel.name == doctor_name).first()
    return data.speciality

def add_scanned_details(db:Session, reference_id:str, status:str):
    try:
        scanned_data = UserVisaScannedDetailsModel(
            reference_id = reference_id,
            status = status,
            scanned_at = func.now()
        )
        db.add(scanned_data)
        db.commit()
    except Exception as e:
        logger.exception(e)

def get_visa_config_data(db: Session):
    db_data=db.query(ConfigModel.data).first()
    data=json.loads(db_data.data)
    return data 