"""enum value changed in request type

Revision ID: 7164906be003
Revises: e3ed9f595846
Create Date: 2023-10-30 09:54:52.063875

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7164906be003'
down_revision = 'e3ed9f595846'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TYPE requeststatus RENAME VALUE 'SHIFTED' TO 'ADMITTED'")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TYPE requeststatus RENAME VALUE 'ADMITTED' TO 'SHIFTED'")
    # ### end Alembic commands ###
