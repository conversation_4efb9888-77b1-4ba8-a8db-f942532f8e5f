"""column added

Revision ID: a4a845125579
Revises: da0dff1c64af
Create Date: 2023-12-06 15:37:18.184676

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a4a845125579'
down_revision = 'da0dff1c64af'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_visa_data', sa.Column('patient_passport_file1', sa.String(), nullable=True))
    op.add_column('user_visa_data', sa.Column('patient_passport_file2', sa.String(), nullable=True))
    op.drop_column('user_visa_data', 'passport_image')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_visa_data', sa.Column('passport_image', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_column('user_visa_data', 'patient_passport_file2')
    op.drop_column('user_visa_data', 'patient_passport_file1')
    # ### end Alembic commands ###
