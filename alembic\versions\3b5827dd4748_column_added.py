"""column added

Revision ID: 3b5827dd4748
Revises: 531a482e3536
Create Date: 2023-12-05 06:23:56.592418

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3b5827dd4748'
down_revision = '531a482e3536'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_visa_data', sa.Column('expiry_date', sa.Date(), nullable=True))
    op.add_column('user_visa_data', sa.Column('additions', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_visa_data', 'additions')
    op.drop_column('user_visa_data', 'expiry_date')
    # ### end Alembic commands ###
