"""Columns added in user table

Revision ID: e586b0157745
Revises: 6417bb027417
Create Date: 2023-09-27 07:00:36.964709

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'e586b0157745'
down_revision = '6417bb027417'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    registration_enum = postgresql.ENUM('MOBILE_REGISTRATION', 'AADHAR_REGISTRATION', name='registrationtypeenum', create_type=False)
    registration_enum.create(op.get_bind(), checkfirst=True)
    op.add_column('user', sa.Column('his_api_time_taken', sa.Float(), nullable=True))
    op.add_column('user', sa.Column('registration_type', registration_enum, nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'registration_type')
    op.drop_column('user', 'his_api_time_taken')
    # ### end Alembic commands ###
