"""new tables created

Revision ID: 332e2641d573
Revises: b613b930a02e
Create Date: 2023-10-12 07:09:32.748446

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '332e2641d573'
down_revision = 'b613b930a02e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_service',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('bill_no', sa.String(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('service_id', sa.Integer(), nullable=True),
    sa.Column('priority', sa.Numeric(), server_default=sa.text('1'), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'ON_PROGRESS', 'COMPLETED', 'CANCELLED', name='servicestatusenum'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['service_id'], ['service.id'], name='user_service_service_id_fk'),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], name='user_service_user_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('rel_user_service_queue',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('user_service_id', sa.Integer(), nullable=True),
    sa.Column('queue_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['queue_id'], ['queue.id'], name='rel_user_service_queue_queue_id_fk'),
    sa.ForeignKeyConstraint(['user_service_id'], ['user_service.id'], name='rel_user_service_queue_user_service_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('service', sa.Column('allow_grouping', sa.Boolean(), nullable=True))
    op.add_column('service', sa.Column('priority', sa.Numeric(), server_default=sa.text('1'), nullable=False))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('service', 'priority')
    op.drop_column('service', 'allow_grouping')
    op.drop_table('rel_user_service_queue')
    op.drop_table('user_service')
    # ### end Alembic commands ###
