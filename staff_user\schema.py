from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Optional
from staff_user.models import <PERSON><PERSON><PERSON> as StaffUserModel
from staff_user.resolvers import activate_or_deactivate_user_role, add_or_edit_staff_user, add_or_edit_user_role, get_selected_modules, get_staff_user_details, get_staff_users, get_user_role_menus_resources, reset_staff_user_password,staff_user_login, activate_or_deactivate_staff_user, delete_staff_user, get_user_role
import strawberry
from graphql_types import MutationResponse, OperationTypes, QueryResponse, StaffUserInput, UserRoleInput
from exceptions.exceptions import MutationError
from user.schema import UserType
import logging, os 
from jose import jws

from util.validators import validate_password
logger = logging.getLogger()
from util.globals import format_datetime
from menu.models import Menu as MenuModel
from user.models import UserType as UserTypeModel
import pandas as pd
from pydantic import Field

@strawberry.type
class StaffUserQueueDetails:
    id: int
    queue_name: str
    queue_code : str
    
    @classmethod
    def from_instance(cls, id: int, queue_name: str, queue_code: str):
        return cls(id = id, queue_name = queue_name, queue_code = queue_code)

@strawberry.type
class SelectedModule:
    module_id: int
    selected: Optional[List[str]] = None
    @classmethod
    def from_instance(cls, module_id: str, selected: Optional[List[str]]):
        selected =list(set(selected))
        selected.remove("") if "" in selected else None
        return cls(module_id = module_id, selected=sorted(selected))
    
@strawberry.type
class StaffUser:
    id : int
    emp_id: str
    name: str
    user_type: UserType
    updated_at: Optional[str] = None
    status: str
    queues: Optional[str] = None
    email: Optional[str] = None
    phone_number: Optional[str] = None
    queues_list: Optional[List[StaffUserQueueDetails]] = None 
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    modules : Optional[List[str]] = None
    menus : Optional[List[str]] = None
    resources : Optional[List[str]] = None
    instance= strawberry.Private[StaffUserModel]

    @classmethod
    def from_instance(cls, instance: StaffUserModel):
        return cls(
            id=instance.id,
            emp_id=instance.emp_id,
            name=instance.name,
            user_type=UserType.from_instance(instance.user_type),
            status=instance.status.name,
            updated_at=format_datetime(instance.updated_at) if instance.updated_at is not None else format_datetime(instance.created_at),
            queues = ", ".join(queue.queue_name for queue in instance.queues),
            queues_list = [StaffUserQueueDetails.from_instance(queue.id, queue.queue_name, queue.queue_code) for queue in instance.queues] if len(instance.queues) > 0 else [],
            email = instance.email,
            phone_number = instance.phone_number,
            created_by = instance.created_by_user.name if instance.created_by is not None else None,
            updated_by = instance.updated_by_user.name if instance.updated_by is not None else None,
            modules = list(set([rel_module.module.module_name for rel_module in instance.rel_staff_resource])) if len(instance.rel_staff_resource) >0 else [],
            menus=list(set([rel_menu.menu.name for rel_menu in instance.rel_staff_resource])) if len(instance.rel_staff_resource) >0 else [],
            resources= list(set([rel_resource.resource.name for rel_resource in instance.rel_staff_resource if rel_resource.resource_id is not None])) if len(instance.rel_staff_resource) >0 else [],
        )

@strawberry.type
class StaffUserInfo:
    id : int
    emp_id: str
    name: str
    user_type: UserType
    updated_at: Optional[str] = None
    status: str
    queues: Optional[str] = None
    email: Optional[str] = None
    phone_number: Optional[str] = None
    queues_list: Optional[List[StaffUserQueueDetails]] = None 
    selected_modules: Optional[List[SelectedModule]] = None
    # modules : Optional[List[str]] = None
    # menus : Optional[List[str]] = None
    # resources : Optional[List[str]] = None
    nurse_stations : Optional[List[str]] = None
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    instance= strawberry.Private[StaffUserModel]

    @classmethod
    def from_instance(cls, instance: StaffUserModel):
        return cls(
            id=instance.id,
            emp_id=instance.emp_id,
            name=instance.name,
            user_type=UserType.from_instance(instance.user_type),
            status=instance.status.name,
            updated_at=format_datetime(instance.updated_at) if instance.updated_at is not None else format_datetime(instance.created_at),
            queues = ", ".join(queue.queue_name for queue in instance.queues),
            queues_list = [StaffUserQueueDetails.from_instance(queue.id, queue.queue_name, queue.queue_code) for queue in instance.queues],
            email = instance.email,
            phone_number = instance.phone_number,
            selected_modules = get_selected_modules(instance.rel_staff_resource) if len(instance.rel_staff_resource) >0 else [],
            # modules = list(set([rel_module.module.module_name for rel_module in instance.rel_staff_resource])) if len(instance.rel_staff_resource) >0 else [],
            # menus=list(set([rel_menu.menu.name for rel_menu in instance.rel_staff_resource])) if len(instance.rel_staff_resource) >0 else [],
            # resources= list(set([rel_resource.resource.name for rel_resource in instance.rel_staff_resource if rel_resource.resource_id is not None])) if len(instance.rel_staff_resource) >0 else [],
            nurse_stations = [obj.nurse_station for obj in instance.nurse_station.all()] if instance.nurse_station.first() else [],
            created_by = instance.created_by_user.name if instance.created_by is not None else None,
            updated_by = instance.updated_by_user.name if instance.updated_by is not None else None,
        )
@strawberry.type
class ChildMenuItems:
    name: Optional[str] =None
    link: Optional[str] =None
    icon: Optional[str] =None
    icon_type: Optional[str] = None
    instance = strawberry.Private[MenuModel]
    @classmethod
    def from_instance(cls, instace: MenuModel):
        return cls(name=instace.name, link=instace.link, icon=instace.icon, icon_type=instace.icon_type)

@strawberry.type
class MenuItems:
    name: Optional[str] =None
    link: Optional[str] =None
    icon: Optional[str] =None
    icon_type: Optional[str] = None
    child_menus: Optional[List[ChildMenuItems]]  = None
    instance = strawberry.Private[MenuModel]
    @classmethod
    def from_instance(cls, instace: MenuModel,child_menus: Optional[List[MenuModel]] = None):
        return cls(name=instace.name, link=instace.link, icon=instace.icon, icon_type=instace.icon_type,
                   child_menus=[ChildMenuItems.from_instance(menu) for menu in child_menus])
        
@strawberry.type
class StaffUserDetails:
    name: str
    role: str
    access_token: Optional[str] = None
    menu_items: Optional[List[MenuItems]] = None
    allocated_resources: Optional[List[str]] = None
    queue_counter_status : Optional[str] = None
    designation:Optional[str]=None
    speciality:Optional[str]=None
    profile_pic:Optional[str]=None
    qualification:Optional[str]=None
    
    instance = strawberry.Private[StaffUserModel]
    @classmethod
    def from_instance(cls, instance:StaffUserModel, access_token: str, menu_items: Optional[List[MenuItems]], resources: Optional[List[str]], queue_counter_status: Optional[str]):
        return cls(
                name=instance.name,
                role =instance.user_type.code, 
                access_token=access_token,
                menu_items= [MenuItems.from_instance(menu[0],menu[1])for menu in menu_items] if len(menu_items) > 0 else [],
                allocated_resources= resources if len(resources) > 0 else [],
                queue_counter_status=queue_counter_status,
                designation=instance.designation,
                speciality=instance.speciality,
                profile_pic=instance.profile_pic,
                qualification=instance.qualification,
                   )

@strawberry.type
class UserRoles:
    id: int
    name: str
    code: str
    status: str
    selected_modules: Optional[List[SelectedModule]] = None
    modules : Optional[List[str]] = None
    menus : Optional[List[str]] = None
    resources : Optional[List[str]] = None
    created_at: Optional[str] = None
    created_by: Optional[str] = None
    updated_by: Optional[str] = None
    instance = strawberry.Private[UserTypeModel]
    @classmethod
    def from_instance(cls, instance: UserTypeModel):
        return cls(
            id=instance.id, 
            name=instance.name, 
            code=instance.code,
            status = instance.status.name,
            selected_modules = get_selected_modules(instance.rel_resource) if len(instance.rel_resource) >0 else [],
            modules = list(set([rel_module.module.module_code for rel_module in instance.rel_resource])) if len(instance.rel_resource) >0 else[],
            menus=list(set([rel_menu.menu.name for rel_menu in instance.rel_resource])) if len(instance.rel_resource) >0 else[],
            resources= list(set([rel_resource.resource.name for rel_resource in instance.rel_resource if rel_resource.resource_id is not None])) if len(instance.rel_resource) >0 else[],
            created_at = format_datetime(instance.created_at),
            created_by= instance.created_by,
            updated_by = instance.updated_by
            )        

@strawberry.type
class Query:
    @strawberry.field
    def get_staff_users(self, info,staff_user_id:Optional[int]=None, status:Optional[str]= None) -> QueryResponse[List[StaffUser]]:
        try:
            db = info.context["db"]
            login_module = info.context["login_module"]
            staff_users = get_staff_users(db,staff_user_id,login_module,status)
            return QueryResponse.from_status_flag(True, "Success", [StaffUser.from_instance(staff_user) for staff_user in staff_users])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_user_role(self, info) -> QueryResponse[List[UserType]]:
        try:
            db = info.context["db"]
            login_module = info.context["login_module"]
            data = get_user_role(db,login_module)
            return QueryResponse.from_status_flag(True, "Success", [UserType.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_user_role_menus_resources(self, info, user_type_id: Optional[int] = None) -> QueryResponse[List[UserRoles]]:
        try:
            db = info.context["db"]
            user_types= get_user_role_menus_resources(db,user_type_id)
            return QueryResponse.from_status_flag(True, "list fetched successfully",[UserRoles.from_instance(user_type) for user_type in user_types])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_staff_user_details(self, info,staff_user_id:Optional[int]) -> QueryResponse[StaffUserInfo]:
        try:
            db = info.context["db"]
            staff_user=None
            if staff_user_id is not None:
                staff_user = get_staff_user_details(db,staff_user_id)
            return QueryResponse.from_status_flag(True, "Success", None if staff_user is None else StaffUserInfo.from_instance(staff_user))
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
        
@strawberry.type
class Mutation:
    @strawberry.mutation
    def add_or_edit_staff_user(self, info,data:StaffUserInput) -> MutationResponse[List[StaffUser]]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            res,staff_users = add_or_edit_staff_user(db,data,staff_user)
            return MutationResponse.from_status_flag(True, res, [StaffUser.from_instance(staff_user) for staff_user in staff_users])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.mutation
    def staff_user_login(self, info, emp_id:str, password: str, module: Optional[str] = None) ->MutationResponse[StaffUserDetails]:
        try:
            db = info.context["db"]
            entity_type=info.context["entity_type"]
            device_id=info.context["device_id"]
            data = staff_user_login(db, emp_id, password,module,entity_type,device_id)
            dt = datetime.now() + \
                timedelta(minutes=float(
                    os.environ["ACCESS_EXPIRE_MINUTES"]))
            access_token = jws.sign({"sub": {"user_id":data['staff_user'].id,"user_sub_type":data['staff_user'].user_type.code, "login_module": data["login_module"]}, "user_type": "STAFF", "exp": dt.isoformat(
                )}, os.environ["SECRET_KEY"], algorithm=os.environ["ALGORITHM"])
            logger.info("Staff user logged in suscessfully")
            return MutationResponse.from_status_flag(True,"Login Successfull",StaffUserDetails.from_instance(data["staff_user"],access_token,data["menu_items"].items(),data["allocated_resources"],data.get("queue_counter_status")))
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message,None)
    
    @strawberry.mutation
    def delete_staff_user(self, info, staff_user_id:int) -> MutationResponse[str]:
        try:
            db = info.context["db"]
            login_staff_user = info.context["staff_user"]
            res = delete_staff_user(db, staff_user_id,login_staff_user)
            return MutationResponse.from_status_flag(True, "User Deleted Successfully", None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.mutation
    def activate_or_deactivate_staff_user(self, info, staff_user_id:int, status:str) -> MutationResponse[str]:
        try:
            db = info.context["db"]
            login_staff_user = info.context["staff_user"]
            res = activate_or_deactivate_staff_user(db, staff_user_id, status,login_staff_user)
            return MutationResponse.from_status_flag(True, res, None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.mutation
    def reset_staff_user_password(self, info, old_password: str, new_password: str)-> MutationResponse[str]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            validate_password(new_password)
            res = reset_staff_user_password(db, staff_user, old_password, new_password)
            return MutationResponse.from_status_flag(True, "Success", "Password Changed Sucessfully")
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.mutation
    def add_or_edit_user_role(self, info,data:UserRoleInput) -> MutationResponse[List[UserRoles]]:
        try:
            db = info.context["db"]
            user_types= add_or_edit_user_role(db,data)
            return MutationResponse.from_status_flag(True, "Data Updated Sucessfully", [UserRoles.from_instance(user_type) for user_type in user_types])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.mutation
    def activate_or_deactivate_user_role(self, info, user_role_id:int, status:str) -> MutationResponse[str]:
        try:
            db = info.context["db"]
            res = activate_or_deactivate_user_role(db, user_role_id, status)
            return MutationResponse.from_status_flag(True, res, None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)

