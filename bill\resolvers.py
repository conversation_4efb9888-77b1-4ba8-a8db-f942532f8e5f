import asyncio
from datetime import date, timedelta, datetime
from itertools import product
import math
import threading
import uuid
import pytz
import time
from bms.resolvers import get_staff_user
from resources.models import RelStaffUserResource as RelStaffUserResourceModel, RelUserTypeResource as RelUserTypeResourceModel, Resource as ResourceModel
from sms_mail_notification.schema import UserDetails
from sqlalchemy import asc, desc, func, or_, and_, case, text, distinct,cast, ARRAY,Integer
from bill.models import ServiceStatusEnum, UserService as UserServiceModel, UserToken as UserTokenModel
from database.db_conf import SessionLocal
from sqlalchemy.orm import Session
import logging
from exceptions.exceptions import MutationError
from service.models import Service as ServiceModel, ServicePrerequisite as ServicePrerequisiteModel
from queues.models import Location as LocationModel, QueueStep as QueueStepModel, QueueWeightageAction as QueueWeightageActionModel, RelQueueService as RelQueueServiceModel, RelStaffUserQueue as RelStaffUserQueueModel, RelUserServiceQueue as RelUserServiceQueueModel, UserPreReqStatusEnum, UserQueueStatusEnum, UserQueueStep as UserQueueStepModel, UserServicePrerequisite as UserServicePrerequisiteModel
from queues.resolvers import checkin_user_into_queue, get_opt_queue, get_tag_or_scan_id, update_rel_device_queue, update_user_queue_step
from user.models import Module as ModuleModel, StatusEnum, User as UserModel
from service.models import StatusEnum as SerStatusEnum
from graphql_types import BillDetail, UserDetail
# from ai4bharat.transliteration import XlitEngine
from queues.models import UserQueue as UserQueueModel, Queue as QueueModel
from sms_mail_notification.resolvers import get_template_name, send_whatsapp_msg
from sms_mail_notification.models import EventCodeEnum, SMSTypeEnum
import string, random
from itertools import groupby

desired_timezone = 'Asia/Kolkata'

from user.resolvers import send_aig_otp
from util.globals import generate_qr, generate_qr1, get_unique_token, get_unique_digit_code


logger = logging.getLogger()

lock = threading.RLock()
def send_welcome_msg(db:Session,user_token: UserTokenModel, user:UserModel, package:str,queue=None):
    tmp_name = get_template_name(db, SMSTypeEnum.WHATSAPP.name,EventCodeEnum.PATIENT_WELCOME.name)  
    try:
        # generate_qrcode = generate_qr({"tokenId":str(user_token.id)})

        generate_qrcode = generate_qr1(user.umr_no,str(user_token.id))
        name=user.name
        if "." in user.name:
            name=user.name.split(".")[1]
        hour = datetime.now().astimezone(pytz.timezone('Asia/Kolkata')).hour
        if 5 <= hour < 12:
            greeting = "Good Morning"
        elif 12 <= hour < 16:
            greeting = "Good Afternoon"
        else:
            greeting = "Good Evening"
        params = [{"type":"text","text":f"{greeting} {name.strip()}"},
                {"type":"text","text":"AIG Hospitals"},{"type":"text","text":package},
                {"type":"text","text":"MIRA"},{"type":"text","text":user_token.token_no.upper()},
                {"type":"text","text":f"Please proceed to Tower {queue.cluster.tower}->Floor {queue.cluster.floor}->Location {queue.cluster.cluster}" if queue else " "},{"type":"text","text":" "},{"type":"text","text":"Please show the above QR code to the supervisor upon arrival."},{"type":"text","text":" "},{"type":"text","text":"Track your journey here: https://aig-mitr.onelink.me/vAHa/kq87mk5r"}]
        logger.info(params)
        send_msg = send_whatsapp_msg(db, tmp_name, user.phone_number, params,generate_qrcode)
    except Exception as ex:
        logger.exception("ex")
        

        

def save_user_service(db: Session, bill_detail: BillDetail):
    with lock:
        try:
            user = db.query(UserModel).filter(
                UserModel.umr_no == bill_detail.umr_no).one_or_none()
            if user is None:
                user = UserModel(
                    umr_no=bill_detail.umr_no,
                    name=bill_detail.name,
                    phone_number=bill_detail.phone_number
                )
                db.add(user)
                db.flush()
            # send_to = user.phone_number
            token_no=get_unique_digit_code()
            list1 = db.scalars(db.query(ServiceModel.id).filter(
                ServiceModel.code.in_(bill_detail.service_codes))).all()
            services = []
            logger.info(list1)
            bill = db.query(UserTokenModel).filter(UserTokenModel.bill_no == bill_detail.bill_no).first()
            if bill is not None:
                raise MutationError("The bill number already exists")
            user_token = UserTokenModel(
                user_id=user.id,
                token_no=token_no,
                bill_no=bill_detail.bill_no
            )
            db.add(user_token)
            db.flush()
            db_objects=[]
            dist={}
            for service_id in list1:
                user_service = UserServiceModel(
                    user_id=user.id,
                    service_id=service_id,
                    token_id=user_token.id,
                )
                db.add(user_service)
                db.flush()
                dist[service_id]=user_service.id
            pre_req_list=db.query(ServicePrerequisiteModel).filter(ServicePrerequisiteModel.service_id.in_(list1), ServicePrerequisiteModel.pre_req_service_id.in_(list1)).all()
            for pre_req in pre_req_list:
                user_pre_req =UserServicePrerequisiteModel(
                    user_service_id=dist[pre_req.service_id],
                    pre_req_user_service_id=dist[pre_req.pre_req_service_id],
                    status= UserPreReqStatusEnum.HOLD,
                )
                # list1.remove(pre_req.service_id)
                db_objects.append(user_pre_req)
            db.add_all(db_objects)
            db.flush()
            # send_welcome_msg(db,user_token, user, "Master Health Checkup") 
            queue,services = get_opt_queue(db, list1, user_token.id) 
            logger.info(queue)
            user_queue, time = checkin_user_into_queue(db, queue, user.id, user_token.token_no, user_token.id,services,user.phone_number, None,None)           
            db.commit()
            return user_token.token_no
        except MutationError as ex:
            logger.exception(ex)
            raise MutationError(ex.message)
        except Exception as ex:
            logger.exception("Exception in add bill")
            raise MutationError("Error occured while saving bill")


def save_user_service_1(db: Session,satff_user, user_detail: UserDetail, is_direct_checkin:bool,tag: str,send_sms:bool, is_from_billing: bool, old_token: int = None):
    with lock:
        try:
            from celery_worker import send_sms_msg_task
            logger.info(f"bill start time: {time.time()}")
            user = db.query(UserModel).filter(
                UserModel.umr_no == user_detail.uhid).one_or_none()
            if user is None:
                user = UserModel(
                    umr_no=user_detail.uhid,
                    name=user_detail.name,
                    phone_number=user_detail.phone_number,
                    is_platinum_user=user_detail.is_platinum,
                    age=user_detail.age,
                    gender=user_detail.gender
                )
                db.add(user)
                db.flush()
            else:
                user.name = user_detail.name
                user.phone_number = user_detail.phone_number
                user.is_platinum_user = user_detail.is_platinum
                user.age = user_detail.age
                user.gender = user_detail.gender
            service_codes=[]
            bills=[]
            res_dict={}
            if is_direct_checkin:
                get_tag_or_scan_id(db, tag,"TAGID")
            service_codes, bills = map(list, zip(*[(detail.service_code, detail.detail_bill_id) for detail in user_detail.bill_detail]))
            logger.info("testing 1")
            list2 = db.query(ServiceModel.id, ServiceModel.code, ServiceModel.service_type).filter(
                ServiceModel.code.in_(service_codes)).filter(ServiceModel.status==SerStatusEnum.ACTIVE).all()
            service_dict = {code: service_id for service_id, code, *_ in list2}
            services = []
            logger.info(list2)
            if len(list2)==0:
                raise MutationError("service codes not found")
            serviceIds=list(map(lambda x: x.id, list2))
            service_types= set(list(map(lambda x: x.service_type, list2)))
            logger.info(service_types)
            if {'CONSULTATIONS', 'INVESTIGATIONS'}.issubset(service_types):
                service_type = "HEALTH PACKAGE"
            elif len(service_types) >0:
                service_type = next(iter(service_types))
            else:
                service_type = "INVESTIGATIONS"
            
            # combinations = product(bills, service_codes)
            # filter_conditions = [
            #     or_(
            #         and_(UserServiceModel.bill_no == val1, ServiceModel.code == val2)
            #         for val1, val2 in combinations
            #     )
            # ]
            user_token= db.query(UserTokenModel).filter(UserTokenModel.user_id==user.id).filter(func.date(UserTokenModel.created_at) == func.date(func.now())).first()
            if is_from_billing:
                pass
            else:
               combination_exists= db.query(UserQueueModel).filter(UserQueueModel.user_id==user.id).filter(and_(UserQueueModel.status!=UserQueueStatusEnum.EXIT,UserQueueModel.status!=UserQueueStatusEnum.PURGED)).first()
               if combination_exists is not None:
                   raise MutationError("Patient already in queue")
            is_new_token=False
            has_in_progress= False
            if user_token is None:
                logger.info("getting unique token start")
                token_no=get_unique_digit_code()
                logger.info("getting unique token end")
                user_token = UserTokenModel(
                    user_id=user.id,
                    token_no=token_no
                )
                db.add(user_token)
                db.flush()
                is_new_token= True
            else:
                token_no= user_token.token_no
                has_in_progress = any(service.status == "ON_PROGRESS" for service in user_token.user_services)
            db_objects=[]
            dist={}
            existing_detail_bill_ids=[]
            if not is_new_token:
                existing_detail_bill_ids=list(map(lambda x: str(x.detail_bill_id), user_token.user_services))
            # user_services_dict = [user_service.detail_bill_id for user_service in user_token.user_services]
            serviceIds1=[]
            for service in user_detail.bill_detail:
                serviceId=service_dict.get(service.service_code, 0)
                logger.info(existing_detail_bill_ids)
                if (service.detail_bill_id is None or str(service.detail_bill_id) not in existing_detail_bill_ids) and serviceId!=0:
                    service_id=service_dict.get(service.service_code),
                    serviceIds1.append(service_id)
                    user_service = UserServiceModel(
                        user_id=user.id,
                        service_id=service_id,
                        token_id=user_token.id,
                        bill_no= service.bill_no,
                        detail_bill_id= service.detail_bill_id,
                        billed_at=  service.billed_at,
                        package_id=service.package_id,
                        old_token_id=old_token,
                        appointment_date_time = service.appointment_date_time,
                        bill= service.bill,
                        test_id= service.test_id
                    )
                    db.add(user_service)
                    db.flush()
                    dist[service_id]=user_service.id                    
                # if dist[service.id] is None:
                # else:
                #     dist[service.id]=[user_service.id]+dist[service.id]
            if dist:
                pre_req_list=db.query(ServicePrerequisiteModel).filter(ServicePrerequisiteModel.service_id.in_(serviceIds1), ServicePrerequisiteModel.pre_req_service_id.in_(serviceIds1)).all()
                for pre_req in pre_req_list:
                    user_pre_req =UserServicePrerequisiteModel(
                        user_service_id=dist[pre_req.service_id],
                        pre_req_user_service_id=dist[pre_req.pre_req_service_id],
                        status= UserPreReqStatusEnum.HOLD,
                    )
                    # list1.remove(pre_req.service_id)
                    db_objects.append(user_pre_req)
                db.add_all(db_objects)
                db.flush()
                if not has_in_progress:
                    logger.info(f"before queue select time: {time.time()}")
                    queue,services = get_opt_queue(db, serviceIds, user_token.id) 
                    logger.info(f"after queue select time: {time.time()}")
                    send_welcome_msg(db,user_token, user, service_type,queue)
                    if send_sms:
                        msg_details = {"token_no":token_no,"proceed_to":f"Tower {queue.cluster.tower}->Floor {queue.cluster.floor}->Location {queue.cluster.cluster}" if queue else " ","track_link":"https://aig-mitr.onelink.me/vAHa/kq87mk5r"}
                        send_sms_msg_task.apply_async(args=[msg_details,user_detail.phone_number, "CREATE_TOKEN","PATIENT",{"sms_type":3}], countdown=0)   
                    logger.info(queue)
                    user_queue_count=db.query(UserQueueModel).filter(UserQueueModel.token_id==user_token.id).filter(and_(UserQueueModel.status!=UserQueueStatusEnum.EXIT,UserQueueModel.status!=UserQueueStatusEnum.PURGED)).count()
                    user_queue_id= 0
                    if user_queue_count==0:
                        user_queue, time1 = checkin_user_into_queue(db, queue, user.id, user_token.token_no, user_token.id,services,user.phone_number, user_detail.weightage_id,satff_user,user_detail.doctor_name,user_detail.patient_type,is_new_token)           
                        user_queue_id= user_queue.id
                        db.commit()
                    logger.info(f"bill completion time: {time.time()}")
                    if is_direct_checkin:
                        data = update_user_queue_step(db,user_queue_id,None,user_detail.remarks,"CHECKOUT", None, tag, "TRANSITION",satff_user,"TAGID")
                    db.commit()
                
                else:
                    send_welcome_msg(db,user_token, user, service_type, None)
                    if send_sms:
                        msg_details = {"token_no":token_no,"proceed_to":" ","track_link":"https://aig-mitr.onelink.me/vAHa/kq87mk5r"}
                        send_sms_msg_task.apply_async(args=[msg_details,user_detail.phone_number, "CREATE_TOKEN","PATIENT",{"sms_type":3}], countdown=0)   
            return user_token.token_no
        except MutationError as ex:
            logger.exception(ex)
            raise MutationError(ex.message)
        except Exception as ex:
            logger.exception("Exception in add bill")
            raise MutationError("Error occured while saving bill")

def get_user_bills(db:Session,phone_number,bill_no,start_date, end_date,uhid ,service_status, staff_user,role,token_no, queue_step_id,patient_name, condition,queue_id,full_list):
    logger.info(start_date)
    latest_user_queue_subquery = (
        db.query(UserQueueModel.token_id, func.max(UserQueueModel.created_at).label("latest_created_at"))
        .group_by(UserQueueModel.token_id)
        .subquery()
    )
    if condition=='STATUS':
        query= db.query(UserQueueModel.status,func.count(UserTokenModel.id)).join(UserQueueModel, UserTokenModel.id==UserQueueModel.token_id).join(UserQueueModel.queue).join(RelStaffUserQueueModel, QueueModel.id == RelStaffUserQueueModel.queue_id).filter(RelStaffUserQueueModel.staff_user_id == staff_user).join(UserQueueModel.queue_weightage_action).filter(func.date(func.timezone(desired_timezone, UserTokenModel.created_at))>=start_date,func.date(func.timezone(desired_timezone,UserTokenModel.created_at))<=end_date)
    elif condition=='QUEUE_STEP':
        parent_loc_id= case(
                [
                    (
                        LocationModel.parent_location_id.is_not(None), LocationModel.parent_location_id
                    )
                ],else_=(LocationModel.id)
            )
        query= db.query(UserQueueModel.queue_step_id,parent_loc_id,func.count(UserTokenModel.id),func.avg(func.now()-UserQueueModel.step_start_time)).join(UserQueueModel, UserTokenModel.id==UserQueueModel.token_id).join(UserQueueModel.queue).join(RelStaffUserQueueModel, QueueModel.id == RelStaffUserQueueModel.queue_id).filter(RelStaffUserQueueModel.staff_user_id == staff_user).join(UserQueueModel.queue_weightage_action).filter(func.date(func.timezone(desired_timezone, UserTokenModel.created_at))>=start_date,func.date(func.timezone(desired_timezone,UserTokenModel.created_at))<=end_date)
    elif condition=='OLD_UNCOMPLETED_RECORDS':
        query= db.query(UserTokenModel,UserQueueModel, UserModel).join(UserQueueModel, UserTokenModel.id==UserQueueModel.token_id).join(UserQueueModel.user).join(
        latest_user_queue_subquery,
        and_(
            UserQueueModel.token_id == latest_user_queue_subquery.c.token_id,
            UserQueueModel.created_at == latest_user_queue_subquery.c.latest_created_at,
        ),
    ).join(UserQueueModel.queue).join(RelStaffUserQueueModel, QueueModel.id == RelStaffUserQueueModel.queue_id).filter(RelStaffUserQueueModel.staff_user_id == staff_user).join(
        UserQueueModel.queue_weightage_action).filter(
            or_(UserQueueModel.status==UserQueueStatusEnum.ENTRY,UserQueueModel.status==UserQueueStatusEnum.PAUSED)).filter(func.date(func.timezone(desired_timezone, UserTokenModel.created_at))<start_date)
    else:
        query= db.query(UserTokenModel,UserQueueModel, UserModel).join(UserQueueModel, UserTokenModel.id==UserQueueModel.token_id).join(UserQueueModel.user).join(
        latest_user_queue_subquery,
        and_(
            UserQueueModel.token_id == latest_user_queue_subquery.c.token_id,
            UserQueueModel.created_at == latest_user_queue_subquery.c.latest_created_at,
        ),
    ).join(UserQueueModel.queue).join(RelStaffUserQueueModel, QueueModel.id == RelStaffUserQueueModel.queue_id).filter(RelStaffUserQueueModel.staff_user_id == staff_user).join(UserQueueModel.queue_weightage_action).filter(func.date(func.timezone(desired_timezone, UserTokenModel.created_at))>=start_date,func.date(func.timezone(desired_timezone,UserTokenModel.created_at))<=end_date)
    # staff_user_1 = get_staff_user(db,staff_user)    
    allocation_count = db.query(RelStaffUserResourceModel).join(ResourceModel).filter(RelStaffUserResourceModel.staff_user_id == staff_user, ResourceModel.code == 'INCLUDE_UNASSIGNED_LOCATIONS').count()
    codes= db.query(LocationModel.id).join(ResourceModel, LocationModel.code==ResourceModel.code).join(RelStaffUserResourceModel,ResourceModel.id==RelStaffUserResourceModel.resource_id).filter(RelStaffUserResourceModel.staff_user_id == staff_user)
    logger.info(codes)
    parent_loc_id= case(
        [
            (
                LocationModel.parent_location_id.is_not(None), LocationModel.parent_location_id
            )
        ],else_=(LocationModel.id)
    )
    if allocation_count > 0 or full_list==True:
        logger.info('count1')
        query=query.join(LocationModel, UserQueueModel.location_id==LocationModel.id, isouter=True).filter(or_(parent_loc_id.in_(codes.scalar_subquery()),UserQueueModel.location_id.is_(None)))
    else:
        logger.info('count2')
        query= query.join(LocationModel, UserQueueModel.location_id==LocationModel.id).filter(parent_loc_id.in_(codes.scalar_subquery()))
    if queue_id is not None and queue_id!=0:
        query=query.filter(UserQueueModel.queue_id==queue_id)
    if token_no is not None and token_no!='':
        query=query.filter(UserTokenModel.token_no==token_no)
    if queue_step_id is not None and queue_step_id!=0:
        query=query.filter(UserQueueModel.queue_step_id==queue_step_id)
    if phone_number is not None and phone_number!='':
        query=query.join(UserTokenModel.user).filter(UserModel.phone_number==phone_number)
    if patient_name is not None and patient_name!='':
        query=query.join(UserTokenModel.user).filter(UserModel.name.ilike('%'+patient_name+'%'))
    if uhid is not None and uhid!='':
        query=query.filter(UserModel.umr_no==uhid)
    logger.info(service_status)
    if len(service_status)>0:
        query=query.filter(UserQueueModel.status.in_(service_status))
    if bill_no is not None and bill_no !='':
        query=query.filter(UserTokenModel.bill_no==bill_no)
    if condition is not None and condition !='OLD_UNCOMPLETED_RECORDS' and condition !='':
        pass
    else:
        # if role=='RECEPTIONIST':
        #     pass
        # elif role =='TRANSITION':
        #     query=query.filter(UserQueueModel.status.in_(service_status))
        query= query.order_by(
                        asc(UserQueueModel.freezed_at),
            text("user_queue.status='FREEZED' desc"),
            desc((QueueModel.avg_procedure_time*QueueWeightageActionModel.weightage)+(func.extract("epoch",
                (func.now() - UserQueueModel.order_by_date))/60)),
            desc(UserQueueModel.created_at)) #TODO Replace to asc
    if condition is not None and condition !='OLD_UNCOMPLETED_RECORDS' and condition !='':
        logger.info(query)
        return query
    logger.info(query)
    return query.all()
 
def get_current_queue(db:Session, user_id):
    return db.query(UserQueueModel).filter(UserQueueModel.user_id==user_id).order_by(desc(UserQueueModel.created_at)).first()


def get_user_token(db:Session,uhid):
    # latest_user_queue_subquery = (
    #     db.query(UserQueueModel.token_id, func.max(UserQueueModel.created_at).label("latest_created_at"))
    #     .group_by(UserQueueModel.token_id)
    #     .subquery()
    # )
    # query= db.query(UserQueueModel,UserModel).join(
    #     latest_user_queue_subquery,
    #     and_(
    #         UserQueueModel.token_id == latest_user_queue_subquery.c.token_id,
    #         UserQueueModel.created_at == latest_user_queue_subquery.c.latest_created_at,
    #     ),
    # ).join(UserQueueModel.user).filter(func.date(UserTokenModel.created_at)== func.date(func.now()))
    # query=query.filter(UserModel.umr_no==uhid)
    query = db.query(UserQueueModel,UserTokenModel,UserModel).join(
            UserTokenModel, UserTokenModel.id==UserQueueModel.token_id).join(
        UserQueueModel.user).filter(UserQueueModel.status!=UserQueueStatusEnum.PURGED).filter(
                func.date(UserQueueModel.created_at)== func.date(func.now())).filter(
                    UserModel.umr_no==uhid).order_by(
                        desc(UserQueueModel.created_at))
    logger.info(query.first())
    return query.first()

def update_service_ids(db:Session, token_id: int,service_ids,weightage_id,staff_user,doctor_name,patient_type):
    user_token= db.query(UserTokenModel).filter(UserTokenModel.id==token_id).one()
    all_service_ids= db.scalars(db.query(distinct(UserServiceModel.service_id)).filter(UserServiceModel.token_id==token_id).filter(or_(UserServiceModel.status==ServiceStatusEnum.ON_PROGRESS,UserServiceModel.status==ServiceStatusEnum.PENDING))).all()
    remove_ids=set(all_service_ids)-set(service_ids)
    add_service_ids= set(service_ids)- set(all_service_ids)
    logger.info(add_service_ids)
    for service_id in add_service_ids:
        user_service = UserServiceModel(
            user_id=user_token.user_id,
            service_id=service_id,
            token_id=token_id,
        )
        db.add(user_service)
    value= db.query(UserServiceModel).filter(UserServiceModel.token_id==token_id).filter(or_(UserServiceModel.status==ServiceStatusEnum.ON_PROGRESS,UserServiceModel.status==ServiceStatusEnum.PENDING)).filter(
        UserServiceModel.service_id.in_(remove_ids)
    ).update({
        UserServiceModel.status: ServiceStatusEnum.CANCELLED
    }, synchronize_session="fetch"
    )
    if value>0:
        user_queue_ids=[]
        rel_user_services = db.query(RelUserServiceQueueModel).filter(RelUserServiceQueueModel.user_service).join(UserServiceModel.service).filter(UserServiceModel.token_id==token_id).filter(ServiceModel.id.in_(remove_ids))
        for rel_user_service in rel_user_services:
            rel_user_service.status=ServiceStatusEnum.CANCELLED
            user_queue_ids.append(rel_user_service.user_queue_id)
        logger.info(set(user_queue_ids))
        logger.info(list(set(user_queue_ids)))
        for user_queue_id in list(set(user_queue_ids)):
            logger.info(user_queue_id)
            rem_count= db.query(RelUserServiceQueueModel).filter(RelUserServiceQueueModel.user_queue_id==user_queue_id).filter(
                RelUserServiceQueueModel.status.in_([ServiceStatusEnum.ON_PROGRESS,ServiceStatusEnum.PENDING])
            ).count()
            logger.info(rem_count)
            if rem_count ==0:
                db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id).update(
                    {
                        UserQueueModel.status:UserQueueStatusEnum.PURGED,
                        UserQueueModel.updated_by:staff_user
                    },synchronize_session="fetch"
                )
    logger.info(all_service_ids)
    logger.info(service_ids)
    if weightage_id is not None:
        db.query(UserQueueModel).filter(UserQueueModel.user_service).filter(UserQueueModel.token_id==token_id).filter(UserServiceModel.status==ServiceStatusEnum.ON_PROGRESS).update(
            {
                UserQueueModel.weightage_id:weightage_id,
                UserQueueModel.doctor_name:doctor_name,
                UserQueueModel.patient_type:patient_type
            },synchronize_session="fetch"
        )
        # rel_user_service = RelUserServiceQueueModel(
        #         user_service_id=row.id,
        #         queue_id=queue.id,
        #         user_queue_id=user_queue.id,
        #         status = ServiceStatusEnum.PENDING
        #     )
    db.commit()
    
def get_user_token_count(db:Session, staff_user,queue_id):
    query= get_user_bills(db,None,None,date.today(),date.today(),None,[],staff_user,None,None,None,None, 'STATUS',queue_id,None)
    query1=query.group_by(UserQueueModel.status)
    res= query1.all()
    query= get_user_bills(db,None,None,date.today(),date.today(),None,[],staff_user,None,None,None,None, 'QUEUE_STEP',queue_id,None)
    parent_loc_id= case(
        [
            (
                LocationModel.parent_location_id.is_not(None), LocationModel.parent_location_id
            )
        ],else_=(LocationModel.id)
    )
    query2=query.group_by(UserQueueModel.queue_step_id, parent_loc_id)
    counts=query2.all()
    grouped_loc = {}
    grouped_step = {}
    logger.info(counts)
    for tup in counts:
        first, second, third, fourth = tup
        logger.info(grouped_step)
        try:
            if first in grouped_step:
                grouped_step[first] += third
            else:
                grouped_step[first] = third
        except Exception as ex:
            logger.exception(ex)
        try:
            if second in grouped_loc:
                if first is not None:
                    steps=grouped_loc[second].get("steps")
                    logger.info(steps)
                    sum={"count":0,"avg":None} if steps.get(first) is None else steps.get(first)
                    avg=None
                    if fourth is None:
                        avg= sum.get("avg")
                    elif sum.get("avg") is None:
                        avg= fourth
                    else:
                        avg= (sum.get("avg")+fourth)/2
                    steps[first]={"count":sum.get("count")+third,"avg":avg}
                    count= grouped_loc[second].get("count")+ third
                    grouped_loc[second] = {"count":count,"steps": steps}
            else:
                steps={}
                if first is not None:
                    steps= {first: {"count":third,"avg":fourth}}
                grouped_loc[second] = {"count":third,"steps": steps}
        except Exception as ex:
            logger.exception(ex)
        logger.info(grouped_loc)
    logger.info(res)
    steps= db.query(QueueStepModel).filter(QueueStepModel.id.in_(grouped_step.keys())).all()   
    locations= db.query(LocationModel).join(LocationModel.queue_step).filter(QueueStepModel.queue_id==queue_id).order_by(
        LocationModel.priority
        ).all()
    logger.info(grouped_loc)
    latest_queue_called= db.query(UserQueueModel).join(UserQueueModel.queue).filter(UserQueueModel.token_id==QueueModel.latest_token_id).first()
    logger.info(f"latest_queue_called {latest_queue_called}")
    return dict(res), grouped_step,grouped_loc,steps,locations, latest_queue_called

def get_token_details(db:Session,token_id:int,staff_user):
    latest_user_queue_subquery = (
        db.query(UserQueueModel.token_id, func.max(UserQueueModel.created_at).label("latest_created_at"))
        .group_by(UserQueueModel.token_id)
        .subquery()
    )
    query= (db.query(UserTokenModel,UserQueueModel, UserModel).join(UserQueueModel, UserTokenModel.id==UserQueueModel.token_id).join(UserQueueModel.user).join(
        latest_user_queue_subquery,
        and_(
            UserQueueModel.token_id == latest_user_queue_subquery.c.token_id,
            UserQueueModel.created_at == latest_user_queue_subquery.c.latest_created_at,
        ))
        .filter(UserQueueModel.token_id == token_id)
        .join(UserQueueModel.queue).join(RelStaffUserQueueModel, QueueModel.id == RelStaffUserQueueModel.queue_id).filter(RelStaffUserQueueModel.staff_user_id == staff_user)
        )
    res_data = query.all()
    return res_data[0]

def get_total_queue_stats(db:Session):
    today = (datetime.now().astimezone(pytz.timezone('Asia/Kolkata'))).date()
    token_counts_subquery = (
        db.query(
            UserServiceModel.token_id,
            func.count(UserServiceModel.token_id).label('total_count'),
            func.sum(case([(UserServiceModel.status == ServiceStatusEnum.COMPLETED, 1)], else_=0)).label('completed_count'),
            func.max(UserServiceModel.updated_at).label('updated_at'),
            func.min(UserServiceModel.created_at).label('created_at'),
        )
        .group_by(UserServiceModel.token_id)
        .filter(func.date(UserServiceModel.created_at)==today)
        .subquery()
    )
    completed_tokens = (
        db.query(
            func.count(UserTokenModel.id).label('total_counts'),
            func.sum(
                case([(token_counts_subquery.c.total_count == token_counts_subquery.c.completed_count, 1)],
             else_=0)).label('completed_counts'),
        func.avg(func.extract('epoch', token_counts_subquery.c.updated_at - token_counts_subquery.c.created_at)/60).label('avg_time')
        )
        .filter(func.date(UserTokenModel.created_at)==today)
        .group_by(UserTokenModel.id)
        .first()
    )
    logger.info(completed_tokens)
    service_times=(
            db.query(
                QueueModel.queue_name,
                func.avg(func.extract('epoch',UserQueueModel.end_time - UserQueueModel.start_time)/60).label('avg_service_time'),
                func.avg(func.extract('epoch',UserQueueModel.start_time - UserQueueModel.arrived_at)/60).label('avg_wait_time'),
            )
            .join(QueueModel)
            .filter(UserQueueModel.date == today)
            .group_by(QueueModel.queue_name)
            .order_by(
                case([
                    (QueueModel.queue_name=='Medical Gastro',0)
                ],else_=1)
            )
            .all()
        )
    # logger.info(service_times)
    return (completed_tokens, service_times)