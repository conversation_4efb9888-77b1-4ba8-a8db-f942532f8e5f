"""total count column added

Revision ID: 180e5e118e52
Revises: 8ef7352ed5cf
Create Date: 2024-03-21 13:03:37.017978

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '180e5e118e52'
down_revision = '8ef7352ed5cf'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('location', sa.Column('total_count', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('location', 'total_count')
    # ### end Alembic commands ###
