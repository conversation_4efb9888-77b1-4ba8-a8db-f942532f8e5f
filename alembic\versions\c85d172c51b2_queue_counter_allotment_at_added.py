"""queue counter allotment at added

Revision ID: c85d172c51b2
Revises: 97cb51e3fcfe
Create Date: 2024-02-12 06:22:53.452011

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c85d172c51b2'
down_revision = '97cb51e3fcfe'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('queue', sa.Column('allocate_counter_at', sa.Enum('CHECKIN', 'ENTRY', 'EXIT', 'HOLD', 'ARRIVED', 'PURGED', 'FREEZED', name='userqueuestatusenum'), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('queue', 'allocate_counter_at')
    # ### end Alembic commands ###
