"""uhid column added

Revision ID: a53d60322017
Revises: 8ac3ac6d487f
Create Date: 2023-09-07 11:13:45.407361

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a53d60322017'
down_revision = '8ac3ac6d487f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('printer', sa.Column('uhid', sa.String(), nullable=True))
    op.create_index(op.f('ix_printer_uhid'), 'printer', ['uhid'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_printer_uhid'), table_name='printer')
    op.drop_column('printer', 'uhid')
    # ### end Alembic commands ###
