"""changed country and embassy as foreign key in user_visa_data

Revision ID: 106d80f3b9ba
Revises: 6c7195afc2c0
Create Date: 2023-11-29 07:41:22.158297

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '106d80f3b9ba'
down_revision = '6c7195afc2c0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_visa_data', sa.Column('country_id', sa.Integer(), nullable=True))
    op.add_column('user_visa_data', sa.Column('embassy_id', sa.Integer(), nullable=True))
    op.create_foreign_key('user_visa_data_country_id_fk', 'user_visa_data', 'country', ['country_id'], ['id'])
    op.create_foreign_key('user_visa_data_embassy_id_fk', 'user_visa_data', 'country_embassy', ['embassy_id'], ['id'])
    op.drop_column('user_visa_data', 'embassy')
    op.drop_column('user_visa_data', 'country')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_visa_data', sa.Column('country', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('user_visa_data', sa.Column('embassy', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_constraint('user_visa_data_embassy_id_fk', 'user_visa_data', type_='foreignkey')
    op.drop_constraint('user_visa_data_country_id_fk', 'user_visa_data', type_='foreignkey')
    op.drop_column('user_visa_data', 'embassy_id')
    op.drop_column('user_visa_data', 'country_id')
    # ### end Alembic commands ###
