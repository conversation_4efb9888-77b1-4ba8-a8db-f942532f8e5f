"""token no unique check removed

Revision ID: 960143006727
Revises: 29917d516cad
Create Date: 2024-01-10 11:50:28.027607

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '960143006727'
down_revision = '29917d516cad'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('user_token_token_no_key', 'user_token', type_='unique')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint('user_token_token_no_key', 'user_token', ['token_no'])
    # ### end Alembic commands ###
