"""pre requisites column added

Revision ID: cd6156ce7fbb
Revises: 2e8a285cf5e5
Create Date: 2024-04-24 06:50:56.104734

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'cd6156ce7fbb'
down_revision = '2e8a285cf5e5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_queue', sa.Column('prerequisites_conditions', sa.ARRAY(sa.String()), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_queue', 'prerequisites_conditions')
    # ### end Alembic commands ###
