"""Column created in location table

Revision ID: eb0464d21448
Revises: 2b37884b7b9f
Create Date: 2024-03-04 10:58:08.898241

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'eb0464d21448'
down_revision = '2b37884b7b9f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("CREATE TYPE locationbedstatusenum AS ENUM ('ALLOTED', 'AVAILABLE', 'OCCUPIED')")
    op.add_column('location', sa.Column('parent_location_id', sa.Integer(), nullable=True))

    op.add_column('location', sa.Column('status', sa.Enum('ALLOTED', 'AVAILABLE', 'OCCUPIED', name='locationbedstatusenum'), nullable=True))
    op.create_foreign_key('location_parent_location_id_fk', 'location', 'location', ['parent_location_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('location_parent_location_id_fk', 'location', type_='foreignkey')
    op.drop_column('location', 'status')
    op.drop_column('location', 'parent_location_id')
    # ### end Alembic commands ###
