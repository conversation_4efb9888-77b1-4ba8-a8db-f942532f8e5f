"""columns added in bedRequestModel

Revision ID: bac1b8e7421a
Revises: aa28051f0bc1
Create Date: 2023-11-23 07:01:47.378765

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'bac1b8e7421a'
down_revision = 'aa28051f0bc1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bed_request', sa.Column('alloted_bed_class', sa.String(), nullable=True))
    op.add_column('bed_request', sa.Column('billing_bed_class', sa.String(), nullable=True))
    op.alter_column('bed_request', 'bed_class', new_column_name='requested_bed_class')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('bed_request', 'billing_bed_class')
    op.drop_column('bed_request', 'alloted_bed_class')
    op.alter_column('bed_request', 'requested_bed_class', new_column_name='bed_class')

    # ### end Alembic commands ###
