"""Models added for menus and resources

Revision ID: 6e1ed9f980e1
Revises: d45d3e6f26c6
Create Date: 2023-11-03 07:47:41.224807

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '6e1ed9f980e1'
down_revision = 'd45d3e6f26c6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('menu',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('code', sa.String(), nullable=True),
    sa.Column('icon_type', sa.String(), nullable=True),
    sa.Column('icon', sa.String(), nullable=True),
    sa.Column('status', postgresql.ENUM('ACTIVE', 'INACTIVE', name='statusenum',create_type=False), nullable=True),
    sa.Column('priority', sa.Integer(), nullable=True),
    sa.Column('link', sa.String(), nullable=True),
    sa.Column('target', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('resource',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('code', sa.String(), nullable=True),
    sa.Column('status', postgresql.ENUM('ACTIVE', 'INACTIVE', name='statusenum',create_type=False), nullable=True),
    sa.Column('priority', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('rel_menu_user_type',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('module_id', sa.Integer(), nullable=True),
    sa.Column('user_type_id', sa.Integer(), nullable=True),
    sa.Column('menu_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['menu_id'], ['menu.id'], name='rel_menu_user_type_menu_id_fk'),
    sa.ForeignKeyConstraint(['module_id'], ['module.id'], name='rel_menu_user_type_module_id_fk'),
    sa.ForeignKeyConstraint(['user_type_id'], ['user_type.id'], name='rel_menu_user_type_user_type_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('rel_user_type_resource',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('module_id', sa.Integer(), nullable=True),
    sa.Column('user_type_id', sa.Integer(), nullable=True),
    sa.Column('resource_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['module_id'], ['module.id'], name='rel_menu_user_type_module_id_fk'),
    sa.ForeignKeyConstraint(['resource_id'], ['resource.id'], name='rel_menu_user_type_resource_id_fk'),
    sa.ForeignKeyConstraint(['user_type_id'], ['user_type.id'], name='rel_menu_user_type_user_type_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('rel_device_module', sa.Column('resource_id', sa.Integer(), nullable=True))
    op.create_foreign_key('rel_device_module_resource_id_fk', 'rel_device_module', 'resource', ['resource_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('rel_device_module_resource_id_fk', 'rel_device_module', type_='foreignkey')
    op.drop_column('rel_device_module', 'resource_id')
    op.drop_table('rel_user_type_resource')
    op.drop_table('rel_menu_user_type')
    op.drop_table('resource')
    op.drop_table('menu')
    # ### end Alembic commands ###
