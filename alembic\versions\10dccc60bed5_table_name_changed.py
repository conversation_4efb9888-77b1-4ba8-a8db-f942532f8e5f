"""table name changed

Revision ID: 10dccc60bed5
Revises: 50666c5efd99
Create Date: 2023-10-11 11:51:02.353998

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '10dccc60bed5'
down_revision = '50666c5efd99'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('rel_queue_service',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('test_id', sa.Integer(), nullable=True),
    sa.Column('queue_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['queue_id'], ['queue.id'], name='rel_queue_service_queue_id_fk'),
    sa.ForeignKeyConstraint(['test_id'], ['service.id'], name='rel_queue_service_test_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_table('rel_queue_test')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('rel_queue_test',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('test_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('queue_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['queue_id'], ['queue.id'], name='rel_queue_test_queue_id_fk'),
    sa.ForeignKeyConstraint(['test_id'], ['service.id'], name='rel_queue_test_test_id_fk'),
    sa.PrimaryKeyConstraint('id', name='rel_queue_test_pkey')
    )
    op.drop_table('rel_queue_service')
    # ### end Alembic commands ###
