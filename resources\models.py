import enum
from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>an, Column, DateTime, Enum, ForeignKey, String, Numeric, Text, Integer, Date
from sqlalchemy.sql import func
import strawberry
from database.db_conf import Base
from user.models import StatusEnum
from sqlalchemy.orm import relationship
from operation.models import Operation

class Resource(Base):
    __tablename__ = "resource"
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String)
    code = Column(String)
    status = Column(Enum(StatusEnum))
    priority = Column(Integer)
    parent_resource_id = Column(Integer, ForeignKey("resource.id",name="resource_parent_resource_id_fk"))
    menu_id = Column(Integer, ForeignKey("menu.id", name="resource_menu_id_fk"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    child_resources = relationship("Resource", remote_side=[id])
    # rel_user_type_resources = relationship("RelUserTypeResource")
    menu = relationship("<PERSON><PERSON>", back_populates="resources")

    def __repr__(self) -> str:
        return "<Resource %r>" % self.id
    
class RelUserTypeResource(Base):
    __tablename__ = "rel_user_type_resource"

    id = Column(BigInteger, primary_key=True, autoincrement =True)
    module_id = Column(Integer,ForeignKey("module.id",name="rel_menu_user_type_module_id_fk"))
    user_type_id = Column(Integer, ForeignKey(
        "user_type.id", name="rel_menu_user_type_user_type_id_fk"))
    resource_id = Column(Integer, ForeignKey(
        "resource.id", name="rel_menu_user_type_resource_id_fk"))
    menu_id = Column(Integer, ForeignKey("menu.id", name="rel_menu_user_type_resource_menu_id_fk"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    resource = relationship("Resource")
    module = relationship("Module")
    menu = relationship("Menu")

    def __repr__(self) -> str:
        return "<RelUserTypeResource %r>" % self.id

class RelResourceOperation(Base):
    __tablename__ = "rel_resource_operation"

    id = Column(BigInteger, primary_key=True, autoincrement =True)
    resource_id = Column(Integer,ForeignKey("resource.id",name="rel_resource_operation_module_id_fk"))
    operation_id = Column(Integer, ForeignKey(
        "operation.id", name="rel_resource_operation_operation_id_fk"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    resource = relationship("Resource")
    operation = relationship("Operation")

    def __repr__(self) -> str:
        return "<RelResourceOperation %r>" % self.id

class RelStaffUserResource(Base):
    __tablename__ = "rel_staff_user_resource"

    id = Column(BigInteger, primary_key=True, autoincrement =True)
    module_id = Column(Integer,ForeignKey("module.id",name="rel_staff_user_resource_module_id_fk"))
    staff_user_id = Column(Integer, ForeignKey(
        "staff_user.id", name="rel_staff_user_resource_staff_user_id_fk"))
    resource_id = Column(Integer, ForeignKey(
        "resource.id", name="rel_staff_user_resource_resource_id_fk"))
    menu_id = Column(Integer, ForeignKey("menu.id", name="rel_staff_user_resource_menu_id_fk"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    resource = relationship("Resource")
    menu = relationship("Menu",order_by="Menu.priority")
    module = relationship("Module")    

    def __repr__(self) -> str:
        return "<RelStaffUserResource %r>" % self.id