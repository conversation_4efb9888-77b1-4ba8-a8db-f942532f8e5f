"""visa attendant doctor tables added

Revision ID: b8f3f0300524
Revises: 615573df01c6
Create Date: 2023-11-30 12:25:51.382831

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b8f3f0300524'
down_revision = '615573df01c6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_visa_attendant',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('user_visa_id', sa.Integer(), nullable=True),
    sa.Column('attendant', sa.String(), nullable=True),
    sa.Column('attendant_passport', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_visa_id'], ['user_visa_data.id'], name='user_visa_attendant_user_visa_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user_visa_doctor',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('user_visa_id', sa.Integer(), nullable=True),
    sa.Column('doctor_name', sa.String(), nullable=True),
    sa.Column('doctor_specalization', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_visa_id'], ['user_visa_data.id'], name='user_visa_doctor_user_visa_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_table('mismatch_bed_classes')
    op.drop_column('user_visa_data', 'attendant2_passport_no')
    op.drop_column('user_visa_data', 'doctor_specialization')
    op.drop_column('user_visa_data', 'attendant3_passport_no')
    op.drop_column('user_visa_data', 'attendant2')
    op.drop_column('user_visa_data', 'attendant1_passport_no')
    op.drop_column('user_visa_data', 'doctor_name')
    op.drop_column('user_visa_data', 'attendant3')
    op.drop_column('user_visa_data', 'attendant1')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_visa_data', sa.Column('attendant1', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('user_visa_data', sa.Column('attendant3', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('user_visa_data', sa.Column('doctor_name', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('user_visa_data', sa.Column('attendant1_passport_no', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('user_visa_data', sa.Column('attendant2', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('user_visa_data', sa.Column('attendant3_passport_no', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('user_visa_data', sa.Column('doctor_specialization', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('user_visa_data', sa.Column('attendant2_passport_no', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.create_table('mismatch_bed_classes',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('requested_bed_class', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('alloted_bed_class', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('billable_bed_class', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('tat', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('uhid', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('patient_name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('contact_no', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('date', sa.DATE(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='mismatch_bed_classes_pkey')
    )
    op.drop_table('user_visa_doctor')
    op.drop_table('user_visa_attendant')
    # ### end Alembic commands ###
