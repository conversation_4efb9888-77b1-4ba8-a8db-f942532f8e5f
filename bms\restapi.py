from fastapi import APIRouter, Response
from bms.resolvers import download_excel_file
from database.db_conf import SessionLocal
import logging

from graphql_types import MutationResponse
logger = logging.getLogger()

router = APIRouter(
    prefix="/bed",
    tags=["bed"],
    responses={404: {"description": "Not found"}},
)

@router.get("/download-excel")
def download_excel():
    try:
        db = SessionLocal()
        excel_data = download_excel_file(db)
        response = Response(content=excel_data.read())
        response.headers["Content-Disposition"] = 'attachment; filename="bed_requests.xlsx"'
        response.headers["Content-Type"] = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"

        return response
    except Exception as e:
        logger.exception(f"error Occured {e}")
        return MutationResponse.from_status_flag(False, e.message, None)
    finally:
        db.close()