"""user service table updated

Revision ID: ad447b0bdae9
Revises: b742d23756a0
Create Date: 2025-03-24 12:02:24.915905

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ad447b0bdae9'
down_revision = 'b742d23756a0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_service', sa.Column('detail_bill_id', sa.String(), nullable=True), schema='queue')
    op.add_column('user_service', sa.Column('billed_at', sa.DateTime(timezone=True), nullable=True), schema='queue')
    op.add_column('user_service', sa.Column('package_id', sa.Integer(), nullable=True), schema='queue')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_service', 'package_id', schema='queue')
    op.drop_column('user_service', 'billed_at', schema='queue')
    op.drop_column('user_service', 'detail_bill_id', schema='queue')
    # ### end Alembic commands ###
