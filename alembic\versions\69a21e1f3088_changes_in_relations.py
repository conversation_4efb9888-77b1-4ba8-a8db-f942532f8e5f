"""changes in relations

Revision ID: 69a21e1f3088
Revises: 332e2641d573
Create Date: 2023-10-12 10:05:22.988317

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '69a21e1f3088'
down_revision = '332e2641d573'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('rel_user_service_queue', sa.Column('user_queue_id', sa.Integer(), nullable=True))
    op.create_foreign_key('rel_user_service_queue_user_queue_id_fk', 'rel_user_service_queue', 'user_queue', ['user_queue_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('rel_user_service_queue_user_queue_id_fk', 'rel_user_service_queue', type_='foreignkey')
    op.drop_column('rel_user_service_queue', 'user_queue_id')
    # ### end Alembic commands ###
