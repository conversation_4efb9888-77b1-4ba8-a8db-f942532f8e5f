# from call_bell.models import CalltoEnum
import enum
from user.models import StatusEnum
from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Column, DateTime, Enum, String, UniqueConstraint, ForeignKey, Integer
from user.models import EntityTypeEnum
from sqlalchemy.sql import func
import strawberry
from sqlalchemy.orm import relationship
from database.db_conf import Base

@strawberry.enum
class OperationTypeEnum(enum.Enum):
    QUERY ='QUERY'
    MUTATION ='MUTATION'
    SUBSCRIPTION ='SUBSCRIPTION'

# @strawberry.enum
# class SubscriptionTypeEnum(enum.Enum):
#     GET_PATIENTS ='GET_PATIENTS'
#     GET_ROOMS ='GET_ROOMS'
#     GET_CALL_BELLS_BY_STAFF ='GET_CALL_BELLS_BY_STAFF'
#     GET_USER_REQUESTED_CALL_BELLS='GET_USER_REQUESTED_CALL_BELLS'
#     GET_REQUESTED_CALL_BELL='GET_REQUESTED_CALL_BELL'

@strawberry.enum
class AuthenticationTypeEnum(enum.Enum):
    NO_AUTH ='NO_AUTH'
    DEVICE_ID ='DEVICE_ID'
    BEARER ='BEARER'

class Operation(Base):
    __tablename__ = "operation"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String, nullable=False)
    type = Column(Enum(OperationTypeEnum),
                    default=OperationTypeEnum.QUERY, nullable=False)
    status = Column(Enum(StatusEnum), default=StatusEnum.ACTIVE, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

   
    __table_args__ = (UniqueConstraint('name','type', name='operation_name_type_uc'),)

    def __repr__(self) -> str:
        return "<Operation %r>" % self.id

class OperationRelUserType(Base):
    __tablename__ = "rel_operation_user_type"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    operation_id = Column(BigInteger, ForeignKey("operation.id", name="rel_operation_user_type_operation_id_fk"))
    entity_type = Column(Enum(EntityTypeEnum), nullable=False)
    user_type_id = Column(BigInteger, ForeignKey("user_type.id", name="rel_operation_user_type_user_type_id_fk"))
    auth_type=Column(Enum(AuthenticationTypeEnum),
                    default=AuthenticationTypeEnum.BEARER, server_default=AuthenticationTypeEnum.BEARER.name, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    operation = relationship("Operation")
    user_type = relationship("UserType")
    __table_args__ = (UniqueConstraint('operation_id','user_type_id', name='rel_operation_user_type_operation_id_user_type_id_uc'),)

    def __repr__(self) -> str:
        return "<OperationRelUserType %r>" % self.id