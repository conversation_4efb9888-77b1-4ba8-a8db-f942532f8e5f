from collections import Counter
from datetime import datetime, timedelta
import logging
import random
from typing import List, Optional
import uuid
from queues.resolvers import get_user_queue_steps_by_user_queue_id, update_user_queue_step
import strawberry
from bill.resolvers import get_current_queue, get_total_queue_stats, get_user_bills, get_user_token, get_user_token_count, save_user_service_1, update_service_ids,get_token_details
from bill.models import UserService as UserServiceModel, UserToken as UserTokenModel
from graphql_types import BillDetail, MutationResponse, QueryResponse, UserDetail
from exceptions.exceptions import MutationError
from queues.models import Tag as TagModel
from user.models import User as UserModel
from util.globals import format_datetime
from queues.models import Location as LocationModel, UserQueue as UserQueueModel, QueueStep as QueueStepModel, UserQueueStatusEnum, UserQueueStep as UserQueueStepModel
from strawberry.scalars import JSON
import pytz
import math

logger = logging.getLogger()
# from user.schema import User
    

@strawberry.type
class Tag1:
    id: int
    name: str
    code: str
    status: str
    created_at: str
    rfid_code: str

    instance = strawberry.Private[TagModel]
    @classmethod
    def from_instance(cls,instance: TagModel):
        return cls(
                id=instance.id, 
                name=instance.name,
                code=instance.code,
                status=instance.status.name if instance.status else None,
                created_at=instance.created_at,
                rfid_code=instance.rfid_code
                )
@strawberry.type
class UserService:
    id: int
    status: str
    service_name:str
    service_id: int
    categories: Optional[List[str]]=None
    service_type: str
    
    instance = strawberry.Private[UserServiceModel]
    @classmethod
    def from_instance(cls,instance: UserServiceModel):
        return cls(id=instance.id, 
                status=instance.status.name, 
                service_name= instance.service.name,
                service_id= instance.service.id,
                categories = instance.service.prerequisites_conditions,
                service_type = instance.service.service_type
                )
        
@strawberry.type
class QueueStepCount:
    id: int
    name: str
    code: str
    avg: Optional[str]
    count: Optional[int]
    instance = strawberry.Private[QueueStepModel]

    @classmethod
    def from_instance(cls,instance: QueueStepModel,count):
        return cls(id=instance.id, 
                name=instance.name,
                code= instance.code,
                avg = None if (count is None or count.get("avg") is None) else round(count.get("avg").total_seconds()/60,2),
                count =None if count is None else  count.get("count"),
                )
@strawberry.type
class LocationCount:
    id: int
    name: str
    count: Optional[int]
    steps: List[QueueStepCount]
    available_count:int
    occupied_count:int
    alloted_count:int
    instance = strawberry.Private[LocationModel]

    @classmethod
    def from_instance(cls,instance: LocationModel,count_obj,steps):
        # status_counts = Counter([bed.status.name for bed in instance.child_locations])
        return cls(id=instance.id, 
                name=instance.name,
                count = None if count_obj is None else count_obj.get("count"),
                steps= [QueueStepCount.from_instance(step,get_count(count_obj,step)) for step in steps],
                available_count=instance.total_count,
                occupied_count=instance.occupied_count,
                alloted_count=instance.alloted_count
                )

        
def get_count(count_obj,step):
    if count_obj is None:
        return {"count":0, "avg": None}
    elif count_obj.get("steps") is None:
        return {"count":0, "avg":None}
    return count_obj.get("steps").get(step.id)

@strawberry.type
class QueueStep:
    id: int
    name: str
    code: str
    created_at: Optional[str]
    completed_at: Optional[str]
    status: Optional[str]
    avg_step_time: Optional[float]
    
    instance = strawberry.Private[QueueStepModel]
    instance_1 = strawberry.Private[UserQueueStepModel]

    @classmethod
    def from_instance(cls,instance: QueueStepModel,instance_1: UserQueueStepModel):
        return cls(id=instance.id, 
                name=instance.name,
                code = instance.code,
                completed_at= None if instance_1 is None else instance_1.updated_at,
                created_at= None if instance_1 is None else instance_1.created_at,
                status = None if instance_1 is None else instance_1.status.name,
                avg_step_time= instance.avg_step_time
                )
def myFunc(x,id):
  return x.queue_step_id ==id
@strawberry.type
class UserQueueDetails1:
    id: int
    status: str
    token_no: str
    weightage: str
    weightage_id: int
    queue_id: int
    queue_type: str
    counter_name : Optional[str] = None
    start_time: Optional[str] = None
    queue_name: str
    steps: List[QueueStep]
    services: List[str]
    created_at: str
    location_id: Optional[int] = None
    location_code: Optional[str] = None
    next_location_id: Optional[int] = None
    location_name: Optional[str] = None
    step_start_time: Optional[str]= None
    next_step_start_time: Optional[str]= None
    tag: Optional[Tag1] = None
    step_name: Optional[str] = None
    end_time: Optional[str] = None
    prerequisites_conditions: Optional[List[str]]= None
    freezed_at: Optional[str] = None
    instance = strawberry.Private[UserQueueModel]

    @classmethod
    def from_instance(cls, instance: UserQueueModel):
        return None if instance is None else cls(
            id=instance.id,
            status=instance.status.name,
            token_no=instance.token_no.upper(),
            weightage=instance.queue_weightage_action.name,
            weightage_id= instance.queue_weightage_action.id,
            counter_name = instance.counter_obj.counter_name if instance.counter is not None else None,
            start_time = format_datetime(instance.start_time),
            end_time = format_datetime(instance.end_time),
            queue_name=instance.queue.queue_name,
            queue_id= instance.queue.id,
            queue_type = instance.queue.queue_type.name,
            created_at = instance.created_at,
            freezed_at = instance.freezed_at,
            services = [obj.service.code for obj in instance.user_service],
            steps= [QueueStep.from_instance(obj,next(filter(lambda x: myFunc(x, obj.id), instance.user_queue_step), None) ) for obj in instance.queue.queue_step],
            location_id=instance.location_id,
            location_code= None if instance.location is None else instance.location.code,
            location_name = None if instance.location is None else instance.location.name,
            step_start_time = instance.step_start_time,
            next_step_start_time = instance.next_step_start_time,
            next_location_id = instance.next_location_id,
            prerequisites_conditions = instance.prerequisites_conditions,
            tag = Tag1.from_instance(instance.tag) if instance.tag is not None else None,
            step_name = None if instance.queue_step is None else instance.queue_step.name
        )

@strawberry.type
class UserBill1:
    id: int
    umr_no:str
    bill_no: Optional[str]
    token_no: str
    user_name: str
    phone_number: Optional[str]
    services: Optional[List[UserService]]
    user_queue: Optional[UserQueueDetails1]
    token_id: int
    service_type: Optional[List[str]]
    created_at: str
    
    instance = strawberry.Private[UserTokenModel]
    @classmethod
    def from_instance(cls,instance: UserTokenModel,instance1: UserQueueModel, instance2: UserModel):
        return cls(id=instance.id, 
                umr_no=instance2.umr_no,
                bill_no=instance.bill_no,
                token_no= instance.token_no,
                user_name=instance2.name,
                phone_number=instance2.phone_number,
                services= [UserService.from_instance(obj) for obj in instance.user_services],
                user_queue= UserQueueDetails1.from_instance(instance1),
                token_id = instance.id,
                service_type = list(set([obj.service.service_type for obj in instance.user_services])),
                created_at = format_datetime(instance.created_at)
                )  


@strawberry.type
class TokenCount:
    ongoing_count: Optional[int]
    waiting_count: Optional[int]
    purged_count: Optional[int]
    completed_count: Optional[int]
    last_token_called_at: Optional[str]
    latest_token_no: Optional[str]
    # steps: Optional[List[QueueStepCount]]
    locations: Optional[List[LocationCount]]

@strawberry.type
class AvgServiceTimes:
    service_name: str
    avg_time: Optional[float]=None

    @classmethod 
    def from_instance(cls,service_name,avg_time):
        return cls(service_name=service_name,avg_time=avg_time if avg_time is not None else 0)
@strawberry.type
class QueueStats:
    journeys_created: int
    journeys_completed: Optional[int]=None
    avg_journey_time: Optional[float]=None
    avg_wait_time: Optional[float]=None
    burn_rate: Optional[int]=None
    service_tats: List[AvgServiceTimes]

    @classmethod 
    def from_instance(cls,journey_tats,service_tats):
        times=[]
        for obj in service_tats:
            if obj.avg_wait_time is not None and obj.avg_wait_time!='0.0':
                times.append(obj.avg_wait_time)
        start_time = datetime.combine(datetime.now().date(), datetime.min.time()) + timedelta(hours=8)
        start_time = pytz.timezone('Asia/Kolkata').localize(start_time)
        present_time = datetime.now(pytz.timezone('Asia/Kolkata'))
        total_hrs = 12 if int((present_time- start_time).total_seconds() / 3600)>12 else int((present_time- start_time).total_seconds() / 3600)
        return cls(
            journeys_created = journey_tats.total_counts,
            journeys_completed = journey_tats.completed_counts,
            avg_journey_time = journey_tats.avg_time,
            service_tats = [AvgServiceTimes.from_instance(obj.queue_name,obj.avg_service_time) for obj in service_tats],
            avg_wait_time= sum(times)/len(times) if len(times)>0 else 0,
            burn_rate = int(math.ceil(journey_tats.completed_counts/total_hrs)) if total_hrs > 0 else 0
        )

@strawberry.type
class Query:
    @strawberry.field
    def get_user_bills(self, info,start_date:str, end_date:str, phone_number:Optional[str]=None, bill_no:Optional[str]=None, uhid: Optional[str]=None, service_status: Optional[List[str]]=[], token_no:Optional[str]=None, queue_step_id:Optional[int]=None, patient_name:Optional[str]=None, full_list:Optional[bool]= None,condition: Optional[str]= None) -> QueryResponse[List[UserBill1]]:
        try:
            msg="Success"
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            # role = info.context["user_sub_type"]
            data = get_user_bills(db,phone_number,bill_no,start_date,end_date,uhid,service_status,staff_user,None, token_no,queue_step_id,patient_name, condition, None,full_list)
            logger.info(len(data))
            if len(data)==0:
                msg="No Records Found"
            return QueryResponse.from_status_flag(True, msg, [UserBill1.from_instance(obj[0],obj[1],obj[2]) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

    @strawberry.field
    def get_user_token(self, info,uhid:str) -> QueryResponse[UserBill1]:
        try:
            msg="Success"
            db = info.context["db"]
            obj = get_user_token(db,uhid)
            if obj is None:
                msg="No Records Found"
            return QueryResponse.from_status_flag(True, msg, UserBill1.from_instance(obj[1],obj[0], obj[2]))
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_user_queue_steps_by_user_queue_id(self, info,user_queue_id:int) -> QueryResponse[List[QueueStep]]:
        try:
            msg="Success"
            db = info.context["db"]
            data:UserQueueModel = get_user_queue_steps_by_user_queue_id(db,user_queue_id)
            return QueryResponse.from_status_flag(True, msg, [] if data is None else [QueueStep.from_instance(obj,next(filter(lambda x: myFunc(x, obj.id), list(sorted(data.user_queue_step, key=lambda x: x.id,reverse=True))), None) ) for obj in data.queue.queue_step])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

    @strawberry.field
    def get_user_token_count(self, info, queue_id:Optional[int]= None) -> QueryResponse[TokenCount]:
        try:
            msg="Success"
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            obj, step_count,loc_count, steps,locations,latest_queue_called = get_user_token_count(db,staff_user,queue_id)
            freezed_count= 0 if obj.get(UserQueueStatusEnum.FREEZED) is None else obj.get(UserQueueStatusEnum.FREEZED)
            arrived_count= 0 if obj.get(UserQueueStatusEnum.ARRIVED) is None else obj.get(UserQueueStatusEnum.ARRIVED)
            checkin_count= 0 if obj.get(UserQueueStatusEnum.CHECKIN) is None else obj.get(UserQueueStatusEnum.CHECKIN)
            hold_count= 0 if obj.get(UserQueueStatusEnum.HOLD) is None else obj.get(UserQueueStatusEnum.HOLD)

            waiting_count= freezed_count+arrived_count+checkin_count+hold_count
            purged_count= 0 if obj.get(UserQueueStatusEnum.PURGED) is None else obj.get(UserQueueStatusEnum.PURGED)
            completed_count=  0 if obj.get(UserQueueStatusEnum.EXIT) is None else obj.get(UserQueueStatusEnum.EXIT)
            ongoing_count=  0 if obj.get(UserQueueStatusEnum.ENTRY) is None else obj.get(UserQueueStatusEnum.ENTRY)
            if obj is None:
                msg="No Records Found"
            latest_token_no = None
            last_token_called_at = None
            if latest_queue_called is not None:
                latest_token_no=latest_queue_called.token_no
                last_token_called_at= latest_queue_called.queue.last_token_called_at
            locations_1=[LocationCount.from_instance(loc,loc_count.get(loc.id),loc.queue_step) for loc in locations] 
            return QueryResponse.from_status_flag(True, msg, TokenCount(ongoing_count=ongoing_count,waiting_count=waiting_count,purged_count=purged_count,completed_count=completed_count,locations=locations_1,last_token_called_at=format_datetime(last_token_called_at),latest_token_no=latest_token_no))
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_token_details(self, info,token_id:int) -> QueryResponse[UserBill1]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            data_obj = get_token_details(db,token_id,staff_user)
            return QueryResponse.from_status_flag(True, "Success", UserBill1.from_instance(data_obj[0],data_obj[1],data_obj[2]))
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.field
    def get_total_queue_stats(self, info) -> QueryResponse[QueueStats]:
        try:
            db = info.context["db"]
            completed_tokens, service_times = get_total_queue_stats(db)
            return QueryResponse.from_status_flag(True, "Success", QueueStats.from_instance(completed_tokens,service_times))
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
        
@strawberry.type
class Mutation:

    @strawberry.mutation
    def save_user_bill(self, info, user_detail: UserDetail, is_direct_checkin: Optional[bool]=False, tagCode: Optional[str]=None) -> MutationResponse[str]:
        try:
            db = info.context["db"]
            staff_user =info.context["staff_user"]
            obj = save_user_service_1(db,staff_user,user_detail=user_detail, is_direct_checkin=is_direct_checkin, tag=tagCode)
            return MutationResponse.from_status_flag(True, "bill linked successfully",  obj)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.mutation
    def update_services(self, info, token_id: int ,service_ids: Optional[List[int]]=[], weightage_id: Optional[int]=None) -> MutationResponse[None]:
        try:
            db = info.context["db"]
            staff_user=info.context["staff_user"]
            obj = update_service_ids(db,token_id,service_ids,weightage_id,staff_user)
            return MutationResponse.from_status_flag(True, "bill linked successfully",  None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)