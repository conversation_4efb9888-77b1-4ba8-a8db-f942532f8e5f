"""enum values added to requestStatus

Revision ID: e3ed9f595846
Revises: 6d6a462d009f
Create Date: 2023-10-25 10:24:50.763083

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e3ed9f595846'
down_revision = '6d6a462d009f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TYPE requeststatus ADD VALUE 'TRANSFERED'")
    op.execute("ALTER TYPE requeststatus ADD VALUE 'SHIFTED'")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TYPE requeststatus DROP VALUE 'TRANSFERED'")
    op.execute("ALTER TYPE requeststatus DROP VALUE 'SHIFTED'")

    # ### end Alembic commands ###
