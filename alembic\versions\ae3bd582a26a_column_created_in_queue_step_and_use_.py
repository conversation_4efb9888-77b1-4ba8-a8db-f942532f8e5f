"""Column created in queue step and use queue step

Revision ID: ae3bd582a26a
Revises: 855b3e193d0d
Create Date: 2024-03-05 07:04:42.751056

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ae3bd582a26a'
down_revision = '855b3e193d0d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('queue_step', sa.Column('checkin_description', sa.String(), nullable=True))
    op.add_column('queue_step', sa.Column('checkout_description', sa.String(), nullable=True))
    op.add_column('user_queue_step', sa.Column('description', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_queue_step', 'description')
    op.drop_column('queue_step', 'checkout_description')
    op.drop_column('queue_step', 'checkin_description')
    # ### end Alembic commands ###
