import logging, os
from util.globals import format_datetime

from visa_letter.resolvers import cancel_user_visa_data, delete_user_visa_data, generate_visa_invitation_letter, generate_visa_user_data, get_authorized_doctors, get_country_embassy, get_user_visa_info, get_all_user_visa_data, get_visa_countries, get_visa_doctor_specality, get_visa_doctors 
logger = logging.getLogger()
from exceptions.exceptions import MutationError
from graphql_types import MutationResponse, QueryResponse, VisaData
import strawberry
from datetime import date, datetime, timedelta
from typing import List, Optional
from visa_letter.models import AttendantTypeEnum, UserVisaData as UserVisaDataModel, UserVisaAttendant as UserVisaAttendantModel, UserVisaDoc<PERSON> as UserVisaDoctorModel

@strawberry.type
class Doctor:
    id: int
    name: str

    @classmethod
    def from_instance(cls,id: int, name: str):
        return cls(id = id, name = name)

@strawberry.type
class UserAttendants:
    name: str
    passport: str
    instance = strawberry.Private[UserVisaAttendantModel]
    @classmethod
    def from_instance(cls, instance: UserVisaAttendantModel):
        return cls( name = instance.attendant, passport = instance.attendant_passport)

@strawberry.type
class UserVisaDoctors:
    name: str
    specialization: str

    instance = strawberry.Private[UserVisaDoctorModel]
    @classmethod
    def from_instance(cls, instance: UserVisaDoctorModel):
        return cls( name = instance.doctor_name, specialization = instance.doctor_specalization)
    
@strawberry.type
class UserVisa:
    id: Optional[int] = None
    patient_name: Optional[str] = None
    patient_passport: Optional[str] = None
    provisional_diagnosis: Optional[str] = None
    treatment_duration: Optional[str] = None
    doctors: Optional[List[UserVisaDoctors]] = None
    hospital_signatory: Optional[str] = None
    contact: Optional[str] = None
    visa_country: Optional[str] = None
    visa_country_id: Optional[int] = None
    reference_id: Optional[str] = None
    attendants: Optional[List[UserAttendants]] = None
    file_path: Optional[str] = None
    embassy: Optional[str] = None
    embassy_id: Optional[int] = None
    status: Optional[str] = None
    appointment_date: Optional[str] = None
    appointment_slot: Optional[str] = None
    ref_no: Optional[str]= None
    donors: Optional[List[UserAttendants]] = None
    patient_passport_file1 : Optional[str] = None
    patient_passport_file2 : Optional[str] = None
    additions : Optional[str] = None
    remarks : Optional[str] = None
    expiry_date : Optional[str] = None
    created_at: Optional[str] = None
    patient_passport_file1_name: Optional[str] = None
    patient_passport_file2_name: Optional[str] = None
    instance = strawberry.Private[UserVisaDataModel]
    @classmethod
    def from_instance(cls, instance: UserVisaDataModel):
        return cls(
            id = instance.id, 
            patient_name = instance.patient_name,
            patient_passport = instance.passport_no,
            provisional_diagnosis = instance.provisional_diagnosis,
            treatment_duration = instance.treatment_duration,
            doctors = [UserVisaDoctors.from_instance(doctor) for doctor in instance.doctor] if len(instance.doctor) > 0 else [],
            hospital_signatory = instance.hospital_signatory,
            contact = instance.contact_details,
            visa_country = instance.country.country_name,
            visa_country_id = instance.country.id,
            attendants = [UserAttendants.from_instance(obj) for obj in instance.attendant if obj.type == AttendantTypeEnum.ATTENDANT] if len(instance.attendant) > 0 else [],
            reference_id = instance.reference_id,
            file_path = f"http://{os.environ['SERVER']}:{os.environ['PORT']}/visa-letter/download_pdf/"+instance.pdf_file_path if instance.pdf_file_path is not None else None,
            embassy = instance.embassy.embassy,
            embassy_id = instance.embassy.id,
            status = "EXPIRED" if (instance.expiry_date is not None and date.today() > instance.expiry_date) else instance.status.name if instance.status is not None else None,
            appointment_date =  instance.appointment_date,
            appointment_slot =  instance.appointment_slot,
            ref_no = instance.ref_no,
            donors = [UserAttendants.from_instance(obj) for obj in instance.attendant if obj.type == AttendantTypeEnum.DONOR] if len(instance.attendant) > 0 else [],
            patient_passport_file1 = f"http://{os.environ['SERVER']}:{os.environ['PORT']}/visa-letter/download_passport/"+instance.patient_passport_file1 if instance.patient_passport_file1 is not None else None,
            additions = instance.additions,
            remarks = instance.remarks,
            expiry_date = instance.expiry_date,
            patient_passport_file2 = f"http://{os.environ['SERVER']}:{os.environ['PORT']}/visa-letter/download_passport/"+instance.patient_passport_file2 if instance.patient_passport_file2 is not None else None,
            created_at = format_datetime(instance.created_at),
            patient_passport_file1_name = instance.patient_passport_file1 if instance.patient_passport_file1 is not None else None,
            patient_passport_file2_name = instance.patient_passport_file2 if instance.patient_passport_file2 is not None else None
            )

@strawberry.type
class Country:
    id:int
    name: str

    @classmethod
    def from_instance(cls, id:int, name:str):
        return cls(id = id , name =name)

@strawberry.type
class CountryEmbassy:
    id:int
    name: str
    type: str

    @classmethod
    def from_instance(cls, id:int, name:str, type: str):
        return cls(id = id , name =name, type= type.name)
    
@strawberry.type
class Query:
    @strawberry.field
    def get_visa_countries(self, info) -> QueryResponse[List[Country]]:
        try:
            db = info.context["db"]
            data = get_visa_countries(db)
            return QueryResponse.from_status_flag(True, "Success", [Country.from_instance(obj.id, obj.country_name) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_authorized_doctors(self, info) -> QueryResponse[List[Doctor]]:
        try:
            db = info.context["db"]
            data = get_authorized_doctors(db)
            return QueryResponse.from_status_flag(True, "Success", [Doctor.from_instance(obj.id, obj.doctor_name) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_all_user_visa_data(self, info, from_date : Optional[str] = None, to_date: Optional[str] = None) -> QueryResponse[List[UserVisa]]:
        try:
            db = info.context["db"]
            data = get_all_user_visa_data(db, from_date,to_date)
            return QueryResponse.from_status_flag(True, "Success", [UserVisa.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_country_embassy(self, info, country_id: Optional[int] = None) -> QueryResponse[List[CountryEmbassy]]:
        try:
            db = info.context["db"]
            data = get_country_embassy(db, country_id)
            return QueryResponse.from_status_flag(True, "Success", [CountryEmbassy.from_instance(obj.id, obj.embassy, obj.pdf_format) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_user_visa_info(self, info, reference_id: str) -> QueryResponse[UserVisa]:
        try:
            db = info.context["db"]
            data = get_user_visa_info(db,reference_id)
            logger.info(data)
            return QueryResponse.from_status_flag(True, "Success", UserVisa.from_instance(data))
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_visa_doctors(self, info) -> QueryResponse[List[str]]:
        try:
            db = info.context["db"]
            data = get_visa_doctors(db)
            return QueryResponse.from_status_flag(True, "Success", data)
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.field
    def get_visa_doctor_specality(self, info, doctor_name: str) -> QueryResponse[str]:
        try:
            db = info.context["db"]
            data = get_visa_doctor_specality(db,doctor_name)
            return QueryResponse.from_status_flag(True, "Success", data)
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)



@strawberry.type
class Mutation:
    @strawberry.mutation
    async def generate_visa_user_data(self, info, visa_data: VisaData) ->MutationResponse[List[UserVisa]]:
        try:
            db= info.context["db"]
            staff_user = info.context["staff_user"]
            data = await generate_visa_user_data(db, visa_data, staff_user)
            return MutationResponse.from_status_flag(True, "Success", [UserVisa.from_instance(obj) for obj in data])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.mutation
    def generate_visa_invitation_letter(self, info, user_visa_id:int, doctor_signatory:int) ->MutationResponse[List[UserVisa]]:
        try:
            db= info.context["db"]
            visa_expiry_days= info.context["visa_expiry_days"]
            staff_user = info.context["staff_user"]
            data = generate_visa_invitation_letter(db, user_visa_id,doctor_signatory,visa_expiry_days,staff_user)
            return MutationResponse.from_status_flag(True, "Success", [UserVisa.from_instance(obj) for obj in data])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.mutation
    def delete_user_visa_data(self, info, user_visa_id:int) ->MutationResponse[List[UserVisa]]:
        try:
            db= info.context["db"]
            data = delete_user_visa_data(db, user_visa_id)
            logger.info("success")
            return MutationResponse.from_status_flag(True, "Success", [UserVisa.from_instance(obj) for obj in data])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.mutation
    def cancel_user_visa_data(self, info, user_visa_id:int, remarks : str) ->MutationResponse[List[UserVisa]]:
        try:
            db= info.context["db"]
            data = cancel_user_visa_data(db, user_visa_id, remarks)
            return MutationResponse.from_status_flag(True, "Success", [UserVisa.from_instance(obj) for obj in data])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)

