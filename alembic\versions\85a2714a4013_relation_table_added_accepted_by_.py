"""relation table added, accepted by, accepted relation added in user

Revision ID: 85a2714a4013
Revises: 387091fbdc64
Create Date: 2024-02-19 10:29:29.105880

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '85a2714a4013'
down_revision = '387091fbdc64'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('relation',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('relation_name', sa.String(), nullable=False),
    sa.Column('relation_code', sa.String(), nullable=False),
    sa.Column('priority', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.<PERSON>eyConstraint('id')
    )
    op.add_column('user', sa.<PERSON>umn('accept_terms', sa.<PERSON>(), nullable=True))
    op.add_column('user', sa.Column('accepted_by', sa.String(), nullable=True))
    op.add_column('user', sa.Column('accepted_rel', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'accepted_rel')
    op.drop_column('user', 'accepted_by')
    op.drop_column('user', 'accept_terms')
    op.drop_table('relation')
    # ### end Alembic commands ###
