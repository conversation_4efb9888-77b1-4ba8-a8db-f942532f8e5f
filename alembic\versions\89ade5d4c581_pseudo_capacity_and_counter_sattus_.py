"""pseudo_capacity and counter sattus columns added

Revision ID: 89ade5d4c581
Revises: 4c80b4e93587
Create Date: 2024-01-05 11:47:03.605360

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '89ade5d4c581'
down_revision = '4c80b4e93587'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('queue', sa.Column('pseudo_capacity', sa.Integer(), server_default=sa.text('0'), nullable=True))
    op.add_column('queue_counter', sa.Column('counter_status', sa.Enum('ACTIVE', 'INACTIVE', name='statusenum',create_type=False), server_default='ACTIVE', nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('queue_counter', 'counter_status')
    op.drop_column('queue', 'pseudo_capacity')
    # ### end Alembic commands ###
