"""queue step action type added

Revision ID: 57bab8e703d0
Revises: ba32e137771c
Create Date: 2024-02-27 14:50:32.000184

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '57bab8e703d0'
down_revision = 'ba32e137771c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_queue_step', sa.Column('action_type', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_queue_step', 'action_type')
    # ### end Alembic commands ###
