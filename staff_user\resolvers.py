from datetime import datetime
from enum import Enum
import logging
import os
from typing import List, Optional

from sqlalchemy import case, ARRAY, Integer, String, and_, cast, distinct, func
from bms.resolvers import get_nurse_stations, get_staff_user
from operation.resolvers import update_operations
logger = logging.getLogger()
from sqlalchemy.orm import Session
from user.models import StatusEnum, UserType as UserTypeModel, Module as ModuleModel, Device as DeviceModel, EntityTypeEnum
from graphql_types import StaffUserInput, UserRoleInput
from exceptions.exceptions import MutationError
from staff_user.models import StaffUser as StaffuserModel, StaffUserAuditLogs as StaffUserAuditLogsModel, StaffUserLoginDetails as StaffUserLoginDetailsModel, UserLoginStatusEnum, UserLoginTypeEnum, RelStaffUserNurseStation as RelStaffUserNurseStationModel, StaffUserAuditLogs as StaffUserAuditLogsModel
from queues.models import RelStaffUserQueue as RelStaffUserQueueModel, Queue as QueueModel
import logging,json
from sqlalchemy.exc import IntegrityError
import bcrypt
from menu.models import Menu as MenuModel
from resources.models import Resource as ResourceModel,RelUserTypeResource as RelUserTypeResourceModel, RelStaffUserResource as RelStaffUserResourceModel
from queues.models import QueueCounter as QueueCounterModel
logger = logging.getLogger()
from itertools import product, chain
import pandas as pd

def add_or_edit_staff_user(db: Session, data: StaffUserInput, staff_user: int):
    try:
        logger.info(data)
        json_data={}
        login_staff_user = get_staff_user(db,staff_user)
        user_type = db.query(UserTypeModel.id).filter(
            UserTypeModel.id == data.user_role_id, UserTypeModel.status == StatusEnum.ACTIVE).first()
        if user_type is None:
            raise MutationError("Selected User Role is not found")
        password = '1234'
        bytePwd = password.encode("utf-8")
        pwd_hash = bcrypt.hashpw(bytePwd, bcrypt.gensalt()).decode("utf-8")
        if data.user_id is None:
            staff_user = StaffuserModel(
                emp_id=data.emp_id.upper(),
                name=data.name,
                email=data.email,
                user_type_id=user_type.id,
                phone_number=data.phone_number,
                password=pwd_hash,
                created_by = login_staff_user.id
            )
            db.add(staff_user)
            db.flush()
            staff_user_dict = model_to_dict(staff_user)
            json_data = json.dumps(staff_user_dict, cls=CustomJSONEncoder)
            msg = "User Added Successfully"
            action="ADDED"
        else:
            staff_user = db.query(StaffuserModel).filter(
                StaffuserModel.id == data.user_id).one_or_none()
            if staff_user is None:
                raise MutationError("Staff user not found")
            staff_user.name = data.name
            staff_user.email = data.email
            staff_user.phone_number = data.phone_number
            staff_user.user_type_id = user_type.id
            staff_user.updated_by = login_staff_user.id
            json_data["name"]=data.name
            json_data["email"]=data.email
            json_data["phone_number"]=data.phone_number
            json_data["updated_by"] = staff_user.name
            json_data = json.dumps(json_data)
            msg = "User Updated Successfully"
            action="UPDATED"
        db.query(RelStaffUserQueueModel).filter(
                        RelStaffUserQueueModel.staff_user_id==staff_user.id).delete()
        satff_user_queues=[]
        if data.queues is not None and len(data.queues)>0:
            satff_user_queues = list(map(lambda pair: {"staff_user_id": pair[0], "queue_id": pair[1]}, product([staff_user.id], data.queues)))
        log_data=StaffUserAuditLogsModel(
            emp_id=data.emp_id,
            data=json_data,
            action=action
        )
        db.add(log_data)
        input_modules = list(filter(lambda x: '_' in x, data.modules))
        # if len(data.nurse_stations)>0:
        add_staff_user_menus_resources_nurse_station(db,staff_user.id,input_modules, data.nurse_stations)
        db.bulk_insert_mappings(RelStaffUserQueueModel, satff_user_queues)
        db.commit()
        return msg,get_staff_users(db)
    except MutationError as e:
        logger.exception(e)
        raise MutationError(e.message)
    except IntegrityError as e:
        logger.exception(e)
        raise MutationError("User already exsists with this Emp ID")
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured While saving data")
    
        
def model_to_dict(model):
    model_dict = {}
    for column in model.__table__.columns:
        model_dict[column.name] = getattr(model, column.name)
    return model_dict

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Enum):
            return obj.value 
        elif isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)
    
def activate_or_deactivate_staff_user(db:Session, staff_user_id:int, status:str,login_staff_user_id: Optional[int] = None):
    try:
        login_staff_user = get_staff_user(db,login_staff_user_id)
        if status == 'ACTIVE':
            db.query(StaffuserModel).filter(StaffuserModel.id == staff_user_id).update({StaffuserModel.status : StatusEnum.ACTIVE, StaffuserModel.updated_by: login_staff_user.id if login_staff_user is not None else None},)
            msg = "User Activated Sucessfully"
        else:
            db.query(StaffuserModel).filter(StaffuserModel.id == staff_user_id).update({StaffuserModel.status : StatusEnum.INACTIVE, StaffuserModel.updated_by: login_staff_user.id if login_staff_user is not None else None},)
            msg = "User Deactivated Sucessfully"
        db.commit()
        return msg
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to update staff user")

def delete_staff_user(db:Session, staff_user_id:int,login_staff_user_id: Optional[int]=None):
    try:
        login_staff_user = get_staff_user(db,login_staff_user_id)
        db.query(StaffuserModel).filter(StaffuserModel.id == staff_user_id).update({StaffuserModel.deleted_at : func.now(), StaffuserModel.updated_by: login_staff_user.id if login_staff_user is not None else None},)
        db.commit()
        return "User Deleted Sucessfully"
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to Delete User")

def get_user_role(db: Session,login_module: Optional[int] = None):
    query = db.query(UserTypeModel).filter(UserTypeModel.entity_type == EntityTypeEnum.STAFF,UserTypeModel.status == StatusEnum.ACTIVE)
    if login_module is not None:
            query = (query.join(RelUserTypeResourceModel, and_(
                    RelUserTypeResourceModel.user_type_id == UserTypeModel.id,
                    RelUserTypeResourceModel.module_id == login_module
                )))
    return query.order_by(UserTypeModel.id).all()

def reset_staff_user_password(db: Session,staff_user_id: int, old_password: str, new_password: str):
    try:
        staff_user= db.query(StaffuserModel).filter(StaffuserModel.id == staff_user_id).first()
        if staff_user is None:
            raise MutationError("Invalid employee details")
        else:
            if not bcrypt.checkpw(
                old_password.encode("utf-8"),staff_user.password.encode("utf-8")):
                raise MutationError(f"Incorrect Credentials")
        bytePwd = new_password.encode("utf-8")
        pwd_hash = bcrypt.hashpw(bytePwd, bcrypt.gensalt()).decode("utf-8")
        staff_user.password = pwd_hash
        db.commit()
    except MutationError as ex:
        raise MutationError(ex.message)
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to reset the password")

def add_login_details(db: Session, staff_user_id,status,type,entity_type,failed_count):
    try:
        user_login=StaffUserLoginDetailsModel(
                    staff_user_id=staff_user_id,
                    status=status,
                    type=type,
                    entity_type=entity_type,
                    failed_count=failed_count
                )
        db.add(user_login)
        db.commit()
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to store login details")

def add_or_edit_user_role(db: Session, data: UserRoleInput):
    try:
        logger.info(data)
        input_modules = list(filter(lambda x: '_' in x, data.modules))
        if data.user_role_id is None and data.user_role_id != "":
            existing_user_type = db.query(UserTypeModel.name).filter(UserTypeModel.name == data.name).first()
            if existing_user_type is not None:
                raise MutationError("User role Already exists with this name")
            user_type = UserTypeModel(
                name=data.name,
                code =data.code.replace(' ', '_'),
                entity_type= EntityTypeEnum.STAFF
            )
            db.add(user_type)
            db.flush()
        else:
            user_type = db.query(UserTypeModel).filter(
                UserTypeModel.id == data.user_role_id).one_or_none()
            if user_type is None:
                raise MutationError("User Role not found")
            user_type.name = data.name
            if data.status is not None and data.status != "":
                user_type.status = StatusEnum(data.status)
        user_type_menu_resources = list(map(lambda record: f"{record.module_id}_{record.menu_id}" if record.resource_id is None else f"{record.module_id}_{record.menu_id}_{record.resource_id}", user_type.rel_resource))
        # logger.info(user_type_menu_resources)
        removed_menu_res=list(set(user_type_menu_resources) - set(input_modules))
        logger.info(removed_menu_res)
        if removed_menu_res is not None and len(removed_menu_res) > 0:
            add_or_delete_staff_user_menus_resources(db,list(removed_menu_res),False,None,user_type.id)
        added_menu_res=list(set(input_modules) - set(user_type_menu_resources))
        logger.info(added_menu_res)
        if (added_menu_res is not None and len(added_menu_res) > 0) and data.assign_to_all_users == True:
            add_or_delete_staff_user_menus_resources(db,list(added_menu_res),True,None,user_type.id)        
        db.query(RelUserTypeResourceModel).filter(
                                RelUserTypeResourceModel.user_type_id==user_type.id).delete()
        module_menu_resource= []
        if input_modules is not None and len(input_modules) > 0:
            module_menu_resource = list(
                            map(
                                lambda module_data: {
                                    "module_id": int(module_data.split('_')[0]),
                                    "user_type_id": user_type.id,
                                    "menu_id": int(module_data.split('_')[1]),
                                    **({"resource_id": int(module_data.split('_')[2])} if len(module_data.split('_')) == 3 else {})
                                },
                                filter(lambda x: x and len(x.split('_')) >=2, input_modules)
                            )
                        )
        # logger.info(module_menu_resource)
        db.bulk_insert_mappings(RelUserTypeResourceModel, module_menu_resource)
        db.flush()
        update_operations(db,user_type.id)
        db.commit()
        return get_user_role_menus_resources(db,None)
    except MutationError as ex:
        logger.exception(ex)
        raise MutationError(ex.message)
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured While saving data")

def get_user_role_menus_resources(db:Session, user_type_id: Optional[int] = None):
    try:
        query = db.query(UserTypeModel).filter(UserTypeModel.entity_type == EntityTypeEnum.STAFF)
        if user_type_id is not None:
            query=query.filter(UserTypeModel.id==user_type_id)
        return query.order_by(UserTypeModel.id).all()
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to retrive data")

def add_staff_user_menus_resources_nurse_station(db:Session, staff_user_id: int,modules: Optional[List[str]] = None,nurse_stations: Optional[List[str]] = None):
    try:
        db.query(RelStaffUserResourceModel).filter(
                                RelStaffUserResourceModel.staff_user_id==staff_user_id).delete()
        if modules is not None and len(modules)>0:
            add_or_delete_staff_user_menus_resources(db,modules,True,staff_user_id,None)
        db.query(RelStaffUserNurseStationModel).filter(
                            RelStaffUserNurseStationModel.staff_user_id == staff_user_id).delete()
        staff_nurse_stations = []
        if nurse_stations is not None and len(nurse_stations) > 0:
            staff_nurse_stations = list(map(lambda pair: {"staff_user_id": pair[0], "nurse_station": pair[1]}, product([staff_user_id], nurse_stations)))
        db.bulk_insert_mappings(RelStaffUserNurseStationModel, staff_nurse_stations)
        db.flush()
    except MutationError as e:
        logger.exception(e)
        raise MutationError(e.message)
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to store data")

def get_staff_users(db:Session, staff_user_id: Optional[int] = None,login_module: Optional[int] = None, status: Optional[str] = None):
    try:
        query = db.query(StaffuserModel).filter(StaffuserModel.deleted_at == None)
        if login_module is not None:
            query = (query.join(UserTypeModel, UserTypeModel.id == StaffuserModel.user_type_id)
                .join(RelUserTypeResourceModel, and_(
                    RelUserTypeResourceModel.user_type_id == UserTypeModel.id,
                    RelUserTypeResourceModel.module_id == login_module
                ))) 
        if status is not None:
            query= query.filter(StaffuserModel.status==status)
        if staff_user_id is not None:
            query = query.filter(StaffuserModel.id == staff_user_id)
        return query.all()
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to retrive data")

def get_staff_user_details(db:Session, staff_user_id: int):
    try:
        return db.query(StaffuserModel).filter(StaffuserModel.id == staff_user_id).first()
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to retrive data")

def staff_user_login(db: Session,emp_id:str, password:str, module: str,entity_type: Optional[str]=None, device_id: Optional[str]=None):
    try:
        staff_user : StaffuserModel=db.query(StaffuserModel).filter(StaffuserModel.emp_id==emp_id.upper()).one_or_none()
        data={}
        if staff_user.status == StatusEnum.INACTIVE:
            raise MutationError("Your account is currently inactive.")
        login_module = db.query(ModuleModel.id).filter(ModuleModel.module_name == module).one_or_none()
        if staff_user is not None:
            cool_down_time=os.environ["MAX_COOLDOWN_TIME"]
            time= case(
                [
                    (
                        StaffUserLoginDetailsModel.updated_at.is_not(None), func.extract("epoch", (func.now() - StaffUserLoginDetailsModel.updated_at))<=int(cool_down_time)
                    )
                ],else_=(func.extract("epoch", (func.now() - StaffUserLoginDetailsModel.created_at))<=int(cool_down_time))
            )
            if not bcrypt.checkpw(
                password.encode("utf-8"),staff_user.password.encode("utf-8")):
                failure= db.query(StaffUserLoginDetailsModel).filter(
                    StaffUserLoginDetailsModel.staff_user_id==staff_user.id, StaffUserLoginDetailsModel.status==UserLoginStatusEnum.FAILURE
                ).filter(time).first()
                if failure is not None and int(os.environ["MAX_LOGIN_ATTEMPTS"])<=failure.failed_count:
                    raise MutationError(f"Account Locked. Please Try after {int(cool_down_time)/60} mins")
                elif failure is not None:
                    failure.failed_count=failure.failed_count+1
                    db.commit()
                else:
                    add_login_details(db,staff_user.id,UserLoginStatusEnum.FAILURE,UserLoginTypeEnum.LOGIN,entity_type,1)
                raise MutationError(f"Incorrect Credentials")
            add_login_details(db,staff_user.id,UserLoginStatusEnum.SUCCESS,UserLoginTypeEnum.LOGIN,entity_type,0)
            data['staff_user'] = staff_user
            data['login_module'] = login_module.id if login_module is not None else None
        else:
            raise MutationError("Invalid Emp Id")
        if (login_module is not None):
            menus = (
                db.query(MenuModel)
                .join(RelStaffUserResourceModel, MenuModel.id == RelStaffUserResourceModel.menu_id)
                .join(ModuleModel, RelStaffUserResourceModel.module_id == ModuleModel.id)
                .filter(RelStaffUserResourceModel.staff_user_id == staff_user.id,ModuleModel.module_code == module, MenuModel.status == StatusEnum.ACTIVE)
                .order_by(MenuModel.priority.desc()).distinct().all()
            )
            # menus=list(set([rel_resource.menu for rel_resource in staff_user.rel_staff_resource if (rel_resource.resource_id is None and rel_resource.module_id == login_module.id and rel_resource.menu.status == StatusEnum.ACTIVE)])) if len(staff_user.rel_staff_resource) >0 else []
            parent_menus = filter(lambda menu: menu.parent_menu_id is None, menus)
            processed_menus = dict(map(lambda parent_menu: (parent_menu, list(filter(lambda menu: menu.parent_menu_id == parent_menu.id, menus))), parent_menus))
            # logger.info(processed_menus)
            data["menu_items"] = processed_menus
            resources=list(set([rel_resource.resource.code for rel_resource in staff_user.rel_staff_resource if rel_resource.resource_id is not None and rel_resource.module_id == login_module.id])) if len(staff_user.rel_staff_resource) >0 else []
            data["allocated_resources"] = resources
        else:
            data["menu_items"] = {}
            data["allocated_resources"] = []
        if device_id is not None:
            counter = (db.query(QueueCounterModel.counter_status)
                    .join(DeviceModel, QueueCounterModel.id == DeviceModel.queue_counter)
                    .filter(DeviceModel.device_code == device_id)
                    .first())
            if counter is not None:
                data["queue_counter_status"] = counter.counter_status.name
        return data
    except MutationError as e:
        logger.exception(e)
        raise MutationError(e.message)
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to login")

def add_or_delete_staff_user_menus_resources(db: Session, modules:List[str],add_elemets: bool,staff_user_id:Optional[int] = None,user_type_id:Optional[int]=None):
    try:
        staff_module_menu_resource = []
        if user_type_id is not None:
            staff_users = db.scalars(db.query(StaffuserModel.id).filter(StaffuserModel.user_type_id==user_type_id).filter(StaffuserModel.deleted_at == None)).all()
            if add_elemets == False:
                for module_data in modules:
                    if module_data and len(module_data.split('_')) >= 2:
                        module_id = int(module_data.split('_')[0])
                        menu_id = int(module_data.split('_')[1])
                        resource_id = int(module_data.split('_')[2]) if len(module_data.split('_')) == 3 else None
                        
                        deletion_query = db.query(RelStaffUserResourceModel).filter(
                            RelStaffUserResourceModel.staff_user_id.in_(staff_users),
                            RelStaffUserResourceModel.module_id == module_id,
                            RelStaffUserResourceModel.menu_id == menu_id,
                            RelStaffUserResourceModel.resource_id == resource_id
                        )
                        deleted_count = deletion_query.delete(synchronize_session="fetch")
        if add_elemets == True:
            staff_module_menu_resource = list(
                            map(
                                lambda module_data: {
                                    "module_id": int(module_data[0].split('_')[0]),
                                    "staff_user_id": module_data[1],
                                    "menu_id": int(module_data[0].split('_')[1]),
                                    **({"resource_id": int(module_data[0].split('_')[2])} if len(module_data[0].split('_')) == 3 else {})
                                },
                                product(modules, staff_users if user_type_id is not None else [staff_user_id]),
                            )
                        )
            # logger.info(staff_module_menu_resource)
            db.bulk_insert_mappings(RelStaffUserResourceModel, (staff_module_menu_resource))
        db.flush()
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while updating menus")

def get_selected_modules(rel_menu_resource):
    from staff_user.schema import SelectedModule

    selected_modules=[]
    data = list(map(lambda record: {'module_id': record.module_id, 
                                'menu_id': f"{record.module_id}_{record.menu_id}" if record.resource_id is None else "", 
                                'resource_id': f"{record.module_id}_{record.menu_id}_{record.resource_id}" if record.resource_id is not None else ""}, 
                rel_menu_resource))
    df = pd.DataFrame.from_records(data)
    grouped_df = df.groupby('module_id').agg({'menu_id': list, 'resource_id': list}).reset_index()
    selected_modules = grouped_df.apply(lambda x: SelectedModule.from_instance(x['module_id'], x['menu_id']+x['resource_id']), axis=1)
    return selected_modules

def activate_or_deactivate_user_role(db:Session, user_role_id:int, status:str):
    try:
        if status == 'ACTIVE':
            db.query(UserTypeModel).filter(UserTypeModel.id == user_role_id).update({UserTypeModel.status : StatusEnum.ACTIVE},)
            db.query(StaffuserModel).filter(StaffuserModel.user_type_id.in_(db.scalars(db.query(UserTypeModel.id).filter(UserTypeModel.id == StaffuserModel.user_type_id)))).update({StaffuserModel.status: StatusEnum.ACTIVE},)
            msg = "User Role Activated Sucessfully"
        else:
            db.query(UserTypeModel).filter(UserTypeModel.id == user_role_id).update({UserTypeModel.status : StatusEnum.INACTIVE},)
            db.query(StaffuserModel).filter(StaffuserModel.user_type_id.in_(db.scalars(db.query(UserTypeModel.id).filter(UserTypeModel.id == StaffuserModel.user_type_id)))).update({StaffuserModel.status: StatusEnum.INACTIVE},)
            msg = "User Role Deactivated Sucessfully"
        db.commit()
        return msg
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to update user role")