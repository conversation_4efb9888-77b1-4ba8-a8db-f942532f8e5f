from datetime import datetime
from distutils.log import info
from typing import List, Optional
from unicodedata import numeric
from exceptions.exceptions import MutationError
from user.schema import User
import logging
from graphql_types import MutationResponse, PatientResponse, FeedbackDetails, UserFeedbackDetails
import strawberry
logger = logging.getLogger()
from feedback.models import UserFeedback as UserFeedbackModel, FBCategory as FBCategoryModel, FBQuestions as FBQuestionsModel, FBQuestionOptions as FBQuestionOptionsModel, RelUserQuestionOptions as RelUserQuestionOptionsModel
from feedback.resolvers import list_fb_category, list_questions_by_category, save_user_feedback


@strawberry.type
class FBQuestionOptions:
    id: int
    name: str
    instance = strawberry.Private[FBQuestionOptionsModel]

    @classmethod
    def from_instance(cls, instance: FBQuestionOptionsModel):
        return cls(
            id=instance.id,
            name=instance.name
        )

@strawberry.type
class UserFeedback:
    id: int
    txn_id: str
    category_id: int
    rating: Optional[str]
    remarks: Optional[str]
    instance = strawberry.Private[UserFeedbackModel]

    @classmethod
    def from_instance(cls, instance: UserFeedbackModel):
        return cls(
            id=instance.id,
            txn_id=instance.txn_id,
            category_id=instance.category_id,
            rating=instance.rating,
            remarks=instance.remarks
        )






@strawberry.type
class FBQuestions:
    id: int
    name: str
    code: str
    type: str
    min:float
    max:float
    options: List[FBQuestionOptions]
    instance = strawberry.Private[FBQuestionsModel]

    @classmethod
    def from_instance(cls, instance: FBQuestionsModel):
        return cls(
            id=instance.id,
            name=instance.name,
            code = instance.code,
            type=instance.type.name,
            min=instance.min_rating,
            max=instance.max_rating,
            options=instance.options.filter(FBQuestionOptionsModel.is_active==True)
        )


@strawberry.type
class FBCategory:
    id: int
    name: str
    code: str
    type: str
    icon: Optional[str]
    questions :List[FBQuestions]
    instance = strawberry.Private[FBCategoryModel]

    @classmethod
    def from_instance(cls, instance: FBCategoryModel):
        return cls(
            id=instance.id,
            name=instance.name,
            code = instance.code,
            icon=instance.icon,
            type=instance.type.name,
            questions=[FBQuestions.from_instance(obj) for obj in instance.questions]
        )




@strawberry.type
class Query:

    @strawberry.field
    def list_fb_category(self, info, code: Optional[str] = None) -> List[FBCategory]:
        db = info.context["db"]
        data = list_fb_category(db, code=code)
        return [FBCategory.from_instance(cate) for cate in data]
    
    @strawberry.field
    def list_questions_by_category(self, info, category_id: int, rating: Optional[int] = None) -> List[FBQuestions]:
        db = info.context["db"]
        data = list_questions_by_category(db=db, rating=rating, category_id=category_id)
        return [FBQuestions.from_instance(cate) for cate in data]


@strawberry.type
class Mutation:

    # @strawberry.mutation
    # def save_feedback(self, info, feedback_details: List[FeedbackDetails], phone_number: Optional[str] = None) -> MutationResponse[None]:
    #     try:
    #         db = info.context["db"]
    #         user_id = None
    #         if info.context["auth_user"] is not None:
    #             user_id = info.context["auth_user"].id
    #         data = save_feedback(db, feedback_details, user_id, phone_number)
    #         return MutationResponse.from_status_flag(True, "Feedback submitted successfully", None)
    #     except MutationError as ex:
    #         return MutationResponse.from_status_flag(False, ex.message, None)

    @strawberry.mutation
    def save_feedback(self, info, feedback_details: List[UserFeedbackDetails], phone_number: Optional[str] = None) -> MutationResponse[UserFeedback]:
        try:
            db = info.context["db"]
            user_id = None
            device_id=info.context["device_id"]
            # if info.context["auth_user"] is not None:
            #     user_id = info.context["auth_user"].id
            obj = save_user_feedback(db=db, feedback_details=feedback_details, user_id=user_id, phone_number=phone_number,device_id=device_id)
            return MutationResponse.from_status_flag(True, "Feedback submitted successfully", UserFeedback.from_instance(obj) if obj is not None else None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)