from database.db_conf import SessionLocal
from sqlalchemy.orm import Session
import logging
# from ai4bharat.transliteration import XlitEngine

from user.models import StatusEnum
logger = logging.getLogger()
from language.models import Language as LanguageModel
# import python_lang as lang
import python_lang as lang

def list_languages(db: Session):
    return db.query(LanguageModel).filter(LanguageModel.status == StatusEnum.ACTIVE).order_by(LanguageModel.priority).all()

def load_languages():
    db=None
    try:
        db=SessionLocal()
        print("Language add started")
        list=list_languages(db)
        print(list)
        for x in list:
            lang.add("lang/"+x.code+".xml",x.code)
        print("Language add ended")
    finally:
        if db is not None: db.close()
    

