"""index added in user assses ans

Revision ID: 6693c5718a07
Revises: f3cff4af0a96
Create Date: 2024-03-07 09:15:03.019285

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6693c5718a07'
down_revision = 'f3cff4af0a96'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_assessment_detail', sa.Column('index', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_assessment_detail', 'index')
    # ### end Alembic commands ###
