"""column added in bed request

Revision ID: 4c80b4e93587
Revises: 8d53b95e9fb8
Create Date: 2024-01-02 10:59:28.282124

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '4c80b4e93587'
down_revision = '8d53b95e9fb8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bed_request', sa.Column('estimated_by', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('bed_request', 'estimated_by')
    # ### end Alembic commands ###
