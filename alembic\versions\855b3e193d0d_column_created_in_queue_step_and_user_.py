"""Column created in queue step and user_queue table

Revision ID: 855b3e193d0d
Revises: d0ca18e03898
Create Date: 2024-03-04 16:51:18.835389

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '855b3e193d0d'
down_revision = 'd0ca18e03898'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('queue_step', sa.Column('checkin_name', sa.String(), nullable=True))
    op.add_column('queue_step', sa.Column('checkout_name', sa.String(), nullable=True))
    op.add_column('user_queue', sa.Column('tag_id', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_queue', 'tag_id')
    op.drop_column('queue_step', 'checkout_name')
    op.drop_column('queue_step', 'checkin_name')
    # ### end Alembic commands ###
