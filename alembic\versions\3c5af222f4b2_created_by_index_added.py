"""created by index added

Revision ID: 3c5af222f4b2
Revises: 8f9deb0d49f5
Create Date: 2024-03-25 12:30:15.241000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3c5af222f4b2'
down_revision = '8f9deb0d49f5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('idx_user_token_created_at', 'user_token', ['created_at'], unique=False, postgresql_using='btree')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_user_token_created_at', table_name='user_token', postgresql_using='btree')
    # ### end Alembic commands ###
