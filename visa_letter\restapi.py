from datetime import date
from database.db_conf import SessionLocal
from fastapi import APIRouter, Depends, Request, Response
from fastapi.responses import FileResponse, HTMLResponse
from pathlib import Path
import logging
import os
from graphql_types import MutationResponse
from visa_letter.models import AttendantTypeEnum
from visa_letter.resolvers import add_scanned_details, get_user_visa_info
logger = logging.getLogger()

router = APIRouter(
    prefix="/visa-letter",
    tags=["visa"],
    responses={404: {"description": "Not found"}},
)

router2 = APIRouter(
    prefix="/doctor_images",
    tags=["doctorimages"],
    responses={404: {"description": "Not found"}},
)
async def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
        
@router.get("/download_pdf/{reference_id}")
def download_pdf(reference_id: str,db=Depends(get_db)):
    try:
        your_pdf_path = f"data/visa_letter/pdfs/{reference_id}"
        reference_no = reference_id.split(".")[0]
        file = get_user_visa_info(db, reference_no)
        filename=file.ref_no
        headers = {
            "Content-Disposition": f"attachment; filename={filename}.pdf",
            "Cache-Control": "no-cache, no-store, must-revalidate",
            "Pragma": "no-cache",
            "Expires": "0",
        }
        return FileResponse(your_pdf_path, headers=headers)
    except Exception as e:
        logger.exception(e)
    
@router.get("/download_passport/{file_name}")
def download_passport(file_name: str, db=Depends(get_db)):
    try:
        image_path = f"data/visa_letter/passport_image/{file_name}"
        reference_no = file_name.split("_")[0]
        no= file_name.split("_")[1]
        file = get_user_visa_info(db, reference_no)
        filename= f"{file.passport_no}_{no}"
        headers = {
            "Content-Disposition": f"attachment; filename={filename}",
            "Cache-Control": "no-cache, no-store, must-revalidate",
            "Pragma": "no-cache",
            "Expires": "0",
        }
        return FileResponse(image_path, headers=headers)
    except Exception as e:
        logger.exception(e)


@router2.get("/{filename}")
async def get_image(filename: str):
    file_path = os.path.join("data/doctor_images", filename)
    
    if os.path.exists(file_path):
        return FileResponse(file_path)
    return {"error": "File not found"}
        
        

@router.get("/validate/{id}", response_class=HTMLResponse)
async def read_item(request: Request, id: str, db=Depends(get_db)):
    
    data = get_user_visa_info(db,id)
    logger.info(data)
    contack_details=''
    patientDetails=''
    patients_header=''
    ref_no=''
    header=''
    user_status = ''
    try:
        status="""<span class="MuiTypography-root MuiTypography-h4 css-wov9ru-MuiTypography-root">Invalid invitation letter</span><br/>"""
        icon="""<div class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-6 MuiGrid-grid-md-6 css-z7x17r-MuiGrid-root"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-18fvlv4-MuiSvgIcon-root" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="CancelIcon"><path d="M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2zm5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"></path></svg></div>"""
        if data is not None and data.status.name !='PENDING':
            # status="VALID"
            ref_no="""<h6 class="MuiTypography-root MuiTypography-h6 css-qnj808-MuiTypography-root">
        Ref No: """+data.ref_no+"""</h6>"""
            logger.info(data.status)
            if data.status.name=='GENERATED':
              if (data.expiry_date is not None and date.today() > data.expiry_date):
                status="""<span class="MuiTypography-root MuiTypography-h4 css-wov9ru-MuiTypography-root">Expired invitation letter</span><br/>"""
                user_status = "EXPIRED"
              else:
                ref_no="""<h6 class="MuiTypography-root MuiTypography-h6 css-1veod4s-MuiTypography-root">
        Ref No: """+data.ref_no+"""</h6>"""
                icon="""<div class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-6 MuiGrid-grid-md-6 css-z7x17r-MuiGrid-root"><svg class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-1j3u2d7-MuiSvgIcon-root" focusable="false" aria-hidden="true" viewBox="0 0 24 24" data-testid="CheckCircleIcon"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"></path></svg></div>"""
                status="""<span class="MuiTypography-root MuiTypography-h4 css-1pdg0ch-MuiTypography-root">Valid</span>"""
                user_status = "VALID"
            elif data.status.name=='CANCELLED':
                status="""<span class="MuiTypography-root MuiTypography-h4 css-wov9ru-MuiTypography-root">Cancelled Letter</span><br/>"""
                user_status = "CANCELLED"
            else:
                status="""<span class="MuiTypography-root MuiTypography-h4 css-wov9ru-MuiTypography-root">Invalid invitation letter</span><br/>"""
            contack_details="""Contact Details:
                <p class="MuiTypography-root MuiTypography-body1 css-1krp0zf-MuiTypography-root"> """+str(data.hospital_signatory)+"""</p>
                <p class="MuiTypography-root MuiTypography-body1 css-1krp0zf-MuiTypography-root"> """+str(data.contact_details)+"""</p>
            """
            header="""<thead class="MuiTableHead-root css-kd84sh-MuiTableHead-root">
        <tr class="MuiTableRow-root MuiTableRow-head css-knwo0a-MuiTableRow-root">
        <th class="MuiTableCell-root MuiTableCell-head MuiTableCell-alignCenter MuiTableCell-sizeMedium css-oaurtj-MuiTableCell-root" scope="col">
        <p class="MuiTypography-root MuiTypography-body2 css-12pq2ck-MuiTypography-root">Name</p></th>
        <th class="MuiTableCell-root MuiTableCell-head MuiTableCell-alignCenter MuiTableCell-sizeMedium css-oaurtj-MuiTableCell-root" scope="col"><p class="MuiTypography-root MuiTypography-body2 css-12pq2ck-MuiTypography-root">Passport Number</p></th></tr></thead>"""
            patients_header="""<h6 class="MuiTypography-root MuiTypography-h6 css-npzne0-MuiTypography-root">Patient &amp; Attendants</h6></div>"""

            patientDetails="""<tr class="MuiTableRow-root css-1p5jqqf-MuiTableRow-root">
            <td class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeMedium css-1f74bsv-MuiTableCell-root">"""+str(data.patient_name)+""" (Patient)</td>
            <td class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeMedium css-1f74bsv-MuiTableCell-root">"""+str(data.passport_no)+"""</td>
            </tr>"""
            
            for attendant1 in data.attendant:
                logger.info(attendant1.attendant_passport)
                atndt_type = 'Attendant' if attendant1.type == AttendantTypeEnum.ATTENDANT else 'Donor'
                patientDetails=patientDetails+"""
                <tr class="MuiTableRow-root css-1p5jqqf-MuiTableRow-root">
                    <td class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeMedium css-1f74bsv-MuiTableCell-root">"""+str(attendant1.attendant)+""" ("""+atndt_type+""")</td>
                    <td class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeMedium css-1f74bsv-MuiTableCell-root">"""+str(attendant1.attendant_passport)+"""</td>
                </tr>
                """
        else:
          user_status = "INVALID"
        add_scanned_details(db,id,user_status)
    except Exception as ex:
        logger.exception(ex)
    logger.info(patientDetails)
    return """
    <html lang="en"><head>
    <meta charset="utf-8">
    <link rel="icon" href="https://aighospitals.com/assets/aig/image/Asian-Institute-of-Gatroenterology_favicon.png" type="image/x-icon">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="theme-color" content="#000000">
    <link rel="apple-touch-icon" href="/logo192.png">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&amp;display=swap">
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="/manifest.json">

    <!--
      Notice the use of  in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>Achala Health</title>
  <script defer="" src="/static/js/bundle.js"></script><style>#root {
  height: 100vh;
}

.active-row {
  background-color: lightgreen; /* Define your styles for the 'Active' status */
}

.inactive-row {
  background-color: lightcoral; /* Define your styles for the 'Inactive' status */
}
.high-row{
  background-color: lightgoldenrodyellow; 
}
.urgent-row{
  background-color: lightcoral; 
}
/* Works for Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Works for Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}
.right-align{
  text-align: end;
}
/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQWE7QUFDZjs7QUFFQTtFQUNFLDRCQUE0QixFQUFFLCtDQUErQztBQUMvRTs7QUFFQTtFQUNFLDRCQUE0QixFQUFFLGlEQUFpRDtBQUNqRjtBQUNBO0VBQ0Usc0NBQXNDO0FBQ3hDO0FBQ0E7RUFDRSw0QkFBNEI7QUFDOUI7QUFDQSwwQ0FBMEM7QUFDMUM7O0VBRUUsd0JBQXdCO0VBQ3hCLFNBQVM7QUFDWDs7QUFFQSxzQkFBc0I7QUFDdEI7RUFDRSwwQkFBMEI7QUFDNUI7QUFDQTtFQUNFLGVBQWU7QUFDakIiLCJzb3VyY2VzQ29udGVudCI6WyIjcm9vdCB7XG4gIGhlaWdodDogMTAwdmg7XG59XG5cbi5hY3RpdmUtcm93IHtcbiAgYmFja2dyb3VuZC1jb2xvcjogbGlnaHRncmVlbjsgLyogRGVmaW5lIHlvdXIgc3R5bGVzIGZvciB0aGUgJ0FjdGl2ZScgc3RhdHVzICovXG59XG5cbi5pbmFjdGl2ZS1yb3cge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiBsaWdodGNvcmFsOyAvKiBEZWZpbmUgeW91ciBzdHlsZXMgZm9yIHRoZSAnSW5hY3RpdmUnIHN0YXR1cyAqL1xufVxuLmhpZ2gtcm93e1xuICBiYWNrZ3JvdW5kLWNvbG9yOiBsaWdodGdvbGRlbnJvZHllbGxvdzsgXG59XG4udXJnZW50LXJvd3tcbiAgYmFja2dyb3VuZC1jb2xvcjogbGlnaHRjb3JhbDsgXG59XG4vKiBXb3JrcyBmb3IgQ2hyb21lLCBTYWZhcmksIEVkZ2UsIE9wZXJhICovXG5pbnB1dDo6LXdlYmtpdC1vdXRlci1zcGluLWJ1dHRvbixcbmlucHV0Ojotd2Via2l0LWlubmVyLXNwaW4tYnV0dG9uIHtcbiAgLXdlYmtpdC1hcHBlYXJhbmNlOiBub25lO1xuICBtYXJnaW46IDA7XG59XG5cbi8qIFdvcmtzIGZvciBGaXJlZm94ICovXG5pbnB1dFt0eXBlPVwibnVtYmVyXCJdIHtcbiAgLW1vei1hcHBlYXJhbmNlOiB0ZXh0ZmllbGQ7XG59XG4ucmlnaHQtYWxpZ257XG4gIHRleHQtYWxpZ246IGVuZDtcbn0iXSwic291cmNlUm9vdCI6IiJ9 */</style><style id="_goober"> .go1475592160{height:0;}.go1671063245{height:auto;}.go1888806478{display:flex;flex-wrap:wrap;flex-grow:1;}@media (min-width:600px){.go1888806478{flex-grow:initial;min-width:288px;}}.go167266335{background-color:#313131;font-size:0.875rem;line-height:1.43;letter-spacing:0.01071em;color:#fff;align-items:center;padding:6px 16px;border-radius:4px;box-shadow:0px 3px 5px -1px rgba(0,0,0,0.2),0px 6px 10px 0px rgba(0,0,0,0.14),0px 1px 18px 0px rgba(0,0,0,0.12);}.go3162094071{padding-left:20px;}.go3844575157{background-color:#313131;}.go1725278324{background-color:#43a047;}.go3651055292{background-color:#d32f2f;}.go4215275574{background-color:#ff9800;}.go1930647212{background-color:#2196f3;}.go946087465{display:flex;align-items:center;padding:8px 0;}.go703367398{display:flex;align-items:center;margin-left:auto;padding-left:16px;margin-right:-8px;}.go3963613292{width:100%;position:relative;transform:translateX(0);top:0;right:0;bottom:0;left:0;min-width:288px;}.go1141946668{box-sizing:border-box;display:flex;max-height:100%;position:fixed;z-index:1400;height:auto;width:auto;transition:top 300ms ease 0ms,right 300ms ease 0ms,bottom 300ms ease 0ms,left 300ms ease 0ms,max-width 300ms ease 0ms;pointer-events:none;max-width:calc(100% - 40px);}.go1141946668 .notistack-CollapseWrapper{padding:6px 0px;transition:padding 300ms ease 0ms;}@media (max-width:599.95px){.go1141946668{width:100%;max-width:calc(100% - 32px);}}.go3868796639 .notistack-CollapseWrapper{padding:2px 0px;}.go3118922589{top:14px;flex-direction:column;}.go1453831412{bottom:14px;flex-direction:column-reverse;}.go4027089540{left:20px;}@media (min-width:600px){.go4027089540{align-items:flex-start;}}@media (max-width:599.95px){.go4027089540{left:16px;}}.go2989568495{right:20px;}@media (min-width:600px){.go2989568495{align-items:flex-end;}}@media (max-width:599.95px){.go2989568495{right:16px;}}.go4034260886{left:50%;transform:translateX(-50%);}@media (min-width:600px){.go4034260886{align-items:center;}}</style><style>.white-background{
  background-color: white;
}
</style><style data-emotion="css-global" data-s="">html{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;box-sizing:border-box;-webkit-text-size-adjust:100%;}</style><style data-emotion="css-global" data-s="">*,*::before,*::after{box-sizing:inherit;}</style><style data-emotion="css-global" data-s="">strong,b{font-weight:700;}</style><style data-emotion="css-global" data-s="">body{margin:0;color:#282828;font-family:Roboto;font-weight:400;font-size:0.825rem;line-height:1.5;letter-spacing:0.00938em;background-color:#FFFFFF;}</style><style data-emotion="css-global" data-s="">@media print{body{background-color:#FFFFFF;}}</style><style data-emotion="css-global" data-s="">body::backdrop{background-color:#FFFFFF;}</style><style data-emotion="css" data-s="">.css-v0s27q{height:100vh;overflow:auto;-webkit-background-size:cover;background-size:cover;background-color:#FFFFFF;}</style><style data-emotion="css" data-s="">.css-1orlxgr-MuiStack-root{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;width:100%;height:100vh;}</style><style data-emotion="css" data-s="">.css-18vjiw7-MuiCircularProgress-root{display:inline-block;color:#37B673;-webkit-animation:animation-61bdi0 1.4s linear infinite;animation:animation-61bdi0 1.4s linear infinite;}</style><style data-emotion="css" data-s="">@-webkit-keyframes animation-61bdi0{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-ms-transform:rotate(0deg);transform:rotate(0deg);}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-ms-transform:rotate(360deg);transform:rotate(360deg);}}</style><style data-emotion="css" data-s="">@keyframes animation-61bdi0{0%{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-ms-transform:rotate(0deg);transform:rotate(0deg);}100%{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-ms-transform:rotate(360deg);transform:rotate(360deg);}}</style><style data-emotion="css" data-s="">.css-1idz92c-MuiCircularProgress-svg{display:block;}</style><style data-emotion="css" data-s="">.css-176wh8e-MuiCircularProgress-circle{stroke:currentColor;stroke-dasharray:80px,200px;stroke-dashoffset:0;-webkit-animation:animation-1p2h4ri 1.4s ease-in-out infinite;animation:animation-1p2h4ri 1.4s ease-in-out infinite;}</style><style data-emotion="css" data-s="">@-webkit-keyframes animation-1p2h4ri{0%{stroke-dasharray:1px,200px;stroke-dashoffset:0;}50%{stroke-dasharray:100px,200px;stroke-dashoffset:-15px;}100%{stroke-dasharray:100px,200px;stroke-dashoffset:-125px;}}</style><style data-emotion="css" data-s="">@keyframes animation-1p2h4ri{0%{stroke-dasharray:1px,200px;stroke-dashoffset:0;}50%{stroke-dasharray:100px,200px;stroke-dashoffset:-15px;}100%{stroke-dasharray:100px,200px;stroke-dashoffset:-125px;}}</style><style data-emotion="css" data-s="">.css-1gyrgx2{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;height:inherit;}</style><style data-emotion="css" data-s="">.css-9qsf6a-MuiAppBar-root{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;width:100%;box-sizing:border-box;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;position:fixed;z-index:1100;top:0;left:auto;right:0;background-color:#37B673;color:rgba(0, 0, 0, 0.87);background:linear-gradient(90deg, #FFF 0%, #37B673 51.82%);box-shadow:0px 4px 8px 0px rgba(0, 0, 0, 0.15);z-index:1201;background:#fff;}</style><style data-emotion="css" data-s="">@media print{.css-9qsf6a-MuiAppBar-root{position:absolute;}}</style><style data-emotion="css" data-s="">.css-1sux5qj-MuiPaper-root-MuiAppBar-root{background-color:#fff;color:#282828;-webkit-transition:box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;transition:box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;box-shadow:0px 2px 4px -1px rgba(0,0,0,0.2),0px 4px 5px 0px rgba(0,0,0,0.14),0px 1px 10px 0px rgba(0,0,0,0.12);display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;width:100%;box-sizing:border-box;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;position:fixed;z-index:1100;top:0;left:auto;right:0;background-color:#37B673;color:rgba(0, 0, 0, 0.87);background:linear-gradient(90deg, #FFF 0%, #37B673 51.82%);box-shadow:0px 4px 8px 0px rgba(0, 0, 0, 0.15);z-index:1201;background:#fff;}</style><style data-emotion="css" data-s="">@media print{.css-1sux5qj-MuiPaper-root-MuiAppBar-root{position:absolute;}}</style><style data-emotion="css" data-s="">.css-dkrrrb-MuiToolbar-root{position:relative;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;padding-left:16px;padding-right:16px;min-height:56px;padding-top:8px;padding-bottom:8px;}</style><style data-emotion="css" data-s="">@media (min-width:400px){.css-dkrrrb-MuiToolbar-root{padding-left:24px;padding-right:24px;}}</style><style data-emotion="css" data-s="">@media (min-width:0px){@media (orientation: landscape){.css-dkrrrb-MuiToolbar-root{min-height:48px;}}}</style><style data-emotion="css" data-s="">@media (min-width:400px){.css-dkrrrb-MuiToolbar-root{min-height:64px;}}</style><style data-emotion="css" data-s="">.css-dkrrrb-MuiToolbar-root.MuiToolbar-root{padding-left:40px;padding-right:40px;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-pack:end;-ms-flex-pack:end;-webkit-justify-content:flex-end;justify-content:flex-end;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;}</style><style data-emotion="css" data-s="">.css-1age63q{width:100%;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;}</style><style data-emotion="css" data-s="">.css-14ga7e5{padding-top:16px;padding-bottom:16px;padding-left:32px;padding-right:32px;overflow:auto;width:inherit;margin-top:40px;}</style><style data-emotion="css" data-s="">.css-153bhgj-MuiContainer-root{width:100%;margin-left:auto;box-sizing:border-box;margin-right:auto;display:block;}</style><style data-emotion="css" data-s="">@media (min-width:900px){.css-153bhgj-MuiContainer-root{max-width:900px;}}</style><style data-emotion="css" data-s="">.css-15snhn0-MuiGrid-root{box-sizing:border-box;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;}</style><style data-emotion="css" data-s="">.css-9m6lda-MuiPaper-root{background-color:#fff;color:#282828;-webkit-transition:box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;transition:box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;border-radius:16px;box-shadow:0px 2px 1px -1px rgba(0,0,0,0.2),0px 1px 1px 0px rgba(0,0,0,0.14),0px 1px 3px 0px rgba(0,0,0,0.12);padding:32px;margin-top:16px;width:100%;}</style><style data-emotion="css" data-s="">.css-z7x17r-MuiGrid-root{box-sizing:border-box;margin:0;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;}</style><style data-emotion="css" data-s="">.css-18fvlv4-MuiSvgIcon-root{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;width:1em;height:1em;display:inline-block;fill:currentColor;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;-webkit-transition:fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;transition:fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;font-size:1.5rem;width:3em;height:3em;color:#ec2020;}</style><style data-emotion="css" data-s="">.css-wov9ru-MuiTypography-root{margin:0;font-size:32px;line-height:35.2px;font-family:Roboto;font-weight:400;letter-spacing:0.00735em;color:#282828;color:#f00;text-align:center;-webkit-box-flex:1;-webkit-flex-grow:1;-ms-flex-positive:1;flex-grow:1;font-weight:500;}</style><style data-emotion="css" data-s="">.css-jnjxy{display:grid;-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;width:100%;}</style><style data-emotion="css" data-s="">.css-0{}</style><style data-emotion="css" data-s="">.css-rmcela{margin-top:15px;padding:10px;}</style><style data-emotion="css" data-s="">.css-11lq3yg-MuiGrid-root{box-sizing:border-box;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-flex-wrap:wrap;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;width:100%;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;}</style><style data-emotion="css" data-s="">.css-13i4rnv-MuiGrid-root{box-sizing:border-box;margin:0;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;}</style><style data-emotion="css" data-s="">.css-1krp0zf-MuiTypography-root{margin:0;font-family:Roboto;font-weight:400;font-size:0.825rem;line-height:1.5;letter-spacing:0.00938em;color:#282828;}</style><style data-emotion="css" data-s="">.css-1j3u2d7-MuiSvgIcon-root{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;width:1em;height:1em;display:inline-block;fill:currentColor;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;-webkit-transition:fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;transition:fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;font-size:1.5rem;width:3em;height:3em;color:green;}</style><style data-emotion="css" data-s="">.css-1pdg0ch-MuiTypography-root{margin:0;font-size:32px;line-height:35.2px;font-family:Roboto;font-weight:400;letter-spacing:0.00735em;color:#282828;color:#37B673;text-align:center;-webkit-box-flex:1;-webkit-flex-grow:1;-ms-flex-positive:1;flex-grow:1;font-weight:500;}</style><style data-emotion="css" data-s="">.css-1tyndxa{-webkit-box-pack:center;-ms-flex-pack:center;-webkit-justify-content:center;justify-content:center;}</style><style data-emotion="css" data-s="">.css-1veod4s-MuiTypography-root{margin:0;font-size:20px;line-height:22px;font-family:Roboto;font-weight:400;letter-spacing:0.0075em;color:#282828;color:#37B673;text-align:center;}</style><style data-emotion="css" data-s="">.css-qnj808-MuiTypography-root{margin:0;font-family:Roboto;font-weight:400;font-size:0.825rem;line-height:1.5;letter-spacing:0.00938em;color:#282828;color:default;text-align:center;}</style><style data-emotion="css" data-s="">.css-pl26la-MuiGrid-root{box-sizing:border-box;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-flex-wrap:wrap;-webkit-flex-wrap:wrap;-ms-flex-wrap:wrap;flex-wrap:wrap;width:100%;-webkit-flex-direction:row;-ms-flex-direction:row;flex-direction:row;padding:10px;}</style><style data-emotion="css" data-s="">.css-npzne0-MuiTypography-root{margin:0;font-size:20px;line-height:22px;font-family:Roboto;font-weight:400;letter-spacing:0.0075em;color:#282828;}</style><style data-emotion="css" data-s="">.css-133vpiv-MuiTableContainer-root{width:100%;overflow-x:auto;border-radius:0;}</style><style data-emotion="css" data-s="">.css-hbi07o-MuiPaper-root-MuiTableContainer-root{background-color:#fff;color:#282828;-webkit-transition:box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;transition:box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;border-radius:16px;box-shadow:0px 2px 1px -1px rgba(0,0,0,0.2),0px 1px 1px 0px rgba(0,0,0,0.14),0px 1px 3px 0px rgba(0,0,0,0.12);width:100%;overflow-x:auto;border-radius:0;}</style><style data-emotion="css" data-s="">.css-1q1waex-MuiTable-root{display:table;width:100%;border-collapse:collapse;border-spacing:0;}</style><style data-emotion="css" data-s="">.css-1q1waex-MuiTable-root caption{font-family:Roboto;font-weight:400;font-size:0.825rem;line-height:1.43;letter-spacing:0.01071em;color:#282828;padding:16px;text-align:left;caption-side:bottom;}</style><style data-emotion="css" data-s="">.css-kd84sh-MuiTableHead-root{display:table-header-group;background-color:#F2F2F2;}</style><style data-emotion="css" data-s="">.css-knwo0a-MuiTableRow-root{color:inherit;display:table-row;vertical-align:middle;outline:0;border:1px solid;border-color:#BDBDBD;}</style><style data-emotion="css" data-s="">.css-knwo0a-MuiTableRow-root.MuiTableRow-hover:hover{background-color:rgba(0, 0, 0, 0.04);}</style><style data-emotion="css" data-s="">.css-knwo0a-MuiTableRow-root.Mui-selected{background-color:rgba(55, 182, 115, 0.08);}</style><style data-emotion="css" data-s="">.css-knwo0a-MuiTableRow-root.Mui-selected:hover{background-color:rgba(55, 182, 115, 0.12);}</style><style data-emotion="css" data-s="">.css-oaurtj-MuiTableCell-root{font-family:Roboto;font-weight:500;font-size:0.825rem;line-height:1.5rem;letter-spacing:0.01071em;color:#282828;display:table-cell;vertical-align:inherit;border-bottom:1px solid rgba(224, 224, 224, 1);text-align:center;padding:16px;}</style><style data-emotion="css" data-s="">.css-12pq2ck-MuiTypography-root{margin:0;font-family:Roboto;font-weight:400;font-size:0.825rem;line-height:1.43;letter-spacing:0.01071em;color:#282828;}</style><style data-emotion="css" data-s="">.css-apqrd9-MuiTableBody-root{display:table-row-group;}</style><style data-emotion="css" data-s="">.css-1p5jqqf-MuiTableRow-root{color:inherit;display:table-row;vertical-align:middle;outline:0;border:1px solid;border-color:#BDBDBD;border:1px solid black;}</style><style data-emotion="css" data-s="">.css-1p5jqqf-MuiTableRow-root.MuiTableRow-hover:hover{background-color:rgba(0, 0, 0, 0.04);}</style><style data-emotion="css" data-s="">.css-1p5jqqf-MuiTableRow-root.Mui-selected{background-color:rgba(55, 182, 115, 0.08);}</style><style data-emotion="css" data-s="">.css-1p5jqqf-MuiTableRow-root.Mui-selected:hover{background-color:rgba(55, 182, 115, 0.12);}</style><style data-emotion="css" data-s="">.css-1f74bsv-MuiTableCell-root{font-family:Roboto;font-weight:400;font-size:0.825rem;line-height:1.43;letter-spacing:0.01071em;color:#282828;display:table-cell;vertical-align:inherit;border-bottom:1px solid rgba(224, 224, 224, 1);text-align:left;padding:16px;border:1px solid black;}</style></head>
  <body data-new-gr-c-s-check-loaded="14.1145.0" data-gr-ext-installed="">
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"><div class="MuiBox-root css-v0s27q"><div class="MuiBox-root css-1gyrgx2"><header class="MuiPaper-root MuiPaper-elevation MuiPaper-elevation4 MuiAppBar-root MuiAppBar-colorPrimary MuiAppBar-positionFixed mui-fixed css-1sux5qj-MuiPaper-root-MuiAppBar-root"><div class="MuiToolbar-root MuiToolbar-gutters MuiToolbar-regular css-dkrrrb-MuiToolbar-root"><img src="https://aighospitals.com/assets/aig/image/logo.png" alt="AIG"></div></header><div class="MuiBox-root css-1age63q"><main class="MuiBox-root css-14ga7e5"><div class="MuiContainer-root MuiContainer-maxWidthLg MuiContainer-disableGutters css-153bhgj-MuiContainer-root"><div class="MuiGrid-root MuiGrid-grid-xs-12 MuiGrid-grid-md-6 css-15snhn0-MuiGrid-root"><div class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation1 css-9m6lda-MuiPaper-root">
    """+icon+"""
    <div class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-6 MuiGrid-grid-md-6 css-z7x17r-MuiGrid-root">
    """+status+"""
    </div>
    <div class="MuiBox-root css-jnjxy"></div><div class="MuiBox-root css-1tyndxa">
    """+ref_no+"""
    <p class="MuiTypography-root MuiTypography-body1 css-qnj808-MuiTypography-root">
    AIG Hospitals, Hyderabad, India.</p></div><div class="MuiBox-root css-0">
    <div class="MuiGrid-root MuiGrid-container css-pl26la-MuiGrid-root">
    """+patients_header+"""
    <div class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation1 MuiTableContainer-root css-hbi07o-MuiPaper-root-MuiTableContainer-root">
    <table class="MuiTable-root css-1q1waex-MuiTable-root" aria-label="simple table">
    """+header+"""
    <tbody class="MuiTableBody-root css-apqrd9-MuiTableBody-root">"""+patientDetails+"""
    </tbody></table></div></div><div class="MuiBox-root css-rmcela"><div class="MuiGrid-root MuiGrid-container css-11lq3yg-MuiGrid-root"><div class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root">
    <p class="MuiTypography-root MuiTypography-body1 css-1krp0zf-MuiTypography-root">
    """+contack_details+"""
    </p>
    </div></div></div></div></div></div></main></div></div></div></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  

</body><grammarly-desktop-integration data-grammarly-shadow-root="true"></grammarly-desktop-integration></html>
    """