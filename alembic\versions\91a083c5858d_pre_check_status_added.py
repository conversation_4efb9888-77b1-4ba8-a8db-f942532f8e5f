"""pre check status added

Revision ID: 91a083c5858d
Revises: eca33d1cdc68
Create Date: 2025-07-16 19:01:14.996871

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '91a083c5858d'
down_revision = 'eca33d1cdc68'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_queue', sa.Column('phlebotomy_precheck_datetime', sa.DateTime(timezone=True), nullable=True), schema='queue')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_queue', 'phlebotomy_precheck_datetime', schema='queue')
    # ### end Alembic commands ###
