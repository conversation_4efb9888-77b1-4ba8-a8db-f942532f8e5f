"""user service table updated

Revision ID: 014ff37d23ce
Revises: 91a083c5858d
Create Date: 2025-07-28 16:14:02.166558

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '014ff37d23ce'
down_revision = '91a083c5858d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_service', sa.Column('bill', sa.String(), nullable=True), schema='queue')
    op.add_column('user_service', sa.Column('test_id', sa.String(), nullable=True), schema='queue')
    op.add_column('user_service', sa.Column('sample_no', sa.String(), nullable=True), schema='queue')
    op.add_column('user_service', sa.Column('order_details', postgresql.JSONB(astext_type=sa.Text()), nullable=True), schema='queue')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_service', 'order_details', schema='queue')
    op.drop_column('user_service', 'sample_no', schema='queue')
    op.drop_column('user_service', 'test_id', schema='queue')
    op.drop_column('user_service', 'bill', schema='queue')
    # ### end Alembic commands ###
