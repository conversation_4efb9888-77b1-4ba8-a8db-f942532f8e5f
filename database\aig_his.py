import os
from urllib.parse import quote
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import scoped_session, sessionmaker
from sqlalchemy.exc import OperationalError

AIG_HIS_DATABASE_URL = os.environ["AIG_HIS_DATABASE_URL"]+'?ApplicationIntent=ReadOnly&driver=ODBC+Driver+17+for+SQL+Server'
AIG_HIS_DB_PASSWORD = os.environ["AIG_HIS_DB_PASSWORD"]

try:
    aig_his_engine = create_engine(
        AIG_HIS_DATABASE_URL% quote(AIG_HIS_DB_PASSWORD), future=True,  pool_size=100,pool_recycle=3600
    )
    SessionLocalHis = sessionmaker(autocommit=False, autoflush=False, bind=aig_his_engine, future=True)
except OperationalError as ex:
    SessionLocalHis = None
# Base = declarative_base()
