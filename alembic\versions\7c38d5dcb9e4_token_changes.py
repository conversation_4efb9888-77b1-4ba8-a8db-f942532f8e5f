"""Token Changes

Revision ID: 7c38d5dcb9e4
Revises: f7de60652c2c
Create Date: 2025-02-14 15:34:05.044478

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7c38d5dcb9e4'
down_revision = 'f7de60652c2c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('rel_consult_doctor_patient',
    sa.<PERSON>umn('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.BigInteger(), nullable=True),
    sa.Column('doctor_id', sa.String(), nullable=True),
    sa.Column('doctor_name', sa.String(), nullable=True),
    sa.Column('speciality', sa.String(), nullable=True),
    sa.Column('speciality_code', sa.String(), nullable=True),
    sa.Column('consultation_amt', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('token_patient',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('token', sa.String(), nullable=True),
    sa.Column('user_id', sa.BigInteger(), nullable=True),
    sa.Column('parent_token_id', sa.BigInteger(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['parent_token_id'], ['token_patient.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('token_patient')
    op.drop_table('rel_consult_doctor_patient')
    # ### end Alembic commands ###
