"""latest called token added

Revision ID: 535e0fd0df6d
Revises: 226a4942fdd9
Create Date: 2024-08-19 18:33:18.113562

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '535e0fd0df6d'
down_revision = '226a4942fdd9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('queue', sa.Column('latest_token_id', sa.Integer(), nullable=True), schema='queue')
    op.create_foreign_key('queue_latest_token_id_fk', 'queue', 'user_token', ['latest_token_id'], ['id'], source_schema='queue', referent_schema='queue')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('queue_latest_token_id_fk', 'queue', schema='queue', type_='foreignkey')
    op.drop_column('queue', 'latest_token_id', schema='queue')
    # ### end Alembic commands ###
