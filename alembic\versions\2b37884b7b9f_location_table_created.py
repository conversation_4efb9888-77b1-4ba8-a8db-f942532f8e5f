"""location table created

Revision ID: 2b37884b7b9f
Revises: f88e77a150bb
Create Date: 2024-03-01 09:13:50.301966

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '2b37884b7b9f'
down_revision = 'f88e77a150bb'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('location',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('code', sa.String(), nullable=True),
    sa.Column('priority', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('rel_queue_step_location',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('location_id', sa.Integer(), nullable=True),
    sa.Column('queue_step_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['location_id'], ['location.id'], name='rel_queue_step_location_location_id_fk'),
    sa.ForeignKeyConstraint(['queue_step_id'], ['queue_step.id'], name='rel_queue_step_location_queue_step_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('user_queue', sa.Column('location_id', sa.Integer(), nullable=True))
    op.create_foreign_key('user_queue_location_id_fk', 'user_queue', 'location', ['location_id'], ['id'])
    op.add_column('user_queue_step', sa.Column('location_id', sa.Integer(), nullable=True))
    op.drop_constraint('user_queue_step_queue_step_location_id_fk', 'user_queue_step', type_='foreignkey')
    op.create_foreign_key('user_queue_step_location_id_fk', 'user_queue_step', 'location', ['location_id'], ['id'])
    op.drop_column('user_queue_step', 'queue_step_location_id')
    op.drop_table('queue_step_location')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_queue_step', sa.Column('queue_step_location_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.drop_constraint('user_queue_step_location_id_fk', 'user_queue_step', type_='foreignkey')
    op.create_foreign_key('user_queue_step_queue_step_location_id_fk', 'user_queue_step', 'queue_step_location', ['queue_step_location_id'], ['id'])
    op.drop_column('user_queue_step', 'location_id')
    op.drop_constraint('user_queue_location_id_fk', 'user_queue', type_='foreignkey')
    op.drop_column('user_queue', 'location_id')
    op.create_table('queue_step_location',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('code', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('priority', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('queue_step_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['queue_step_id'], ['queue_step.id'], name='queue_step_location_queue_step_id_fk'),
    sa.PrimaryKeyConstraint('id', name='queue_step_location_pkey')
    )
    op.drop_table('rel_queue_step_location')
    op.drop_table('location')
    # ### end Alembic commands ###
