"""queue counter column added , created user login

Revision ID: e86e241db8d2
Revises: a3d455987a96
Create Date: 2024-02-08 05:14:07.291536

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e86e241db8d2'
down_revision = 'a3d455987a96'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TYPE devicetypeenum ADD VALUE 'APP'")
    op.execute("ALTER TYPE subdevicetypeenum ADD VALUE 'QUEUE_COUNTER'")

    op.create_table('staff_user_login_details',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('status', sa.Enum('SUCCESS', 'FAILURE', name='userloginstatusenum'), nullable=True),
    sa.Column('type', sa.Enum('LOGIN', 'LOGOUT', name='userlogintypeenum'), nullable=True),
    sa.Column('staff_user_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['staff_user_id'], ['staff_user.id'], name='user_login_details_staff_user_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('device', sa.Column('queue_counter', sa.Integer(), nullable=True))
    op.create_foreign_key('device_queue_counter_fk', 'device', 'queue_counter', ['queue_counter'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('device_queue_counter_fk', 'device', type_='foreignkey')
    op.drop_column('device', 'queue_counter')
    op.drop_table('staff_user_login_details')
    # ### end Alembic commands ###
