"""adding status to vitalUser

Revision ID: a67d55df9fcd
Revises: 7f9efe1ed68f
Create Date: 2024-11-04 13:51:15.510794

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a67d55df9fcd'
down_revision = '7f9efe1ed68f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("""
    CREATE TYPE vitaluserstatusenum AS ENUM ('IN_PROGRESS', 'COMPLETED', 'FAILED');
    """)
    op.add_column('vital_user', sa.Column('status', sa.Enum('IN_PROGRESS', 'COMPLETED', 'FAILED', name='vitaluserstatusenum'), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('vital_user', 'status')
    op.execute("DROP TYPE IF EXISTS vitaluserstatusenum")
    # ### end Alembic commands ###
