"""doctor name column added

Revision ID: 917a2e969724
Revises: aa3a14df2b89
Create Date: 2024-09-03 13:51:23.037679

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '917a2e969724'
down_revision = 'aa3a14df2b89'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_queue', sa.Column('doctor_name', sa.String(), nullable=True), schema='queue')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_queue', 'doctor_name', schema='queue')
    # ### end Alembic commands ###
