"""creating allergy master table

Revision ID: 0d415e93581a
Revises: 34d10a24dced
Create Date: 2024-06-13 07:33:28.133383

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0d415e93581a'
down_revision = '34d10a24dced'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('allergy_master',
    sa.<PERSON>umn('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('domain_code', sa.String(), nullable=False),
    sa.Column('master_code', sa.String(), nullable=False),
    sa.Column('short_desc', sa.String(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('allergy_master')
    # ### end Alembic commands ###
