"""columns added in service table

Revision ID: 5668073c5bcf
Revises: 4d315d143d7f
Create Date: 2024-04-23 07:03:09.957220

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '5668073c5bcf'
down_revision = '4d315d143d7f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('service', sa.Column('prerequisites_conditions', sa.ARRAY(sa.String()), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('service', 'prerequisites_conditions')
    # ### end Alembic commands ###
