# Local database connection
# DATABASE_URL=postgresql+psycopg2://ai96evapp:%s@52.66.144.247:4763/aig_opd_dev
# DB_USER=ai96evapp
# DB_PASSWORD='K1M59ev@pp$23'
# DB_NAME=aig_opd_dev
# DATABASE_URL=postgresql+psycopg2://ai9_ua7_app:%s@3.111.25.228:4763/aig_opd_uat2
# DB_USER=ai9_ua7_app
# DB_PASSWORD='A!GU@7a996B@32$'
# DB_NAME=aig_opd_uat2
# DATABASE_URL=postgresql+psycopg2://ai96evapp:%s@13.200.223.240:4763/aig_opd_dev
# DB_USER=ai96evapp
# DB_PASSWORD='K1M59ev@pp$23'
# DB_NAME=aig_opd_dev
# FEEDBACK_APP_URL=mysql+pymysql://kfadmin:%s@10.10.100.132:15506/FEEDBACK_APP_TEST
# DATABASE_URL=postgresql+psycopg2://ai9_ua7_app:%s@10.10.102.142:4763/aig_opd_uat3
# DB_USER=ai9_ua7_app
# DB_PASSWORD='A!GU@7a996B@32$'
# DB_NAME=aig_opd_uat3
DATABASE_URL=postgresql+psycopg2://ai96evapp:%s@52.66.144.247:4763/aig_opd_uat3
DB_USER=ai96evapp
DB_PASSWORD='K1M59ev@pp$23'
DB_NAME=aig_opd_uat3
# FEEDBACK_APP_URL=mysql+pymysql://kfadmin:%s@10.10.100.132:15506/FEEDBACK_APP_TEST
AIG_HIS_DATABASE_URL=mssql+pyodbc://SA:%s@52.66.144.247:1433/AIG_UAT
AIG_HIS_DB_PASSWORD='M5SQLdb@32!'

SECRET_KEY =09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7123
ALGORITHM =HS256
ACCESS_EXPIRE_MINUTES=4320

#ABDM
SESSION_API=https://dev.abdm.gov.in/gateway/v0.5/sessions
CLIENT_ID=SBX_001664
CLIENT_SECRET=89d08f83-7305-40c2-9b33-31094cbd67b3
ABDM_RSA_PUBLIC_KEY=MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAstWB95C5pHLXiYW59qyO4Xb+59KYVm9Hywbo77qETZVAyc6VIsxU+UWhd/k/YtjZibCznB+HaXWX9TVTFs9Nwgv7LRGq5uLczpZQDrU7dnGkl/urRA8p0Jv/f8T0MZdFWQgks91uFffeBmJOb58u68ZRxSYGMPe4hb9XXKDVsgoSJaRNYviH7RgAI2QhTCwLEiMqIaUX3p1SAc178ZlN8qHXSSGXvhDR1GKM+y2DIyJqlzfik7lD14mDY/I4lcbftib8cv7llkybtjX1AayfZp4XpmIXKWv8nRM488/jOAF81Bi13paKgpjQUUuwq9tb5Qd/DChytYgBTBTJFe7irDFCmTIcqPr8+IMB7tXA3YXPp3z605Z6cGoYxezUm2Nz2o6oUmarDUntDhq/PnkNergmSeSvS8gD9DHBuJkJWZweG3xOPXiKQAUBr92mdFhJGm6fitO5jsBxgpmulxpG0oKDy9lAOLWSqK92JMcbMNHn4wRikdI9HSiXrrI7fLhJYTbyU3I4v5ESdEsayHXuiwO/1C8y56egzKSw44GAtEpbAkTNEEfK5H5R0QnVBIXOvfeF4tzGvmkfOO6nNXU3o/WAdOyV3xSQ9dqLY5MEL4sJCGY1iJBIAQ452s8v0ynJG5Yq+8hNhsCVnklCzAlsIzQpnSVDUVEzv17grVAw078CAwEAAQ==
HEALTH_ID_BASE_URL=https://healthidsbx.abdm.gov.in/api
REGIS_AADHAAR_GENERATE_OTP=/v2/registration/aadhaar/generateOtp
REGIS_AADHAAR_VERIFY_OTP=/v2/registration/aadhaar/verifyOTP
REGIS_AADHAR_LINK_MOBILE=/v2/registration/aadhaar/checkAndGenerateMobileOTP
VERFIY_AADHAR_LINK_MOBILE = /v2/registration/aadhaar/verifyMobileOTP

CREATE_HEALTHID_BY_AADHAAR=/v2/registration/aadhaar/createHealthIdByAdhaar
AUTH_INIT=/v2/auth/init
AUTH_CONFIRM_AADHAR_OTP=/v2/auth/confirmWithAadhaarOtp
AUTH_ACCOUNT_PROFILE=/v2/account/profile
AUTH_ACCOUNT_QR_CODE=/v2/account/qrCode
AUTH_ACCOUNT_HEALTH_CARD=/v2/account/getPngCard
AADHAR_RESEND_OTP = /v2/registration/aadhaar/resendAadhaarOtp


MAIL_ID = "<EMAIL>"
PASSWORD = "vmqcbwydgbyjbzyr"

CELERY_BROKER_URL=redis://:Aigh123@opd_redis:6379/0
CELERY_RESULT_BACKEND=redis://:Aigh123@opd_redis:6379/0

DEMOGRAPHICS_API_TOKEN=JiHhqOH9pAkib9LaBj14uhgUPOQ5dtxy5Hvvk4H-GN9klbG1Fyo9X18_ZonlSeOaFUE
DEMOGRAPHICS_USER_MAIL=<EMAIL>
DEMOGRAPHICS_CITY_URL=https://www.universal-tutorial.com/api/cities/
DEMOGRAPHICS_STATE_URL=https://www.universal-tutorial.com/api/states/India
DEMOGRAPHICS_TOKEN_URL=https://www.universal-tutorial.com/api/getaccesstoken



#SMS REGISTRATION
AIG_BASE_URL=http://************/HISTreeInbound
AIG_SMS_AUTH_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************.OJTVcSgAxLtfFw74qBRY-u5bX5ipahbTxp7N4ifU8oE
AIG_SMS_X_AUTH_TOKEN=dGNsLWFzaWFubXRlbmdhZ2VhcGlwcmVmZW50aW5kZG9tdHJhbnNsbW5mOjJLd3BCeFVW
AIG_SMS_URL=https://engage-api.digo.link/v1/messaging/sms
AIG_SECONDARY_SMS_URL=https://enterprise.smsgupshup.com/GatewayAPI/rest
AIG_SMS_USER_ID= "2000189938"
AIG_SMS_PASSWORD= "Asi@n@20"

#SMS TOKEN
TOKEN_USERNAME= "AchalaKiosk_User"
TOKEN_PASSWORD = "11"

DIALY_REPORT_MAIL=<EMAIL>
TYPE = DEV
MAIL_SERVER="smtp.office365.com:587"

# Redis container name
REDIS_CONTAINER ="opd_redis"

# Wait  Time 
WAIT_TIME=60
STATIC_OTP=1

#name splitter
NAME_SPLITTER_BASE_URL=https://v2.namsor.com
X_API_KEY=58167f1815cb41739644bd777763dafe

#surepass
SUREPASS_TOKEN=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************.ZmCjvDnimKOJybtx2e0DC-xRPvX8UxeS1yEpZdm7KPI
SUREPASS_BASE_URL=https://kyc-api.surepass.io
USE_SUREPASS=1

AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=cqxS0qNt253zEm2eE5rc86Xfk4ZPAGAKuVs4Qsar
#whatsapp message
FACEBOOK_BASE_URL=https://graph.facebook.com
AIRTEL_WHATSAPP_URL = https://iqwhatsapp.airtel.in

#server details
SERVER= 52.66.144.247
PORT = 8001
WATSAPP_DISABLED_PHONE_NUMBERS=["**********","**********","**********","**********"]
WHATSAPP_ENABLED_PHONE_NUMBERS=[""]
FRONTEND_URL=https://52.66.144.247/vil/validate

# configurable days to update pending bed request
THERSHOLD_DAYS = 1

VITAL_MAIL = <EMAIL>
REDIS_PASSWORD='Aigh123'
ENABLE_GRAPHQL=1
MAX_LOGIN_ATTEMPTS=3
MAX_COOLDOWN_TIME=900

#emr
EMR_BASE_URL=https://uat-emr.aighospitals.com
EMR_API_KEY_VALUE="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************.5L0mUjzTF_5x3DHZoLmDAuhqKy9xaYAxowwsYhjUVvU"
TAG_BASE_URL=http://10.10.102.145:8080


#bed 360 whatsapp msg
ONE_AIG_BASE_URL = https://uat-api-oneaig.aighospitals.com:8090

ACHALA_DATABASE_URL=postgresql+psycopg2://ai96evapp:%s@52.66.144.247:4763/daily_reports
ACHALA_DB_USER=ai96evapp
ACHALA_DB_PASSWORD='K1M59ev@pp$23'
ACHALA_DB_NAME=daily_reports