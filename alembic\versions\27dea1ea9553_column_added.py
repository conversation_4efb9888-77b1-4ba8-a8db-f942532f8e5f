"""column added

Revision ID: 27dea1ea9553
Revises: 3b5827dd4748
Create Date: 2023-12-05 08:05:58.173657

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '27dea1ea9553'
down_revision = '3b5827dd4748'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_visa_data', sa.Column('remarks', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_visa_data', 'remarks')
    # ### end Alembic commands ###
