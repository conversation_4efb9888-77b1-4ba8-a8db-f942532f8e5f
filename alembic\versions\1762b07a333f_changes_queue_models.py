"""changes queue models

Revision ID: 1762b07a333f
Revises: ec40b258aa0d
Create Date: 2023-10-16 11:23:31.548861

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1762b07a333f'
down_revision = 'ec40b258aa0d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('queue', sa.Column('freezed_count', sa.Integer(), server_default=sa.text('0'), nullable=True))
    op.add_column('user_queue', sa.Column('arrived_at', sa.DateTime(timezone=True), nullable=True))
    op.drop_column('user_service', 'bill_no')
    op.add_column('user_token', sa.Column('bill_no', sa.String(), nullable=False))
    op.create_unique_constraint(None, 'user_token', ['bill_no'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'user_token', type_='unique')
    op.drop_column('user_token', 'bill_no')
    op.add_column('user_service', sa.Column('bill_no', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.drop_column('user_queue', 'arrived_at')
    op.drop_column('queue', 'freezed_count')
    # ### end Alembic commands ###
