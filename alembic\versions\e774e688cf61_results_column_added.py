"""results column added

Revision ID: e774e688cf61
Revises: c53d65791919
Create Date: 2024-03-20 09:23:50.048440

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'e774e688cf61'
down_revision = 'c53d65791919'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('assessment_question', sa.Column('results', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('assessment_question', 'results')
    # ### end Alembic commands ###
