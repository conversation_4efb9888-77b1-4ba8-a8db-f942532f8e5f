origins = ["*"]
methods = ["*"]
headers = ["*"]

#AIG
BASIC_USER_REGISTRATION = "/api/FrontOffice/FOPatientRegistrationBasic"
GET_PATIENT_DETAILS_BY_PHONE_NO = "/api/FrontOffice/GetPatientDetailsfromPhoneNo"

#ABDM
SESSION_API="https://dev.abdm.gov.in/gateway/v0.5/sessions"
CLIENT_ID="SBX_001664"
CLIENT_SECRET="89d08f83-7305-40c2-9b33-31094cbd67b3"
ABDM_RSA_PUBLIC_KEY= "MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAstWB95C5pHLXiYW59qyO4Xb+59KYVm9Hywbo77qETZVAyc6VIsxU+UWhd/k/YtjZibCznB+HaXWX9TVTFs9Nwgv7LRGq5uLczpZQDrU7dnGkl/urRA8p0Jv/f8T0MZdFWQgks91uFffeBmJOb58u68ZRxSYGMPe4hb9XXKDVsgoSJaRNYviH7RgAI2QhTCwLEiMqIaUX3p1SAc178ZlN8qHXSSGXvhDR1GKM+y2DIyJqlzfik7lD14mDY/I4lcbftib8cv7llkybtjX1AayfZp4XpmIXKWv8nRM488/jOAF81Bi13paKgpjQUUuwq9tb5Qd/DChytYgBTBTJFe7irDFCmTIcqPr8+IMB7tXA3YXPp3z605Z6cGoYxezUm2Nz2o6oUmarDUntDhq/PnkNergmSeSvS8gD9DHBuJkJWZweG3xOPXiKQAUBr92mdFhJGm6fitO5jsBxgpmulxpG0oKDy9lAOLWSqK92JMcbMNHn4wRikdI9HSiXrrI7fLhJYTbyU3I4v5ESdEsayHXuiwO/1C8y56egzKSw44GAtEpbAkTNEEfK5H5R0QnVBIXOvfeF4tzGvmkfOO6nNXU3o/WAdOyV3xSQ9dqLY5MEL4sJCGY1iJBIAQ452s8v0ynJG5Yq+8hNhsCVnklCzAlsIzQpnSVDUVEzv17grVAw078CAwEAAQ=="
HEALTH_ID_BASE_URL= "https://healthidsbx.abdm.gov.in/api"
REGIS_AADHAAR_GENERATE_OTP= "/v2/registration/aadhaar/generateOtp"
REGIS_AADHAAR_VERIFY_OTP= "/v2/registration/aadhaar/verifyOTP"
REGIS_AADHAR_LINK_MOBILE= "/v2/registration/aadhaar/checkAndGenerateMobileOTP"

CREATE_HEALTHID_BY_AADHAAR= "/v2/registration/aadhaar/createHealthIdByAdhaar"
AUTH_INIT= "/v2/auth/init"
AUTH_CONFIRM_AADHAR_OTP= "/v2/auth/confirmWithAadhaarOtp"
AUTH_ACCOUNT_PROFILE= "/v2/account/profile"
AUTH_ACCOUNT_QR_CODE= "/v2/account/qrCode"
AUTH_ACCOUNT_HEALTH_CARD= "/v2/account/getPngCard"
AADHAR_RESEND_OTP = "/v2/registration/aadhaar/resendAadhaarOtp"
VERFIY_AADHAR_LINK_MOBILE = "/v2/registration/aadhaar/verifyMobileOTP"
SEARCH_BY_HEALTH_ID = "/v2/search/searchByHealthId"
AUTH_CONFIRM_MOBILE_OTP= "/v1/auth/confirmWithMobileOTP"
RESEND_MOBILE_OTP="/v2/registration/mobile/resendOtp"

TOKEN_GENERATION="/api/Auth/Login"

LAB_REPORTS ="/api/FrontOffice/GetPatientsLabDiagnosticReportList"
BASE64_REPORT ="/api/FrontOffice/GetPatientsLabDiagnosticReport"
LAB_WHATSAPP_MSG ="/api/labReport/whatsapp/sendReport"
NAME_SPLITTER="/NamSorAPIv2/api2/json/parseNameGeoBatch"

SUREPASS_GENERATE_OTP="/api/v1/aadhaar-v2/generate-otp"
SUREPASS_SUBMIT_OTP="/api/v1/aadhaar-v2/submit-otp"

SEND_WHATSAPP_MSG = "/v17.0/***************/messages"
QR_CODE = "/v18.0/{app-id}/uploads?access_token={access-token}"

SEND_AIRTEL_WHATSAPP= "/gateway/airtel-xchange/basic/whatsapp-manager/v1/template/send"

AIG_LOGO = "https://aigbot.aighospitals.com/assets/aig-logo-transparent-CzEYiWih.png"

PACS_RISORDER_ID = "/YASASII-RIS_API/API/Reporting"
PACS_RISORDER_REPORT = "/YASASII-RIS_API/API/RisReporting"

GET_PATIENT_DETAILS_BY_UHID = "/api/FrontOffice/GetPatientDetailsfromUHID"

GET_VISIT_DETAILS = "/api/integration/getOPVisits"
POST_ASSESSMENT_DETAILS = "/api/integration/postEvent"
EMR_API_KEY="hpapp-token"

CREATE_TAG = "/create_tag"
DELETE_TAG = "/delete_tag"
UPDATE_TAG = "/update_tag"

GET_DOCTORS="/api/FrontOffice/GetFODoctors"
GET_SPECIALIZATION="/api/FrontOffice/GetFOSpecialization"


#ONE_AIG
GET_VITAL_STATUS_BY_UHIDS = "/api/emr/getVitalStatus"
GET_EMPLOYEE_DETAILS = "/api/his/employee/userDetails/"
ONE_AIG_VIEW_REPORTS = "/api/labReport/view/report/kiosk"

#phlebotomy
GENERATE_BAR_CODE="/api/Lab/GenerateBarcode"
UPDATE_SAMPLE_COLLECTION="/api/Lab/UpdateSampleCollectionDateTime"
GET_ACKNOWLEDGE_DETAILS="/api/Diagnostics/GetAcknowledgedTestDetails"
GET_ORDER_DETAILS_URL='/api/Lab/GetOrderDetails'