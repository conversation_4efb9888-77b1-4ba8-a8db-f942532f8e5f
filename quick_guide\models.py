import enum
from sqlalchemy import BigInteger, Column, DateTime, Enum, ForeignKey, SmallInteger, Text, String, Time, UniqueConstraint, Integer, Numeric, Boolean,Date
from sqlalchemy.sql import func
import strawberry
from sqlalchemy.orm import relationship
from database.db_conf import Base
from user.models import StatusEnum


class MedicalServices(Base):
    __tablename__ = "medical_services"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    investigations = Column(String, nullable=False)
    tower = Column(String, nullable=False)
    floor = Column(String, nullable=False)
    location = Column(String, nullable=False)
    timings = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self) -> str:
        return "<MedicalServices %r>" % self.id
    

class KeyLocations(Base):
    __tablename__ = "key_locations"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String , nullable = False)
    code = Column(String , nullable = False)
    priority= Column(Integer)
    description = Column(Text, nullable=False)
    icon = Column(String)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self) -> str:
        return "<KeyLocations %r>" % self.id
    

class Specialtity(Base):
    __tablename__ = "specialtity"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String, nullable=False)
    tower = Column(String, nullable=False)
    floor = Column(String, nullable=False)
    location = Column(String, nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self) -> str:
        return "<Specialtity %r>" % self.id
    
