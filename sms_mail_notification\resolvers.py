import json
import os
from constants import SEND_WHATSAPP_MSG
from sms_mail_notification.models import EventCodeEnum, SMSMailNotificationConfig as SMSMailNotificationConfigModel, SMSTypeEnum
import datetime
import random
from typing import List, Optional
from exceptions.exceptions import MutationError
from sqlalchemy.exc import NoResultFound, IntegrityError
from sqlalchemy.orm import Session
from sqlalchemy import or_
import logging
import bcrypt
import pytz
from jose import jws
from sqlalchemy import cast, Date, func
from sqlalchemy import asc, desc
import pandas as pd
from config.models import Config as ConfigModel
from util.globals import handle_request, handle_request1
from cachetools import cached, LRUCache, TTLCache
logger = logging.getLogger()


def sms_mail_notification_message(title: str, code: str, type: str, user_type: str, otp: Optional[str] = None):
    logger.info(user_type)
    if (type == SMSTypeEnum.SMS):
        if (code == EventCodeEnum.REGISTER.name):
            message = title.replace("<$1>", otp).replace("<$2>", "account")
            return message
        if (code == EventCodeEnum.LOGIN.name and user_type.__contains__("PATIENT")):
            message = title.replace("<$1>", otp).replace("<$2>", "account")
            return message
        if (code == EventCodeEnum.CONSULTATION.name and user_type.__contains__("PATIENT")):
            message = title.replace("<$1>", otp).replace("<$2>", "account")
            return message
        if (code == EventCodeEnum.CONSULTATION.name and user_type.__contains__("DOCTOR")):
            message = title.replace("<$1>", otp).replace("<$2>", "account")
            return message
        if (code == EventCodeEnum.QUEUE_NO_GENERATION.name and user_type.__contains__("PATIENT")):
            message = title.replace("<$1>", otp).replace("<$2>", "queue")
            return message
        if (code == EventCodeEnum.UMR_GENERATION.name and user_type.__contains__("PATIENT")):
            message = title.replace("<$1>", otp).replace("<$2>", "account")
            return message


def get_template_name(db: Session, msg_type, event_code):
    try:
        data = db.query(SMSMailNotificationConfigModel.data).filter(
            SMSMailNotificationConfigModel.type == msg_type, SMSMailNotificationConfigModel.code == event_code).first()
        tmp_name = json.loads(data.data)
        return tmp_name['template_name']
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while getting message")

def get_whatsapp_details(db:Session):
    try:
        config_data = db.query(ConfigModel.data).one()
        config_data= json.loads(config_data.data)
        token = config_data["whatsapp_msg_token"]
        hsp_phone_no = config_data["hsp_phon_number"]
        return (token, hsp_phone_no)
    except Exception as e:
        logger.exception(e)
        
def send_whatsapp_msg(db: Session, tmp_name: str, send_to: str, params,generated_link=None):
    try:
        ph_nos=os.environ["WATSAPP_DISABLED_PHONE_NUMBERS"]
        if(len(ph_nos)>0):
            if(send_to in ph_nos):
                return
            else:
                pass
        send_to= "91"+send_to
        if tmp_name == "patient_welcome":
             body = {
                    "messaging_product": "whatsapp",
                    "to": send_to,
                    "type": "template",
                    "template": {
                        "name": tmp_name,
                        "language": {
                            "code": "en"
                        },
                        "components": [
                            {
                                "type": "header",
                                "parameters":[
                                    {
                                        "type": "image",
                                        "image": {
                                        "link": f"{generated_link}"
                                        }
                                    }
                                ]
                            },
                            {
                                "type": "BODY",
                                "parameters": params
                            }
                        ]
                    }
                }
        else: 
            body = {
                        "messaging_product": "whatsapp",
                        "to": send_to,
                        "type": "template",
                        "template": {
                            "name": tmp_name,
                            "language": {
                                "code": "en"
                            },
                            "components": [
                                {
                                    "type": "BODY",
                                    "parameters": params
                                }
                            ]
                        }
                    }
        logger.info(body)
        config_data = get_whatsapp_details(db)
        from celery_worker import call_whatsapp_msg_task
        logger.info("whatsapp msg initiated")
        headers = {"Authorization": "Bearer " + config_data[0]}
        if tmp_name=='patient_queue_guidance':
            call_whatsapp_msg_task.apply_async(args=[headers, body], countdown=20)
        else:
            call_whatsapp_msg_task.apply_async(args=[headers, body], countdown=0)

    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while sending message")