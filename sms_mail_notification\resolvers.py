import json
import os
from constants import SEND_WHATSAPP_MSG
from sms_mail_notification.models import EventCodeEnum, SMSMailNotificationConfig as SMSMailNotificationConfigModel, SMSTypeEnum
import datetime
import random,base64
from typing import List, Optional
from exceptions.exceptions import MutationError
from sqlalchemy.exc import NoResultFound, IntegrityError
from sqlalchemy.orm import Session
from sqlalchemy import or_
import logging
import bcrypt
import pytz
from jose import jws
from sqlalchemy import cast, Date, func
from sqlalchemy import asc, desc
import pandas as pd
from config.models import Config as ConfigModel
from util.globals import handle_request, handle_request1, cache
from cachetools import cached, LRUCache, TTLCache
logger = logging.getLogger()


def sms_mail_notification_message(title: str, code: str, type: str, user_type: str, otp: Optional[str] = None):
    logger.info(user_type)
    if (type == SMSTypeEnum.SMS):
        if (code == EventCodeEnum.REGISTER.name):
            message = title.replace("<$1>", otp).replace("<$2>", "account")
            return message
        if (code == EventCodeEnum.LOGIN.name and user_type.__contains__("PATIENT")):
            message = title.replace("<$1>", otp).replace("<$2>", "account")
            return message
        if (code == EventCodeEnum.CONSULTATION.name and user_type.__contains__("PATIENT")):
            message = title.replace("<$1>", otp).replace("<$2>", "account")
            return message
        if (code == EventCodeEnum.CONSULTATION.name and user_type.__contains__("DOCTOR")):
            message = title.replace("<$1>", otp).replace("<$2>", "account")
            return message
        if (code == EventCodeEnum.QUEUE_NO_GENERATION.name and user_type.__contains__("PATIENT")):
            message = title.replace("<$1>", otp).replace("<$2>", "queue")
            return message
        if (code == EventCodeEnum.UMR_GENERATION.name and user_type.__contains__("PATIENT")):
            message = title.replace("<$1>", otp).replace("<$2>", "account")
            return message


def get_template_name(db: Session, msg_type, event_code):
    try:
        data = db.query(SMSMailNotificationConfigModel.data).filter(
            SMSMailNotificationConfigModel.type == msg_type, SMSMailNotificationConfigModel.code == event_code).first()
        tmp = json.loads(data.data)
        return tmp
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while getting message")

def get_template_msg(db: Session, msg_type, event_code):
    try:
        data = db.query(SMSMailNotificationConfigModel.title).filter(
            SMSMailNotificationConfigModel.type == msg_type, SMSMailNotificationConfigModel.code == event_code).first()
        return data['title']
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while getting message")

def get_whatsapp_details(db:Session):
    try:
        config_data = db.query(ConfigModel.data).filter(ConfigModel.hospital_code=="1100").one()
        config_data= json.loads(config_data.data)
        token = config_data["whatsapp_msg_token"]
        hsp_phone_no = config_data["hsp_phon_number"]
        return (token, hsp_phone_no)
    except Exception as e:
        logger.exception(e)

# @cache.cache(ttl=3600)
def get_config_data_cache(db:Session):    
    config_data = db.query(ConfigModel.data).first()
    return config_data.data

        
def send_whatsapp_msg(db: Session, tmp, send_to: str, params,generated_link=None,headers=None):
    try:
        tmp_name = tmp.get("template_name")
        tmp_id = tmp.get("template_id")
        ph_nos=os.environ["WATSAPP_DISABLED_PHONE_NUMBERS"]

        config_data = get_config_data_cache(db)
        config_data= json.loads(config_data)
        if(len(ph_nos)>0):
            if(send_to in ph_nos):
                return
            else:
                pass
        envirmnt = os.environ.get("TYPE")
        nos = os.environ.get("WHATSAPP_ENABLED_PHONE_NUMBERS")
        if (envirmnt != "PROD" and send_to in nos) or (envirmnt == "PROD"):
            send_to= "91"+send_to
            if config_data["whatsapp_message_type"] == "WHATSAPP":
                if tmp_name == "queue_token_creation":
                    body = {
                            "messaging_product": "whatsapp",
                            "to": send_to,
                            "type": "template",
                            "template": {
                                "name": tmp_name,
                                "language": {
                                    "code": "en"
                                },
                                "components": [
                                    {
                                        "type": "header",
                                        "parameters":[
                                            {
                                                "type": "image",
                                                "image": {
                                                "link": f"{generated_link}"
                                                }
                                            }
                                        ]
                                    },
                                    {
                                        "type": "BODY",
                                        "parameters": params
                                    }
                                ]
                            }
                        }
                else:
                    component = [
                        {
                            "type": "BODY",
                            "parameters": params
                        }
                    ]
                    if headers:
                        component = [
                            {
                                "type": "header",
                                "parameters": headers
                            },
                            {
                                "type": "BODY",
                                "parameters": params
                            }
                        ]
                    body = {
                                "messaging_product": "whatsapp",
                                "to": send_to,
                                "type": "template",
                                "template": {
                                    "name": tmp_name,
                                    "language": {
                                        "code": "en"
                                    },
                                    "components": component
                                }
                            }
                logger.info(body)
                whatsapp_data = get_whatsapp_details(db)
                from celery_worker import call_whatsapp_msg_task
                logger.info("whatsapp msg initiated")
                headers = {"Authorization": "Bearer " + whatsapp_data[0]}
                if tmp_name=='appointment_confirmation__instructions':
                    call_whatsapp_msg_task.apply_async(args=[headers, body], countdown=5)
                else:
                    call_whatsapp_msg_task.apply_async(args=[headers, body], countdown=0)

            if config_data["whatsapp_message_type"] == "AIRTEL":
                updated_param = [param.get("text") for param in params if param.get("text") != "MIRA"]
                if tmp_name == "queue_token_creation":
                    body = {
                        "templateId": tmp_id,
                        "to": send_to,
                        "from": config_data["send_from"],
                        "message": {
                            "variables": updated_param
                        },
                        "mediaAttachment" :{
                            "type":"IMAGE",
                            "url":generated_link
                        }
                    }
                else:
                    body = {
                        "templateId": tmp_id,
                        "to": send_to,
                        "from": config_data["send_from"],
                        "message": {
                            "variables": updated_param
                        }
                        }
                logger.info(body)
                from celery_worker import call_airtel_whatsapp_msg_task
                logger.info("whatsapp msg initiated")

                credentials = f'{config_data["whatsapp_user_name"]}:{config_data["whatsapp_password"]}'
                encoded_credentials = base64.b64encode(credentials.encode()).decode()

                headers = {"Authorization": f"Basic {encoded_credentials}"}
                if tmp_name=='appointment_confirmation__instructions':
                    call_airtel_whatsapp_msg_task.apply_async(args=[headers, body], countdown=5)
                else:
                    call_airtel_whatsapp_msg_task.apply_async(args=[headers, body], countdown=0)

    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while sending message")