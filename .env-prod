# Local database connection
DATABASE_URL=postgresql+psycopg2://aig_prod:%s@*************:4763/aig_opd_prod
DB_USER=aig_prod
DB_PASSWORD='@!gApp@123$'
DB_NAME=aig_opd_prod
# FEEDBACK_APP_URL=mysql+pymysql://kfadmin:%s@10.10.100.132:15506/FEEDBACK_APP_TEST
AIG_HIS_DATABASE_URL=mssql+pyodbc://kiosk:%s@10.10.102.65:1433/HISTREE_AIG_LIVE
AIG_HIS_DB_PASSWORD='G@$TR)@@!G#@k!osk'

SECRET_KEY =09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7
ALGORITHM =HS256
ACCESS_EXPIRE_MINUTES=43200

#ABDM
SESSION_API=https://dev.abdm.gov.in/gateway/v0.5/sessions
CLIENT_ID=SBX_001664
CLIENT_SECRET=89d08f83-7305-40c2-9b33-31094cbd67b3
ABDM_RSA_PUBLIC_KEY=MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAstWB95C5pHLXiYW59qyO4Xb+59KYVm9Hywbo77qETZVAyc6VIsxU+UWhd/k/YtjZibCznB+HaXWX9TVTFs9Nwgv7LRGq5uLczpZQDrU7dnGkl/urRA8p0Jv/f8T0MZdFWQgks91uFffeBmJOb58u68ZRxSYGMPe4hb9XXKDVsgoSJaRNYviH7RgAI2QhTCwLEiMqIaUX3p1SAc178ZlN8qHXSSGXvhDR1GKM+y2DIyJqlzfik7lD14mDY/I4lcbftib8cv7llkybtjX1AayfZp4XpmIXKWv8nRM488/jOAF81Bi13paKgpjQUUuwq9tb5Qd/DChytYgBTBTJFe7irDFCmTIcqPr8+IMB7tXA3YXPp3z605Z6cGoYxezUm2Nz2o6oUmarDUntDhq/PnkNergmSeSvS8gD9DHBuJkJWZweG3xOPXiKQAUBr92mdFhJGm6fitO5jsBxgpmulxpG0oKDy9lAOLWSqK92JMcbMNHn4wRikdI9HSiXrrI7fLhJYTbyU3I4v5ESdEsayHXuiwO/1C8y56egzKSw44GAtEpbAkTNEEfK5H5R0QnVBIXOvfeF4tzGvmkfOO6nNXU3o/WAdOyV3xSQ9dqLY5MEL4sJCGY1iJBIAQ452s8v0ynJG5Yq+8hNhsCVnklCzAlsIzQpnSVDUVEzv17grVAw078CAwEAAQ==
HEALTH_ID_BASE_URL=https://healthidsbx.abdm.gov.in/api
REGIS_AADHAAR_GENERATE_OTP=/v2/registration/aadhaar/generateOtp
REGIS_AADHAAR_VERIFY_OTP=/v2/registration/aadhaar/verifyOTP
REGIS_AADHAR_LINK_MOBILE=/v2/registration/aadhaar/checkAndGenerateMobileOTP
VERFIY_AADHAR_LINK_MOBILE = /v2/registration/aadhaar/verifyMobileOTP

CREATE_HEALTHID_BY_AADHAAR=/v2/registration/aadhaar/createHealthIdByAdhaar
AUTH_INIT=/v2/auth/init
AUTH_CONFIRM_AADHAR_OTP=/v2/auth/confirmWithAadhaarOtp
AUTH_ACCOUNT_PROFILE=/v2/account/profile
AUTH_ACCOUNT_QR_CODE=/v2/account/qrCode
AUTH_ACCOUNT_HEALTH_CARD=/v2/account/getPngCard
AADHAR_RESEND_OTP = /v2/registration/aadhaar/resendAadhaarOtp


MAIL_ID = "<EMAIL>"
PASSWORD = "vmqcbwydgbyjbzyr"

CELERY_BROKER_URL=redis://:zp0tPoxYtMTbIz1@*************:6379/0
CELERY_RESULT_BACKEND=redis://:zp0tPoxYtMTbIz1@*************:6379/0

DEMOGRAPHICS_API_TOKEN=JiHhqOH9pAkib9LaBj14uhgUPOQ5dtxy5Hvvk4H-GN9klbG1Fyo9X18_ZonlSeOaFUE
DEMOGRAPHICS_USER_MAIL=<EMAIL>
DEMOGRAPHICS_CITY_URL=https://www.universal-tutorial.com/api/cities/
DEMOGRAPHICS_STATE_URL=https://www.universal-tutorial.com/api/states/India
DEMOGRAPHICS_TOKEN_URL=https://www.universal-tutorial.com/api/getaccesstoken



#SMS REGISTRATION
AIG_BASE_URL=http://************/HISTreeInboundEngine
AIG_SMS_AUTH_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************.OJTVcSgAxLtfFw74qBRY-u5bX5ipahbTxp7N4ifU8oE
AIG_SMS_X_AUTH_TOKEN=dGNsLWFzaWFubXRlbmdhZ2VhcGlwcmVmZW50aW5kZG9tdHJhbnNsbW5mOjJLd3BCeFVW
AIG_SMS_URL=https://engage-api.digo.link/v1/messaging/sms
AIG_SECONDARY_SMS_URL=https://enterprise.smsgupshup.com/GatewayAPI/rest
AIG_SMS_USER_ID= "2000189938"
AIG_SMS_PASSWORD= "Asi@n@20"

#SMS TOKEN
TOKEN_USERNAME= "AchalaKiosk_User"
TOKEN_PASSWORD = "11"

DIALY_REPORT_MAIL=<EMAIL>
TYPE = PROD
MAIL_SERVER="smtp.office365.com:587"

# Redis container name
REDIS_CONTAINER ="*************"

# Wait  Time 
WAIT_TIME=60
STATIC_OTP=0

#name splitter
NAME_SPLITTER_BASE_URL=https://v2.namsor.com
X_API_KEY=58167f1815cb41739644bd777763dafe

#surepass
SUREPASS_TOKEN=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************.ZmCjvDnimKOJybtx2e0DC-xRPvX8UxeS1yEpZdm7KPI
SUREPASS_BASE_URL=https://kyc-api.surepass.io
USE_SUREPASS=1

#whatsapp message
FACEBOOK_BASE_URL=https://graph.facebook.com

SERVER= *************
PORT = 8001
WATSAPP_DISABLED_PHONE_NUMBERS=[""]
FRONTEND_URL=https://verify.aighospitals.com/vil/validate

# configurable days to update pending bed request
THERSHOLD_DAYS = 0
VITAL_MAIL = <EMAIL>
REDIS_PASSWORD='zp0tPoxYtMTbIz1'
ENABLE_GRAPHQL=0
MAX_LOGIN_ATTEMPTS=3000
MAX_COOLDOWN_TIME=60

#emr
EMR_BASE_URL=https://emr.aighospitals.com
EMR_API_KEY_VALUE="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************.PN6InGt--PmERpcNlkDzjWLzmkPz06hvm0zne0Rmh9c"
DOCKER_VOLUME="/app-data"
TAG_BASE_URL=http://10.10.102.145:8080

#bed 360 whatsapp msg
ONE_AIG_BASE_URL = https://api-oneaig.aighospitals.com

ACHALA_DATABASE_URL=postgresql+psycopg2://ah_user:%s@34.205.234.23:5247/ah_daily_stats_prod
ACHALA_DB_USER=ah_user
ACHALA_DB_PASSWORD='Ah_User@332!'
ACHALA_DB_NAME=ah_daily_stats_prod