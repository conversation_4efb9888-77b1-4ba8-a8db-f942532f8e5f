"""column added

Revision ID: 6eb26c2f0b16
Revises: b8f3f0300524
Create Date: 2023-11-30 14:01:08.200419

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6eb26c2f0b16'
down_revision = 'b8f3f0300524'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_visa_data', sa.Column('ref_no', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_visa_data', 'ref_no')
    # ### end Alembic commands ###
