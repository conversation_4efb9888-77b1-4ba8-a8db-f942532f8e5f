"""printer_name added to device table

Revision ID: b1975ee56619
Revises: 10213bc6f15a
Create Date: 2023-08-29 12:12:58.083784

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b1975ee56619'
down_revision = '10213bc6f15a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('device', sa.Column('printer_name', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('device', 'printer_name')
    # ### end Alembic commands ###
