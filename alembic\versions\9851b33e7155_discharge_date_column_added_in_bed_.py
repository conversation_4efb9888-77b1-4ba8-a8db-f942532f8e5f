"""discharge date column added in bed request model

Revision ID: 9851b33e7155
Revises: 89ade5d4c581
Create Date: 2024-01-10 05:13:41.948469

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9851b33e7155'
down_revision = '89ade5d4c581'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bed_request', sa.Column('discharged_date', sa.Date(), nullable=True))
    op.execute("ALTER TYPE requeststatus ADD VALUE 'DISCHARGED'")

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('bed_request', 'discharged_date')
    op.execute("ALTER TYPE requeststatus DROP VALUE 'DISCHARGED'")

    # ### end Alembic commands ###
