"""date column added in user token model

Revision ID: 8f9deb0d49f5
Revises: 180e5e118e52
Create Date: 2024-03-22 12:34:45.030406

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8f9deb0d49f5'
down_revision = '180e5e118e52'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_token', sa.Column('date', sa.Date(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_token', 'date')
    # ### end Alembic commands ###
