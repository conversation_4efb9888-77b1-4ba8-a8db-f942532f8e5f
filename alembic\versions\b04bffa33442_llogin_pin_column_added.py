"""llogin_pin column added 

Revision ID: b04bffa33442
Revises: 6417bb027417
Create Date: 2023-09-29 08:31:21.031602

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b04bffa33442'
down_revision = '6417bb027417'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('device', sa.Column('login_pin', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('device', 'login_pin')
    # ### end Alembic commands ###
