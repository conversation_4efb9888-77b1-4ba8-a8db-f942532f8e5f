"""Column types changed in user queue

Revision ID: f2ddfab96f15
Revises: cd6156ce7fbb
Create Date: 2024-04-30 04:49:27.964727

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f2ddfab96f15'
down_revision = 'cd6156ce7fbb'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TABLE user_queue_logs ALTER COLUMN created_by TYPE INTEGER USING created_by::INTEGER")
    op.create_foreign_key('user_queue_updated_by_fk', 'user_queue', 'staff_user', ['updated_by'], ['id'])
    op.create_foreign_key('user_queue_created_by_fk', 'user_queue', 'staff_user', ['created_by'], ['id'])
    op.create_foreign_key('user_queue_created_by_fk', 'user_queue_logs', 'staff_user', ['created_by'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('user_queue_created_by_fk', 'user_queue_logs', type_='foreignkey')
    op.drop_constraint('user_queue_created_by_fk', 'user_queue', type_='foreignkey')
    op.drop_constraint('user_queue_updated_by_fk', 'user_queue', type_='foreignkey')
    # ### end Alembic commands ###
