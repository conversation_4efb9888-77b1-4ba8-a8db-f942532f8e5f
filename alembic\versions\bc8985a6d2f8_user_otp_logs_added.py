"""user otp logs added

Revision ID: bc8985a6d2f8
Revises: e362a0bfda21
Create Date: 2023-08-02 06:44:47.561578

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'bc8985a6d2f8'
down_revision = 'e362a0bfda21'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_otp_logs',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('Phone_number', sa.String(), nullable=False),
    sa.Column('otp_status', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('user_otp', sa.Column('request_id', sa.String(), nullable=True))
    op.add_column('user_otp', sa.Column('count', sa.Integer(), nullable=True))
    op.add_column('user_otp', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_otp', 'updated_at')
    op.drop_column('user_otp', 'count')
    op.drop_column('user_otp', 'request_id')
    op.drop_table('user_otp_logs')
    # ### end Alembic commands ###
