"""Screen table added

Revision ID: 584ddb9f9c40
Revises: 2deefa4ca6e2
Create Date: 2023-08-04 11:16:35.811860

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '584ddb9f9c40'
down_revision = '2deefa4ca6e2'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('screen',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('screen_code', sa.String(), nullable=True),
    sa.Column('initial_action', sa.String(), nullable=True),
    sa.Column('step', sa.String(), nullable=True),
    sa.Column('is_final', sa.SmallInteger(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('screen_code')
    )
    op.add_column('track_screen', sa.Column('initial_action', sa.String(), nullable=True))
    op.add_column('track_screen', sa.Column('is_final', sa.SmallInteger(), nullable=True))
    op.add_column('track_screen', sa.Column('step', sa.String(), nullable=True))
    op.add_column('track_screen_logs', sa.Column('initial_action', sa.String(), nullable=True))
    op.add_column('track_screen_logs', sa.Column('is_final', sa.SmallInteger(), nullable=True))
    op.add_column('track_screen_logs', sa.Column('step', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('track_screen_logs', 'step')
    op.drop_column('track_screen_logs', 'is_final')
    op.drop_column('track_screen_logs', 'initial_action')
    op.drop_column('track_screen', 'step')
    op.drop_column('track_screen', 'is_final')
    op.drop_column('track_screen', 'initial_action')
    op.drop_table('screen')
    # ### end Alembic commands ###
