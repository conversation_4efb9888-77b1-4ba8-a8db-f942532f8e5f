"""patient type column added

Revision ID: f7de60652c2c
Revises: 917a2e969724
Create Date: 2024-09-05 12:18:55.024773

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f7de60652c2c'
down_revision = '917a2e969724'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("CREATE TYPE patienttypeenum AS ENUM ('OP', 'IP', 'DAY_CARE')")
    op.add_column('user_queue', sa.Column('patient_type', sa.Enum('OP', 'IP', 'DAY_CARE', name='patienttypeenum'), nullable=True), schema='queue')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("DROP TYPE patienttypeenum")
    op.drop_column('user_queue', 'patient_type', schema='queue')
    # ### end Alembic commands ###
