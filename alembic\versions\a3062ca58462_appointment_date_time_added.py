"""appointment date time added

Revision ID: a3062ca58462
Revises: 859e80b08d6f
Create Date: 2025-05-15 12:08:13.986690

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a3062ca58462'
down_revision = '859e80b08d6f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_queue', sa.Column('appointment_date_time', sa.DateTime(timezone=True), nullable=True), schema='queue')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_queue', 'appointment_date_time', schema='queue')
    # ### end Alembic commands ###
