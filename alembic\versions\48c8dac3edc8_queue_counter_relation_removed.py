"""queue counter relation removed

Revision ID: 48c8dac3edc8
Revises: dc9110a07ac7
Create Date: 2024-07-22 11:27:17.395810

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '48c8dac3edc8'
down_revision = 'dc9110a07ac7'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('device_queue_counter_fk', 'device', type_='foreignkey')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key('device_queue_counter_fk', 'device', 'queue_counter', ['queue_counter'], ['id'], referent_schema='queue')
    # ### end Alembic commands ###
