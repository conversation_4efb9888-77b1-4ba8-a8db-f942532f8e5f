"""unique constraint added

Revision ID: 807cf7d76232
Revises: 7710e232f27f
Create Date: 2023-10-13 13:20:16.234761

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '807cf7d76232'
down_revision = '7710e232f27f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(None, 'user_token', ['token_no'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'user_token', type_='unique')
    # ### end Alembic commands ###
