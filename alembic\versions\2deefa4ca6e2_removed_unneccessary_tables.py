"""removed unneccessary tables

Revision ID: 2deefa4ca6e2
Revises: 3cb8d434cab1
Create Date: 2023-08-04 06:58:38.323174

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '2deefa4ca6e2'
down_revision = '3cb8d434cab1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('payment_logs')
    op.drop_table('payments')
    op.drop_table('location')
    op.drop_table('feedback_question')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('feedback_question',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('code', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('type', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('priority', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='feedback_question_pkey'),
    sa.UniqueConstraint('code', name='feedback_question_code_uc')
    )
    op.create_table('payment_logs',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('payments_id', sa.BIGINT(), autoincrement=False, nullable=False),
    sa.Column('user_id', sa.BIGINT(), autoincrement=False, nullable=False),
    sa.Column('payment_status', postgresql.ENUM('PENDING', 'RECIEVED', 'FAILED', 'HOLD', 'SUCCESS', name='paymentstatusenum'), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['payments_id'], ['payments.id'], name='payment_logs_payments_id'),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], name='payment_user_id'),
    sa.PrimaryKeyConstraint('id', name='payment_logs_pkey')
    )
    op.create_table('location',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('code', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('priority', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('overview_message', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('image_url', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='location_pkey')
    )
    op.create_table('payments',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('description', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('user_id', sa.BIGINT(), autoincrement=False, nullable=False),
    sa.Column('service', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('payment_id', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('amount', sa.BIGINT(), autoincrement=False, nullable=True),
    sa.Column('ref_id', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('payment_status', postgresql.ENUM('PENDING', 'RECIEVED', 'FAILED', 'HOLD', 'SUCCESS', name='paymentstatusenum'), autoincrement=False, nullable=False),
    sa.Column('razorpay_status', postgresql.ENUM('CREATED', 'AUTHORIZED', 'CAPTURED', 'REFUNDED', 'FAILED', name='razorpaystatusenum'), autoincrement=False, nullable=True),
    sa.Column('refund_status', postgresql.ENUM('UNINITIATED', 'REQUESTED', 'INITIATED', 'PROCESSING', 'DONE', name='refundstatusenum'), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], name='payment_user_id'),
    sa.PrimaryKeyConstraint('id', name='payments_pkey')
    )
    # ### end Alembic commands ###
