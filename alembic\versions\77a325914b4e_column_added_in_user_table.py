"""column added in user table

Revision ID: 77a325914b4e
Revises: a905fd1f06bc
Create Date: 2024-04-02 12:00:36.294772

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '77a325914b4e'
down_revision = 'a905fd1f06bc'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user', sa.Column('device_id', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'device_id')
    # ### end Alembic commands ###
