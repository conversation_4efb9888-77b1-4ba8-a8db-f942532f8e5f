"""column added to bed rquest table

Revision ID: d45d3e6f26c6
Revises: 57a1f9eaa1c4
Create Date: 2023-11-02 09:19:33.495423

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd45d3e6f26c6'
down_revision = '57a1f9eaa1c4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("CREATE TYPE casetypeenum AS ENUM ('INSURANCE', 'CASH')")
    op.execute("ALTER TABLE bed_request ALTER COLUMN case_type TYPE casetypeenum USING case_type::casetypeenum")
    op.execute("CREATE TYPE priorityenum AS ENUM ('REGULAR', 'HIGH', 'URGENT')")

    op.add_column('bed_request', sa.Column('referred_by', sa.String(), nullable=True))
    op.add_column('bed_request', sa.Column('source', sa.String(), nullable=True))
    op.add_column('bed_request', sa.Column('rate_of_contract', sa.String(), nullable=True))
    op.add_column('bed_request', sa.Column('priority', sa.Enum('REGULAR', 'HIGH', 'URGENT', name='priorityenum'), nullable=True))
    op.add_column('bed_request', sa.Column('admission_no', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('bed_request', 'admission_no')
    op.drop_column('bed_request', 'priority')
    op.drop_column('bed_request', 'rate_of_contract')
    op.drop_column('bed_request', 'source')
    op.drop_column('bed_request', 'referred_by')
    op.execute("DROP TYPE casetypeenum")

    # Restore the original column type
    op.alter_column('bed_request', 'case_type', type_=sa.String(), nullable=True)
    # ### end Alembic commands ###
