import enum
from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON>umn, DateTime, Enum, Foreign<PERSON>ey, SmallInteger, Text, String, Time, UniqueConstraint, Integer, Numeric, Boolean, Date, Float
from sqlalchemy.sql import func
import strawberry
from sqlalchemy.orm import relationship
from database.db_conf import Base
from user.models import EntityType<PERSON>num, StatusEnum, UserType

@strawberry.enum
class UserLoginStatusEnum(enum.Enum):
    SUCCESS = "SUCCESS"
    FAILURE = "FAILURE"

@strawberry.enum
class UserLoginTypeEnum(enum.Enum):
    LOGIN = "LOGIN"
    LOGOUT = "LOGOUT"

class StaffUser(Base):
    __tablename__ = "staff_user"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    emp_id = Column(String,unique=True)
    name = Column(String)
    email = Column(String)
    phone_number = Column(String)
    user_type_id = Column(Integer, ForeignKey(
        "user_type.id", name="staff_user_user_type_id_fk"))
    status = Column(Enum(StatusEnum),
                    default=StatusEnum.ACTIVE, nullable=False)
    password = Column(String)
    created_by = Column(Integer, ForeignKey(
        "staff_user.id", name="staff_user_created_by_fk"))
    updated_by = Column(Integer, ForeignKey(
        "staff_user.id", name="staff_user_updated_by_fk"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    deleted_at = Column(DateTime(timezone=True),nullable=True)
    queues = relationship(
        "Queue",
        secondary="queue.rel_staff_user_queue",
        back_populates="staff_users",
    )
    rel_staff_resource = relationship("RelStaffUserResource")
    # queue=relationship("Queue", foreign_keys="[RelStaffUserQueue.queue_id]" ,secondary="rel_staff_user_queue")
    user_type = relationship("UserType")
    nurse_station = relationship("RelStaffUserNurseStation",lazy='dynamic')
    created_by_user = relationship("StaffUser", remote_side=[id],foreign_keys=[created_by])
    updated_by_user = relationship("StaffUser", remote_side=[id],foreign_keys=[updated_by])
    designation = Column(String)
    speciality = Column(String)
    profile_pic = Column(String)
    qualification = Column(String)
    __table__args__= (UniqueConstraint('emp_id',name='staff_user_emp_id'),)

    def __repr__(self) -> str:
        return "<StaffUser %r>" % self.id


class StaffUserAuditLogs(Base):
    __tablename__ = 'staff_user_audit_logs'
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    data = Column(Text)
    action = Column(String)
    emp_id=Column(String)
    created_by = Column(Integer, ForeignKey(
        "staff_user.id", name="staff_user_audit_logs_created_by_fk"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    staff_user = relationship("StaffUser", foreign_keys=[created_by])

    def __repr__(self) -> str:
        return "<StaffUserAuditLogs %r>" % self.id

class StaffUserLoginDetails(Base):
    __tablename__ = 'staff_user_login_details'
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    status = Column(Enum(UserLoginStatusEnum))
    type = Column(Enum(UserLoginTypeEnum))
    staff_user_id = Column(Integer, ForeignKey("staff_user.id", name="user_login_details_staff_user_id_fk"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    entity_type = Column(Enum(EntityTypeEnum))
    failed_count = Column(Integer)
    
    staff_user = relationship("StaffUser", foreign_keys=[staff_user_id])

    def __repr__(self) -> str:
        return "<StaffUserLoginDetails %r>" % self.id
class RelStaffUserNurseStation(Base):
    __tablename__ = 'rel_staff_user_nurse_station'
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    staff_user_id = Column(Integer, ForeignKey(
        "staff_user.id", name="rel_staff_user_nurse_station_staff_user_id_fk"))
    nurse_station = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self) -> str:
        return "<RelStaffUserNurseStation %r>" % self.id
