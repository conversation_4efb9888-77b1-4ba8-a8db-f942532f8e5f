"""rel_device_queue model added

Revision ID: a5ca64e3fd26
Revises: bcb20323c8c9
Create Date: 2023-11-16 10:45:18.865368

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = 'a5ca64e3fd26'
down_revision = 'bcb20323c8c9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('rel_device_queue',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.Integer(), nullable=True),
    sa.Column('queue_id', sa.Integer(), nullable=True),
    sa.Column('is_updated', sa.<PERSON>(), nullable=True),
    sa.Column('subscription_name', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['device_id'], ['device.id'], name='rel_device_queue_device_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    device_type_enum = postgresql.ENUM('KIOSK', 'STAFF_STATION', name='devicetypeenum')
    device_type_enum.create(op.get_bind(), checkfirst=True)
    sub_device_type_enum = postgresql.ENUM('PRINTER_ENABLED', 'PRINTER_DISABLED', 'TV', name='subdevicetypeenum')
    sub_device_type_enum.create(op.get_bind(), checkfirst=True)
    op.add_column('device', sa.Column('device_type', device_type_enum, nullable=True))
    op.add_column('device', sa.Column('sub_device_type', sub_device_type_enum, nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('device', 'sub_device_type')
    op.drop_column('device', 'device_type')
    op.drop_table('rel_device_queue')
    # ### end Alembic commands ###
