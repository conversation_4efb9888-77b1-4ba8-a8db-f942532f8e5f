"""user discharge table removed, queue counter freezed capacity added

Revision ID: a3d455987a96
Revises: 308950b73f96
Create Date: 2024-02-02 05:16:59.623446

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'a3d455987a96'
down_revision = '308950b73f96'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_discharge')
    userqueueprecheckstatusenum = postgresql.ENUM('PENDING', 'COMPLETED', name='userqueueprecheckstatusenum')
    userqueueprecheckstatusenum.create(op.get_bind(), checkfirst=True)
    op.add_column('queue_counter', sa.Column('freeze_count', sa.Integer(), nullable=True))
    op.add_column('queue_counter', sa.Column('upcoming_capacity', sa.Integer(), nullable=True))
    op.add_column('user_queue', sa.Column('pre_check_status', userqueueprecheckstatusenum, nullable=True))
    op.create_index(op.f('ix_user_queue_queue_id'), 'user_queue', ['queue_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_queue_queue_id'), table_name='user_queue')
    op.drop_column('user_queue', 'pre_check_status')
    op.drop_column('queue_counter', 'upcoming_capacity')
    op.drop_column('queue_counter', 'freeze_count')
    op.create_table('user_discharge',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('admission_no', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('uhid', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('patient_name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('bed', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('admission_date', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('requested_discharge_date', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('blood_bank_clearance_date', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('discharge_summary', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('nursing_clearance_date', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('pharmacy_clearance_date', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('audit_clearance_date', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('billing_ack_date', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('bill_ready_date', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('discharge_date', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('otc_clearance_date', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('f_and_b_clearance_date', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['admission_no'], ['bed_status.admission_number'], name='user_discharge_admission_no_fk'),
    sa.PrimaryKeyConstraint('id', name='user_discharge_pkey')
    )
    # ### end Alembic commands ###
