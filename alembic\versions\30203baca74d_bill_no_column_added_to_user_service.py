"""bill_no column added to user service

Revision ID: 30203baca74d
Revises: c85d172c51b2
Create Date: 2024-02-13 12:33:32.715580

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '30203baca74d'
down_revision = 'c85d172c51b2'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_service', sa.Column('bill_no', sa.String(), nullable=True))
    op.create_unique_constraint(None, 'user_service', ['bill_no'])
    op.alter_column('user_token', 'bill_no',
               existing_type=sa.VARCHAR(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('user_token', 'bill_no',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.drop_constraint(None, 'user_service', type_='unique')
    op.drop_column('user_service', 'bill_no')
    # ### end Alembic commands ###
