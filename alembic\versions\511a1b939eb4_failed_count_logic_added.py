"""failed count logic added

Revision ID: 511a1b939eb4
Revises: 44c32b319cad
Create Date: 2024-04-17 11:45:42.865911

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '511a1b939eb4'
down_revision = '44c32b319cad'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('staff_user_login_details', sa.Column('failed_count', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('staff_user_login_details', 'failed_count')
    # ### end Alembic commands ###
