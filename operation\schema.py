from typing import List, Optional
from operation.models import Operation as OperationModel
from operation.resolvers import saveOperation, update_operations
import strawberry
from graphql_types import MutationResponse, OperationTypes
from exceptions.exceptions import MutationError

@strawberry.type
class Operation:
    id : int
    instance= strawberry.Private[OperationModel]

    @classmethod
    def from_instance(cls, instance: OperationModel):
        return cls(
            id=instance.id,
        )
@strawberry.type
class Mutation:
    @strawberry.mutation
    def save_operation(self, info,operation_name:str,operation_type:str,type_list:List[OperationTypes], operation_id:Optional[int] = None) -> MutationResponse[str]:
        try:
            db = info.context["db"]
            data = saveOperation(db,operation_name, operation_type, type_list,operation_id)
            return MutationResponse.from_status_flag(True, "Data saved", None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
        
    def update_operations(self, info, user_type_id: int) -> MutationResponse[str]:
        try:
            db = info.context["db"]
            data = update_operations(db,user_type_id)
            return MutationResponse.from_status_flag(True, "Data saved", None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)