"""condition column addded in assement question model

Revision ID: d2153cef7d0d
Revises: 57bab8e703d0
Create Date: 2024-02-28 13:26:59.172003

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd2153cef7d0d'
down_revision = '57bab8e703d0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('assessment_question', sa.Column('condition', sa.JSON(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('assessment_question', 'condition')
    # ### end Alembic commands ###
