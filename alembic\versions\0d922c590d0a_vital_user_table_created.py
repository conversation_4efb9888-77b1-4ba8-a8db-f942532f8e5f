"""vital user table created

Revision ID: 0d922c590d0a
Revises: ffbfce7500ed
Create Date: 2024-08-20 15:11:42.119503

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0d922c590d0a'
down_revision = 'ffbfce7500ed'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('vital_user',
    sa.<PERSON>umn('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('patient_name', sa.String(), nullable=True),
    sa.Column('uhid', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('encounter_id', sa.String(), nullable=True),
    sa.Column('failure_reason', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_unique_constraint(None, 'tag', ['rfid_code'], schema='queue')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'tag', schema='queue', type_='unique')
    op.drop_table('vital_user')
    # ### end Alembic commands ###
