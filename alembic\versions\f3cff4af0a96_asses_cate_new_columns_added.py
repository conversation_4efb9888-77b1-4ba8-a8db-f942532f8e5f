"""asses cate new columns added

Revision ID: f3cff4af0a96
Revises: 23b8590a81dd
Create Date: 2024-03-05 11:11:43.959853

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'f3cff4af0a96'
down_revision = '23b8590a81dd'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('assessment_category', sa.Column('domain_code', sa.String(), nullable=True))
    op.add_column('assessment_category', sa.Column('category_conditions', postgresql.JSONB(astext_type=sa.Text()), nullable=True))
    op.add_column('assessment_category', sa.Column('multiple', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('assessment_category', 'multiple')
    op.drop_column('assessment_category', 'category_conditions')
    op.drop_column('assessment_category', 'domain_code')
    # ### end Alembic commands ###
