from feedback.models import UserFeedback as UserFeedbackModel, FBCategory as FBCategoryModel, FBQuestions as FBQuestionsModel, FBQuestionOptions as FBQuestionOptionsModel, FBCategoryTypeEnum, FBQuestionsTypeEnum, UserQuestions as UserQuestionsModel, RelUserQuestionOptions as RelUserQuestionOptionsModel
from datetime import datetime
from distutils.log import info
from typing import List, Optional
from unicodedata import numeric
from exceptions.exceptions import MutationError
from user.schema import User
import logging
from graphql_types import MutationResponse, PatientResponse, FeedbackDetails, UserFeedbackDetails
import strawberry
logger = logging.getLogger()
from sqlalchemy.orm import Session
from sqlalchemy.exc import NoResultFound, IntegrityError
from sqlalchemy import asc, desc
from user.models import User as UserModel
# import shortuuid
import uuid


def list_fb_category(db: Session, code: Optional[str] = None):
    if code is not None:
        return db.query(FBCategoryModel).filter(FBCategoryModel.code == code).all()
    return db.query(FBCategoryModel).filter(FBCategoryModel.is_active == True).order_by(FBCategoryModel.priority).all()


def list_questions_by_category(db: Session, category_id: int, rating: Optional[int]=None):
    if rating is not None:
        return db.query(FBQuestionsModel).filter(FBQuestionsModel.fb_category_id == category_id).filter(FBQuestionsModel.min_rating <= rating, FBQuestionsModel.max_rating >= rating).filter(FBQuestionsModel.is_active == True).order_by(FBQuestionsModel.priority).all()
    return db.query(FBQuestionsModel).filter(FBQuestionsModel.fb_category_id == category_id).order_by(FBQuestionsModel.priority).all()



def save_user_feedback(db: Session, feedback_details: List[UserFeedbackDetails], user_id: Optional[int] = None, phone_number: Optional[str]=None,device_id: Optional[str] = None):
    try:
        # txn_id = uuid.uuid4()
        # logger.info(feedback_details)
        if user_id is not None:
            user = db.query(UserModel.phone_number).filter(UserModel.id == user_id).one()
            phone_number = user.phone_number
        for fb_detail in feedback_details:
            # logger.info(fb_detail)
            if fb_detail.response is not None and fb_detail.response.strip() != "":
                if fb_detail.category_id is None:
                    cate=db.query(FBCategoryModel.id).filter(FBCategoryModel.code==fb_detail.category_code).one()
                    fb_detail.category_id=cate.id
                if fb_detail.type == FBCategoryTypeEnum.TEN_RATING.name or fb_detail.type == FBCategoryTypeEnum.FIVE_RATING.name:
                    rating = fb_detail.response
                    remarks = None
                elif fb_detail.type == FBCategoryTypeEnum.TEXT.name:
                    remarks = fb_detail.response
                    rating = None
                if fb_detail.txn_id is None or fb_detail.txn_id == "":
                    txn_id = uuid.uuid4()
                else:
                    txn_id = fb_detail.txn_id
                user_feedback = UserFeedbackModel(
                        phone_number = phone_number,
                        user_id = user_id,
                        rating = rating,
                        remarks = remarks,
                        category_id = fb_detail.category_id,
                        txn_id = txn_id,
                        device_id = device_id
                    )
                db.add(user_feedback)
                db.flush()
                for question in fb_detail.questions:
                    rating = None
                    remarks = None
                    if question.question_id is None and (question.question_code is None or question.question_code == ""):
                        continue
                    if question.question_id is None:
                        quest=db.query(FBQuestionsModel.id).filter(FBQuestionsModel.code==question.question_code).one()
                        question.question_id=quest.id
                    if question.type == FBQuestionsTypeEnum.RATING.name:
                        rating = question.response
                    elif question.type == FBQuestionsTypeEnum.TEXT.name:
                        remarks = question.response
                    elif (question.type == FBQuestionsTypeEnum.SELECT_MULTIPLE.name or question.type == FBQuestionsTypeEnum.SELECT.name) and (question.options is None or len(question.options) == 0):
                        continue
                    question_obj = UserQuestionsModel(
                        user_feedback_id = user_feedback.id,
                        question_id = question.question_id,
                        rating = rating,
                        remarks = remarks
                    )
                    db.add(question_obj)
                    db.flush()
                    if question.options is not None:
                        for option_id in question.options:
                            user_option = RelUserQuestionOptionsModel(
                                user_question_id = question_obj.id,
                                option_id = option_id
                            )
                            db.add(user_option)
        db.commit()
        logger.info("Feedback saved successfully")
        # return user_feedback
    except Exception as e:
        logger.exception(f"Failed to save feedback")
        raise MutationError(f"Failed to save feedback")