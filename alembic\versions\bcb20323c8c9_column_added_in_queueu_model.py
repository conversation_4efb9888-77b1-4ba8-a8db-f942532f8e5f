"""Column added in queueu model

Revision ID: bcb20323c8c9
Revises: 9c6be61f0db4
Create Date: 2023-11-15 12:16:05.868942

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'bcb20323c8c9'
down_revision = '9c6be61f0db4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('queue', sa.Column('waiting_capacity', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('queue', 'waiting_capacity')
    # ### end Alembic commands ###
