"""latest called token updated at

Revision ID: 124be71216de
Revises: 535e0fd0df6d
Create Date: 2024-08-19 19:16:19.093142

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '124be71216de'
down_revision = '535e0fd0df6d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('queue', sa.Column('last_token_called_at', sa.DateTime(timezone=True), nullable=True), schema='queue')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('queue', 'last_token_called_at', schema='queue')
    # ### end Alembic commands ###
