## operations
INSERT INTO operation(id, name, type, status) VALUES (109,'getBedRequestsInDateRange', 'QUERY', 'ACTIVE');
INSERT INTO rel_operation_user_type(id, operation_id,entity_type, auth_type) VALUES (97,109, 'STAFF', 'NO_AUTH' );

## operations 02-01-24
INSERT INTO operation(id, name, type, status) VALUES (115,'updateBedRequestStatus', 'MUTATION', 'ACTIVE'),
(116,'getCommonRemarks', 'QUERY', 'ACTIVE');
INSERT INTO rel_operation_user_type(operation_id,entity_type,user_type) VALUES (115, 'STAFF', 4 ),
(115, 'STAFF', 5 ),(115, 'STAFF', 6 ),(115, 'STAFF', 7 ),(116, 'STAFF', 4 )
(116, 'STAFF', 5 ),(116, 'STAFF', 6 ),(116, 'STAFF', 7 );

## resource
INSERT INTO resource(id,name,code,status) VALUES (11,'Update Bed Request Status', 'UPDATE_BED_REQUEST_STATUS','ACTIVE');
INSERT INTO rel_user_type_resource(module_id,user_type_id,resource_id) VALUES(3,4,11),(3,5,11),(3,6,11),(3,7,11)

--10-01-2024
INSERT INTO operation (name,type,status) VALUES('getAvgTimeOfErBeds','QUERY','ACTIVE');
INSERT INTO rel_operation_user_type(operation_id,entity_type,user_type_id,auth_type) VALUES
((SELECT id from operation WHERE name='getAvgTimeOfErBeds'),'STAFF',(SELECT id from user_type WHERE code='BEDADMIN'),'BEARER'),
((SELECT id from operation WHERE name='getAvgTimeOfErBeds'),'STAFF',(SELECT id from user_type WHERE code='BEDOPS'),'BEARER');


INSERT INTO menu(name,code,icon_type,icon,status,priority,link) VALUES('Emergency','EMERGENCY','Material-UI','Healing','ACTIVE',1,'/emergency');
INSERT INTO rel_menu_user_type(module_id,menu_id,user_type_id) VALUES
((SELECT id from module WHERE module_name='Bed 360'),(SELECT id from menu WHERE name='Emergency'),(SELECT id from user_type WHERE code='BEDADMIN')),
((SELECT id from module WHERE module_name='Bed 360'),(SELECT id from menu WHERE name='Emergency'),(SELECT id from user_type WHERE code='BEDOPS'));

--16-01-2024
INSERT INTO operation (name,type,status) VALUES('getErBedDetails','QUERY','ACTIVE');
INSERT INTO rel_operation_user_type(operation_id,entity_type,user_type_id,auth_type) VALUES
((SELECT id from operation WHERE name='getErBedDetails'),'STAFF',(SELECT id from user_type WHERE code='BEDADMIN'),'BEARER'),
((SELECT id from operation WHERE name='getErBedDetails'),'STAFF',(SELECT id from user_type WHERE code='BEDOPS'),'BEARER');
INSERT INTO operation(name, type, status) VALUES ('getErBeds', 'QUERY', 'ACTIVE'),
('getAvgTimeOfErBeds', 'QUERY', 'ACTIVE');
INSERT INTO rel_operation_user_type(operation_id,entity_type,user_type_id) VALUES 
((SELECT id from operation WHERE name='getErBeds'),'STAFF',(SELECT id from user_type WHERE code='BEDADMIN')),
((SELECT id from operation WHERE name='getErBeds'),'STAFF',(SELECT id from user_type WHERE code='BEDOPS')),
((SELECT id from operation WHERE name='getAvgTimeOfErBeds'),'STAFF',(SELECT id from user_type WHERE code='BEDADMIN')),
((SELECT id from operation WHERE name='getAvgTimeOfErBeds'),'STAFF',(SELECT id from user_type WHERE code='BEDOPS'));


INSERT INTO menu(name,code,icon_type,icon,status,priority,link) VALUES('Queue Search','QUEUE_SEARCH','Material-UI','PersonSearch','ACTIVE',1,'/queue/search');
INSERT INTO rel_menu_user_type(module_id,menu_id,user_type_id) VALUES
((SELECT id from module WHERE module_name='Smart Queues'),(SELECT id from menu WHERE code='QUEUE_SEARCH'),(SELECT id from user_type WHERE code='QUEUEADMIN')),
((SELECT id from module WHERE module_name='Smart Queues'),(SELECT id from menu WHERE code='QUEUE_SEARCH'),(SELECT id from user_type WHERE code='OPDOPS'));

--31-01-2024
INSERT INTO operation (name,type,status) VALUES('updateAdmissionDate','MUTATION','ACTIVE');
INSERT INTO rel_operation_user_type(operation_id,entity_type,user_type_id,auth_type) VALUES
((SELECT id from operation WHERE name='updateAdmissionDate'),'STAFF',(SELECT id from user_type WHERE code='BEDADMIN'),'BEARER'),
((SELECT id from operation WHERE name='updateAdmissionDate'),'STAFF',(SELECT id from user_type WHERE code='BEDOPS'),'BEARER'),
((SELECT id from operation WHERE name='updateAdmissionDate'),'STAFF',(SELECT id from user_type WHERE code='ADMISSIONS'),'BEARER'),
((SELECT id from operation WHERE name='updateAdmissionDate'),'STAFF',(SELECT id from user_type WHERE code='BEDREQUESTS'),'BEARER');

INSERT INTO resource(name,code,status) VALUES('Pending Bed Request','PENDING_BED_REQUEST','ACTIVE');
 
INSERT INTO  rel_user_type_resource(module_id,user_type_id,resource_id)
VALUES
((SELECT id FROM module where module_name='Bed 360'),(SELECT  id from user_type WHERE code ='BEDADMIN'),(SELECT id from resource WHERE name='Pending Bed Request')),
((SELECT id FROM module where module_name='Bed 360'),(SELECT  id from user_type WHERE code ='BEDOPS'),(SELECT id from resource WHERE name='Pending Bed Request')),
((SELECT id FROM module where module_name='Bed 360'),(SELECT  id from user_type WHERE code ='ADMISSIONS'),(SELECT id from resource WHERE name='Pending Bed Request')),
((SELECT id FROM module where module_name='Bed 360'),(SELECT  id from user_type WHERE code ='BEDREQUESTS'),(SELECT id from resource WHERE name='Pending Bed Request'));

-- queue_counter related operations

INSERT INTO operation (name,type,status) VALUES('getCounterDetails','QUERY','ACTIVE');
INSERT INTO rel_operation_user_type(operation_id,entity_type,auth_type) VALUES
((SELECT id from operation WHERE name='getCounterDetails'),'HOSPITAL','NO_AUTH');

INSERT INTO rel_operation_user_type(operation_id,entity_type,auth_type) VALUES
((SELECT id from operation WHERE name='getConfigData'),'HOSPITAL','NO_AUTH'),
((SELECT id from operation WHERE name='staffUserLogin'),'HOSPITAL','NO_AUTH'),
((SELECT id from operation WHERE name='getQueues'),'HOSPITAL','NO_AUTH');
#02-02-2024  -- User management related operations  # need to authentication accordingly
INSERT INTO operation(name, type, status) VALUES ('getUserRoleMenusResources', 'QUERY', 'ACTIVE'),
('getModuleMenusResources', 'QUERY', 'ACTIVE'),('addOrEditUserRole', 'MUTATION', 'ACTIVE'),('getModulesData', 'QUERY', 'ACTIVE'),
('getAllModuleMenusResources', 'QUERY', 'ACTIVE'),('getNurseStations', 'QUERY', 'ACTIVE');
INSERT INTO rel_operation_user_type(operation_id,entity_type,auth_type) VALUES 
((SELECT id from operation WHERE name='getUserRoleMenusResources'),'STAFF','NO_AUTH'),
((SELECT id from operation WHERE name='getModuleMenusResources'),'STAFF','NO_AUTH'),
((SELECT id from operation WHERE name='addOrEditUserRole'),'STAFF','NO_AUTH'),
((SELECT id from operation WHERE name='getModulesData'),'STAFF','NO_AUTH'),
((SELECT id from operation WHERE name='getAllModuleMenusResources'),'STAFF','NO_AUTH'),
((SELECT id from operation WHERE name='getNurseStations'),'STAFF','NO_AUTH');

-- rel_module_menu


INSERT INTO rel_module_menu (module_id, menu_id)
VALUES
    ((SELECT id FROM module WHERE module_code = 'Smart Queues'), (SELECT id FROM menu WHERE code = 'DASHBOARD')),
    ((SELECT id FROM module WHERE module_code = 'Smart Queues'), (SELECT id FROM menu WHERE code = 'USER')),
    ((SELECT id FROM module WHERE module_code = 'Smart Queues'), (SELECT id FROM menu WHERE code = 'CHANGE_PASSWORD')),
    ((SELECT id FROM module WHERE module_code = 'Smart Queues'), (SELECT id FROM menu WHERE code = 'QUEUE')),
    ((SELECT id FROM module WHERE module_code = 'Bed 360'), (SELECT id FROM menu WHERE code = 'DASHBOARD')),
    ((SELECT id FROM module WHERE module_code = 'Bed 360'), (SELECT id FROM menu WHERE code = 'BED_MISMATCH')),
    ((SELECT id FROM module WHERE module_code = 'Bed 360'), (SELECT id FROM menu WHERE code = 'BED_REQUESTS')),
    ((SELECT id FROM module WHERE module_code = 'Bed 360'), (SELECT id FROM menu WHERE code = 'CHANGE_PASSWORD')),
    ((SELECT id FROM module WHERE module_code = 'Bed 360'), (SELECT id FROM menu WHERE code = 'DISCHARGES')),
    ((SELECT id FROM module WHERE module_code = 'Bed 360'), (SELECT id FROM menu WHERE code = 'CANCELLED_REQUESTS')),
    ((SELECT id FROM module WHERE module_code = 'Bed 360'), (SELECT id FROM menu WHERE code = 'BED_HISTORY')),
    ((SELECT id FROM module WHERE module_code = 'Bed 360'), (SELECT id FROM menu WHERE code = 'EMERGENCY')),
    ((SELECT id FROM module WHERE module_code = 'International Patients'), (SELECT id FROM menu WHERE code = 'VISA')),
    ((SELECT id FROM module WHERE module_code = 'International Patients'), (SELECT id FROM menu WHERE code = 'CHANGE_PASSWORD')),
	((SELECT id FROM module WHERE module_code = 'International Patients'), (SELECT id FROM menu WHERE code = 'HOME'));

-- resource name updated 
UPDATE resource SET name ='Allocate Bed Request' where code='EDIT_BED_REQUEST';
-- view resources addOrEditUserRoleINSERT INTO resource(name,code,status,menu_id)
VALUES('View Bed Request','VIEW_BED_REQUEST','ACTIVE',(SELECT id FROM menu WHERE name = 'Bed Requests')),
('View Visa','VIEW_VISA','ACTIVE',(SELECT id FROM menu WHERE name = 'Bed Requests'));

-- added linked menu id in resource table
UPDATE resource
SET menu_id = (
    CASE
        WHEN code = 'ADD_BED_REQUEST' THEN (SELECT id FROM menu WHERE code = 'BED_REQUESTS')
        WHEN code = 'EDIT_BED_REQUEST' THEN (SELECT id FROM menu WHERE code = 'BED_REQUESTS')
        WHEN code = 'WITHDRAW_BED_ALLOCATION' THEN (SELECT id FROM menu WHERE code = 'BED_REQUESTS')
        WHEN code = 'CANCEL_BED_REQUEST' THEN (SELECT id FROM menu WHERE code = 'BED_REQUESTS')
        WHEN code = 'GENERATE_VISA_LETTER' THEN (SELECT id FROM menu WHERE code = 'VISA')
        WHEN code = 'ADMISSION' THEN (SELECT id FROM menu WHERE code = 'BED_REQUESTS')
        WHEN code = 'TRANSFER' THEN (SELECT id FROM menu WHERE code = 'BED_REQUESTS')
        WHEN code = 'UPDATE_BED_REQUEST_STATUS' THEN (SELECT id FROM menu WHERE code = 'BED_REQUESTS')
        WHEN code = 'PENDING_BED_REQUEST' THEN (SELECT id FROM menu WHERE code = 'BED_REQUESTS')
        -- Add more WHEN clauses for other resource codes
    END
)
WHERE code IN ('ADD_BED_REQUEST', 'EDIT_BED_REQUEST', 'WITHDRAW_BED_ALLOCATION', 'CANCEL_BED_REQUEST', 'GENERATE_VISA_LETTER', 'ADMISSION', 'TRANSFER', 'UPDATE_BED_REQUEST_STATUS', 'PENDING_BED_REQUEST');

-- insert menu_id in rel_user_type_resource 
UPDATE rel_user_type_resource
SET menu_id = (SELECT menu_id FROM resource WHERE resource.id = rel_user_type_resource.resource_id);

-- linked staff user nurse stations
DO $$ 
DECLARE 
    staff_user_id INTEGER;
    nurse_station_names VARCHAR[] := ARRAY[
        'A-4-HDU 3', 'A-8-DOT WARD 1', 'A-8-DOT WARD 2', 'IP DayCare', 'A-8-NS 2', 'A-4-ICU 8',
        'A-8-DAYCARE', 'A-6-NS 2', 'A-7-DAYCARE', 'A-B1-TRAUMA OBSERVATION', 'A-2-AMC/SPECIALTY WARD 3',
        'A-GF-GW 1', 'A-7-SLEEP LAB 1', 'A-3-IR WARD 1', 'A-9-NS 1', 'A-2-COLONOSCOPY RECOVERY (I)',
        'A-8-SLEEP LAB 2', 'A-3-CATH LAB (C)', 'A-4-ICU 6', 'A-6-NS 5', 'A-3-HDU 2', 'B-11-NS 1',
        'A-4-KT ICU', 'A-3- PRE OP WARD', 'B-10-NS 1', 'A-6-NS 1-A', 'A-4-IS 2', 'A-7-NS 3', 'A-6-DAYCARE',
        'A-3-POST TRANSIT', 'A-9-EEG ROOM 2', 'A-6-NS 1-B', 'A-4-ICU 7', 'A- 2-CHEMOTHERAPY/SPECIALTY WARD 4',
        'A-8-NS 2/BMT', 'A-7-NS 4', 'A-GF-GW 6', 'A-3-DAYCARE', 'A-3-HDU 1', 'A-3-LT ICU', 'A-GF-ER PROCEDURE ROOM',
        'A-7-NS 1', 'A-2-ERCP RECOVERY', 'A-9-NS 2', 'A-2-ENDOSCOPY RECOVERY', 'A-3-ICU 1', 'A-7-NS 6/LT POST OP',
        'A-4-PRE OP WARD', 'A-6-NS 4', 'A-3-IS 1', 'A-GF-RESUSCITATION/P1', 'A-9-NS 4', 'A-4-ICU 5', 'A-9-NS 5',
        'A-GF-GW 2', 'A-6-NEPHRO DAYCARE', 'A-GF-ER ISOLATION', 'A-4-HDU 4', 'A-GF-SPECIALITY WARD 6', 'A-9-NS 7',
        'A-7-NS 7', 'A-3-ICU 2', 'A-8-NS 6', 'A-GF-GW 4', 'A-3- CT ICU', 'A-GF-EMERGENCY', 'A-7-NS 5/LT PRE OP',
        'A-4-POST TRANSIT', 'A- 2-CHEMOTHERAPY/SPECIALTY WARD 5', 'A-2-PLATINUM RECOVERY', 'A-6-NS 3', 'A-8-NS 5',
        'A-GF-GW 3', 'A-2-DAYCARE', 'A-9-NS 6', 'A- 6-DIALYSIS', 'A-2- PICU /SPECIALITY WARD 2', 'A-B3-OBSERVATION',
        'A-2-PRE ASSESMENT AREA', 'A-B1-TRAUMA ICU', 'A-GF-ER-EXTENSION', 'A-6-DIALYSIS', 'A-3-ICU 3', 'A-8-NS 1',
        'A-8-NS 7', 'A-7-NS 2', 'A-GF-GW 5', 'A-8-NS 3', 'A-3-RADIAL LOUNGE', 'A-8-NS 4', 'A-8-IR WARD 2', 'A-9-NS 3',
        'A-GF-TRIAGE', 'A-2-ESWL /SPECIALITY WARD 1'
    ];
BEGIN
    FOR staff_user_id IN (SELECT id FROM staff_user WHERE  deleted_at IS NULL) 
    LOOP
        FOR i IN 1..ARRAY_LENGTH(nurse_station_names, 1)
        LOOP
            INSERT INTO rel_staff_user_nurse_station (staff_user_id, nurse_station) 
            VALUES (staff_user_id, nurse_station_names[i]);
        END LOOP;
    END LOOP;
END $$;

-- SUPER ADMIN MENUS
INSERT INTO menu(name,code,icon_type,icon,status,priority,link) VALUES('User Role','USER_ROLE','Material-UI','AdminPanelSettings','ACTIVE',1,'/user_roles');
INSERT INTO rel_module_menu (module_id, menu_id)
VALUES
    ((SELECT id FROM module WHERE module_code = 'Admin Module'), (SELECT id FROM menu WHERE code = 'USER_ROLE'));
INSERT INTO rel_menu_user_type(module_id,menu_id,user_type_id) VALUES
((SELECT id from module WHERE module_name='Admin Module'),(SELECT id from menu WHERE name='User Role'),(SELECT id from user_type WHERE code='SUPER_ADMIN'));
INSERT INTO rel_staff_user_menu (module_id, menu_id,staff_user_id)
VALUES
    ((SELECT m.id FROM module m WHERE m.module_code = 'Admin Module'), (SELECT mn.id FROM menu mn  WHERE mn.code = 'USER_ROLE'),
	 (SELECT su.id FROM staff_user  su JOIN user_type ON su.user_type_id = user_type.id WHERE user_type.code = 'SUPER_ADMIN'));

-- menu name updated 
UPDATE menu SET name ='Emergency Dashboard',priority=3 where code='EMERGENCY';

--23/2/2024
INSERT INTO resource(name,code,status,menu_id)
VALUES('Include Unassigned Requests','INCLUDE_UNASSIGNED_REQUESTS','ACTIVE',(SELECT id FROM menu WHERE name = 'Bed Requests'));

-- USER Menu operations
INSERT INTO operation (name,type,status) VALUES('getStaffUserDetails','QUERY','ACTIVE');
INSERT INTO rel_menu_operation (operation_id,menu_id) VALUES
((SELECT id from operation WHERE name='getStaffUserDetails'),(SELECT id FROM menu WHERE code='USER')),
((SELECT id from operation WHERE name='getStaffUsers'),(SELECT id FROM menu WHERE code='USER')),
((SELECT id from operation WHERE name='getAllModuleMenusResources'),(SELECT id FROM menu WHERE code='USER')),
((SELECT id from operation WHERE name='activateOrDeactivateStaffUser'),(SELECT id FROM menu WHERE code='USER')),
((SELECT id from operation WHERE name='getUserRoleMenusResources'),(SELECT id FROM menu WHERE code='USER')),
((SELECT id from operation WHERE name='getUserRole'),(SELECT id FROM menu WHERE code='USER')),
((SELECT id from operation WHERE name='getNurseStations'),(SELECT id FROM menu WHERE code='USER')),
((SELECT id from operation WHERE name='getModulesData'),(SELECT id FROM menu WHERE code='USER')),
((SELECT id from operation WHERE name='addOrEditStaffUser'),(SELECT id FROM menu WHERE code='USER')),
((SELECT id from operation WHERE name='deleteStaffUser'),(SELECT id FROM menu WHERE code='USER'));

-- 13-03-24
INSERT INTO rel_menu_operation VALUES
((SELECT id from operation WHERE name='getQueues'),(SELECT id FROM menu WHERE code='USER'));

-- 20-03-2024
INSERT INTO operation (name,type,status) VALUES('getAvailableTags','QUERY','ACTIVE');
INSERT INTO rel_menu_operation VALUES
((SELECT id from operation WHERE name='getAvailableTags'),(SELECT id FROM menu WHERE code='CHECKIN'));
-- added one operation to dasboard menu 
INSERT INTO rel_menu_operation (operation_id,menu_id) VALUES
((SELECT id from operation WHERE name='getBedClasses'),(SELECT id FROM menu WHERE code='DASHBOARD'));

INSERT INTO operation (name,type,status) VALUES('getTokenDetails','QUERY','ACTIVE');
INSERT INTO rel_menu_operation (operation_id,menu_id) VALUES
((SELECT id from operation WHERE name='getUserServices'),(SELECT id FROM menu WHERE code='QUEUE_SEARCH')),
((SELECT id from operation WHERE name='getUserServices'),(SELECT id FROM menu WHERE code='QUEUE_PATIENTS')),
((SELECT id from operation WHERE name='getTokenDetails'),(SELECT id FROM menu WHERE code='QUEUE_SEARCH')),
((SELECT id from operation WHERE name='getTokenDetails'),(SELECT id FROM menu WHERE code='QUEUE_PATIENTS'));

INSERT INTO operation (name,type,status) VALUES('getServiceCategories','QUERY','ACTIVE');
INSERT INTO rel_menu_operation (operation_id,menu_id) VALUES
((SELECT id from operation WHERE name='getServiceCategories'),(SELECT id FROM menu WHERE code='OPD_PATIENTS'));
INSERT INTO rel_menu_operation (operation_id,menu_id) VALUES
((SELECT id from operation WHERE name='getServiceCategories'),(SELECT id FROM menu WHERE code='QUEUE_PATIENTS'));

INSERT INTO operation (name,type,status) VALUES('getTotalQueueStats','QUERY','ACTIVE');
INSERT INTO rel_menu_operation (operation_id,menu_id) VALUES
((SELECT id from operation WHERE name='getTotalQueueStats'),(SELECT id FROM menu WHERE code='HOME'));
INSERT INTO rel_menu_operation (operation_id,menu_id) VALUES
((SELECT id from operation WHERE name='updateServices'),(SELECT id FROM menu WHERE code='HOME'));