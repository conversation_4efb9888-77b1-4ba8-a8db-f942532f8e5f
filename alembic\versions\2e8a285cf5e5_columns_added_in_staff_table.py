"""columns added in staff table

Revision ID: 2e8a285cf5e5
Revises: e1c5986e7253
Create Date: 2024-04-24 06:08:20.806628

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2e8a285cf5e5'
down_revision = 'e1c5986e7253'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('staff_user', sa.Column('designation', sa.String(), nullable=True))
    op.add_column('staff_user', sa.Column('speciality', sa.String(), nullable=True))
    op.add_column('staff_user', sa.Column('profile_pic', sa.String(), nullable=True))
    op.add_column('staff_user', sa.Column('qualification', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('staff_user', 'qualification')
    op.drop_column('staff_user', 'profile_pic')
    op.drop_column('staff_user', 'speciality')
    op.drop_column('staff_user', 'designation')
    # ### end Alembic commands ###
