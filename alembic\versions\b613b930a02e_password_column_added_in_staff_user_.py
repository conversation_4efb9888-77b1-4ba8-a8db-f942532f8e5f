"""password column added in staff user model

Revision ID: b613b930a02e
Revises: cc27cc63459c
Create Date: 2023-10-12 06:02:48.420041

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b613b930a02e'
down_revision = 'cc27cc63459c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('staff_user', sa.Column('password', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('staff_user', 'password')
    # ### end Alembic commands ###
