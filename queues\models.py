import enum
from sqlalchemy import Big<PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON><PERSON><PERSON>, Enum, Foreign<PERSON>ey, SmallInteger, text,Text, String, Time, UniqueConstraint, Integer, Numeric, Boolean, Date, Float, case, ARRAY
from sqlalchemy.sql import func
import strawberry
from sqlalchemy.orm import relationship
from database.db_conf import Base
from user.models import StatusEnum, User
from staff_user.models import StaffUser
from bill.models import ServiceStatusEnum, UserService
@strawberry.enum
class UserQueueStatusEnum(enum.Enum):
    CHECKIN = "CHECKIN"
    ENTRY = "ENTRY"
    EXIT = "EXIT"
    HOLD = "HOLD"
    ARRIVED = "ARRIVED"
    PURGED = "PURGED"
    FREEZED = "FREEZED"
    PAUSED = "PAUSED"
    
@strawberry.enum
class QueueTypeEnum(enum.Enum):
    MANUAL = "MANUAL"
    AUTO = "AUTO"
    SEMIAUTO = "SEMIAUTO"
    
@strawberry.enum
class UserQueuePreCheckStatusEnum(enum.Enum):
    PENDING = "PENDING"
    COMPLETED = "COMPLETED"
    
@strawberry.enum
class QueueCounterStatusEnum(enum.Enum):
    ALLOTED = "ALLOTED"
    UNALLOTED = "UNALLOTED"
    INACTIVE = "INACTIVE"

@strawberry.enum
class UserQueueStepStatusEnum(enum.Enum):
    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    CANCELLED = "CANCELLED"
    HOLD = "HOLD"
    SKIPPED = "SKIPPED"

@strawberry.enum
class UserPreReqStatusEnum(enum.Enum):
    HOLD = "HOLD"
    WAITING = "WAITING"
    IGNORE = "IGNORE"

@strawberry.enum
class LocationBedStatusEnum(enum.Enum):
    ALLOTED = "ALLOTED"
    AVAILABLE = "AVAILABLE"
    OCCUPIED = "OCCUPIED"
    UNAVAILABLE = "UNAVAILABLE"


@strawberry.enum
class PatientTypeEnum(enum.Enum):
    OP = "OP"
    IP = "IP"
    DAY_CARE = "DAY_CARE"

class Tag(Base):
    __tablename__ = 'tag'
    __table_args__ = {'schema': "queue"}
   
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String)
    code = Column(String, unique=True)
    rfid_code = Column(String, unique=True)
    status = Column(Enum(StatusEnum),default=StatusEnum.ACTIVE)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    def __repr__(self) -> str:
        return "<Tag %r>" % self.id

class Location(Base):
    __tablename__ = 'location'
    __table_args__ = {'schema': "queue"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String)
    code = Column(String, unique=True)
    priority = Column(Integer)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True),  onupdate=func.now())
    parent_location_id = Column(Integer, ForeignKey("queue.location.id",name="location_parent_location_id_fk"))
    status = Column(Enum(LocationBedStatusEnum))
    alloted_count = Column(Integer)
    occupied_count = Column(Integer)
    iot_code = Column(String)
    total_count = Column(Integer)

    queue_step = relationship("QueueStep",secondary="queue.rel_queue_step_location", viewonly=True,
                             )
    child_locations = relationship("Location", foreign_keys=[parent_location_id])
    
    # Relationship to represent the parent location for a child record
    parent_location = relationship("Location", remote_side=[id],overlaps="child_locations")

    
    def __repr__(self) -> str:
        return "<Location %r>" % self.id

class QueueWeightageAction(Base):
    __tablename__ = 'queue_weightage_action'
    __table_args__ = {'schema': "queue"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String)
    code = Column(String)
    weightage = Column(Integer)
    status = Column(Enum(StatusEnum),default=StatusEnum.ACTIVE)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    def __repr__(self) -> str:
        return "<QueueWeightageAction %r>" % self.id
    
class Queue(Base):
    __tablename__ = "queue"
    __table_args__ = {'schema': "queue"}

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    queue_name = Column(String,unique=True)
    queue_code = Column(String)
    avg_procedure_time = Column(Float)
    cluster_id = Column(Integer, ForeignKey(
        "queue.cluster.id", name="queue_cluster_id_fk"))
    upcoming_patients = Column(Integer)
    service_type = Column(String)
    status = Column(Enum(StatusEnum),
                    default=StatusEnum.ACTIVE, nullable=False)
    created_by = Column(Integer, ForeignKey(
        "staff_user.id", name="queue_created_by_fk"))
    updated_by = Column(Integer, ForeignKey(
        "staff_user.id", name="queue_updated_by_fk"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    deleted_at = Column(DateTime(timezone=True),nullable=True)
    last_token_called_at = Column(DateTime(timezone=True))
    
    show_patient_name = Column(Boolean,nullable=True)
    capacity = Column(Integer)
    total_count = Column(Integer,server_default=text("0"))
    ongoing_count = Column(Integer,server_default=text("0"))
    completed_count = Column(Integer,server_default=text("0"))
    cancelled_count = Column(Integer,server_default=text("0"))
    freezed_count= Column(Integer,server_default=text("0"))
    buffer_time= Column(Float)
    deviation_rate= Column(Float,server_default=text("1"))
    queue_type = Column(Enum(QueueTypeEnum),nullable=True, default=QueueTypeEnum.AUTO) 
    allocate_counter_at = Column(Enum(UserQueueStatusEnum),nullable=True) 
    waiting_capacity = Column(Integer)
    pseudo_capacity = Column(Integer,server_default=text("0"),nullable=True)
    pre_check_req = Column(Boolean,nullable=True)
    assignment = Column(ARRAY(String))
    latest_token_id = Column(Integer,ForeignKey(
        "queue.user_token.id", name="queue_latest_token_id_fk"))
    created_by_obj= relationship('StaffUser', foreign_keys=[created_by])
    updated_by_obj= relationship('StaffUser', foreign_keys=[updated_by])
    cluster = relationship("Cluster")
    services = relationship("Service",secondary="queue.rel_queue_service", viewonly=True,
                             )

    staff_users = relationship(
        "StaffUser",
        secondary="queue.rel_staff_user_queue",
        back_populates="queues",
    )
    counters = relationship("QueueCounter",back_populates='queue')
    queue_step = relationship("QueueStep",back_populates='queue', order_by="asc(QueueStep.priority), asc(QueueStep.id)")
    user_token = relationship("UserToken")
    
    __table__args__= (UniqueConstraint('queue_name',name='queue_queue_name'),)

    def __repr__(self) -> str:
        return "<Queue %r>" % self.id

class QueueStep(Base):
    __tablename__ = 'queue_step'
    __table_args__ = {'schema': "queue"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String)
    code = Column(String)
    priority = Column(Integer)
    avg_step_time = Column(Numeric)
    queue_id = Column(Integer, ForeignKey(
        "queue.queue.id", name="queue_step_queue_id_fk"), index=True)
    checkin_status = Column(Enum(UserQueueStatusEnum))
    checkout_status = Column(Enum(UserQueueStatusEnum))
    created_by = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    checkin_name = Column(String)
    checkout_name = Column(String)
    checkin_description = Column(String)
    checkout_description = Column(String)
    queue = relationship("Queue")
    # locations = relationship("QueueStepLocation",back_populates='queue_step')
    locations = relationship("Location",secondary="queue.rel_queue_step_location", viewonly=True,
                             )
    def __repr__(self) -> str:
        return "<QueueStep %r>" % self.id
    
class RelStaffUserQueue(Base):
    __tablename__ = "rel_staff_user_queue"
    __table_args__ = {'schema': "queue"}

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    staff_user_id = Column(Integer, ForeignKey(
        "staff_user.id", name="rel_staff_user_queue_staff_user_id_fk"))
    queue_id = Column(Integer, ForeignKey(
        "queue.queue.id", name="rel_staff_user_queue_queue_id_fk"))

    def __repr__(self) -> str:
        return "<RelStaffUserQueue %r>" % self.id


class QueueCounter(Base):
    __tablename__ = "queue_counter"
    __table_args__ = {'schema': "queue"}

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    queue_id = Column(Integer, ForeignKey(
        "queue.queue.id", name="rel_queue_counter_queue_id_fk"))
    counter = Column(Integer)
    counter_name = Column(String)
    counter_code = Column(String)
    doctor_image_name = Column(String)
    status = Column(Enum(QueueCounterStatusEnum),
                    default=QueueCounterStatusEnum.UNALLOTED, nullable=False)
    freeze_count = Column(Integer)
    upcoming_capacity = Column(Integer)
    priority = Column(Numeric, server_default=text("1"),nullable=False)
    counter_status = Column(Enum(StatusEnum),server_default=StatusEnum.ACTIVE.name,nullable=True)

    user_queue = relationship("UserQueue")
    # user_queue = relationship("UserQueue", primaryjoin="and_(QueueCounter.id == UserQueue.counter, UserQueue.status.in_(['FREEZED', 'ENTRY']))",
    #                           order_by=case(value=UserQueue.status,whens={'ENTRY': 0}, else_=1))
    queue = relationship("Queue",back_populates='counters')
    def __repr__(self) -> str:
        return "<QueueCounter %r>" % self.id

class UserQueue(Base):
    __tablename__ = 'user_queue'
    __table_args__ = {'schema': "queue"}

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    queue_id = Column(Integer, ForeignKey(
        "queue.queue.id", name="user_queue_queue_id_fk"), index=True)
    user_id = Column(Integer, ForeignKey(
        "user.id", name="user_queue_user_id_fk"))
    token_no = Column(String)
    doctor_name = Column(String,nullable=True)
    patient_type = Column(Enum(PatientTypeEnum),nullable=True)
    token_id = Column(Integer, ForeignKey(
        "queue.user_token.id", name="user_queue_user_token_id_fk"))
    date = Column(Date,index=True)
    status = Column(Enum(UserQueueStatusEnum), nullable=False,index=True)
    pre_check_status = Column(Enum(UserQueuePreCheckStatusEnum))
    weightage_id = Column(Integer, ForeignKey(
        "queue.queue_weightage_action.id", name="user_queue_weightage_id_fk"))
    start_time = Column(DateTime(timezone=True))
    estimated_time=Column(DateTime(timezone=True))
    end_time = Column(DateTime(timezone=True))
    created_by = Column(Integer, ForeignKey(
        "staff_user.id", name="user_queue_created_by_fk"))
    updated_by = Column(Integer, ForeignKey(
        "staff_user.id", name="user_queue_updated_by_fk"))
    allocate_counter_at = Column(Enum(UserQueueStatusEnum),nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    freezed_at = Column(DateTime(timezone=True))
    arrived_at = Column(DateTime(timezone=True))
    force_exit = Column(Boolean,nullable=True)
    counter = Column(Integer, ForeignKey(
        "queue.queue_counter.id", name ="user_queue_counter_fk"), nullable=True
    )
    queue_step_id = Column(Integer, ForeignKey(
        "queue.queue_step.id", name="user_queue_queue_step_id_fk"), nullable=True)
    location_id = Column(Integer, ForeignKey(
        "queue.location.id", name="user_queue_location_id_fk"), nullable=True)
    next_location_id = Column(Integer, ForeignKey(
        "queue.location.id", name="user_queue_next_location_id_fk"), nullable=True)
    step_start_time = Column(DateTime(timezone=True))
    next_step_start_time = Column(DateTime(timezone=True))
    tag_id = Column(Integer, ForeignKey(
        "queue.tag.id", name="user_queue_tag_id_fk"), nullable=True)
    prerequisites_conditions = Column(ARRAY(String),nullable=True)
    vitals_datetime = Column(DateTime(timezone=True))
    phy_ass_datetime = Column(DateTime(timezone=True))
    phlebotomy_precheck_datetime = Column(DateTime(timezone=True))
    appointment_date_time= Column(DateTime(timezone=True))
    order_by_date= Column(DateTime(timezone=True))
    remarks = Column(Text)
    # user_token =relationship('UserToken')
    queue = relationship('Queue')
    user = relationship('User',foreign_keys=[user_id])
    queue_weightage_action = relationship('QueueWeightageAction')
    counter_obj = relationship('QueueCounter', foreign_keys=[counter],back_populates='user_queue')
    user_service = relationship("UserService",primaryjoin='UserQueue.token_id == foreign(UserService.token_id)',overlaps="user_services,user_token")
    user_queue_step = relationship("UserQueueStep",back_populates='user_queue')
    location = relationship('Location', foreign_keys=[location_id])
    tag = relationship("Tag")
    queue_step = relationship("QueueStep")

    @property
    def all_prerequisites(self):
        conditions = []
        for us in self.user_service:
            if us.service and us.service.prerequisites_conditions:
                conditions.extend(us.service.prerequisites_conditions)
        return list(set(conditions))

    def __repr__(self) -> str:
        return "<UserQueue %r>" % self.id
    
class RelUserServiceQueue(Base):
    __tablename__ = "rel_user_service_queue"
    __table_args__ = {'schema': "queue"}
        
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_service_id = Column(Integer, ForeignKey(
        "queue.user_service.id", name="rel_user_service_queue_user_service_id_fk"))
    queue_id = Column(Integer, ForeignKey(
        "queue.queue.id", name="rel_user_service_queue_queue_id_fk"))
    user_queue_id = Column(Integer, ForeignKey(
        "queue.user_queue.id", name="rel_user_service_queue_user_queue_id_fk"))
    status = Column(Enum(ServiceStatusEnum,default=ServiceStatusEnum.PENDING))
    user_service = relationship("UserService")
    
    def __repr__(self) -> str:
        return "<RelUserServiceQueue %r>" % self.id



class Cluster(Base):
    __tablename__ = 'cluster'
    __table_args__ = {'schema': "queue"}

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    tower = Column(String)
    floor = Column(String)
    cluster = Column(String)
    status = Column(Enum(StatusEnum),
                    default=StatusEnum.ACTIVE, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self) -> str:
        return "<Cluster %r>" % self.id



class QueueAuditLogs(Base):
    __tablename__ = 'queue_audit_logs'
    __table_args__ = {'schema': "queue"}

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    queue_code=Column(String)
    data = Column(Text)
    action = Column(String)
    created_by = Column(Integer, ForeignKey(
        "staff_user.id", name="queue_audit_logs_created_by_fk"))

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by_obj= relationship('StaffUser', foreign_keys=[created_by])
    def __repr__(self) -> str:
        return "<QueueAuditLogs %r>" % self.id
    

class RelQueueService(Base):
    __tablename__ = 'rel_queue_service'
    __table_args__ = {'schema': "queue"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    test_id = Column(Integer, ForeignKey(
        "service.id", name="rel_queue_service_test_id_fk"))
    queue_id = Column(Integer, ForeignKey(
        "queue.queue.id", name="rel_queue_service_queue_id_fk"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    queue = relationship('Queue')
    service = relationship('Service')
    def __repr__(self) -> str:
        return "<RelQueueService %r>" % self.id
    
class RelDeviceQueue(Base):
    __tablename__ = 'rel_device_queue'
    __table_args__ = {'schema': "queue"}

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    device_id = Column(Integer, ForeignKey(
        "device.id", name="rel_device_queue_device_id_fk"))
    queue_id = Column(Integer)
    is_updated = Column(Boolean)
    subscription_name = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self) -> str:
        return "<RelDeviceQueue %r>" % self.id
    

class UserServicePrerequisite(Base):
    __tablename__ = 'user_service_prerequisite'
    __table_args__ = {'schema': "queue"}

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_service_id = Column(Integer, ForeignKey(
        "queue.user_service.id", name="user_service_prerequisite_user_service_id_fk"))
    pre_req_user_service_id = Column(Integer, ForeignKey(
        "queue.user_service.id", name="user_service_prerequisite_pre_req_user_service_id_fk"))
    status = Column(Enum(UserPreReqStatusEnum),
                    default=UserPreReqStatusEnum.HOLD, nullable=False)
    un_hold_time = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now()) 
    
    def __repr__(self) -> str:
        return "<UserServicePrerequisite %r>" % self.id

class UserQueueLogs(Base):
    __tablename__ = 'user_queue_logs'
    __table_args__ = {'schema': "queue"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_queue_id = Column(Integer, ForeignKey(
        "queue.user_queue.id", name="user_queue_logs_user_queue_id_fk"), index=True)
    status = Column(Enum(UserQueueStatusEnum))
    pre_check_status = Column(Enum(UserQueuePreCheckStatusEnum))
    weightage_id = Column(Integer, ForeignKey(
        "queue.queue_weightage_action.id", name="user_queue_weightage_id_fk"))
    counter = Column(Integer, ForeignKey(
        "queue.queue_counter.id", name ="user_queue_counter_fk"), nullable=True
    )
    created_by = Column(Integer, ForeignKey(
        "staff_user.id", name="user_queue_created_by_fk"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    remarks= Column(Text)
    queue_weightage_action = relationship('QueueWeightageAction')
    user_queue = relationship("UserQueue")
    queue_counter = relationship("QueueCounter")
    created_by_obj= relationship('StaffUser', foreign_keys=[created_by])
    def __repr__(self) -> str:
        return "<UserQueueLogs %r>" % self.id


class UserQueueStep(Base):
    __tablename__ = 'user_queue_step'
    __table_args__ = {'schema': "queue"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    queue_step_id = Column(Integer, ForeignKey(
        "queue.queue_step.id", name="user_queue_step_queue_step_id_fk"))
    user_queue_id = Column(Integer, ForeignKey(
        "queue.user_queue.id", name="user_queue_step_user_queue_id_fk"), index=True)
    location_id = Column(Integer, ForeignKey(
        "queue.location.id", name="user_queue_step_location_id_fk"))
    remarks = Column(Text)
    status = Column(Enum(UserQueueStepStatusEnum))
    action_type = Column(String)
    created_by = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_by = Column(String)
    updated_at = Column(DateTime(timezone=True),  onupdate=func.now())
    created_location = Column(String)
    description= Column(String)
    tag_id = Column(Integer, ForeignKey(
        "queue.tag.id", name="user_queue_tag_id_fk"), nullable=True)
    
    user_queue = relationship("UserQueue")
    queue_step = relationship("QueueStep")
    location = relationship("Location") 
    tag = relationship("Tag")

        
    def __repr__(self) -> str:
        return "<UserQueueStep %r>" % self.id
    

class RelQueueStepLocation(Base):
    __tablename__ = 'rel_queue_step_location'
    __table_args__ = {'schema': "queue"}
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    location_id = Column(Integer, ForeignKey(
        "queue.location.id", name="rel_queue_step_location_location_id_fk"))
    queue_step_id = Column(Integer, ForeignKey(
        "queue.queue_step.id", name="rel_queue_step_location_queue_step_id_fk"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True),  onupdate=func.now())
    
    
    def __repr__(self) -> str:
        return "<RelQueueStepLocation %r>" % self.id
    
class ErrorLog(Base):
    __tablename__ = "error_logs"
    
    id = Column(BigInteger, primary_key=True, index=True)
    endpoint = Column(String, nullable=False)
    body = Column(String)
    status_code = Column(Integer, nullable=False)
    response = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True),  onupdate=func.now())
    
    def __repr__(self):
        return f"<ErrorLog(id={self.id}, endpoint='{self.endpoint}', status_code={self.status_code})>"