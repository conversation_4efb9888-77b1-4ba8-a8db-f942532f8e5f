"""columns added in visa mdodels

Revision ID: fb670fa7b157
Revises: 9ca16d99ad7d
Create Date: 2023-11-27 11:14:09.649448

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql



# revision identifiers, used by Alembic.
revision = 'fb670fa7b157'
down_revision = '9ca16d99ad7d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('country', sa.Column('subject', sa.Text(), nullable=True))
    op.add_column('country', sa.Column('sub_body_part_1', sa.Text(), nullable=True))
    op.add_column('country', sa.Column('sub_body_part_2', sa.Text(), nullable=True))
    op.add_column('country', sa.Column('sub_body_part_3', sa.Text(), nullable=True))
    op.add_column('country', sa.Column('conclusion', sa.Text(), nullable=True))
    op.add_column('country', sa.Column('note', sa.Text(), nullable=True))
    op.add_column('user_visa_data', sa.Column('embassy', sa.String(), nullable=True))
    pdf_generation_enum = postgresql.ENUM('PENDING', 'GENERATED', name='pdfgenerationenum')
    pdf_generation_enum.create(op.get_bind(), checkfirst=True)
    op.add_column('user_visa_data', sa.Column('status', pdf_generation_enum, nullable=True))
    op.add_column('user_visa_data', sa.Column('appointment_date', sa.Date(), nullable=True))
    op.add_column('user_visa_data', sa.Column('appointment_slot', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_visa_data', 'appointment_slot')
    op.drop_column('user_visa_data', 'appointment_date')
    op.drop_column('user_visa_data', 'status')
    op.drop_column('user_visa_data', 'embassy')
    op.drop_column('country', 'note')
    op.drop_column('country', 'conclusion')
    op.drop_column('country', 'sub_body_part_3')
    op.drop_column('country', 'sub_body_part_2')
    op.drop_column('country', 'sub_body_part_1')
    op.drop_column('country', 'subject')
    # ### end Alembic commands ###
