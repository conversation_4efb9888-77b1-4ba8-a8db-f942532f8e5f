"""columns added in user_queue, rel_user_queueu_service and added user_queue_logs model

Revision ID: 85512adafa60
Revises: 1ad66cd34438
Create Date: 2024-02-09 07:08:57.616433

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '85512adafa60'
down_revision = '1ad66cd34438'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_queue_logs',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('user_queue_id', sa.Integer(), nullable=True),
    sa.Column('status', postgresql.ENUM('CHECKIN', 'ENTRY', 'EXIT', 'HOLD', 'ARRIVED', 'PURGED', 'FREEZED', name='userqueuestatusenum',create_type=False), nullable=True),
    sa.Column('pre_check_status',postgresql.ENUM('PENDING', 'COMPLETED', name='userqueueprecheckstatusenum',create_type=False), nullable=True),
    sa.Column('weightage_id', sa.Integer(), nullable=True),
    sa.Column('counter', sa.Integer(), nullable=True),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['counter'], ['queue_counter.id'], name='user_queue_counter_fk'),
    sa.ForeignKeyConstraint(['user_queue_id'], ['user_queue.id'], name='user_queue_logs_user_queue_id_fk'),
    sa.ForeignKeyConstraint(['weightage_id'], ['queue_weightage_action.id'], name='user_queue_weightage_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_queue_logs_user_queue_id'), 'user_queue_logs', ['user_queue_id'], unique=False)
    op.add_column('rel_user_service_queue', sa.Column('status', postgresql.ENUM('PENDING', 'ON_PROGRESS', 'COMPLETED', 'CANCELLED', name='servicestatusenum',create_type=False), nullable=True))
    op.add_column('user_queue', sa.Column('allocate_counter_at', postgresql.ENUM('CHECKIN', 'ENTRY', 'EXIT', 'HOLD', 'ARRIVED', 'PURGED', 'FREEZED', name='userqueuestatusenum',create_type=False), nullable=True))
    op.drop_constraint('user_queue_created_by_fk', 'user_queue', type_='foreignkey')
    op.drop_constraint('user_queue_updated_by_fk', 'user_queue', type_='foreignkey')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key('user_queue_updated_by_fk', 'user_queue', 'staff_user', ['updated_by'], ['id'])
    op.create_foreign_key('user_queue_created_by_fk', 'user_queue', 'user', ['created_by'], ['id'])
    op.drop_column('user_queue', 'allocate_counter_at')
    op.drop_column('rel_user_service_queue', 'status')
    op.drop_index(op.f('ix_user_queue_logs_user_queue_id'), table_name='user_queue_logs')
    op.drop_table('user_queue_logs')
    # ### end Alembic commands ###
