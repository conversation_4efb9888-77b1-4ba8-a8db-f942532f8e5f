import enum
from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateT<PERSON>, Enum, <PERSON><PERSON>ey, String, Numeric, Text, Integer, Date, ARRAY
from sqlalchemy.sql import func
import strawberry
from database.db_conf import Base
from user.models import StatusEnum
from sqlalchemy.orm import relationship
from operation.models import Operation
@strawberry.enum
class BedRequestType(enum.Enum):
    ADMISSION = "ADMISSION"
    TRANSFER = "TRANSFER"
    
@strawberry.enum
class RequestStatus(enum.Enum):
    REQUESTED = "REQUESTED"
    ALLOTED = "ALLOTED"
    CANCELLED = "CANCELLED"
    TRANSFERED = "TRANSFERED"
    ADMITTED = "ADMITTED"
    HOLD = "HOLD"
    VACATED = "VACATED"
    PENDING = "PENDING"

@strawberry.enum
class CaseTypeEnum(enum.Enum):
    INSURANCE = "INSURANCE"
    CASH = "CASH"
    COORPORATE_CREDIT = "COORPORATE_CREDIT"
    
class PriorityEnum(enum.Enum):
    REGULAR = "REGULAR"
    HIGH = "HIGH"
    URGENT = "URGENT"

@strawberry.enum
class RemarksTypeEnum(enum.Enum):
    CANCELLATION = "CANCELLATION"
    QUEUE_PURGE = "QUEUE_PURGE"
    QUEUE_HOLD = "QUEUE_HOLD"

class HashTagTypeENum(enum.Enum):
    BED360 = "BED360"

class BedRemarks(Base):
    __tablename__ = "bed_remarks"
    
    id = Column(BigInteger,primary_key=True, autoincrement=True)
    bed_request_id = Column(BigInteger, ForeignKey("bed_request.id", name="bed_remarks_bed_request_id_fk"))
    remarks = Column(Text)
    user_role = Column(String)
    created_by = Column(String, nullable=False)
    status = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self) -> str:
        return "<BedRemarks %r>" % self.id
    
class BedRequest(Base):
    __tablename__ = "bed_request"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    type = Column(Enum(BedRequestType) , nullable = False, index=True)
    wl_no = Column(String(length=10) , nullable = False)
    uhid = Column(String(length=20) , nullable = False)
    case_type = Column(Enum(CaseTypeEnum))
    patient_category= Column(String)
    user_contact_no = Column(String(length=10))
    patient_name = Column(String)
    department = Column(String)
    doctor_name = Column(String)
    requested_bed_class= Column(String)
    req_from= Column(String)
    estimation = Column(String)
    bed_no =Column(String)
    bed_status = Column(String)
    alloted_time = Column(DateTime(timezone=True))
    admission_status= Column(String)
    shifting_time = Column(DateTime(timezone=True))
    informed_to = Column(String)
    status= Column(Enum(RequestStatus), default=RequestStatus.REQUESTED)
    referred_by = Column(String)
    source = Column(String)
    rate_of_contract = Column(String)
    priority = Column(Enum(PriorityEnum))
    admission_no = Column(String)
    created_by = Column(String, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    alloted_bed_class = Column(String)
    billing_bed_class = Column(String)
    reason = Column(String)
    floor = Column(String, nullable=True)
    completed_at = Column(DateTime(timezone=True))
    mismatch_duration = Column(Integer)
    completed_status = Column(Boolean, default=False, index= True)
    cancellation_remarks = Column(String, nullable=True)
    estimated_by = Column(String, nullable=True)
    discharged_time = Column(DateTime(timezone=True),nullable=True)
    tentative_admission_date = Column(Date)
    nurse_station = Column(String,nullable=True)
    alloted_by =  Column(String, nullable = True)
    supporting_care_services = Column(ARRAY(String))
    
    bed_remarks = relationship("BedRemarks",order_by='desc(BedRemarks.created_at)')
    bed_hash_tags = relationship("MasterHashTags",secondary="rel_bed_request_hash_tag", viewonly=True,)
    def __repr__(self) -> str:
        return "<BedRequest %r>" % self.id
    
class ExcluedBeds(Base):
    __tablename__ = 'exclude_beds'

    id = Column(BigInteger, primary_key=True, autoincrement =True)
    bed_no = Column(String)
    tower = Column(String)
    floor = Column(String)
    bed_class = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    def __repr__(self) -> str:
        return "<ExcluedBeds %r>" % self.id

class CommonRemarks(Base):
    __tablename__ = 'common_remarks'
    id = Column(BigInteger, primary_key=True, autoincrement =True)
    name = Column(String)
    code = Column(String)
    status = Column(String)
    type = Column(Enum(RemarksTypeEnum),default=RemarksTypeEnum.CANCELLATION)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    def __repr__(self) -> str:
        return "<CommonRemarks %r>" % self.id

class DischargeHistory(Base):
    __tablename__ = "discharge_history"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    uhid = Column(String)
    ip_number = Column(BigInteger)
    patient_name= Column(String)
    bed = Column(String)
    admission_date_time = Column(DateTime(timezone=True))
    requested_discharge_date = Column(DateTime(timezone=True))
    nurse_clearance_date_time = Column(DateTime(timezone=True))
    blood_bank_clearance_date_time =Column(DateTime(timezone=True))
    discharge_summary_date_time = Column(DateTime(timezone=True))
    fnb_clearance_date_time = Column(DateTime(timezone=True))
    pharmacy_clearance_date_time =Column(DateTime(timezone=True))
    audit_clrsave_date_time = Column(DateTime(timezone=True))
    billing_ack_date_time = Column(DateTime(timezone=True))
    bill_ready_date_time = Column(DateTime(timezone=True))
    clinical_discharge = Column(DateTime(timezone=True))
    otc_clearance_date_time = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    completion_status = Column(Boolean,default=False,index=True)
    discharge_time = Column(DateTime(timezone=True))

    def __repr__(self) -> str:
        return "<DischargeHistory %r>" % self.id
    
class BedStatusHistory(Base):
    __tablename__ = "bed_status_history"

    id = Column(Integer, primary_key=True, autoincrement=True)
    his_id = Column(Integer)
    bed_no = Column(String)
    bed_class = Column(String)
    bed_status = Column(String)
    nurse_station = Column(String)
    uhid = Column(String)
    ip_number = Column(Integer)
    patient_name = Column(String)
    speciality = Column(String)
    primary_consultant = Column(String)
    secondary_consultant = Column(String)
    contact_number = Column(String)
    req_bed_type = Column(String)
    billable_bed_type = Column(String)
    date = Column(Date, index=True)

class MasterHashTags(Base):
    __tablename__ = "master_hash_tags"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name=Column(String)
    code=Column(String,unique=True)
    status = Column(Enum(StatusEnum))
    type = Column(Enum(HashTagTypeENum))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self) -> str:
        return "<MasterHashTags %r>" % self.id
class RelBedRequestHashTag(Base):
    __tablename__ = "rel_bed_request_hash_tag"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    hash_tag_id = Column(Integer,ForeignKey("master_hash_tags.id",name="rel_bed_request_hash_tag_hash_tag_id_fk"))
    bed_request_id = Column(Integer, ForeignKey("bed_request.id", name="rel_bed_request_hash_tag_bed_request_id_fk"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    def __repr__(self) -> str:
        return "<RelBedRequestHashTag %r>" % self.id
    
class SupportinCareService(Base):
    __tablename__ = "supportin_care_service"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String)
    code = Column(String)
    status = Column(Enum(StatusEnum), default=StatusEnum.ACTIVE, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    created_by = Column(String)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    updated_by = Column(String)

    def __repr__(self) -> str:
        return "<SupportinCareService %r>" % self.id