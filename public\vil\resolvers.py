from sqlalchemy import func
from sqlalchemy.orm import Session
import os, logging
from visa_letter.models import UserVisaScannedDetails as UserVisaScannedDetailsModel, UserVisaData as UserVisaDataModel
logger = logging.getLogger()

def add_scanned_details(db:Session, reference_id:str, status:str):
    try:
        scanned_data = UserVisaScannedDetailsModel(
            reference_id = reference_id,
            status = status,
            scanned_at = func.now()
        )
        db.add(scanned_data)
        db.commit()
    except Exception as e:
        logger.exception(e)
        
def get_user_visa_info(db: Session, reference_id: str):
    data =  db.query(UserVisaDataModel).filter(UserVisaDataModel.reference_id == reference_id).first()
    return data
