"""Columns added in user type model

Revision ID: d4a8fae6a2ce
Revises: 0951751c0877
Create Date: 2024-02-01 08:56:07.159387

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd4a8fae6a2ce'
down_revision = '0951751c0877'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_type', sa.Column('created_by', sa.String(), nullable=True))
    op.add_column('user_type', sa.Column('updated_by', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_type', 'updated_by')
    op.drop_column('user_type', 'created_by')
    # ### end Alembic commands ###
