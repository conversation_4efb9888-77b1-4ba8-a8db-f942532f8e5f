"""bms table added

Revision ID: c990c8fc2f36
Revises: 33aecb7c6533
Create Date: 2023-10-20 05:53:15.694853

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c990c8fc2f36'
down_revision = '33aecb7c6533'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('bed_request',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('type', sa.Enum('ADMISSION', 'TRANSFER', name='bedrequesttype'), nullable=False),
    sa.Column('wl_no', sa.String(length=10), nullable=False),
    sa.Column('uhid', sa.String(length=20), nullable=False),
    sa.Column('case_type', sa.String(), nullable=True),
    sa.Column('patient_category', sa.String(), nullable=True),
    sa.Column('user_contact_no', sa.String(length=10), nullable=True),
    sa.Column('patient_name', sa.String(), nullable=True),
    sa.Column('department', sa.String(), nullable=True),
    sa.Column('doctor_name', sa.String(), nullable=True),
    sa.Column('bed_class', sa.String(), nullable=True),
    sa.Column('req_from', sa.String(), nullable=True),
    sa.Column('estimation', sa.String(), nullable=True),
    sa.Column('remarks', sa.Text(), nullable=True),
    sa.Column('bed_no', sa.String(), nullable=True),
    sa.Column('bed_status', sa.String(), nullable=True),
    sa.Column('alloted_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('admission_status', sa.String(), nullable=True),
    sa.Column('shifting_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('informed_to', sa.String(), nullable=True),
    sa.Column('bed_ops_remarks', sa.Text(), nullable=True),
    sa.Column('status', sa.Enum('REQUESTED', 'ALLOTED', 'CANCELLED', name='requeststatus'), nullable=True),
    sa.Column('created_by', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_bed_request_type'), 'bed_request', ['type'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_bed_request_type'), table_name='bed_request')
    op.drop_table('bed_request')
    # ### end Alembic commands ###
