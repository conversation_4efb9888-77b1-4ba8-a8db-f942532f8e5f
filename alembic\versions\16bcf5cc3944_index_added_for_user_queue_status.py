"""index added for user queue status

Revision ID: 16bcf5cc3944
Revises: 960143006727
Create Date: 2024-01-19 00:54:30.816932

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '16bcf5cc3944'
down_revision = '960143006727'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_user_queue_status'), 'user_queue', ['status'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_queue_status'), table_name='user_queue')
    # ### end Alembic commands ###
