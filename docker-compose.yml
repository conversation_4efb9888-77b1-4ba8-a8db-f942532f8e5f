version: "3.8"

services:

  # opd_db:
  #   container_name: opd_db
  #   image: postgres
  #   restart: always
  #   ports:
  #     - 5433:5432
  #   env_file:
  #     - .env
  #   environment:
  #     - POSTGRES_USER=${DB_USER}
  #     - POSTGRES_PASSWORD=${DB_PASSWORD}
  #     - POSTGRES_DB=${DB_NAME}
  #   volumes:
  #     - ../data/opd_db:/var/lib/postgresql/data

  opd_redis:
    container_name: opd_redis
    image: redis:6.2-alpine
    ports:
      - 6240:6379
    env_file:
      - .env
    volumes:
      - ${DOCKER_VOLUME}/volumes/opd_redis:/data
    command: ["redis-server", "--requirepass", "${REDIS_PASSWORD}", "--appendonly", "yes"]
    

  opd_app:
    container_name: opd_app
    build: .
    command: bash -c "uvicorn main:app --host 0.0.0.0 --port 8001 --reload"
    volumes:
      - .:/opd_app
      - ../data/visa_letter:/opd_app/data/visa_letter
    ports:
      - 8001:8001
    env_file:
      - .env
    restart: always
    depends_on:
      # - opd_db
      - opd_redis
  
  public_opd_app:
    container_name: public_opd_app
    build: .
    command: bash -c "hypercorn public_main:app --keyfile-password=Aigh@123 --bind 0.0.0.0:443 --insecure-bind 0.0.0.0:8145 --workers 1 --keyfile=public_key.pem --certfile=public_cert.pem"
    volumes:
      - .:/public_opd_app
    ports:
      - 8145:8145
    env_file:
      - .env
    restart: always
    depends_on:
      # - opd_db
      - opd_redis

  opd_celery_worker:
    container_name: opd_celery_worker
    command: celery -A celery_worker worker --loglevel=info
    build: 
      context: .
    restart: always
    env_file:
      - .env
    environment:
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
    depends_on:
      - opd_app
      - opd_redis
  # opd_flower:
  #   container_name: opd_flower
  #   restart: always
  #   build: .
  #   command: celery -A celery_worker flower --port=5557 --basic_auth=admin:admin --persistent=True
  #   volumes:
  #     - ../data/opd_app/flower:/data
  #   ports:
  #     - 5558:5557
  #   environment:
  #     - CELERY_BROKER_URL=${CELERY_BROKER_URL}
  #     - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
  #   env_file:
  #     - .env
  #   depends_on:
  #     - opd_celery_worker
    
  opd_celery_beat:
    container_name: opd_celery_beat
    build: 
      context: .
    depends_on:
      - opd_celery_worker
    env_file:
      - .env
    environment:
        - CELERY_BROKER_URL=${CELERY_BROKER_URL}
        - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
    command: celery -A celery_worker beat -l INFO --scheduler celery_sqlalchemy_scheduler.schedulers:DatabaseScheduler
    