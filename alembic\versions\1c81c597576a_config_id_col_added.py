"""config id col added

Revision ID: 1c81c597576a
Revises: 2860694ef89b
Create Date: 2025-06-30 15:16:56.109133

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '1c81c597576a'
down_revision = '2860694ef89b'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('assessment_category', sa.Column('config_id', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('assessment_category', 'config_id')
    # ### end Alembic commands ###
