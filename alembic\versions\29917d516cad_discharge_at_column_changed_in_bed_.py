"""discharge at  column changed in bed request model

Revision ID: 29917d516cad
Revises: 9851b33e7155
Create Date: 2024-01-10 05:50:05.073072

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '29917d516cad'
down_revision = '9851b33e7155'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bed_request', sa.Column('discharged_time', sa.DateTime(timezone=True), nullable=True))
    op.drop_column('bed_request', 'discharged_date')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bed_request', sa.Column('discharged_date', sa.DATE(), autoincrement=False, nullable=True))
    op.drop_column('bed_request', 'discharged_time')
    # ### end Alembic commands ###
