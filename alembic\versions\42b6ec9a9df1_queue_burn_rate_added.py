"""queue burn_rate added

Revision ID: 42b6ec9a9df1
Revises: 807cf7d76232
Create Date: 2023-10-16 05:42:43.520472

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '42b6ec9a9df1'
down_revision = '807cf7d76232'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('queue', sa.Column('deviation_rate', sa.Float(), server_default=sa.text('1'), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('queue', 'deviation_rate')
    # ### end Alembic commands ###
