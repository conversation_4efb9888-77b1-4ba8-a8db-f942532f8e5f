"""enum updated

Revision ID: b275754c29d2
Revises: ad447b0bdae9
Create Date: 2025-03-24 13:23:46.767035

"""
from alembic import op


# revision identifiers, used by Alembic.
revision = 'b275754c29d2'
down_revision = 'ad447b0bdae9'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TYPE queuetypeenum ADD VALUE 'SEMIAUTO'")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TYPE queuetypeenum DROP VALUE 'SEMIAUTO'")
    # ### end Alembic commands ###
