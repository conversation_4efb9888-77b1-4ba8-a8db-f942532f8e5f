import os
from urllib.parse import quote
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import scoped_session, sessionmaker


DATABASE_URL = os.environ["ACHALA_DATABASE_URL"]
DB_PASSWORD = os.environ["ACHALA_DB_PASSWORD"]

engine = create_engine(
    DATABASE_URL% quote(DB_PASSWORD), future=True,  pool_size=100,pool_recycle=3600
)
AchalaSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine, future=True)

Base = declarative_base()
