"""enum type added in cash type

Revision ID: fb10983731f2
Revises: ffdc6d02a3bc
Create Date: 2024-05-06 04:52:46.023172

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fb10983731f2'
down_revision = 'ffdc6d02a3bc'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TYPE casetypeenum ADD VALUE 'COORPORATE_CREDIT'")
    pass
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TYPE casetypeenum DROP VALUE 'COORPORATE_CREDIT'")
    pass
    # ### end Alembic commands ###
