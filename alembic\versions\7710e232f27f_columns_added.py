"""columns added

Revision ID: 7710e232f27f
Revises: f4f6f1f6c113
Create Date: 2023-10-13 11:55:35.553449

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7710e232f27f'
down_revision = 'f4f6f1f6c113'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_token',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('token_no', sa.String(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], name='user_service_user_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('queue', sa.Column('buffer_time', sa.Float(), nullable=True))
    op.add_column('user_queue', sa.Column('token_id', sa.Integer(), nullable=True))
    op.add_column('user_queue', sa.Column('estimated_time', sa.DateTime(timezone=True), nullable=True))
    op.create_foreign_key('user_queue_user_token_id_fk', 'user_queue', 'user_token', ['token_id'], ['id'])
    op.add_column('user_service', sa.Column('token_id', sa.Integer(), nullable=True))
    op.create_foreign_key('user_service_user_token_id_fk', 'user_service', 'user_token', ['token_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('user_service_user_token_id_fk', 'user_service', type_='foreignkey')
    op.drop_column('user_service', 'token_id')
    op.drop_constraint('user_queue_user_token_id_fk', 'user_queue', type_='foreignkey')
    op.drop_column('user_queue', 'estimated_time')
    op.drop_column('user_queue', 'token_id')
    op.drop_column('queue', 'buffer_time')
    op.drop_table('user_token')
    # ### end Alembic commands ###
