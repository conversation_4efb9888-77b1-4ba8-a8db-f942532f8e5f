from datetime import datetime
import logging
from typing import List, Optional
import strawberry
from language.models import Language as LanguageModel
from language.resolvers import list_languages
from graphql_types import MutationResponse
from exceptions.exceptions import MutationError

logger = logging.getLogger()
# from user.schema import User

@strawberry.type
class Language:
    id: int
    name: str
    code: str

    instance = strawberry.Private[LanguageModel]

    @classmethod
    def from_instance(cls, instance: LanguageModel):
        return cls(
            id=instance.id,
            name=instance.name,
            code=instance.code
        )


@strawberry.type
class Query:
    
    @strawberry.field
    def list_languages(self, info) -> List[Language]:
        db = info.context["db"]
        languages = list_languages(db)
        return [Language.from_instance(language) for language in languages]
