"""counter column changed to foreign key

Revision ID: 9c6be61f0db4
Revises: 59387b3dfcce
Create Date: 2023-11-14 12:02:34.461866

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9c6be61f0db4'
down_revision = '59387b3dfcce'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key('user_queue_counter_fk', 'user_queue', 'queue_counter', ['counter'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('user_queue_counter_fk', 'user_queue', type_='foreignkey')
    # ### end Alembic commands ###
