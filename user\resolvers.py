import base64
from io import BytesIO
import tempfile
import uuid
import json

import PyPDF2
from constants import BASIC_USER_REGISTRATION,ONE_AIG_VIEW_REPORTS, PACS_RISORDER_ID, PACS_RISORDER_REPORT, TOKEN_GENERATION, GET_PATIENT_DETAILS_BY_PHONE_NO, LAB_REPORTS,BASE64_REPORT, GET_DOCTORS, GET_SPECIALIZATION, GET_PATIENT_DETAILS_BY_UHID, LAB_WHATSAPP_MSG
from sms_mail_notification.resolvers import sms_mail_notification_message
from sms_mail_notification.schema import UserDetails
from util.globals import calculate_age, handle_request, handle_request1, cache, handle_get_request, generate_unique_location_token, pdf_to_base64
from email import encoders
from email.mime.base import MIMEBase
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import smtplib
from urllib import response
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5
import datetime
import os
import random
from typing import List, Optional
import requests
from graphql_types import DoctorAvailableDay, DoctorRegistration, ReportDetail, UserRegister, WhatsappData
from config.models import Config as ConfigModel
from user.models import (
    OTPTypeEnum,
    Relation as RelationModel,
    StatusEnum,
    UserRefIdTypeEnum,
    UserStatusEnum,
    RefTypeEnum,
    UserType as UserTypeModel,
    User as UserModel,
    UserOTP as UserOTPModel,
    trackScreenLogs as trackScreenLogModel,
    Screen as ScreenModel,
    trackScreen as trackScreenModel,
    SmsSendDetail as SmsSendDetailModel,
    UserOtpLogs as UserOtpLogsModel,
    RelDeviceModule as RelDeviceModuleModel,
    Module as ModuleModel,
    Device as DeviceModel,
    MandatoryFields as MandatoryFieldsModel,
    Printer as PrinterModel,
    RelConsultDoctorPatient as RelConsultDoctorPatientModel,
    TokenPatient as TokenPatientModel
)
from exceptions.exceptions import MutationError
from sqlalchemy.exc import NoResultFound, IntegrityError
from sqlalchemy.orm import Session
from sqlalchemy import literal_column, or_, and_, text
import logging
import bcrypt
import pytz
from jose import jws
from sqlalchemy import cast, Date, func, Time, extract, case
from sqlalchemy import asc, desc
import pandas as pd
from database.db_conf import SessionLocal
from sqlalchemy.dialects.postgresql import array_agg
from sqlalchemy.orm import aliased
from sms_mail_notification.models import (
    EventCodeEnum,
    SMSMailNotificationConfig as SMSMailNotificationConfigModel,
    SMSTypeEnum,
)
from collections import Counter
from operation.models import Operation as OperationModel, OperationRelUserType as OperationRelUserTypeModel
import cups, time, asyncio
from PyPDF2 import PdfMerger, PdfReader
logger = logging.getLogger()
from resources.models import Resource as ResourceModel
from urllib.parse import urlparse, parse_qs
from util.email import send_email
from thefuzz import fuzz
from thefuzz import process

from PyPDF2.generic import RectangleObject

def generate_otp(db: Session, ref_id: str, flow: str,ref_type: str, config_data: dict):
    return generate_otp_by_phone_no(db, ref_id, flow, ref_type, config_data)


def generate_otp_by_phone_no(db: Session, ref_id: str, flow: str, ref_type: str, config_data: dict):
    data = {}
    otp = None
    try:
        if ref_type == RefTypeEnum.UHID.name:
            phone_no=db.query(UserModel.phone_number).filter(UserModel.umr_no==ref_id).first()
            if phone_no is None:
                raise MutationError("The provided UHID is invalid")
            phone_no=phone_no.phone_number
        else:
            phone_no=ref_id
        if len(phone_no) == 10:
            send_otp_ = False
            if flow == OTPTypeEnum.HEALTH_PACKAGE.name:
                send_otp_ = True
                flow = OTPTypeEnum.HEALTH_PACKAGE
                event_code = EventCodeEnum.LOGIN
            elif flow == OTPTypeEnum.BOOK_APPOINTMENT.name:
                send_otp_ = True
                flow = OTPTypeEnum.BOOK_APPOINTMENT
                event_code = EventCodeEnum.LOGIN
            elif flow == OTPTypeEnum.REGISTER.name:
                send_otp_ = True
                flow = OTPTypeEnum.REGISTER
                event_code = EventCodeEnum.REGISTER
            elif flow == OTPTypeEnum.GENERATE_TOKEN.name:
                send_otp_ = True
                flow = OTPTypeEnum.GENERATE_TOKEN
                event_code = EventCodeEnum.LOGIN
            else:
                send_otp_ = True
                event_code = EventCodeEnum.LOGIN
            if send_otp_:
                utc = pytz.UTC
                exists_otp = (
                    db.query(UserOTPModel)
                    .filter(
                        UserOTPModel.ref_id == phone_no, UserOTPModel.otp_type == flow
                    )
                    .one_or_none()
                )
                expired_on = (
                    datetime.datetime.now() - datetime.timedelta(minutes=5)
                ).replace(tzinfo=utc)
                if os.environ["STATIC_OTP"]==str(1):
                    otp= "1111"
                else:
                    otp = str(random.randint(1000, 9999))
                if exists_otp is None:
                    request_id = uuid.uuid4()
                    UserOTP = UserOTPModel(
                        ref_id=phone_no,
                        ref_id_type=UserRefIdTypeEnum.PHONE_NUMBER,
                        otp=otp,
                        otp_type=flow,
                        request_id = request_id,
                        count = 0
                    )
                    db.add(UserOTP)
                else:
                    request_id = uuid.uuid4()
                    exists_otp.count=0
                    exists_otp.request_id=request_id
                    exists_otp.otp = otp
                    exists_otp.sent_at = func.now()
                db.commit()
            elif flow == "REGISTER":
                data["status"] = "ALREADY_REGISTERED"
                data["message"] = "Mobile number already registered"
            else:
                data["status"] = "USER_NOT_REGISTERED"
                data["message"] = "Mobile number not registered"
        else:
            logger.exception("Invalid mobile number")
            raise MutationError("Invalid mobile number")
        if otp is not None:
            user_details = [
                UserDetails(
                    phone_number=phone_no,
                    event_code=event_code.name,
                    user_type="PATIENT",
                )
            ]
            if os.environ["STATIC_OTP"]!=str(1):
                send_aig_otp(otp, user_details, db, config_data)
            data["status"] = "OTP Sent"
            data["message"] = "OTP sent successfully"
        logger.info(data["message"])
        return data,request_id
    except IntegrityError as e:
        logger.exception("Failed to generate OTP")
        raise MutationError("Failed to generate OTP")


def calling_sms_first_api(headers,data,txn_id,user,message):
    res1 = handle_request1(
        os.environ["AIG_SMS_URL"],
        headers,
        {
            "from": data.get("AIG_SMS_SENDER"),
            "to": "91"+user.phone_number,
            "msg": message,
            "dlr": {"mask": 1, "url": data.get("SMS_WEBHOOK_URL")},
            "type": data.get("SMS_TRANSACTIONAL_TYPE"),
            "transactionID": txn_id,
            "tiny": "0",
            "tlv": {
                "PE_ID": data.get("SMS_PE_ID"),
                "TEMPLATE_ID": data.get("SMS_TEMPLATE_ID"),
                "TELEMARKETER_ID": data.get("AIG_TELEMARKETER_ID"),
            },
        },
    )
    return res1


def calling_sms_secondary_api(user,message):
    res1 = handle_get_request(
        os.environ["AIG_SECONDARY_SMS_URL"],
        {
            "method": "SendMessage",
            "send_to": "91"+user.phone_number,
            "msg": message,
            "msg_type": "TEXT",
            "userid": os.environ["AIG_SMS_USER_ID"],
            "auth_scheme": "plain",
            "password": os.environ["AIG_SMS_PASSWORD"],
            "v": "1.1",
            "format": "text"
        },
    )
    return res1


def calling_airtel_api(data,user,message):
    body = {
            "customerId": data.get("CUSTOMER_ID"),
            "destinationAddress": user.phone_number,
            "message": message,
            "sourceAddress": data.get("AIG_SMS_SENDER"),
            "messageType": data.get("SMS_TRANSACTIONAL_TYPE"),
            "dltTemplateId": data.get("SMS_TEMPLATE_ID"),
            "entityId": data.get("ENTITY_ID")
        }
    logger.info(body)
    print(body)
    res1 = handle_request1(
        data.get("SMS_URL"),
        {
            "Authorization": f"Basic {data.get('SMS_ACCESS_TOKEN')}"
        },
        body
    )
    return res1


def send_aig_otp(
    otp,
    user_details: Optional[List[UserDetails]] = None,
    db: Optional[Session] = None,
    config_data: Optional[dict] = {},
):
    try:
        for user in user_details:
            sms = (
                db.query(SMSMailNotificationConfigModel.title,SMSMailNotificationConfigModel.data)
                .filter(
                    SMSMailNotificationConfigModel.type == SMSTypeEnum.SMS,
                    SMSMailNotificationConfigModel.code == user.event_code,
                )
                .filter(
                    or_(
                        SMSMailNotificationConfigModel.receiver_type.in_(
                            db.query(UserTypeModel.id)
                            .filter(UserTypeModel.code == user.user_type)
                            .scalar_subquery()
                        ),
                        SMSMailNotificationConfigModel.receiver_type == None,
                    )
                )
                .order_by((SMSMailNotificationConfigModel.receiver_type))
                .first()
            )
            headers = {
                "Authorization": "Bearer " + os.environ["AIG_SMS_AUTH_TOKEN"],
                "X-Authorization": "Basic " + os.environ["AIG_SMS_X_AUTH_TOKEN"],
            }
            data = json.loads(sms.data)
            data_json = json.dumps(data)
            txn_id=str(uuid.uuid4())
            otp_sent=False
            count=0
            sms_type = config_data.get("sms_type",1)
            if sms_type == 3:
                message = sms.title.replace("<$1>", otp.get("token_no")).replace("<$2>", otp.get("proceed_to")).replace("<$3>", otp.get("track_link"))
            else:
                message = sms.title.replace("<$1>", otp)
            logger.info(message)
            flag=True
            while flag:
                if sms_type == 2:
                    res1 = calling_sms_secondary_api(user,message)
                elif sms_type == 3:
                    res1 = calling_airtel_api(data,user,message)
                else:
                    res1 = calling_sms_first_api(headers,data,txn_id,user,message)
                if res1.status_code == 200 or res1.status_code ==202:
                    otp_details = SmsSendDetailModel(
                            transaction_id = txn_id,
                            sent_from = data.get("AIG_SMS_SENDER"),
                            sent_to = user.phone_number,
                            status = "PENDING",
                            count = 0,
                            message = message,
                            data=data_json
                        )
                    db.add(otp_details)
                    db.commit()
                    otp_sent=True
                    flag=False
                    break
                else:
                    logger.info(res1.content)
                    count=count+1
                    if count>=3:
                        flag=False
            if otp_sent:        
                return "OTP sent successfully"
            raise MutationError("Failed to send otp to the user")
    except MutationError as ex:
        logger.exception(ex.message)
        raise MutationError(ex.message)
    except:
        logger.exception("Error occured while calling SMS server")
        raise MutationError("Error occured while calling SMS server")

def resend_otp_msg(db:Session, resp_body:any):
    sms_detail=db.query(SmsSendDetailModel).filter(SmsSendDetailModel.transaction_id==resp_body.get("transactionID")).one()
    headers = {
                "Authorization": "Bearer " + os.environ["AIG_SMS_AUTH_TOKEN"],
                "X-Authorization": "Basic " + os.environ["AIG_SMS_X_AUTH_TOKEN"],
            }
    data = json.loads(sms_detail.data)
    if resp_body.get("status")=="DELIVERED":
        sms_detail.status="DELIVERED"
        db.commit()
    else:
        if sms_detail.count<=3:
            txn_id=str(uuid.uuid4())
            sms_detail.count=sms_detail.count+1
            sms_detail.transaction_id=txn_id
            sms_detail.status=resp_body.get("status")
            db.commit()
            res1 = handle_request1(
                os.environ["AIG_SMS_URL"],
                headers,
                {
                    "from": data.get("AIG_SMS_SENDER"),
                    "to": "91"+sms_detail.sent_to,
                    "msg": sms_detail.message,
                    "dlr": {"mask": 1, "url": data.get("SMS_WEBHOOK_URL")},
                    "type": data.get("SMS_TRANSACTIONAL_TYPE"),
                    "transactionID": txn_id,
                    "tiny": "0",
                    "tlv": {
                        "PE_ID": data.get("SMS_PE_ID"),
                        "TEMPLATE_ID": data.get("SMS_TEMPLATE_ID"),
                        "TELEMARKETER_ID": data.get("AIG_TELEMARKETER_ID"),
                    },
                },
            )
            if res1.status_code==202:
                pass

@cache.cache(ttl=82800)
def get_access_token():    
        res = handle_request1(
            os.environ["AIG_BASE_URL"] + TOKEN_GENERATION,
            None,
            {"userName": os.environ["TOKEN_USERNAME"], "password": os.environ["TOKEN_PASSWORD"]},
        )
        if res.status_code == 401 or res.status_code==404:
            raise MutationError("Error while calling HIS token")
        if ('application/json' not in res.headers.get('Content-Type')):
            raise MutationError("Error while calling HIS token")     
        return res.json()
def get_user_by_user_id(db, user_id):
    return db.query(UserModel).filter(UserModel.id==user_id).one_or_none()

def validate_otp(db: Session,request_id: str,otp : str):
    try:
        otp_obj = (
            db.query(UserOTPModel)
            .filter(UserOTPModel.request_id == request_id)
            .order_by(desc(UserOTPModel.sent_at))
            .first()
        )
        if otp_obj is None:
            logger.exception("Invalid request")
            raise MutationError("Invalid request")
        utc = pytz.UTC
        expired_on = (otp_obj.sent_at + datetime.timedelta(minutes=5)).replace(
            tzinfo=utc
        )
        current_time = datetime.datetime.now().replace(tzinfo=utc)
        if current_time < expired_on and otp_obj.otp == otp:
            otp_logs=UserOtpLogsModel(
                Phone_number= otp_obj.ref_id,
                otp_status = "OTP verified Successfully",
                request_id = request_id  
            )
            db.add(otp_logs)
            logger.info("Validataion successfull")
            res = get_access_token()
            token = "Bearer " + res["access_token"]
            headers = {"Authorization": token}
            res1 = handle_request(
                os.environ["AIG_BASE_URL"] + GET_PATIENT_DETAILS_BY_PHONE_NO,
                headers,{"phoneNo": otp_obj.ref_id})
            logger.info(res1.status_code)
            if res1.status_code == 200:
                db.query(UserOTPModel).filter(
                    UserOTPModel.request_id == request_id and UserOTPModel == otp
                ).delete()
            else:
                raise MutationError("Error calling HIS")
            # if res1.status_code == 401 or res1.status_code== 403:
            #     headers = get_new_access_token()
            #     res1 = handle_request(
            #         os.environ["AIG_BASE_URL"] + GET_PATIENT_DETAILS_BY_PHONE_NO,
            #         headers,{"phoneNo": otp_obj.ref_id})
            db.commit()
            return res1.json()['response'], otp_obj.ref_id
        else:
            otp_logs=UserOtpLogsModel(
                Phone_number= otp_obj.ref_id,
                otp_status = "OTP verification Failed",
                request_id = request_id
            )
            db.add(otp_logs)
            if otp_obj.count< 3:
                otp_obj.count = otp_obj.count+1
            else:
                logger.exception("Invalid request")
                raise MutationError("Invalid request")
            db.commit()
            logger.exception("Invalid OTP")
            raise MutationError("Invalid OTP")
    except MutationError as e:
        logger.exception("Invalid OTP")
        raise MutationError(e.message)


def register_user_aig(user_register: UserRegister, hosp_code:str):
    try:
        res = get_access_token()
        token = "Bearer " + res["access_token"]
        headers = {"Authorization": token}
        start_time=time.time()
        res1 = handle_request(
            os.environ["AIG_BASE_URL"] + BASIC_USER_REGISTRATION,
            headers,
            {
                "hospCode": hosp_code,
                "title": user_register.title,
                "firstName": user_register.firstName,
                "middleName": user_register.middleName,
                "lastName": user_register.lastName,
                "gender": user_register.gender,
                "maritalStatus": "Unknown" if user_register.maritalStatus=='' else user_register.maritalStatus,
                "dateofBirth": user_register.date_of_birth.replace("/", "-")
                if user_register.date_of_birth is not None
                else None,
                "address": user_register.address,
                "locality": user_register.locality,
                "cityName": user_register.cityName,
                "nationality": "Indian",
                "mobile": user_register.phone_number,
                "passportNo": "",
                "passportIssueDate": "",
                "passportExpiryDate": "",
                "passportIssuedPlace": "",
                "sourceofInfo": "Others",
                "idCardType": user_register.idCardType,
                "idCardNo": user_register.idCardNo,
                "savedBy": os.environ["TOKEN_USERNAME"],
                "Remarks": "",
                "mobilePrefix":"91"
            },
        )
        # if res1.status_code == 401 or res1.status_code ==403:
        #     headers = get_new_access_token()
        #     res1 = handle_request(
        #         os.environ["AIG_BASE_URL"] + BASIC_USER_REGISTRATION,
        #         headers,
        #         {
        #             "hospCode": hosp_code,
        #             "title": user_register.title,
        #             "firstName": user_register.firstName,
        #             "middleName": user_register.middleName,
        #             "lastName": user_register.lastName,
        #             "gender": user_register.gender,
        #             "maritalStatus": "Unknown" if user_register.maritalStatus=='' else user_register.maritalStatus,
        #             "dateofBirth": user_register.date_of_birth.replace("/", "-")
        #             if user_register.date_of_birth is not None
        #             else None,
        #             "address": user_register.address,
        #             "locality": user_register.locality,
        #             "cityName": user_register.cityName,
        #             "nationality": "Indian",
        #             "mobile": user_register.phone_number,
        #             "passportNo": "",
        #             "passportIssueDate": "",
        #             "passportExpiryDate": "",
        #             "passportIssuedPlace": "",
        #             "sourceofInfo": "Others",
        #             "idCardType": user_register.idCardType,
        #             "idCardNo": user_register.idCardNo,
        #             "savedBy": os.environ["TOKEN_USERNAME"],
        #             "Remarks": ""
        #         },
        #     )
        end_time=time.time()
        time_taken=format(end_time-start_time, '.4f')
        if res1.status_code == 200:
            response = res1.json()
            if response["succeeded"] == True:
                return (response["response"]["registrationNo"],time_taken)
            else:
                logger.info(res1.content)
                logger.info(user_register)
                send_email([os.environ["DIALY_REPORT_MAIL"]],"Aig Register Error",res1.content,os.environ["MAIL_ID"],os.environ["PASSWORD"])
                raise MutationError(response["message"])
        else:
            logger.info(res1.content)
            logger.info(user_register)
            logger.exception(res1.content)
            send_email([os.environ["DIALY_REPORT_MAIL"]],"Aig Register Error",res1.content,os.environ["MAIL_ID"],os.environ["PASSWORD"])
            raise MutationError("Error occured while calling AIG server")
    except MutationError as ex:
        logger.exception(ex.message)
        raise MutationError(ex.message)
    except:
        logger.exception("Error occured while calling AIG server")
        raise MutationError("Error occured while calling AIG server")


def register_user(db: Session, user_register: UserRegister,hosp_code:str,device_id:str):
    try:
        logger.info(f"Gender: {user_register.gender}")
        umr,time_taken= register_user_aig(user_register, hosp_code)
        user = UserModel(
            name=user_register.name,
            umr_no=umr,
            phone_number = user_register.phone_number,
            his_api_time_taken=time_taken,
            registration_type=user_register.registration_type,
            accept_terms=user_register.accept_terms,
            accepted_by=user_register.accepted_by,
            accepted_rel=user_register.accepted_rel,
            device_id = device_id
        )
        db.add(user)
        db.commit()
        logger.info("Registration successfull")
        token_data = generate_token(db,device_id,umr)
        return umr,token_data
    except IntegrityError as e:
        logger.exception("Failed to register")
        raise MutationError("Failed to register")



def start_time_format(start_time):
    return start_time.strftime("%H:%M")



def send_email_1(to, subject, text, file_name, data, sender_email, sender_password):
    try:
        gmail_user = sender_email
        gmail_pwd = sender_password
        msg = MIMEMultipart()
        msg["From"] = gmail_user
        msg["To"] = to
        msg["Subject"] = subject
        msg.attach(MIMEText(text))
        part = MIMEBase("application", "octet-stream")
        part.set_payload(data)
        encoders.encode_base64(part)
        part.add_header("Content-Disposition", "attachment", filename=file_name)
        msg.attach(part)
        mailServer = smtplib.SMTP("smtp.gmail.com:587")
        mailServer.ehlo()
        mailServer.starttls()
        mailServer.ehlo()
        mailServer.login(gmail_user, gmail_pwd)
        mailServer.sendmail(gmail_user, to, msg.as_string())
        mailServer.close()
        # if os.path.exists(file_name):
        #     os.remove(file_name)
    except smtplib.SMTPException as error:
        logger.exception("Failed to share report/prescription")
        raise MutationError(f"Failed to share report/prescription")



def resend_otp(db: Session, request_id: str):
    data = {}
    otp = None
    try:
        exists_otp = (
                db.query(UserOTPModel)
                .filter(UserOTPModel.request_id == request_id)
                .order_by(desc(UserOTPModel.sent_at))
                .first()
            )
        if exists_otp is None:
            logger.exception("OTP generation is not initiated")
            raise MutationError("OTP generation is not initiated")
        else:
            if os.environ["STATIC_OTP"]!=str(1):
                otp = str(random.randint(1000, 9999))
            else:
                otp="1111"
            request_id = uuid.uuid4()
            exists_otp.request_id = request_id
            exists_otp.count=0
            exists_otp.otp = otp
            exists_otp.sent_at = func.now()
            db.commit()
            event_code = (
                "REGISTER" if (exists_otp.otp_type.name == "REGISTER") else EventCodeEnum.LOGIN.name
            )
            user_details = [
                UserDetails(
                    phone_number=exists_otp.ref_id, event_code=event_code, user_type="PATIENT"
                )
            ]
            if os.environ["STATIC_OTP"]!=str(1):
                send_aig_otp(otp, user_details, db)
            data["status"] = "OTP Sent"
            data["message"] = "OTP sent successfully"
            logger.info(data["message"])
            return data,request_id
    except IntegrityError as e:
        logger.exception("Failed to generate OTP")
        raise MutationError("Failed to generate OTP")



def get_states(db_his:Session):
    try:
        sql_select_Query = f"SELECT distinct upper(state_name) as state_name FROM dbo.GetLocalityMasterforKiosk where Country_Name='India'"
        logger.info(sql_select_Query)
        res_data = db_his.execute(sql_select_Query).all()
        state_names = list(map(lambda x: x["state_name"], res_data))
        if "." in state_names:
            state_names.remove(".")
        state_names.sort()
        if "TELANGANA" in state_names:
            state_names.remove("TELANGANA")
            state_names.insert(0,"TELANGANA")
        return state_names
    except:
        logger.exception("error while calling get states")
        return []


def get_cities(state_name: str, db_his:Session):
    try:
        sql_select_Query = f"SELECT distinct upper(city_name) as city_name FROM dbo.GetLocalityMasterforKiosk where upper(state_name)='{state_name.upper()}'"
        logger.info(sql_select_Query)
        res_data = db_his.execute(sql_select_Query).all()
        city_names = list(map(lambda x: x["city_name"], res_data))
        city_names.sort()
        if (state_name == "TELANGANA") and "HYDERABAD" in city_names:
            city_names.remove("HYDERABAD")
            city_names.insert(0,"HYDERABAD")
        return city_names
    except:
        logger.exception("error while calling get cities")
        return []


def track_screen(db: Session, action: str, request_id: str,device_id:str,lang:str):
    try:
        logger.info(f"request id: {request_id}")
        logger.info(f"action: {action}")
        screen=db.query(ScreenModel.initial_action,ScreenModel.is_final).filter(ScreenModel.screen_code==action).one()
        if request_id is None or request_id=='':
            request_id = uuid.uuid4()
            screen_data = trackScreenModel(request_id=request_id, action=action,initial_action=screen.initial_action,is_final=screen.is_final,device_id=device_id,lang=lang)
            db.add(screen_data)
            db.flush()
            screen_log_data = trackScreenLogModel(track_screen_id=screen_data.id,request_id=request_id, action=action,is_final=screen.is_final)
            db.add(screen_log_data)
            db.commit()
            logger.info("data stored")
        elif request_id is not None:
            track_screen=db.query(trackScreenModel).filter(
                trackScreenModel.request_id == request_id
            ).one()
            track_screen.action=action
            track_screen.is_final=screen.is_final
            if screen.initial_action is not None:track_screen.initial_action=screen.initial_action
            screen_log_data = trackScreenLogModel(track_screen_id=track_screen.id,request_id=request_id, action=action,is_final=screen.is_final)
            db.add(screen_log_data)
            db.commit()
            logger.info("Data updated successfully")
        return request_id
    except NoResultFound as e:
        logger.exception(f"Failed to store data")
        raise MutationError(f"Failed to store data")


def insert_into_operations(db: Session, name: str, operation_type: str, status: str, entity_type: str, user_type_id: int, auth_type: str):
    try:
        operation = OperationModel(
            name=name,
            type=operation_type,
            status=status
        )
        db.add(operation)
        db.commit()
        rel_op = db.query(OperationModel.id).filter(OperationModel.name == name).first()
        rel_operation = OperationRelUserTypeModel(
            operation_id=rel_op.id,
            entity_type=entity_type,
            user_type_id=user_type_id,
            auth_type=auth_type

        )
        db.add(rel_operation)
        db.commit()
        return "Stored successfully"
    except Exception as e:
        logger.exception(f"Failed to store data")
        raise MutationError(f"Failed to store data")

def get_gender(his_db: Session):
    try:
        sql_select_Query = f"SELECT gender_name FROM dbo.GetGenderMasterforKiosk"
        res_data = his_db.execute(sql_select_Query).all()
        logger.info(res_data)
        gender = list(map(lambda x: x["gender_name"], res_data))
        return gender
    except Exception as e:
        logger.info(f"Query execution failed: {e}")
        return []
    
def get_locality(city_name:str,locality_name:str,his_db: Session,state_name:str):
    try:
        sql_select_Query = f"SELECT upper(State_Name), upper(City_Name), upper(Locality_Name),PinCode FROM dbo.GetLocalityMasterforKiosk where"
        append=False
        if city_name is not None and city_name!='':
            sql_select_Query+=" upper(City_Name)='"+city_name.upper()+"'"
            append=True
        if locality_name is not None and locality_name!='':
            if append:
                sql_select_Query+=" and " 
            sql_select_Query+=" upper(Locality_Name) LIKE '"+locality_name.upper()+"%'"
            append=True
        if state_name is not None and state_name!='':
            if append:
                sql_select_Query+=" and " 
            sql_select_Query+=" upper(State_Name) = '"+state_name.upper()+"'"
            append=True
        if append:
            logger.info(sql_select_Query)
            res_data = his_db.execute(sql_select_Query).all()
        else:
            return None
        return res_data
    except Exception as e:
        logger.info(f"Query execution failed: {e}")
        return None
    
def get_marital_status(his_db: Session):
    try:
        sql_select_Query = f"SELECT maritalstatus_name FROM dbo.GetMariatlstatusMasterforKiosk"
        res_data = his_db.execute(sql_select_Query).all()
        logger.info(res_data)
        marital_status = list(map(lambda x: x["maritalstatus_name"], res_data))
        items_to_check = ["Married", "Single", "Divorced", "Widowed", "Unknown"]
        marital_status = [item for item in items_to_check if item in marital_status]
        return marital_status
    except Exception as e:
        logger.info(f"Query execution failed: {e}")
        return []
    
def get_nationality(his_db: Session):
    try:
        sql_select_Query = f"SELECT Natioanlity_Name FROM dbo.GetNationalityMasterforKiosk"
        res_data = his_db.execute(sql_select_Query).all()
        marital_status = list(map(lambda x: x["Natioanlity_Name"], res_data))
        return marital_status
    except Exception as e:
        logger.info(f"Query execution failed: {e}")
        return []

def get_title(his_db: Session, gender: Optional[str] = ""):
    try:
        # if os.environ["TYPE"] == "PROD" or os.environ["TYPE"] == "UAT":
        #     sql_select_Query = f"SELECT title_name FROM dbo.GetTitleMasterforKiosk WITH ( NOLOCK ) "
        #     column_name="title_name"
        # else:
        #     sql_select_Query = f"SELECT name FROM dbo.GetTitleMasterforKiosk WITH ( NOLOCK ) "
        #     column_name="name"
        sql_select_Query = f"EXEC PR_GetTitleMasterforKiosk"
        res_data = his_db.execute(sql_select_Query).all()
        title = list(map(lambda x: x["Title_Name"], res_data))
        lst=["Mr.","Ms.","Mrs.","Dr.","Baby","Baby of"]
        title=[data for data in lst if data in title]+[data for data in title if data not in lst]
        
        if gender.lower() == "male":
            title = [item for item in title if item in ["Mr.","Dr.","Adm.", "Air Cdre.", "Air Chf Ms", "Air Mshl.", "AVM.", "Br.","Brig.", "Brig. Gen.", "Capt.", "CJI.", "Cmdr.", "Col.","Mast.", "Master.","Radm.","Sqn Ldr.", "Sub Lt.", "Vadm.", "Wg Cdr."]]
        elif gender.lower() == "female":
            title = [item for item in title if item in ["Ms.","Mrs.","Miss.","Dr.","Baby", "Baby of", "Adm.", "Air Cdre.", "Air Chf Ms", "Air Mshl.", "AVM.", "Br.", "Brig.", "Brig. Gen.", "Capt.", "CJI.", "Cmdr.", "Col.", "Crde.", "Fg Off.", "Flt Lt.", "Fr.", "Gen.", "Justice.", "Lt Cdr.", "Lt.","Lt. Col.", "Lt. Gen.", "M/s.", "Maj.", "Maj. Gen.", "Prof.", "Radm.", "Sis.", "Sqn Ldr.", "Sub Lt.","Vadm.","Wg Cdr."]]
        elif gender.lower() == "others":
            title = [item for item in title if item in ["Mx.","Radm."]]
        return title
    except Exception as e:
        logger.info(f"Query execution failed: {e}")
        return []
    
def get_mandatory_fields(db: Session):
    try:
        res_data = db.query(MandatoryFieldsModel.name).filter(MandatoryFieldsModel.is_mandatory==1).all()
        fields = list(map(lambda x: x["name"], res_data))
        return fields
    except Exception as e:
        logger.info(f"Query execution failed: {e}")
        return []
    
def get_address_by_pincode(his_db: Session, pincode:str):
    try:     
        sql_select_query=f"SELECT UPPER(State_Name), UPPER(City_Name), UPPER(Locality_Name) FROM dbo.GetLocalityMasterforKiosk WHERE PinCode = :pincode"
        pincode_data=his_db.execute(sql_select_query, {"pincode": pincode}).all()
        logger.info(pincode_data)
        return pincode_data
    except Exception as e:
        logger.info(f"Query execution failed: {e}")
        return []
    
def save_pdf_from_url(url, local_file_path):
     try:
        response = requests.get(url)
        logger.info(response)
        response.raise_for_status()
        with open(local_file_path, 'wb') as file:
            file.write(response.content)
            logger.info(f"File saved: {local_file_path}")
     except requests.exceptions.RequestException as e:
          logger.info(f"Error downloading file: {str(e)}")
          raise MutationError(f"Failed to download pdf from specified URL")
def save_pdf_from_base64(base64_string, local_file_path):
     try:
          decoded_data = base64.urlsafe_b64decode(base64_string)
          with open(local_file_path, 'wb') as file:
               file.write(decoded_data)
          state=True
          logger.info(f"PDF file saved: {local_file_path}")
     except Exception as e:
        state=False
        logger.info(f"Error saving PDF file: {str(e)}")
     return state
        #   raise MutationError(f"Error occured while converting HIS report")
      
def get_modules_list(db:Session, device_id: str, user :str):
    try:
        if user == "PATIENT":
            resources = (
                    db.query(ResourceModel.code)
                    .join(RelDeviceModuleModel, RelDeviceModuleModel.resource_id == ResourceModel.id)
                    .join(ModuleModel, RelDeviceModuleModel.module_id == ModuleModel.id)
                    .join(DeviceModel, RelDeviceModuleModel.device_id == DeviceModel.id)
                    .filter(ModuleModel.module_code == "Self-Service Kiosks", DeviceModel.device_code == device_id)
                    .all()
                )

        return list(map(lambda x: x.code,resources ))
    except Exception as e:
        logger.info(f"Error occured: {str(e)}")
        raise MutationError(f"Failed to get data")

def get_devices(db:Session,device_id:str):
    try:
        device=db.query(DeviceModel.device_code,DeviceModel.hospital_code,DeviceModel.printer_name).filter(DeviceModel.device_code == device_id,DeviceModel.status==StatusEnum.ACTIVE).one_or_none()
        data=None
        if device is not None:
            db_data=db.query(ConfigModel.data).filter(ConfigModel.hospital_code==device.hospital_code).first()
            data=json.loads(db_data.data)
        return (device,data)
    except Exception as e:
        logger.info(f"Error occurred {e}")
        raise MutationError(f"Error occured")

def get_all_reports(uhid:str, hosp_code,reports_count,db:Session):
    try:
        res = get_access_token()
        token = "Bearer " + res["access_token"]
        headers = {"Authorization": token}
        res1 = handle_request(
            os.environ["AIG_BASE_URL"] + LAB_REPORTS,
            headers,
            {
                "hospCode": hosp_code,
                "patientID": uhid,
                "type": "LAB"
            })
        # if res1.status_code==401 or res1.status_code==403:
        #     headers=get_new_access_token()
        #     res1 = handle_request(
        #     os.environ["AIG_BASE_URL"] + LAB_REPORTS,
        #     headers,
        #     {
        #         "hospCode": hosp_code,
        #         "patientID": uhid,
        #         "type": "LAB"
        #     })
        data1= res1.json()['response']
        data1 = list(map(lambda x:{**x,"type":"LAB"},data1))
        # test_ids=list(map(lambda x:f'{x.get("testID")}_{x.get("orderID")}_{x.get("patType")}',data))
        # testids1=db.scalars(db.query(PrinterModel.test_order_pat_id).filter(PrinterModel.cups_job_status=="COMPLETED").having(func.count(PrinterModel.test_order_pat_id) >= reports_count).filter(
        #                              and_(PrinterModel.uhid==uhid , PrinterModel.test_order_pat_id.in_(test_ids))).group_by(PrinterModel.test_order_pat_id)).all()
        # list1=list(map(lambda x:{**x,"status":"COMPLETED" if (f'{x.get("testID")}_{x.get("orderID")}_{x.get("patType")}' in testids1) else "PENDING"},data))
        res2 = handle_request(
            os.environ["AIG_BASE_URL"] + LAB_REPORTS,
            headers,
            {
                "hospCode": hosp_code,
                "patientID": uhid,
                "type": "DIAG"
            })
        data2= res2.json()['response']
        data2 = list(map(lambda x:{**x,"type":"DIAG"},data2))
        data= data1+ data2
        data = list(map(lambda x:{**x,"id":f'{x.get("testID")}_{x.get("orderID")}_{x.get("patType")}'},data))

        test_ids=list(map(lambda x:f'{x.get("testID")}_{x.get("orderID")}_{x.get("patType")}',data))
        testids1=db.scalars(db.query(PrinterModel.test_order_pat_id).filter(PrinterModel.cups_job_status=="COMPLETED").having(func.count(PrinterModel.test_order_pat_id) >= reports_count).filter(
                                     and_(PrinterModel.uhid==uhid , PrinterModel.test_order_pat_id.in_(test_ids))).group_by(PrinterModel.test_order_pat_id)).all()
        list1=list(map(lambda x:{**x,"status":"COMPLETED" if (f'{x.get("testID")}_{x.get("orderID")}_{x.get("patType")}' in testids1) else "PENDING"},data))
        list1.sort(key=lambda x: datetime.datetime.strptime(x['orderDate'],'%d-%b-%Y %H:%M'),reverse=True)
        return list1
    except Exception as e:
        logger.info(f"Error occurred {e}")
        return []

def view_reports(db:Session, order_id:int,patient_id:str,pat_type:int,test_id:int,type: str, hosp_code, pacs_base_url, order_number:str, order_date:str, show_logo: bool):
    try:
        config_data = db.query(ConfigModel.data).filter(ConfigModel.hospital_code==hosp_code).one()
        config_data= json.loads(config_data.data)
        token = "Bearer " + config_data.get("one_aig_token","")
        headers = {"Authorization": token}
        print(headers,"headers")

        res1 = handle_request(
            os.environ["ONE_AIG_BASE_URL"] + ONE_AIG_VIEW_REPORTS,
            headers,
            {
                "hospCode": hosp_code,
                "patientID": patient_id,
                "showlogo": show_logo,
                "orderNo": order_number,
                "orderID": order_id,
                "orderDate": order_date,
                "testID": test_id,
                "patType": pat_type,
                "type": type
            })
        if res1.json()['succeeded'] == False:
            logger.info(res1.content)
            base64_string=""
        else:
            base64_string=""
            if res1.json()['response'].get('reportBase64') !="":
                base64_string = res1.json()['response'].get('reportBase64')
            elif res1.json()['response'].get('reportLink') !="":
                base64_string = pdf_to_base64(res1.json()['response'].get('reportLink'))
            else:
                logger.info(res1.json())
            return base64_string
    except MutationError as e:
        raise MutationError(e.message)
    except Exception as e:
        logger.info(f"Error occurred {e}")
        raise MutationError(f"Error occured while viewing reports")

def change_page_size_to_a4(input_pdf_path, output_pdf_path):
    with open(input_pdf_path, 'rb') as input_file:
        pdf_reader = PyPDF2.PdfReader(input_file)
        pdf_writer = PyPDF2.PdfWriter()

        for page_num in range(len(pdf_reader.pages)):
            page = pdf_reader.pages[page_num]
            # page.mediabox.lower_left = (0, 0)
            page.cropbox = RectangleObject((0,0, 595, 842))
            page.mediabox = RectangleObject((0,0, 595, 842))
            pdf_writer.add_page(page)

        with open(output_pdf_path, 'wb') as output_file:
            pdf_writer.write(output_file)
            
async def print_report_old(db:Session,patient_id:str,report_details:List[ReportDetail],printer_name,hosp_code,reports_count,pacs_base_url,device_id,max_report_select_count,print_wait_time,request_id):
    try:
        if printer_name is None or printer_name.strip() == '':
            raise MutationError("Printer is not configured for this device")
        if len(report_details) > max_report_select_count:
            raise MutationError(f"Maximum of {max_report_select_count} reports can be chosen")
        test_ids=list(map(lambda x: f'{x.test_id}_{x.order_id}_{x.pat_type}',report_details))
        print=db.query(PrinterModel.test_order_pat_id).filter(PrinterModel.test_order_pat_id.in_(test_ids)).filter(PrinterModel.cups_job_status =="COMPLETED").having(func.count(PrinterModel.test_order_pat_id) >= reports_count).group_by(PrinterModel.test_order_pat_id).all()
        if len(set(print)) !=0:
            raise MutationError("Printing is already done for these reports")
        logger.info("print started")
        print_pdf = f'print/output/{patient_id}.pdf'
        merger = PdfMerger()
        group_id=str(uuid.uuid4())
        ids=[]
        for report_detail in report_details:
            try:
                base64=view_reports(db,report_detail.order_id,patient_id,report_detail.pat_type,report_detail.test_id,report_detail.type,hosp_code,pacs_base_url,report_detail.order_number,report_detail.order_date,False)
            except Exception as ex:
                base64=''
            dtype="BASE64"
            status='PENDING'
            if base64!='':
                file_path=f'print/{report_detail.test_id}_{report_detail.order_id}_{report_detail.pat_type}.pdf'
                save_pdf_from_base64(base64,file_path)
                file_reader= PdfReader(file_path)
                merger.append(file_reader,'rb')
            else:
                status='FAILED'
            printer_data=PrinterModel(printer_name=printer_name,test_order_pat_id=f'{report_detail.test_id}_{report_detail.order_id}_{report_detail.pat_type}',
                            cups_job_status=status,type=dtype,uhid=patient_id,device_id =device_id,request_id=request_id,group_id=group_id,total_pages=len(file_reader.pages))
            db.add(printer_data)
            db.commit()
            if status!='FAILED':
                ids.append(printer_data.id)
        merger.write(print_pdf)
        merger.close()
        change_page_size_to_a4(print_pdf,print_pdf)
        # logger.info(len(merger.pages))
        db.commit()
        conn = cups.Connection()
        job_id = conn.printFile(printer_name, print_pdf, "Print Job", {
            'media': 'A4',  # Change this to the appropriate media size
            'scaling': '100',  # Change this to the desired scaling percentage
            # 'fit-to-page': True,
            # 'page-ranges': str(1)+'-'+str(len(merger.pages)),
        })
        logger.info(job_id)
        db.query(PrinterModel).filter(PrinterModel.id.in_(ids)).update(
                                {PrinterModel.cups_job_id: job_id,PrinterModel.print_start_time: datetime.datetime.now(pytz.timezone('Asia/Kolkata'))},
                                synchronize_session="fetch",
                            )
        job_state= conn.getJobAttributes(job_id)["job-state"]
        logger.info(conn.getJobAttributes(job_id))
        logger.info(job_state)
        t_end = time.time() + print_wait_time
        is_cancelled=False
        job_printer_state_message=''
        job_media_sheets_completed=0
        while time.time() < t_end :
            job_state= conn.getJobAttributes(job_id)["job-state"]
            if job_state==5:
                # job_printer_state_message = conn.getJobAttributes(job_id)["job-printer-state-message"]
                try:
                    job_media_sheets_completed= conn.getJobAttributes(job_id)["job-media-sheets-completed"]
                except Exception as ex:
                    pass
            if job_state == 9:
                try:
                    completed_job=conn.getJobs(which_jobs="all", requested_attributes=["job-id", "job-state", "job-name", "job-printer-uri","job-media-sheets-completed"]).get(job_id)
                    # completed_job= conn.getJobs().get(print_data.cups_job_id)
                    job_media_sheets_completed=completed_job.get("job-media-sheets-completed")
                    # print_data.job_media_sheets_completed= completed_job.get("job-media-sheets-completed")
                except Exception as ex:
                    logger.exception(ex)
                if job_media_sheets_completed>0:
                    message=update_completed_count(db,ids,job_media_sheets_completed)
                else:
                    message=add_reports_data(db,11,ids,is_cancelled)
                return message
            elif job_state == 6 or job_state == 8:
                message=add_reports_data(db,job_state,ids,is_cancelled)
                try:
                    conn.cancelJob(job_id)
                    is_cancelled=True
                    message=add_reports_data(db,job_state,ids,is_cancelled)
                    raise MutationError(message)
                except Exception as e:
                    if job_state == 9:
                        is_cancelled=False
                        message=add_reports_data(db,job_state,ids,is_cancelled)
                        return message
                    else:
                        logger.info(f"Error occured:{e}")
                        raise MutationError(e.message)
            await asyncio.sleep(0.25)
        logger.info(job_state)
        try:
            conn.cancelJob(job_id)
            message=add_reports_data(db,job_state,ids,is_cancelled)
            raise MutationError(message)
        except Exception as e:
            if job_state == 9:
                is_cancelled=False
                message=add_reports_data(db,job_state,ids,is_cancelled)
                return message
            else:
                logger.info(f"Error occured:{e}")
                raise MutationError("Printer is not available at this moment")
    except cups.IPPError as e:
        logger.exception(f"Error occured {e}")
        raise MutationError("Printer is not available at this moment")
    except MutationError as e:
        raise MutationError(e.message)
    except Exception as e:
        logger.exception(f"error Occured: {e}")
        raise MutationError(f"Error occured while printing")
    finally:
        db.commit()

async def print_report(db:Session,patient_id:str,report_details:List[ReportDetail],printer_name,hosp_code,reports_count,pacs_base_url,device_id,max_report_select_count,print_wait_time,request_id,group_id):
    try:
        new_session=False
        if db is None:
            new_session=True
            db=SessionLocal()
        if len(report_details) > max_report_select_count:
            raise MutationError(f"Maximum of {max_report_select_count} reports can be chosen")
        test_ids=list(map(lambda x: f'{x.test_id}_{x.order_id}_{x.pat_type}',report_details))
        print=db.query(PrinterModel.test_order_pat_id).filter(PrinterModel.test_order_pat_id.in_(test_ids)).filter(PrinterModel.cups_job_status =="COMPLETED").having(func.count(PrinterModel.test_order_pat_id) >= reports_count).group_by(PrinterModel.test_order_pat_id).all()
        if len(set(print)) !=0:
            raise MutationError("Printing is already done for these reports")
        logger.info("print started")
        ids=[]
        print_list=[]
        for report_detail in report_details:
            try:
                base64=view_reports(db,report_detail.order_id,patient_id,report_detail.pat_type,report_detail.test_id,report_detail.type,hosp_code,pacs_base_url,report_detail.order_number,report_detail.order_date,False)
                dtype="BASE64"
                if base64!='':
                    file_path=f'print/{report_detail.test_id}_{report_detail.order_id}_{report_detail.pat_type}.pdf'
                    state= save_pdf_from_base64(base64,file_path)
                    if state:
                        job_id= print_base64_content(printer_name,file_path,0)
                    else:
                        job_id=0
                    # merger.append(PdfReader(file_path),'rb')
                else:
                    dtype="BASE64"
                    job_id=0
            except Exception as ex:
                dtype="BASE64"
                job_id=0
            printer_data=PrinterModel(printer_name=printer_name,cups_job_id= job_id, test_order_pat_id=f'{report_detail.test_id}_{report_detail.order_id}_{report_detail.pat_type}',
                            cups_job_status="PENDING" if job_id!=0 else "FAILED",type=dtype,uhid=patient_id,device_id =device_id,print_start_time= datetime.datetime.now(pytz.timezone('Asia/Kolkata')),request_id=request_id,group_id=group_id)
            print_list.append(printer_data)
            # db.add(printer_data)
            ids.append(job_id)
        db.add_all(print_list)
        db.commit()
        # return "success"
        t_end = time.time() + print_wait_time
        common_job_state=0
        common_job_printer_state_message=''
        success_count=0
        completed_list=[]
        conn = cups.Connection()
        while time.time() < t_end or (common_job_state==5 and common_job_printer_state_message=='Waiting for job to complete.'):
            flag=True
            common_job_state=0
            common_job_printer_state_message=''
            for print_data in print_list:
                is_cancelled=False
                if print_data.cups_job_id==0:
                    pass
                else:
                    job_state= conn.getJobAttributes(print_data.cups_job_id)["job-state"]
                    if job_state==5:
                        flag=False
                        common_job_state=job_state
                        try:
                            job_printer_state_message = conn.getJobAttributes(print_data.cups_job_id)["job-printer-state-message"]
                        except Exception:
                            job_printer_state_message=''
                        if job_printer_state_message == 'Waiting for job to complete.':
                            common_job_printer_state_message=job_printer_state_message
                        print_data.job_media_sheets_completed= conn.getJobAttributes(print_data.cups_job_id)["job-media-sheets-completed"]
                    if job_state == 9:
                        if print_data.cups_job_id not in completed_list:
                            job_media_sheets_completed=0
                            try:
                                completed_job=conn.getJobs(which_jobs="all", requested_attributes=["job-id", "job-state", "job-name", "job-printer-uri","job-media-sheets-completed"]).get(print_data.cups_job_id)
                                # completed_job= conn.getJobs().get(print_data.cups_job_id)
                                print_data.job_media_sheets_completed= completed_job.get("job-media-sheets-completed")
                            except Exception as ex:
                                logger.exception(ex)
                            if print_data.job_media_sheets_completed is not None and print_data.job_media_sheets_completed>0:
                                success_count=success_count+1
                                message, status=add_reports_data_1(job_state,is_cancelled)
                                print_data.cups_job_status=status
                                print_data.print_end_time= datetime.datetime.now(pytz.timezone('Asia/Kolkata'))
                                db.commit()
                                completed_list.append(print_data.cups_job_id)
                            else:
                                if print_data.retry_count is None or print_data.retry_count<=3:
                                    logger.info('retry added')
                                    message, status=add_reports_data_1(11,is_cancelled)
                                    file_path=f'print/{print_data.test_order_pat_id}.pdf'
                                    new_job_id= print_base64_content(printer_name,file_path,0)
                                    if new_job_id!=0:
                                        flag=False
                                        print_data.cups_job_id=new_job_id
                                        print_data.retry_count= 1 if print_data.retry_count is None else print_data.retry_count+1
                                        print_data.cups_job_status=status
                                        db.commit()
                                    else:
                                        logger.info('failed 1')
                                        message, status=add_reports_data_1(12,is_cancelled) 
                                        print_data.cups_job_status=status
                                        db.commit()
                                else:
                                    message, status=add_reports_data_1(12,is_cancelled) 
                                    print_data.cups_job_status=status
                                    db.commit()
                    elif job_state == 6 or job_state == 8:
                        message, status=add_reports_data_1(job_state,is_cancelled)
                        print_data.cups_job_status=status
                        try:
                            conn.cancelJob(print_data.cups_job_id)
                            is_cancelled=True
                            message, status=add_reports_data_1(job_state,is_cancelled)
                            print_data.cups_job_status=status
                            db.commit()
                            # raise MutationError(message)
                        except Exception as e:
                            logger.info(f"Error occured:{e}")
                            # raise MutationError(e.message)
            if flag:
                for print_data in print_list:
                    if print_data.cups_job_status=='PENDING' or print_data.cups_job_status=='RETRY':
                        message, status=add_reports_data_1(job_state,is_cancelled)
                        print_data.cups_job_status=status
                        db.commit()
                        try:
                            conn.cancelJob(print_data.cups_job_id)
                            is_cancelled=True
                            message, status=add_reports_data_1(job_state,is_cancelled)
                            print_data.cups_job_status=status
                            db.commit()
                            # raise MutationError(message)
                        except Exception as e:
                            logger.info(f"Error occured:{e}")
                            # raise MutationError(e.message)
                break
            else:
                await asyncio.sleep(0.25)
            # ids.append(printer_data.id)
        for print_data in print_list:
            if print_data.cups_job_status=='PENDING' or print_data.cups_job_status=='RETRY':
                message, status=add_reports_data_1(job_state,is_cancelled)
                print_data.cups_job_status=status
                db.commit()
                try:
                    conn.cancelJob(print_data.cups_job_id)
                    is_cancelled=True
                    message, status=add_reports_data_1(job_state,is_cancelled)
                    print_data.cups_job_status=status
                    db.commit()
                    # raise MutationError(message)
                except Exception as e:
                    logger.info(f"Error occured:{e}")
        return str(success_count)+" Reports Pront success"
    except cups.IPPError as e:
        logger.exception(f"Error occured {e}")
        # raise MutationError("Printer is not available at this moment")
    except Exception as e:
        logger.exception(f"error Occured: {e}")
        # raise MutationError(f"Error occured while printing")
    finally:
        db.commit()
        if new_session and db is not None: db.close()
def set_a4_page_size(input_pdf, output_pdf):
    # Open the input PDF file in binary mode
    with open(input_pdf, 'rb') as pdf_file:
        # Create a PDF reader object
        pdf_reader = PyPDF2.PdfReader(pdf_file)
        # Create a PDF writer object
        pdf_writer = PyPDF2.PdfWriter()
        # Set A4 page size (210mm x 297mm)
        a4_page_size = (595.276, 841.890)
        pdf_writer.pages[0].mediaBox.upperRight = a4_page_size
        # Add all pages to the writer object
        for page in pdf_reader.pages:
            pdf_writer.addPage(page)
        # Create the output PDF file with A4 page size
        with open(output_pdf, 'wb') as output_pdf_file:
            # Write the modified content to the output file
            pdf_writer.write(output_pdf_file)
def print_base64_content(printer_name, file_path,reset_count):
    try:
        # Decode base64 content to obtain binary data
        # binary_content = base64.b64decode(base64_content)
        # Create a BytesIO object from the binary content
        # with tempfile.NamedTemporaryFile(delete=False) as temp_file:
        #     temp_file.write(binary_content)
        #     temp_file_path = temp_file.name
        # set_a4_page_size(temp_file_path,temp_file_path)
        # Use pycups to print the document
        conn = cups.Connection()
        # Get the list of available printers
        # Set up the print options
        options = {
            'media': 'A4',  # Change this to the appropriate media size
            'scaling': '100',  # Change this to the desired scaling percentage
        }
        # Print the document
        return conn.printFile(printer_name, file_path, "Print Job", options)
    except cups.IPPError as e:
        if reset_count>=3:
            return 0
        else:
            print_base64_content(printer_name, file_path,reset_count+1)

@cache.cache(ttl=3600)
def get_id_card_type():
    try:
        # sql_select_Query = f"SELECT CardType_Name FROM dbo.GetIdcardtypeMasterforKiosk"
        # res_data = his_db.execute(sql_select_Query).all()
        # id_card_types = list(map(lambda x: x["CardType_Name"], res_data))
        # id_card_types.remove("VISA") if "VISA" in id_card_types else None
        id_card_types=['Aadhar Card','Pan Card','Passport Number','Voter ID']
        logger.info(id_card_types)
        return id_card_types
    except Exception as e:
        logger.exception(f"Query execution failed: {e}")
        return []

def get_config_data(db: Session, hosp_code,device_id: Optional[str] = None):
    try:
        if device_id is not None:
            device: DeviceModel =db.query(DeviceModel).filter(DeviceModel.device_code==device_id).first()
        if device is not None:
            hosp_code = device.hospital_code
        db_data=db.query(ConfigModel.data).filter(ConfigModel.hospital_code==hosp_code).first()
        data=json.loads(db_data.data)
        data["device_type"]=device.device_type.name if device.device_type is not None else None
        data["sub_device_type"]=device.sub_device_type.name if device.sub_device_type is not None else None
        data["vital_print_enabled"]=device.vital_print_enabled if device.vital_print_enabled is not None else False
        return data
    except Exception as e:
        logger.exception(e)
        raise MutationError("Data not found")

def check_device_login(db: Session, device_id:str, pin:int):
    try:
        data=db.query(DeviceModel.id).filter(and_(DeviceModel.login_pin==pin,DeviceModel.device_code == device_id)).first()
        if data is None:
            raise MutationError("Invalid Login credentials")
        return "Device Login successfully"
    except Exception as e:
        logger.info(f"Error occured: {e}")
        raise MutationError("Invalid Login credentials")

def add_reports_data(db:Session,job_state,ids,is_cancelled):
    try:
        status_messages = {
            9: ("COMPLETED", "Reports Printed Successfully"),
            3: ("UNAVAILABLE", "Printer is not available at this moment"),
            6: ("OUT_OF_PAPER", "Printer is out of paper"),
            8: ("ABORTED_BY_PRINTER", "Printing is aborted by printer"),
            11: ("FAILED_BY_PRINTER", "Printing success but print not happened"),
        }
        if job_state in status_messages:
            status, message = status_messages[job_state]
            if is_cancelled:
                status += "_CANCELLED"
        else:
            status = "TIMEOUT_CANCELLED"
            message = "Printer is not available at this moment"

        db.query(PrinterModel).filter(PrinterModel.id.in_(ids)).update(
                                {PrinterModel.cups_job_status: status,PrinterModel.print_end_time: datetime.datetime.now(pytz.timezone('Asia/Kolkata'))},
                                synchronize_session="fetch",
                            )
        db.commit()
        return message
    except Exception as e:
        logger.info(f"Error occured{e}")
        raise MutationError("Error occured while storing")

def update_completed_count(db:Session,ids,total_completed_count):
    try:
        list= db.query(PrinterModel).filter(PrinterModel.id.in_(ids)).order_by(asc(PrinterModel.created_at)).all()
        count=0
        for print in list:
            total_completed_count=total_completed_count-print.total_pages
            if total_completed_count >= 0:
                count=count+1
                print.job_media_sheets_completed=print.total_pages
                print.cups_job_status = "COMPLETED"
                print.print_end_time= datetime.datetime.now(pytz.timezone('Asia/Kolkata'))
            else:
                print.job_media_sheets_completed=0
                print.cups_job_status = "FAILED_BY_PRINTER"
        message=f'{count} Reports Printed Successfully'
        db.commit()
        return message
    except Exception as e:
        logger.info(f"Error occured{e}")
        raise MutationError("Error occured while storing")


def add_reports_data_1(job_state,is_cancelled):
    try:
        status_messages = {
            9: ("COMPLETED", "Reports Printed Successfully"),
            3: ("UNAVAILABLE", "Printer is not available at this moment"),
            6: ("OUT_OF_PAPER", "Printer is out of paper"),
            8: ("ABORTED_BY_PRINTER", "Printing is aborted by printer"),
            11: ("RETRY", "Printing success but print not happened"),
            12: ("FAILED", "Retry failed"),
        }
        if job_state in status_messages:
            status, message = status_messages[job_state]
            if is_cancelled:
                status += "_CANCELLED"
        else:
            status = "TIMEOUT_CANCELLED"
            message = "Printer is not available at this moment"
        return message, status
    except Exception as e:
        logger.info(f"Error occured{e}")
        raise MutationError("Error occured while storing")
    
def get_ris_report(pacs_base_url,token,accessionNo):
    body={
        "mode": "21",
        "dsData": {
            "dtCriteria": [
                {
                    "AccessionNo": accessionNo,
                    "Token": token
                }
            ]
        }
    }
    res=handle_request1(pacs_base_url+PACS_RISORDER_ID ,None,body)
    if res.status_code == 200:
        data= res.json()
        if data["StatusCode"]==200:
            body={
                "mode": "39",
                "dsData": {
                    "dtCriteria": [
                        {
                            "RisOrderId": data["Result"][0]["RisOrderId"]
                        }
                    ]
                }
            }
            res1=handle_request1(pacs_base_url+PACS_RISORDER_REPORT,None,body)
            if res1.status_code == 200:
                data1= res1.json()
                if data1["StatusCode"]==200:
                   return data1["Result"][0]["ImageUrlpath"]
    raise MutationError("Error occured while calling his pacs report")


def get_print_status(db:Session, uhid:str, group_id:str):
    return db.query(PrinterModel).filter(PrinterModel.uhid==uhid).filter(PrinterModel.group_id==group_id).all()

def get_master_details_by_lang(db: Session, hosp_code):
    try:
        db_data=db.query(ConfigModel.data1).filter(ConfigModel.hospital_code==hosp_code).scalar()
        return db_data
    except Exception as e:
        logger.info(f"Error occured{e}")
        raise MutationError("Data not found")

def check_fuzzy_logic(list_of_choices,choice):
    return process.extractOne(choice, list_of_choices, scorer=fuzz.token_sort_ratio)

def get_relations(db):
    return db.query(RelationModel).order_by(RelationModel.priority).all()
def get_hospital_devices(db, device_id):
    device=db.query(DeviceModel.device_code).filter(DeviceModel.device_code == device_id,DeviceModel.status==StatusEnum.ACTIVE).one_or_none()
    if device is not None:
        return device.device_code
    return None

def enter_vitals(data,uhid):
    logger.info(data)
    html_page=''
    current_time = datetime.datetime.now(pytz.timezone('Asia/Kolkata')).strftime('%Y-%m-%d %H:%M')
    html_page+=f'<html><body>Patient Id: <strong>{uhid}</strong> <br> Vital Details Generated at {current_time}<br><br>'
    html_page+="<table border=1 width='100%' style='border-collapse: collapse;'><tr><th width='30%'>Vital Data</th><th width='30%'>Value</th></tr>"
    for details  in data:
        html_page += f"<tr><td style='text-align: center;'>{details.name}</td><td style='text-align: center;'>{details.value}</td></tr>"
    html_page+="</table></body></html>"
    send_email(os.environ["VITAL_MAIL"].split(','),"Patient Vital Details",html_page,os.environ["MAIL_ID"],os.environ["PASSWORD"])
    return True

def get_all_specialization(hosp_code: str):
    res = get_access_token()
    token = "Bearer " + res["access_token"]
    headers = {"Authorization": token}
    res1 = handle_request(
        os.environ["AIG_BASE_URL"] + GET_SPECIALIZATION,
        headers,{"hospCode": hosp_code})
    return res1.json()['response']

def get_all_doctors(db:Session,hosp_code: str, speciality_code: Optional[str], doctor_code: Optional[str]):
    now = datetime.datetime.now(pytz.timezone('Asia/Kolkata'))
    start_of_day = now.replace(hour=0, minute=0, second=0, microsecond=0)
    res = get_access_token()
    token = "Bearer " + res["access_token"]
    headers = {"Authorization": token}
    res1 = handle_request(
        os.environ["AIG_BASE_URL"] + GET_DOCTORS,
        headers,{
            "hospCode": hosp_code,
            "specialityCode": speciality_code
            })
    response = res1.json()['response'][:10]
    for doctor in response:
        total_count = db.query(func.count(RelConsultDoctorPatientModel.id)).filter(
            RelConsultDoctorPatientModel.created_at > start_of_day
        ).filter(RelConsultDoctorPatientModel.doctor_id == doctor.get("doctorCode")).scalar()
        doctor["total_count"] = total_count
        doctor["consultation_amt"] = 0
        if doctor_code:
            if doctor['doctorCode'] == doctor_code:
                response = [doctor]
    return response

def generate_token(db:Session,device_id:str,uhid:str):
    try:
        now = datetime.datetime.now(pytz.timezone('Asia/Kolkata'))
        start_of_day = now.replace(hour=0, minute=0, second=0, microsecond=0)
        user_id = db.query(UserModel.id).filter(UserModel.umr_no == uhid).one_or_none()
        if not user_id:
            res = get_access_token()
            token = "Bearer " + res["access_token"]
            headers = {"Authorization": token}
            logger.info(os.environ["AIG_BASE_URL"])
            res1 = handle_request(
                os.environ["AIG_BASE_URL"] + GET_PATIENT_DETAILS_BY_UHID,
                headers,{"patientID": uhid})
            if res1.json().get('response') is not None:
                user_registered=  res1.json()['response']
                user = UserModel(
                    name=user_registered.get("patientName"),
                    umr_no=user_registered.get("patientID"),
                    phone_number =user_registered.get("phone_number"),
                    device_id = device_id
                )
                db.add(user)
                db.commit() 
                user_id = db.query(UserModel.id).filter(UserModel.umr_no == uhid).one_or_none()
            else:
                raise MutationError("Error While Getting From HIS")
        existing_token = db.query(TokenPatientModel.token).filter(TokenPatientModel.user_id == user_id[0]).filter(TokenPatientModel.created_at > start_of_day).one_or_none()
        if existing_token:
            token_data = {
                "tower" : "Tower A",
                "floor" : "Floor 1",
                "location" : "Helpdesk",
                "token_id" : existing_token[0]
            }
            return token_data
        token_id = generate_unique_location_token()
        token = TokenPatientModel(
            user_id= user_id[0],
            token=token_id
        )
        db.add(token)
        db.commit()
        token_data = {
            "tower" : "Tower A",
            "floor" : "Floor 1",
            "location" : "Helpdesk",
            "token_id" : token_id
        }
        return token_data
    except Exception as e:
        logger.exception("Failed to store data")
        raise MutationError("Failed to store data")

def get_patient_details_by_uhid_token(db:Session,device_id, uhid:str, token:str):
    try:
        now = datetime.datetime.now(pytz.timezone('Asia/Kolkata'))
        start_of_day = now.replace(hour=0, minute=0, second=0, microsecond=0)
        if token:
            user_details = db.query(UserModel.name,UserModel.phone_number,UserModel.umr_no,UserModel.id,TokenPatientModel.token).filter(TokenPatientModel.token == token
                ).filter(TokenPatientModel.created_at > start_of_day).join(UserModel, UserModel.id == TokenPatientModel.user_id).one_or_none()
        if uhid:
            user_details = db.query(UserModel.name,UserModel.phone_number,UserModel.umr_no,UserModel.id,TokenPatientModel.token).filter(UserModel.umr_no == uhid).filter(TokenPatientModel.created_at > start_of_day).join(TokenPatientModel, TokenPatientModel.user_id == UserModel.id).one_or_none()
            if not user_details:
                try:
                    res = get_access_token()
                    token = "Bearer " + res["access_token"]
                    headers = {"Authorization": token}
                    logger.info(os.environ["AIG_BASE_URL"])
                    res1 = handle_request(
                        os.environ["AIG_BASE_URL"] + GET_PATIENT_DETAILS_BY_UHID,
                        headers,{"patientID": uhid})
                except MutationError as ex:
                    raise MutationError(ex.message)
                if res1.json().get('response') is not None:
                    user_registered=  res1.json()['response']
                    user = UserModel(
                        name=user_registered.get("patientName"),
                        umr_no=user_registered.get("patientID"),
                        phone_number =user_registered.get("phone_number"),
                        device_id = device_id
                    )
                    db.add(user)
                    db.commit()
                    user_details = db.query(UserModel.name,UserModel.phone_number,UserModel.umr_no,UserModel.id,TokenPatientModel.token).filter(UserModel.umr_no == uhid).filter(TokenPatientModel.created_at > start_of_day).join(TokenPatientModel, TokenPatientModel.user_id == UserModel.id).one_or_none()
                else:
                    raise MutationError("uhid not found")
        user_details1 = {
            "name" : user_details[0],
            "age": "",
            "gender": "",
            "phone_number": user_details[1],
            "umr_no": user_details[2],
            "id": user_details[3],
            "token" : user_details[4] 
            }
        return user_details1
    except Exception as ex:
        raise MutationError("Error While Getting Data")

def consult_doctor_patient_mapping(db:Session,uhid:str, doctor_id:str, doctor_name:str,speciality_code:str, speciality: str, consultation_amt: int):
    try:
        user_id = db.query(UserModel.id).filter(UserModel.umr_no == uhid).one_or_none()
        if user_id:
            PatientMap = RelConsultDoctorPatientModel(
                user_id = user_id[0],
                doctor_id = doctor_id,
                doctor_name= doctor_name,
                speciality = speciality,
                speciality_code = speciality_code,
                consultation_amt = consultation_amt
                )
            db.add(PatientMap)
            db.commit()
        else:
            raise MutationError("uhid not found")
    except Exception as e:
        logger.exception("Failed to store data")
        raise MutationError("Failed to store data")

def check_user_exists(db:Session, first_name: str, date_of_birth: str, gender: str):
    try:
        phone_number_query = f"SELECT CONCAT(IACode,'.',Registrationno),Pcellno FROM dbo.M_Patient WHERE ((CASE WHEN LastName IS NOT NULL AND LastName != '.' AND LastName != '' THEN CASE WHEN MiddleName IS NOT NULL AND MiddleName != '.' AND MiddleName != '' THEN CONCAT(Firstname, ' ', MiddleName, ' ', LastName) ELSE CONCAT(Firstname, ' ', LastName) END WHEN MiddleName IS NOT NULL AND MiddleName != '.' AND MiddleName != '' THEN CONCAT(Firstname, ' ', MiddleName) ELSE Firstname END)='{first_name}' OR (CASE WHEN LastName IS NOT NULL AND LastName != '.' AND LastName != '' THEN CASE WHEN MiddleName IS NOT NULL AND MiddleName != '.' AND MiddleName != '' THEN CONCAT(LastName, ' ', MiddleName, ' ', Firstname) ELSE CONCAT(LastName, ' ', Firstname) END WHEN MiddleName IS NOT NULL AND MiddleName != '.' AND MiddleName != '' THEN CONCAT(MiddleName, ' ', Firstname) ELSE Firstname END) ='{first_name}') AND CAST(DateOfBirth AS DATE)='{date_of_birth}' AND Sex={gender}"
        phone_number_result = db.execute(phone_number_query).one_or_none()
        logger.info(f'phone_number {phone_number_result}')
        return phone_number_result
    except Exception as e:
        logger.info(f"Error occured{e}")
        return None

def whatsapp_lab_msg(db:Session,hosp_code,whatsapp_data:List[WhatsappData]):
    config_data = db.query(ConfigModel.data).filter(ConfigModel.hospital_code==hosp_code).one()
    config_data= json.loads(config_data.data)
    token = "Bearer " + config_data.get("one_aig_token","")
    headers = {"Authorization": token}
    print(headers,"headers")

    success_resp = None

    for data in whatsapp_data:
        body=  {
            "hospCode": hosp_code,
            "patientID": data.patient_id,
            "type": data.type,
            "orderID": data.order_id,
            "testID": data.test_id,
            "patType": data.pat_type,
            "orderDate": data.order_date,
            "orderNo": data.order_no,
            "showlogo": True
        }
        logger.info(body)
        res1 = handle_request1(
            os.environ["ONE_AIG_BASE_URL"] + LAB_WHATSAPP_MSG,headers,body
        )
        logger.info(res1.text)
        if res1.status_code == 200 and success_resp is None:
            success_resp = res1.text
    if success_resp:
        return True, success_resp
    else:
        return False, "Failed to send"