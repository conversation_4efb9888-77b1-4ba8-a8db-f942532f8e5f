import asyncio
from typing_extensions import Annotated
import uuid
from jose import jws
from datetime import datetime, timedelta
import logging
import os
from bms.resolvers import get_patient_details
from database.db_conf import SessionLocal
from user.resolvers import (check_fuzzy_logic, enter_vitals, get_cities, get_master_details_by_lang, get_print_status,get_relations, get_states, generate_otp, print_report_old,track_screen,validate_otp, register_user, resend_otp,
        insert_into_operations,get_gender,get_locality,get_marital_status,get_nationality,get_title,get_mandatory_fields,
        get_address_by_pincode,get_modules_list,get_all_reports,view_reports,print_report,get_id_card_type,get_config_data,check_device_login,generate_token,get_all_doctors,get_all_specialization,get_patient_details_by_uhid_token,consult_doctor_patient_mapping,check_user_exists,whatsapp_lab_msg)
from exceptions.exceptions import MutationError
from graphql_types import MutationResponse, QueryResponse, ReportDetail, UserRegister, VitalD<PERSON>, WhatsappData
from typing import AsyncGenerator, List, Optional
import strawberry
from user.models import  UserType as UserTypeModel,User as UserModel,RefTypeEnum, Printer as PrinterModel,Relation as RelationModel
logger = logging.getLogger()
from util.globals import calculate_age
import python_lang as lang
import re
_ = lang.get

@strawberry.type
class User:
    id: int
    name: str
    phone_number: str
    umr_no: str
    instance = strawberry.Private[UserModel]

    @classmethod
    def from_instance(cls, instance: UserModel):
        return cls(
            id=instance.id,
            name=instance.name,
            phone_number= instance.phone_number,
            umr_no =instance.umr_no
        )



@strawberry.type
class UserType:
    id: int
    name: str
    code: str
    status: str

    instance = strawberry.Private[UserTypeModel]

    @classmethod
    def from_instance(cls, instance: UserTypeModel):
        return cls(
            id=instance.id,
            name=instance.name,
            code=instance.code,
            status=instance.status.name
        )



@strawberry.type
class Payment:
    name: str
    amount: str
    code: str

    @classmethod
    def from_instance(cls, name: str,  code: str,amount: str):
        return cls(
            name=name,
            code=code,
            amount=amount,
        )


@strawberry.type
class PatientDetails:
    patientId: Optional[str]
    patientName: Optional[str]
    gender: Optional[str]
    age: Optional[str]
    address: Optional[str]
    city: Optional[str]
    state: Optional[str]
    country: Optional[str]
    phone_number: Optional[str]

    @classmethod
    def from_instance(cls, patient_id: str, patient_name: str, gender: str, age: str, address: str, city:str, state: str, country: str, phone_number: str):
        return cls(
            patientId=patient_id,
            patientName=patient_name,
            gender=gender,
            age=age,
            address=address,
            city=city,
            state=state,
            country=country,
            phone_number=phone_number
            )


@strawberry.type
class PatientLogin:
    status: Optional[str] = None
    access_token: Optional[str] = None
    payment: Optional[Payment] = None
    users: Optional[List[PatientDetails]] = None
    aadhar : Optional[Annotated["AadharData", strawberry.lazy("abdm.schema")]] = None

    @classmethod
    def from_instance(cls, users: Optional[List[PatientDetails]], phone_number: str,  access_token: str, payment: Payment, status: str, amount_list: List[Payment], db,aadhar):
        return cls(
            access_token=access_token,
            payment=Payment.from_instance(
                payment.name,payment.code, payment.amount) if payment is not None else None,
            status=status,
            # appointment=Appointments.from_instance(
            #     appointment, db, amount_list) if appointment is not None else None,
            users=[PatientDetails.from_instance(
                user.get("patientID"), user.get("patientName"),user.get("gender"), user.get("age"), user.get("address"), user.get("city"), user.get("state"), user.get("country"), phone_number) 
                for user in users] if users is not None else None,
            aadhar=aadhar
        )


@strawberry.type
class UserProfile:
    id: int
    name: str
    age: Optional[int]
    gender: str
    date_of_birth: Optional[str]
    instance = strawberry.Private[UserModel]

    @classmethod
    def from_instance(cls, instance: UserModel, vital_codes: List[str], db):
        return cls(
            id=instance.id,
            name=instance.name,
            age=calculate_age(instance.date_of_birth, 1),
            gender="Male",
            department="Cardialogy",
            date_of_birth=instance.date_of_birth,
        )


@strawberry.type
class DoctorAvailableSlots:
    doctor_id: int
    slots: List[str]


@strawberry.type
class OTPStatus:
    status: str
    request_id: str


@strawberry.type
class AadharOTP:
    txn_id: Optional[str]
    mobile_number: Optional[str]
    status: Optional[str]

@strawberry.type
class AadharData:
    first_name: Optional[str]
    phone_number: Optional[str]
    address:Optional[str]
    age:Optional[str]
    status:Optional[str]
    gender:Optional[str]
    access_token:Optional[str]
    health_id:Optional[str]
    aadhar:Optional[str]
    txn_id:Optional[str]
    qr_code:Optional[str]
    date_of_birth : Optional[str]
    photo:Optional[str]
    phr_address : Optional[str]
    health_card: Optional[str]
    mobile_linked : Optional[str]


@strawberry.type
class LabReports:
    report_name: str
    report_category: str
    record_no: str
    record_date: str
    record_file: Optional[str]

@strawberry.type
class Printer:
    report_id: int
    url:Optional[str]
    base64:Optional[str]

@strawberry.type
class PrintStatus:
    id: str
    status: Optional[str]
    instance = strawberry.Private[PrinterModel]

    @classmethod
    def from_instance(cls, instance: PrinterModel):
        return cls(
            id=instance.test_order_pat_id,
            status= instance.cups_job_status,
        )

@strawberry.type
class UhidTokenType:
    patient_name : Optional[str]
    age : Optional[str] = ""
    gender : Optional[str] =""
    phone_number : Optional[str]
    token_id : Optional[str]
    uhid : Optional[str]
    user_id : Optional[str]

@strawberry.type
class TokenDataType:
    tower: Optional[str] = ""
    floor: Optional[str] =""
    location: Optional[str] =""
    token_id: Optional[str] =""
    
@strawberry.type
class RegistrationNo:
    registration_no: str
    token_data : Optional[TokenDataType]
    
    @classmethod
    def from_instance(cls,registration_no: str, token_data: TokenDataType):
        return cls(registration_no=registration_no, token_data= token_data)
    
@strawberry.type
class Address:
    state_name: Optional[str] =None
    city_name: Optional[str] = None
    locality: Optional[str] = None
    pincode: Optional[str] = None

    @classmethod
    def from_instance(cls,state_name:str,city_name:str,locality:str,pincode:str):
            return cls(
            state_name=state_name,
            city_name=city_name,
            locality=locality,
            pincode=pincode
            )

@strawberry.type
class Reports:
    order_number:str
    order_id:int
    test_name:str
    test_id:int
    order_date:str
    pat_type:int
    status:str
    type: str
    id: str
    
    @classmethod
    def from_instance(cls,order_number:str,order_id:int,test_name:str,test_id:int,order_date:str,pat_type:int,status:str,type:str,id:str):
        return cls(
            order_number=order_number,
            order_id=order_id,
            test_name=test_name,
            test_id=test_id,
            order_date=order_date,
            pat_type=pat_type,
            status=status,
            type= type,
            id = id
        )

@strawberry.type
class ConfigData:
    ms_age:int
    session_expiry_count:int
    session_popup_count:int
    success_session_expiry_count:int
    success_session_popup_count:int
    image_url: str
    icon_url: str
    app_url: str
    footer_sub_header:str
    footer_sub_text:str
    footer_address:str
    footer_web_link:str
    footer_ph_no:str
    footer_mail:str
    footer_social_media:str
    print_server_ip: str
    location:str
    device_type: Optional[str] = None
    sub_device_type: Optional[str] = None
    vital_print_enabled: Optional[bool] = False

    @classmethod
    def from_instance(cls,msage:int,session_expiry_count:int,session_popup_count:int,success_session_expiry_count:int,success_session_popup_count:int,image_url:str,icon_url:str,app_url:str,footer_sub_header:str,
    footer_sub_text:str,
    footer_address:str,
    footer_web_link:str,
    footer_ph_no:str,
    footer_mail:str,
    footer_social_media:str,print_server_ip:str,location:str, device_type: Optional[str] = None, sub_device_type: Optional[str] = None, vital_print_enabled: Optional[bool] = False):
        return cls(
            ms_age=msage,
            session_expiry_count=session_expiry_count,
            session_popup_count=session_popup_count,
            success_session_expiry_count=success_session_expiry_count,
            success_session_popup_count=success_session_popup_count,
            image_url=image_url,
            icon_url=icon_url,
            app_url=app_url,
            footer_sub_header=footer_sub_header,
            footer_sub_text=footer_sub_text,
            footer_address=footer_address,
            footer_web_link=footer_web_link,
            footer_ph_no=footer_ph_no,
            footer_mail=footer_mail,
            footer_social_media=footer_social_media,
            print_server_ip=print_server_ip,
            location=location,
            device_type=device_type,
            sub_device_type=sub_device_type,
            vital_print_enabled=vital_print_enabled
            )
    

@strawberry.type
class LangChange:
    name: str    
    code: str
    
    @classmethod
    def from_instance(cls, name:str, language:str):
        lang.select(language if language is not None else 'en')
        return cls(name=name, code=_(name))

@strawberry.type
class Relation:
    name: str    
    code: str
    
    instance = strawberry.Private[RelationModel]

    @classmethod
    def from_instance(cls, instance: RelationModel):
        return cls(name=instance.relation_name, code=instance.relation_code)
@strawberry.type
class Disclamers:
    disclamer1: Optional[str] = None
    disclamer2: Optional[str] = None
    @classmethod
    def from_instance(cls, disclamer1:str,disclamer2:str, language:str):
        lang.select(language if language is not None else 'en')
        return cls(disclamer1=_(disclamer1), disclamer2=_(disclamer2))

@strawberry.type
class NameMatch:
    name: Optional[str] = None
    percentage: Optional[str] = None
  
@strawberry.type
class VitalDetails:
    name: Optional[str] = None
    age: Optional[str] = None
    gender: Optional[str] = None
    access_token: Optional[str] = None
    @classmethod
    def from_instance(cls, name:str, age:str, gender:str, access_token:str):
        return cls(name=name,age=age, gender=gender, access_token=access_token)

@strawberry.type
class Specialization:
    speciality_code: Optional[str]
    speciality_name: Optional[str]

    @classmethod
    def from_instance(cls, speciality_code: str, speciality_name: str):
        return cls(
            speciality_code=speciality_code,
            speciality_name=speciality_name
            )

@strawberry.type
class Doctor:
    doctor_code: Optional[str]
    doctor_name: Optional[str]
    walking: Optional[int]
    speciality: Optional[str]
    speciality_code: Optional[str]
    total_mapped_count: Optional[int]
    consultation_amt: Optional[int]
    
    @classmethod
    def from_instance(cls, doctor_code: str, doctor_name: str,walking: int,speciality_code:str,speciality:str, total_mapped_count:int,consultation_amt: int):
        return cls(
            doctor_code=doctor_code,
            doctor_name=doctor_name.title(),
            walking=walking,
            speciality_code =speciality_code,
            speciality = speciality,
            total_mapped_count=total_mapped_count,
            consultation_amt=consultation_amt
            )

@strawberry.type
class Query:
    @strawberry.field
    def get_user_profile(self, info) -> Optional[UserProfile]:
        db = info.context["db"]
        return None
    
    @strawberry.field
    def get_states(self, info) -> List[str]:
        his_db = info.context["db_aig_his"]
        data = get_states(his_db)
        logger.info(data)
        return data

    @strawberry.field
    def get_cities(self, info,state_name:str) -> List[str]:
        his_db = info.context["db_aig_his"]
        data = get_cities(state_name,his_db)
        return data
    
    @strawberry.field
    def get_gender(self,info) -> List[str]:
        his_db = info.context["db_aig_his"]
        # lang = info.context["lang"]
        data = get_gender(his_db)
        return data
    
    @strawberry.field
    def get_gender_1(self,info) -> QueryResponse[List[LangChange]]:
        his_db = info.context["db_aig_his"]
        lang = info.context["lang"]
        data = get_gender(his_db)
        return QueryResponse.from_status_flag(True, "Data Fetched successfully", [LangChange.from_instance(obj,lang) for obj in data]if data is not None else [])
    
    @strawberry.field
    def get_locality(self, info, city_name:Optional[str]=None, locality_name:Optional[str]=None, state_name:Optional[str]=None) -> List[Address]:
        his_db = info.context["db_aig_his"]
        data = get_locality(city_name,locality_name,his_db,state_name)
        return [Address.from_instance(data[0],data[1],data[2],data[3])for data in data] if data is not None else []
    
    @strawberry.field
    def get_marital_status(self,info)-> List[str]:
        his_db = info.context["db_aig_his"]
        data = get_marital_status(his_db)
        return data
    
    @strawberry.field
    def get_relations(self,info)-> QueryResponse[List[Relation]]:
        db = info.context["db"]
        data = get_relations(db)
        return QueryResponse.from_status_flag(True, "Data Fetched successfully", [Relation.from_instance(obj) for obj in data]if data is not None else [])
    
    @strawberry.field
    def get_marital_status_1(self,info) -> QueryResponse[List[LangChange]]:
        his_db = info.context["db_aig_his"]
        lang = info.context["lang"]
        data = get_marital_status(his_db)
        return QueryResponse.from_status_flag(True, "Data Fetched successfully", [LangChange.from_instance(obj,lang) for obj in data]if data is not None else [])
    
    @strawberry.field
    def get_nationality(self,info)-> List[str]:
        his_db = info.context["db_aig_his"]
        data = get_nationality(his_db)
        return data
    
    @strawberry.field
    def get_title(self,info,gender: Optional[str] = "")-> List[str]:
        his_db = info.context["db_aig_his"]
        data = get_title(his_db, gender)
        return data
    
    @strawberry.field
    def get_mandatory_fields(self,info)-> List[str]:
        db = info.context["db"]
        data = get_mandatory_fields(db)
        return data
    
    @strawberry.field
    def get_address_by_pincode(self,info,pincode: str)-> Optional[List[Address]]:
        his_db = info.context["db_aig_his"]
        data = get_address_by_pincode(his_db, pincode)
        return [Address.from_instance(data[0],data[1],data[2],pincode)for data in data] if data is not None else None
    
    @strawberry.field
    def get_modules_list(self,info) -> Optional[List[str]]:
        device_id = info.context["device_id"]
        user = info.context["auth_user"]
        db = info.context["db"]
        data = get_modules_list(db,device_id, user)
        return data 
    
    @strawberry.field
    def get_all_reports(self,info,uhid:str)-> Optional[List[Reports]]:
        hosp_code=info.context["device_detail"].hospital_code
        db=info.context["db"]
        reports_count=info.context["device_detail"].max_report_count
        data=get_all_reports(uhid,hosp_code,reports_count,db)
        return [Reports.from_instance(data['orderNo'],data['orderID'],data['testName'],data['testID'],data['orderDate'],data['patType'],data["status"],data["type"],data["id"]) for data in data]if data is not None else None
        
    @strawberry.field
    def view_reports(self,info,order_id:int,patient_id:str,pat_type:int,test_id:int, type: str, order_number:str, order_date:str) -> QueryResponse[str]:
        try:
            pacs_base_url=info.context["device_detail"].pacs_base_url
            hosp_code=info.context["device_detail"].hospital_code
            db= info.context["db"]
            base64=view_reports(db,order_id,patient_id,pat_type,test_id,type,hosp_code,pacs_base_url,order_number,order_date,True)
            return QueryResponse.from_status_flag(True, "Reports Fetched successfully", base64)
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    @strawberry.field
    def get_id_card_type(self,info)->Optional[List[str]]:
        his_db = info.context["db_aig_his"]
        data = get_id_card_type()
        return data
    
    @strawberry.field
    def get_id_card_type_1(self,info) -> QueryResponse[List[LangChange]]:
        his_db = info.context["db_aig_his"]
        lang = info.context["lang"]
        data = get_id_card_type()
        return QueryResponse.from_status_flag(True, "Data Fetched successfully", [LangChange.from_instance(obj,lang) for obj in data]if data is not None else [])
    
    @strawberry.field
    def get_config_data(self,info)-> QueryResponse[ConfigData]:
        try:
            db=info.context["db"]
            hosp_code=info.context["device_detail"].hospital_code if info.context["device_detail"] is not None else None
            device_id = info.context["device_id"]
            data=get_config_data(db,hosp_code,device_id)
            return QueryResponse.from_status_flag(True, "Data Fetched successfully", ConfigData.from_instance(data['ms_age'],data['session_expiry_count'],data['session_popup_count'],data['success_session_expiry_count'],data['success_session_popup_count'], data['image_url'],data['icon_url'],data['app_url'],data["footer_sub_header"],data["footer_sub_text"],
                data["footer_address"],data["footer_web_link"],data["footer_ph_no"],data["footer_mail"],data["footer_social_media"],data['print_server_ip'],data['location'],data["device_type"],data["sub_device_type"],data["vital_print_enabled"]))
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

    
    @strawberry.field
    def get_master_details_by_lang(self,info)-> QueryResponse[Disclamers]:
        try:
            db=info.context["db"]
            hosp_code=info.context["device_detail"].hospital_code
            lang = info.context["lang"]
            data=get_master_details_by_lang(db,hosp_code)
            return QueryResponse.from_status_flag(True, "Data Fetched successfully", Disclamers.from_instance(data.get("disclamer1"),data.get("disclamer2"),lang) if data is not None else None)
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def check_already_registered(self,info,names:List[str], name:str) -> QueryResponse[NameMatch]:
        success=True
        res=None
        message="No Match Found"
        per = info.context["device_detail"].reg_match_perc
        language = info.context["lang"]
        his_db = info.context["db_aig_his"]
        if len(names)>0:
            titles= get_title(his_db)
            pattern = '|'.join(map(re.escape, titles))
            names = [re.sub(r'^(' + pattern + r')\s*', '', name) for name in names]
            name = re.sub(r'^(' + pattern + r')\s*', '', name)
            data = check_fuzzy_logic(names, name)
            res= NameMatch(name=data[0],percentage=data[1])
            if data[1]>per:
                success=False
                selected_language = language if language is not None else 'en'
                lang.select(selected_language)
                message = _("Found match with the name")
                message = message + f" {data[0]}" if selected_language  == 'en' else f"{data[0]} " +message
        else:
            success=True
        return QueryResponse.from_status_flag(success, message,res)
    
    @strawberry.field
    def get_all_specialization(self, info,hosp_code: str) -> QueryResponse[List[Specialization]]:
        try:
            response = get_all_specialization(hosp_code)
            return QueryResponse.from_status_flag(True, "Data Fetched Successfully", [Specialization.from_instance(
            data.get("specialityCode"), data.get("specialityName")) 
            for data in response] if response is not None else None)
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

    @strawberry.field
    def get_all_doctors(self,info, hosp_code: str,speciality_code: Optional[str]="", doctor_code: Optional[str]="") -> QueryResponse[List[Doctor]]:
        try:
            db = info.context["db"]
            response = get_all_doctors(db,hosp_code,speciality_code,doctor_code)
            return QueryResponse.from_status_flag(True, "Data Fetched Successfully", [Doctor.from_instance(
            data.get("doctorCode"), data.get("doctorName"),data.get("walking"),data.get("specialityCode"),data.get("specialityName"),data.get("total_count"),data.get("consultation_amt")) 
            for data in response] if response is not None else [])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None) 
        
    @strawberry.field
    def check_user_exists_or_not(self,info, name:str, date_of_birth:Optional[str]="", gender:Optional[str]="") -> QueryResponse[NameMatch]:
        success=True
        res=None
        message="No Match Found"
        # his_db = info.context["db_aig_his"]
        # if gender.lower() == "m" or gender.lower() == "male":
        #     gender = 1
        # elif gender.lower() == "f" or gender.lower() == "female":
        #     gender = 2
        # else:
        #     gender = 3
        # phone_number = check_user_exists(his_db, name, date_of_birth, gender)
        # if phone_number:
        #     success=False
        #     message =f"You are already registered with AIG using the mobile number ending in {phone_number[1][6:10]}. Your UHID is {phone_number[0]}."
        #     res= NameMatch(name=name,percentage="100")
        return QueryResponse.from_status_flag(success, message,res)

@strawberry.type
class Mutation:


    @strawberry.mutation
    def generate_otp(self, info, ref_id: str,ref_type:Optional[str]=RefTypeEnum.PHONE_NUMBER.name, flow:Optional[str]=None) -> MutationResponse[OTPStatus]:
        try:
            db = info.context["db"]
            hosp_code=info.context["device_detail"].hospital_code
            device_id = info.context["device_id"]
            config_data=get_config_data(db,hosp_code,device_id)
            data,request_id = generate_otp(db, ref_id,flow,ref_type,config_data)
            status = OTPStatus(status=data["status"], request_id=request_id)
            return MutationResponse.from_status_flag(True, data["message"], status)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)

    @strawberry.mutation
    def validate_otp(self, info,request_id: str,otp: str) -> MutationResponse[PatientLogin]:
        try:
            db = info.context["db"]
            users, ref_id = validate_otp(db,request_id,otp)
            status = None
            obj = None
            dt = datetime.now() + \
                timedelta(minutes=float(
                    os.environ["ACCESS_EXPIRE_MINUTES"]))
            access_token = jws.sign({"sub": {"user_id": ref_id ,"user_sub_type": "PATIENT"}, "user_type": "PATIENT", "exp": dt.isoformat(
                ), "device_id": info.context["device_id"]}, os.environ["SECRET_KEY"], algorithm=os.environ["ALGORITHM"])
            if(len(users)>0):
                return MutationResponse.from_status_flag(True, "Login Successfull", PatientLogin.from_instance(users, ref_id, access_token, obj, status, None, db,None) if users is not None else None)
            else:
                status = "REGISTER"
                return MutationResponse.from_status_flag(True, "Login Successfull", PatientLogin.from_instance([], None, access_token, None, status,None, None,None))
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)

    @strawberry.mutation
    def register_user(self, info,  user: UserRegister) -> MutationResponse[RegistrationNo]:
        try:
            db = info.context["db"]
            hosp_code=info.context["device_detail"].hospital_code
            device_id = info.context["device_id"]
            registration_no,token_data = register_user(db, user,hosp_code,device_id)
            return MutationResponse.from_status_flag(True, "Registration successfull", RegistrationNo.from_instance(registration_no, token_data=TokenDataType(tower=token_data.get("tower"),floor=token_data.get("floor"),location=token_data.get("location"),token_id=token_data.get("token_id"))))
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)

    @strawberry.mutation
    def resend_otp(self, info, request_id: str) -> MutationResponse[OTPStatus]:
        try:
            db = info.context["db"]
            data,request_id = resend_otp(db, request_id)
            status = OTPStatus(status=data["status"], request_id=request_id)
            return MutationResponse.from_status_flag(True, data["message"], status)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)

        

    

    @strawberry.mutation
    def track_screen(self, info, action: str , request_id: Optional[str]=None) -> MutationResponse[str]:
        try:
            db = info.context["db"]
            device_id=info.context["device_id"]
            lang = info.context["lang"]
            request_id=track_screen(db,action,request_id,device_id,lang)
            return MutationResponse.from_status_flag(True, "Screen Tracked Successfully", request_id)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.mutation
    def insert_into_operations(self,info,name:str, operation_type: str,status: str,entity_type:str,auth_type:str, user_type_id: Optional[int] = None)->MutationResponse[None]:
        try:
            db= info.context["db"]
            insert_into_operations(db,name,operation_type,status,entity_type,user_type_id,auth_type)
            return MutationResponse.from_status_flag(True,"Inserted the values Successfully",None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False,ex.message,None)
    
    @strawberry.mutation
    async def request_print(self,info,patient_id:str, report_details:List[ReportDetail], request_id:Optional[str]=None)->MutationResponse[str]:
        try:
            db=info.context["db"]
            hosp_code=info.context["device_detail"].hospital_code
            printer_name=info.context["device_detail"].printer_name
            reports_count=info.context["device_detail"].max_report_count
            pacs_base_url=info.context["device_detail"].pacs_base_url
            device_id=info.context["device_id"]
            max_report_select_count = info.context["device_detail"].max_report_select_count
            print_wait_time = info.context["device_detail"].print_wait_time
            msg=await print_report_old(db,patient_id,report_details,printer_name,hosp_code,reports_count,pacs_base_url,device_id,max_report_select_count,print_wait_time,request_id)
            return MutationResponse.from_status_flag(True,msg,None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False,ex.message,None)
   
    @strawberry.mutation
    async def request_print_1(self,info,patient_id:str, report_details:List[ReportDetail], request_id:Optional[str]=None)->MutationResponse[str]:
        try:
            db=info.context["db"]
            hosp_code=info.context["device_detail"].hospital_code
            printer_name=info.context["device_detail"].printer_name
            reports_count=info.context["device_detail"].max_report_count
            pacs_base_url=info.context["device_detail"].pacs_base_url
            device_id=info.context["device_id"]
            max_report_select_count = info.context["device_detail"].max_report_select_count
            print_wait_time = info.context["device_detail"].print_wait_time
            group_id=str(uuid.uuid4())
            if printer_name is None or printer_name.strip() == '':
                raise MutationError("Printer is not configured for this device")
            background_tasks = info.context["background_tasks"]
            background_tasks.add_task(print_report,None,patient_id,report_details,printer_name,hosp_code,reports_count,pacs_base_url,device_id,max_report_select_count,print_wait_time,request_id,group_id)
            try:
                lang = info.context["lang"]
                track_screen(db,"PRINT_STATUS_SCREEN",request_id,device_id,lang)
            except Exception as e:
                logger.exception(e)
            return MutationResponse.from_status_flag(True,"print initated successfully",group_id)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False,ex.message,None)
         
    @strawberry.mutation
    def abort_print(self,info,patient_id:str, group_id:str)->MutationResponse[str]:
        try:
            db=info.context["db"]
            hosp_code=info.context["device_detail"].hospital_code
            printer_name=info.context["device_detail"].printer_name
            reports_count=info.context["device_detail"].max_report_count
            pacs_base_url=info.context["device_detail"].pacs_base_url
            device_id=info.context["device_id"]
            max_report_select_count = info.context["device_detail"].max_report_select_count
            print_wait_time = info.context["device_detail"].print_wait_time
            background_tasks = info.context["background_tasks"]
            return MutationResponse.from_status_flag(True,"print aborted successfully",group_id)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False,ex.message,None)

    @strawberry.mutation
    def device_login(self,info,login_pin:int)->MutationResponse[None]:
        try:
            device_id = info.context["device_id"]
            db = info.context["db"]
            msg = check_device_login(db,device_id,login_pin)
            return MutationResponse.from_status_flag(True, msg, None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.mutation
    def vital_details_login(self, info, uhid: str) -> MutationResponse[VitalDetails]:
        try:
            db= info.context['db']
            # result = get_patient_details(uhid)
            result={"patientName":"Monika","age":"20 years","gender":"Female"}
            dt = datetime.now() + \
                timedelta(minutes=float(
                    os.environ["ACCESS_EXPIRE_MINUTES"]))
            access_token = jws.sign({"sub": {"user_sub_type":"VITAL_KIOSK","patient_id":uhid}, "device_id":info.context["device_id"],"user_type": "HOSPITAL", "exp": dt.isoformat(
                )}, os.environ["SECRET_KEY"], algorithm=os.environ["ALGORITHM"])
            logger.info("Vital device logged in suscessfully")
            return MutationResponse.from_status_flag(True,"Login Successfull",VitalDetails.from_instance(result["patientName"],result["age"],result["gender"],access_token))
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message,None)
    
    @strawberry.mutation
    def enter_vitals(self,info,data: List[VitalData]) -> MutationResponse[str]:
        try:
            uhid = info.context["patient_id"]
            data = enter_vitals(data,uhid)
            return MutationResponse.from_status_flag(True,"Success","Details added Successfully")
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message,None)
        
    @strawberry.mutation
    def generate_token(self,info,uhid:str) -> MutationResponse[TokenDataType]:
        try:
            db = info.context["db"]
            device_id = info.context["device_id"]
            token_data = generate_token(db,device_id,uhid)
            return MutationResponse.from_status_flag(True,"Success",TokenDataType(tower=token_data.get("tower"),floor=token_data.get("floor"),location=token_data.get("location"),token_id=token_data.get("token_id")))
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message,None)
    
    @strawberry.mutation
    def get_patient_details_by_uhid_token(self,info,uhid:Optional[str]="",token_id:Optional[str]="") -> MutationResponse[UhidTokenType]:
        try:
            db = info.context["db"]
            device_id = info.context["device_id"]
            response = get_patient_details_by_uhid_token(db,device_id,uhid,token_id)
            return MutationResponse.from_status_flag(True,"Success",UhidTokenType(patient_name=response.get("name"),age=response.get("age",""),gender=response.get("gender",""),phone_number=response.get("phone_number"),token_id=response.get("token"),uhid=response.get("umr_no"),user_id=response.get("id")))
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message,None)
    
    @strawberry.mutation
    def consult_doctor_patient_mapping(self,info, uhid:str, doctor_id:str, doctor_name:str, speciality_code:str, speciality:str, consultation_amt: int) -> MutationResponse[str]:
        try:
            db = info.context["db"]
            consult_doctor_patient_mapping(db, uhid, doctor_id, doctor_name, speciality_code,speciality, consultation_amt)
            return MutationResponse.from_status_flag(True,"Patient Doctor Map Successfull",None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message,None)
    
    @strawberry.mutation
    def lab_whatsapp_msg(self,info,whatsapp_data:List[WhatsappData]) -> MutationResponse[str]:
        try:
            db= info.context['db']
            lang = info.context["lang"]
            hosp_code=info.context["device_detail"].hospital_code
            device_id=info.context["device_detail"].device_code
            is_success,data=whatsapp_lab_msg(db,hosp_code,whatsapp_data)
            return MutationResponse.from_status_flag(is_success,data,None)
        except MutationResponse as ex:
            return MutationResponse.from_status_flag(False,ex.message,None)


@strawberry.type
class Subscription:
    @strawberry.subscription
    async def get_print_status(self,info,group_id: str, uhid:str) -> AsyncGenerator[List[PrintStatus], None]:
        try:
            while(True):
                db = SessionLocal()
                data = get_print_status(db, uhid, group_id)
                db.close()
                yield [PrintStatus.from_instance(obj) for obj in data]
                await asyncio.sleep(0.5)
        except Exception as e:
            logger.exception(e)
        finally:
            if db is not None: db.close()