"""vital print column added

Revision ID: aa4d3f33f2b6
Revises: a3062ca58462
Create Date: 2025-06-20 13:53:37.553898

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'aa4d3f33f2b6'
down_revision = 'a3062ca58462'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('device', sa.Column('printer_ip', sa.String(), nullable=True))
    op.add_column('device', sa.Column('vital_print_enabled', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('device', 'vital_print_enabled')
    op.drop_column('device', 'printer_ip')
    # ### end Alembic commands ###
