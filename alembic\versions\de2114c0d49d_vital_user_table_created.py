"""vital user table created

Revision ID: de2114c0d49d
Revises: ea867b688290
Create Date: 2024-08-20 12:46:05.860997

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'de2114c0d49d'
down_revision = 'ea867b688290'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
