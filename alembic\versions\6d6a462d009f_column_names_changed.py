"""column names changed

Revision ID: 6d6a462d009f
Revises: 354736b7007e
Create Date: 2023-10-24 13:22:46.550254

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '6d6a462d009f'
down_revision = '354736b7007e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_discharge', sa.Column('otc_clearance_date', sa.DateTime(timezone=True), nullable=True))
    op.add_column('user_discharge', sa.Column('f_and_b_clearance_date', sa.DateTime(timezone=True), nullable=True))
    op.drop_column('user_discharge', 'F_and_B_clearance_date')
    op.drop_column('user_discharge', 'OTC_clearance_date')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_discharge', sa.Column('OTC_clearance_date', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True))
    op.add_column('user_discharge', sa.Column('F_and_B_clearance_date', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True))
    op.drop_column('user_discharge', 'f_and_b_clearance_date')
    op.drop_column('user_discharge', 'otc_clearance_date')
    # ### end Alembic commands ###
