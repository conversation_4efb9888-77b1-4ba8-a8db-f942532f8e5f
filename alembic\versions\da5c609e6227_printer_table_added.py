"""Printer table added

Revision ID: da5c609e6227
Revises: bafbd7cb74fd
Create Date: 2023-08-21 09:53:48.984931

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'da5c609e6227'
down_revision = 'bafbd7cb74fd'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('printer',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('printer_name', sa.String(), nullable=True),
    sa.Column('report_id', sa.Integer(), nullable=True),
    sa.Column('cups_job_id', sa.Integer(), nullable=True),
    sa.Column('cups_job_status', sa.String(), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('printer')
    # ### end Alembic commands ###
