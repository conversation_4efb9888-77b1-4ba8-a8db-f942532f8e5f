"""screen name added

Revision ID: bafbd7cb74fd
Revises: 3453f45123f5
Create Date: 2023-08-08 06:29:03.974310

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'bafbd7cb74fd'
down_revision = '3453f45123f5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('screen', sa.Column('screen_name', sa.String(), nullable=True))
    op.create_foreign_key('track_screen_action_fk', 'track_screen', 'screen', ['action'], ['screen_code'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('track_screen_action_fk', 'track_screen', type_='foreignkey')
    op.drop_column('screen', 'screen_name')
    # ### end Alembic commands ###
