"""appointment time column added

Revision ID: 859e80b08d6f
Revises: d5308c39eff4
Create Date: 2025-05-08 12:09:31.837550

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '859e80b08d6f'
down_revision = 'd5308c39eff4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_service', sa.Column('appointment_date_time', sa.DateTime(timezone=True), nullable=True), schema='queue')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_service', 'appointment_date_time', schema='queue')
    # ### end Alembic commands ###
