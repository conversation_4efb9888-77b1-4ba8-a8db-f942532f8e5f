"""report_id column changed to string

Revision ID: 178cc4f92751
Revises: a53d60322017
Create Date: 2023-09-14 11:26:40.978173

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '178cc4f92751'
down_revision = 'a53d60322017'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('printer', sa.Column('test_order_pat_id', sa.String(), nullable=True))
    op.drop_column('printer', 'report_id')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('printer', sa.Column('report_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.drop_column('printer', 'test_order_pat_id')
    # ### end Alembic commands ###
