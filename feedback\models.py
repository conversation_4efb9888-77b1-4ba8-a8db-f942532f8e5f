
import enum
from sqlalchemy import BigInteger, Column, DateTime, Enum, ForeignKey, SmallInteger, Text, String, Time, UniqueConstraint, text, Integer, Boolean, Numeric
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import strawberry

from database.db_conf import Base

@strawberry.enum
class FBCategoryTypeEnum(enum.Enum):
    TEN_RATING = "TEN_RATING"
    FIVE_RATING = "FIVE_RATING"
    TEXT = "TEXT"



@strawberry.enum
class FBQuestionsTypeEnum(enum.Enum):
    SELECT = "SELECT"
    SELECT_MULTIPLE = "SELECT_MULTIPLE"
    TEXT = "TEXT"
    RATING = "RATING"

class FBCategory(Base):
    __tablename__ = "fb_category"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String, nullable=False)
    code = Column(String, nullable=False)
    type = Column(Enum(FBCategoryTypeEnum), nullable=False)
    is_active = Column(Boolean, default=True)
    icon = Column(String)
    priority = Column(Numeric)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    __table_args__ = (UniqueConstraint('code', name='fb_category_code_uc'),)
    questions = relationship("FBQuestions", back_populates="fb_category")

    def __repr__(self) -> str:
        return "<FBCategory %r>" % self.id



class FBQuestions(Base):
    __tablename__ = "fb_questions"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String, nullable=False)
    code = Column(String, nullable=False, unique=True)
    type = Column(Enum(FBQuestionsTypeEnum), nullable=False)
    min_rating = Column(Numeric)
    max_rating = Column(Numeric)
    fb_category_id = Column(BigInteger, ForeignKey(
        "fb_category.id", name="fb_questions_fb_category_id_fk"), nullable=False)
    is_active = Column(Boolean, default=True)
    priority = Column(Numeric)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    fb_category = relationship("FBCategory")
    options = relationship("FBQuestionOptions", back_populates="fb_questions", lazy="dynamic", order_by="FBQuestionOptions.priority")
    __table_args__ = (UniqueConstraint('code', name='fb_questions_code_uc'),)
    
    def __repr__(self) -> str:
        return "<FBQuestions %r>" % self.id
    
class FBQuestionOptions(Base):
    __tablename__ = "fb_question_options"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String, nullable=False)
    fb_question_id = Column(BigInteger, ForeignKey(
        "fb_questions.id", name="fb_question_options_fb_question_id_fk"), nullable=False)
    is_active = Column(Boolean, default=True)
    priority = Column(Numeric)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    fb_questions = relationship("FBQuestions")
    
    def __repr__(self) -> str:
        return "<FBQuestionOptions %r>" % self.id

class UserFeedback(Base):
    __tablename__ = "user_feedback"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, ForeignKey("user.id", name="user_feedback_user_id_fk"))
    category_id = Column(BigInteger, ForeignKey("fb_category.id", name="users_feedback_fb_category_id_fk"), nullable=False)
    phone_number = Column(String)
    rating = Column(Numeric)
    remarks = Column(String)
    txn_id = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    device_id = Column(String)

    def __repr__(self) -> str:
        return "<UsersFeedback %r>" % self.id

class UserQuestions(Base):
    __tablename__ = "user_questions"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_feedback_id = Column(BigInteger, ForeignKey("user_feedback.id", name="user_questions_user_feedback_id_fk"), nullable=False)
    question_id = Column(BigInteger, ForeignKey("fb_questions.id", name="user_questions_fb_question_id_fk"))
    rating = Column(Numeric)
    remarks = Column(String)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self) -> str:
        return "<UserQuestions %r>" % self.id


class RelUserQuestionOptions(Base):
    __tablename__ = "rel_user_question_options"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_question_id = Column(BigInteger, ForeignKey("user_questions.id", name="rel_user_question_options_user_question_id_fk"), nullable=False)
    option_id = Column(BigInteger, ForeignKey("fb_question_options.id", name="rel_user_question_options_option_id_fk"), nullable=False)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self) -> str:
        return "<RelUserQuestionOptions %r>" % self.id




