"""bed status and discharge models added

Revision ID: 354736b7007e
Revises: c990c8fc2f36
Create Date: 2023-10-24 06:05:15.986121

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '354736b7007e'
down_revision = 'c990c8fc2f36'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('bed_status',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('ward_name', sa.String(), nullable=True),
    sa.Column('admission_type', sa.String(), nullable=True),
    sa.Column('bed_status', sa.String(), nullable=True),
    sa.Column('patient_name', sa.String(), nullable=True),
    sa.Column('admission_number', sa.String(), nullable=True),
    sa.Column('bed_no', sa.String(), nullable=True),
    sa.Column('bed_class', sa.String(), nullable=True),
    sa.Column('floor', sa.String(), nullable=True),
    sa.Column('uhid', sa.String(), nullable=True),
    sa.Column('tower', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('admission_number')
    )
    op.create_table('user_discharge',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('admission_no', sa.String(), nullable=True),
    sa.Column('uhid', sa.String(), nullable=True),
    sa.Column('patient_name', sa.String(), nullable=True),
    sa.Column('bed', sa.String(), nullable=True),
    sa.Column('admission_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('requested_discharge_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('OTC_clearance_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('blood_bank_clearance_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('discharge_summary', sa.Text(), nullable=True),
    sa.Column('F_and_B_clearance_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('nursing_clearance_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('pharmacy_clearance_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('audit_clearance_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('billing_ack_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('bill_ready_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('discharge_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['admission_no'], ['bed_status.admission_number'], name='user_discharge_admission_no_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_discharge')
    op.drop_table('bed_status')
    # ### end Alembic commands ###
