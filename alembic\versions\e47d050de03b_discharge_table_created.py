"""discharge table created

Revision ID: e47d050de03b
Revises: 2b37884b7b9f
Create Date: 2024-03-27 11:58:38.979309

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e47d050de03b'
down_revision = '2b37884b7b9f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('discharge_history',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('uhid', sa.String(), nullable=True),
    sa.Column('ip_number', sa.BigInteger(), nullable=True),
    sa.Column('patient_name', sa.String(), nullable=True),
    sa.Column('bed', sa.String(), nullable=True),
    sa.Column('admission_date_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('requested_discharge_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('nurse_clearance_date_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('blood_bank_clearance_date_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('discharge_summary_date_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('fnb_clearance_date_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('pharmacy_clearance_date_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('audit_clrsave_date_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('billing_ack_date_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('bill_ready_date_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('clinical_discharge', sa.DateTime(timezone=True), nullable=True),
    sa.Column('otc_clearance_date_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('discharge_history')
    # ### end Alembic commands ###
