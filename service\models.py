import enum
from sqlalchemy import <PERSON>RA<PERSON>, text, JSON, BigInteger, Column, DateTime, Enum, ForeignKey, SmallInteger, Text, String, Time, UniqueConstraint, Integer, Numeric, Boolean,Date, Float
from sqlalchemy.sql import func
import strawberry
from sqlalchemy.orm import relationship
from database.db_conf import Base

@strawberry.enum
class StatusEnum(enum.Enum):
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    
class Service(Base):
    __tablename__ = 'service'
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name= Column(String)
    code =Column(String,unique=True)
    type = Column(String)
    procedure_time = Column(Float)
    allow_grouping = Column(Boolean, default=True)
    priority = Column(Numeric, server_default=text("1"),nullable=False)
    prerequisites = Column(String)
    landmark = Column(String)
    navigation_instructions = Column(String)
    reports_collection = Column(Text)
    service_type = Column(String)
    source = Column(String)
    reports_completion_time = Column(Float)
    profile_ids= Column(ARRAY(Integer))
    prerequisites_conditions = Column(ARRAY(String))  
    status = Column(Enum(StatusEnum), default=StatusEnum.ACTIVE)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    queues=relationship("Queue",secondary="queue.rel_queue_service",viewonly=True)
    __table__args__= (UniqueConstraint('code',name='service_code'),)

    def __repr__(self) -> str:
        return "<Service %r>" % self.id

class ServicePrerequisite(Base):
    __tablename__ = 'service_prerequisite'

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    service_id = Column(Integer, ForeignKey(
        "service.id", name="service_prerequisite_service_id_fk") )
    pre_req_service_id = Column(Integer, ForeignKey(
        "service.id", name="service_prerequisite_pre_req_service_id_fk"))
    time_in_min = Column(Integer)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self) -> str:
        return "<ServicePrerequisite %r>" % self.id
    