"""initial migration

Revision ID: ddbe4721252d
Revises: 
Create Date: 2023-07-31 13:01:36.954120

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ddbe4721252d'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('fb_category',
    sa.<PERSON>umn('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('code', sa.String(), nullable=False),
    sa.Column('type', sa.Enum('TEN_RATING', 'FIVE_RATING', 'TEXT', name='fbcategorytypeenum'), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('icon', sa.String(), nullable=True),
    sa.Column('priority', sa.Numeric(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code', name='fb_category_code_uc')
    )
    op.create_table('feedback_question',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('code', sa.String(), nullable=False),
    sa.Column('type', sa.String(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('priority', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code', name='feedback_question_code_uc')
    )
    op.create_table('operation',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('type', sa.Enum('QUERY', 'MUTATION', 'SUBSCRIPTION', name='operationtypeenum'), nullable=False),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='statusenum'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name', 'type', name='operation_name_type_uc')
    )
    op.create_table('sms_send_detail',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('transaction_id', sa.String(), nullable=False),
    sa.Column('sent_from', sa.String(), nullable=True),
    sa.Column('to', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('resend_count', sa.Integer(), nullable=True),
    sa.Column('message', sa.String(), nullable=True),
    sa.Column('data', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('track_screen',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('request_id', sa.String(), nullable=True),
    sa.Column('action', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('request_id')
    )
    op.create_table('track_screen_logs',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('request_id', sa.String(), nullable=True),
    sa.Column('action', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('umr_no', sa.String(), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('phone_number', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('umr_no', name='user_umr_no')
    )
    op.create_table('user_otp',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('ref_id', sa.String(), nullable=False),
    sa.Column('ref_id_type', sa.Enum('PHONE_NUMBER', 'AADHAR', 'HEALTH_ID', name='userrefidtypeenum'), nullable=False),
    sa.Column('sent_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('otp', sa.String(), nullable=False),
    sa.Column('otp_type', sa.Enum('BOOK_APPOINTMENT', 'REGISTER', 'CHECK_IN', 'PAYMENT', 'REPORTS', 'OTHER', 'HEALTH_PACKAGE', name='otptypeenum'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user_type',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('code', sa.String(), nullable=False),
    sa.Column('entity_type', sa.Enum('HOSPITAL', 'PATIENT', 'STAFF', name='entitytypeenum'), nullable=False),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', name='statusenum'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('fb_questions',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('code', sa.String(), nullable=False),
    sa.Column('type', sa.Enum('SELECT', 'SELECT_MULTIPLE', 'TEXT', 'RATING', name='fbquestionstypeenum'), nullable=False),
    sa.Column('min_rating', sa.Numeric(), nullable=True),
    sa.Column('max_rating', sa.Numeric(), nullable=True),
    sa.Column('fb_category_id', sa.BigInteger(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('priority', sa.Numeric(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['fb_category_id'], ['fb_category.id'], name='fb_questions_fb_category_id_fk'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code'),
    sa.UniqueConstraint('code', name='fb_questions_code_uc')
    )
    op.create_table('payments',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('user_id', sa.BigInteger(), nullable=False),
    sa.Column('service', sa.String(), nullable=False),
    sa.Column('payment_id', sa.String(), nullable=True),
    sa.Column('amount', sa.BigInteger(), nullable=True),
    sa.Column('ref_id', sa.String(), nullable=True),
    sa.Column('payment_status', sa.Enum('PENDING', 'RECIEVED', 'FAILED', 'HOLD', 'SUCCESS', name='paymentstatusenum'), nullable=False),
    sa.Column('razorpay_status', sa.Enum('CREATED', 'AUTHORIZED', 'CAPTURED', 'REFUNDED', 'FAILED', name='razorpaystatusenum'), nullable=True),
    sa.Column('refund_status', sa.Enum('UNINITIATED', 'REQUESTED', 'INITIATED', 'PROCESSING', 'DONE', name='refundstatusenum'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], name='payment_user_id'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('rel_operation_user_type',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('operation_id', sa.BigInteger(), nullable=True),
    sa.Column('entity_type', sa.Enum('HOSPITAL', 'PATIENT', 'STAFF', name='entitytypeenum'), nullable=False),
    sa.Column('user_type_id', sa.BigInteger(), nullable=True),
    sa.Column('auth_type', sa.Enum('NO_AUTH', 'DEVICE_ID', 'BEARER', name='authenticationtypeenum'), server_default='BEARER', nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['operation_id'], ['operation.id'], name='rel_operation_user_type_operation_id_fk'),
    sa.ForeignKeyConstraint(['user_type_id'], ['user_type.id'], name='rel_operation_user_type_user_type_id_fk'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('operation_id', 'user_type_id', name='rel_operation_user_type_operation_id_user_type_id_uc')
    )
    op.create_table('sms_mail_notification_config',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('code', sa.String(), nullable=True),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('sub', sa.String(), nullable=True),
    sa.Column('type', sa.Enum('SMS', 'MAIL', 'NOTIFICATION', name='smstypeenum'), nullable=False),
    sa.Column('sender_email_id', sa.String(), nullable=True),
    sa.Column('sender_password', sa.String(), nullable=True),
    sa.Column('receiver_type', sa.BigInteger(), nullable=True),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('data', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['receiver_type'], ['user_type.id'], name='sms_mail_notification_config_user_type_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user_feedback',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.BigInteger(), nullable=True),
    sa.Column('category_id', sa.BigInteger(), nullable=False),
    sa.Column('phone_number', sa.String(), nullable=True),
    sa.Column('rating', sa.Numeric(), nullable=True),
    sa.Column('remarks', sa.String(), nullable=True),
    sa.Column('txn_id', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['category_id'], ['fb_category.id'], name='users_feedback_fb_category_id_fk'),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], name='user_feedback_user_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('fb_question_options',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('fb_question_id', sa.BigInteger(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('priority', sa.Numeric(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['fb_question_id'], ['fb_questions.id'], name='fb_question_options_fb_question_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('payment_logs',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('payments_id', sa.BigInteger(), nullable=False),
    sa.Column('user_id', sa.BigInteger(), nullable=False),
    sa.Column('payment_status', sa.Enum('PENDING', 'RECIEVED', 'FAILED', 'HOLD', 'SUCCESS', name='paymentstatusenum'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['payments_id'], ['payments.id'], name='payment_logs_payments_id'),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], name='payment_user_id'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user_questions',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('user_feedback_id', sa.BigInteger(), nullable=False),
    sa.Column('question_id', sa.BigInteger(), nullable=True),
    sa.Column('rating', sa.Numeric(), nullable=True),
    sa.Column('remarks', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['question_id'], ['fb_questions.id'], name='user_questions_fb_question_id_fk'),
    sa.ForeignKeyConstraint(['user_feedback_id'], ['user_feedback.id'], name='user_questions_user_feedback_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('rel_user_question_options',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('user_question_id', sa.BigInteger(), nullable=False),
    sa.Column('option_id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['option_id'], ['fb_question_options.id'], name='rel_user_question_options_option_id_fk'),
    sa.ForeignKeyConstraint(['user_question_id'], ['user_questions.id'], name='rel_user_question_options_user_question_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('rel_user_question_options')
    op.drop_table('user_questions')
    op.drop_table('payment_logs')
    op.drop_table('fb_question_options')
    op.drop_table('user_feedback')
    op.drop_table('sms_mail_notification_config')
    op.drop_table('rel_operation_user_type')
    op.drop_table('payments')
    op.drop_table('fb_questions')
    op.drop_table('user_type')
    op.drop_table('user_otp')
    op.drop_table('user')
    op.drop_table('track_screen_logs')
    op.drop_table('track_screen')
    op.drop_table('sms_send_detail')
    op.drop_table('operation')
    op.drop_table('feedback_question')
    op.drop_table('fb_category')
    # ### end Alembic commands ###
