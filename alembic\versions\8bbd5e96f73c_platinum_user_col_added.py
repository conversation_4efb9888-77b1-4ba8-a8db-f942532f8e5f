"""platinum user col added

Revision ID: 8bbd5e96f73c
Revises: aa4d3f33f2b6
Create Date: 2025-07-10 17:29:42.026729

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8bbd5e96f73c'
down_revision = 'aa4d3f33f2b6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user', sa.Column('is_platinum_user', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'is_platinum_user')
    # ### end Alembic commands ###
