"""config table added

Revision ID: 6417bb027417
Revises: 178cc4f92751
Create Date: 2023-09-21 13:27:05.923768

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6417bb027417'
down_revision = '178cc4f92751'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('config',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('hospital_code', sa.String(), nullable=False),
    sa.Column('data', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('config')
    # ### end Alembic commands ###
