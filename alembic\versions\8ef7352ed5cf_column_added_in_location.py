"""column added in location

Revision ID: 8ef7352ed5cf
Revises: e952d43e0423
Create Date: 2024-03-21 06:29:47.743243

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8ef7352ed5cf'
down_revision = 'e952d43e0423'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('location', sa.Column('iot_code', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('location', 'iot_code')
    # ### end Alembic commands ###
