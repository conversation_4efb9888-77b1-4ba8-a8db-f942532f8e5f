"""columns added in service model

Revision ID: 49ab5d3d468e
Revises: fce7faa51f39
Create Date: 2023-10-13 06:36:49.610153

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '49ab5d3d468e'
down_revision = 'fce7faa51f39'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('service', sa.Column('prerequisites', sa.String(), nullable=True))
    op.add_column('service', sa.Column('landmark', sa.String(), nullable=True))
    op.add_column('service', sa.Column('navigation_instructions', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('service', 'navigation_instructions')
    op.drop_column('service', 'landmark')
    op.drop_column('service', 'prerequisites')
    # ### end Alembic commands ###
