"""assessment category added

Revision ID: 81124ceacaa1
Revises: 30203baca74d
Create Date: 2024-02-13 12:40:30.251090

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '81124ceacaa1'
down_revision = '30203baca74d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('assessment_category',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('code', sa.String(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('status', postgresql.ENUM('ACTIVE', 'INACTIVE', name='statusenum',create_type=False), nullable=True),
    sa.Column('priority', sa.Numeric(), server_default=sa.text('1'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    )
    op.create_table('assessment_question',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('assessment_category_id', sa.Integer(), nullable=True),
    sa.Column('code', sa.String(), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('type', sa.String(), nullable=True),
    sa.Column('options', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('validations', postgresql.ARRAY(sa.String()), nullable=True),
    sa.Column('status', postgresql.ENUM('ACTIVE', 'INACTIVE', name='statusenum',create_type=False), nullable=True),
    sa.Column('priority', sa.Numeric(), server_default=sa.text('1'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['assessment_category_id'], ['assessment_category.id'], name='assessment_category_assessment_category_id_fk'),
    sa.PrimaryKeyConstraint('id'),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('assessment_question')
    op.drop_table('assessment_category')
    # ### end Alembic commands ###
