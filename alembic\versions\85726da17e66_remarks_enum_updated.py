"""remarks enum updated

Revision ID: 85726da17e66
Revises: f8d2eb9974a8
Create Date: 2025-04-01 14:53:53.980482

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '85726da17e66'
down_revision = 'f8d2eb9974a8'
branch_labels = None
depends_on = None

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TYPE remarkstypeenum ADD VALUE 'QUEUE_PURGE'")
    op.execute("ALTER TYPE remarkstypeenum ADD VALUE 'QUEUE_HOLD'")

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TYPE remarkstypeenum DROP VALUE 'QUEUE_PURGE'")
    op.execute("ALTER TYPE remarkstypeenum DROP VALUE 'QUEUE_HOLD'")
