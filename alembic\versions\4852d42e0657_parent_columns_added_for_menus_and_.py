""" parent columns added for menus and resources

Revision ID: 4852d42e0657
Revises: 6e1ed9f980e1
Create Date: 2023-11-06 06:05:41.103285

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '4852d42e0657'
down_revision = '6e1ed9f980e1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('menu', sa.Column('parent_menu_id', sa.Integer(), nullable=True))
    op.create_foreign_key('menu_parent_menu_id_fk', 'menu', 'menu', ['parent_menu_id'], ['id'])
    op.add_column('resource', sa.Column('parent_resource_id', sa.Integer(), nullable=True))
    op.create_foreign_key('resource_parent_resource_id_fk', 'resource', 'resource', ['parent_resource_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('resource_parent_resource_id_fk', 'resource', type_='foreignkey')
    op.drop_column('resource', 'parent_resource_id')
    op.drop_constraint('menu_parent_menu_id_fk', 'menu', type_='foreignkey')
    op.drop_column('menu', 'parent_menu_id')
    # ### end Alembic commands ###
