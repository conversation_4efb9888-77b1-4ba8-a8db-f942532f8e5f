"""columns added in visa data and bed remarks

Revision ID: e6db8d8e173f
Revises: 73e864aaa974
Create Date: 2023-12-22 06:07:27.869366

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e6db8d8e173f'
down_revision = '73e864aaa974'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bed_remarks', sa.Column('status', sa.String(), nullable=True))
    op.add_column('user_visa_data', sa.Column('created_by', sa.String(), nullable=True))
    op.add_column('user_visa_data', sa.Column('generated_by', sa.String(), nullable=True))
    op.add_column('user_visa_data', sa.Column('last_updated_by', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_visa_data', 'last_updated_by')
    op.drop_column('user_visa_data', 'generated_by')
    op.drop_column('user_visa_data', 'created_by')
    op.drop_column('bed_remarks', 'status')
    # ### end Alembic commands ###
