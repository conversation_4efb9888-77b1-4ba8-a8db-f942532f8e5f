from datetime import date, datetime, timedelta
import io
import json
import os
import secrets
from time import sleep
from typing import Optional
import uuid
from exceptions.exceptions import MutationError
import logging,pytz
from threading import Thread
import ssl
import urllib3
logger = logging.getLogger()
import requests
import qrcode
import base64
import boto3
from botocore.exceptions import ClientError
from botocore.client import Config
import six
import xml.etree.ElementTree as ET
def initialize():
    global send_notification, update_call_bell
    send_notification = True
    update_call_bell = True
 
from redis import StrictRedis
from redis_cache import RedisCache
import redis
from redis_lock import RedisLock

client = StrictRedis(host=os.environ["REDIS_CONTAINER"], password=os.environ["REDIS_PASSWORD"], decode_responses=True)
cache = RedisCache(redis_client=client)

client_lock = redis.Redis(host=os.environ["REDIS_CONTAINER"], password=os.environ["REDIS_PASSWORD"])

def calculate_age(dob,type):
    try:
        if type==1:
            date_object = datetime.strptime(dob, '%d-%m-%Y').date()
        else:
            date_object = datetime.strptime(dob, '%Y-%m-%d').date()
        today = date.today()
        time_difference = today - date_object
        years = int(time_difference.days/365)
        months = int((time_difference.days/365)*12) - (years*12)
        days = time_difference.days - months*30
        # if years >0:
        return str(years)
        # elif months >0 :
        #     return str(months)+"(M)"
        # else:
        #     return str(days)+"(D)"
    except:
        return None

def handle_request(adress,headers,body,count:Optional[int]=0):
    try:
        import user
        if headers is not None:
            headers["Accept-Language"]= "en-US"
            headers["Content-Type"]="application/json"
        else:
            headers={"Content-Type": "application/json","Accept-Language":"en-US"}
        request= requests.post(adress,headers=headers,json=body,timeout=60)

        if request.status_code==401 or request.status_code==403:
            user.resolvers.get_access_token.invalidate_all()
            res = user.resolvers.get_access_token()
            token = "Bearer " + res["access_token"]
            headers = {"Authorization": token}
            if count<5:
                return handle_request(adress,headers,body,count+1)
        return request
    except requests.ConnectionError as e:
        print(e)
        raise MutationError("OOPS!! Connection Error. Make sure you are connected to Internet. Technical Details given below.\n")        
    except requests.Timeout as e:
        raise MutationError("OOPS!! Timeout Error")
    except requests.RequestException as e:
        raise MutationError("OOPS!! General Error")
    except KeyboardInterrupt:
        raise MutationError("Someone closed the program")

def handle_request1(adress,headers,body):
    try:
        if headers is not None:
            headers["Accept-Language"]= "en-US"
            headers["Content-Type"]="application/json"
        else:
            headers={"Content-Type": "application/json","Accept-Language":"en-US"}
        return requests.post(adress,headers=headers,json=body,timeout=60)
    except requests.ConnectionError as e:
        print(e)
        raise MutationError("OOPS!! Connection Error. Make sure you are connected to Internet. Technical Details given below.\n")        
    except requests.Timeout as e:
        raise MutationError("OOPS!! Timeout Error")
    except requests.RequestException as e:
        raise MutationError("OOPS!! General Error")
    except KeyboardInterrupt:
        raise MutationError("Someone closed the program")
    
def handle_get_request(adress,params,headers=None):
    class CustomHttpAdapter (requests.adapters.HTTPAdapter):
        def __init__(self, ssl_context=None, **kwargs):
            self.ssl_context = ssl_context
            super().__init__(**kwargs)

        def init_poolmanager(self, connections, maxsize, block=False):
            self.poolmanager = urllib3.poolmanager.PoolManager(
                num_pools=connections, maxsize=maxsize,
                block=block, ssl_context=self.ssl_context)


    def get_legacy_session():
        ctx = ssl.create_default_context(ssl.Purpose.SERVER_AUTH)
        ctx.options |= 0x4
        session = requests.session()
        session.mount('https://', CustomHttpAdapter(ctx))
        return session
    try:
        if headers is not None:
            return get_legacy_session().get(adress,params=params,headers=headers,timeout=60)
        else:
            return get_legacy_session().get(adress,params=params,timeout=60)
    except requests.ConnectionError as e:
        print(e)
        raise MutationError("OOPS!! Connection Error. Make sure you are connected to Internet. Technical Details given below.\n")        
    except requests.Timeout as e:
        raise MutationError("OOPS!! Timeout Error")
    except requests.RequestException as e:
        raise MutationError("OOPS!! General Error")
    except KeyboardInterrupt:
        raise MutationError("Someone closed the program")

    
class ThreadWithReturnValue(Thread):
    def __init__(self, *init_args, **init_kwargs):
        Thread.__init__(self, *init_args, **init_kwargs)
        self._return = None
    def run(self):
        self._return = self._target(*self._args, **self._kwargs)
    def join(self):
        Thread.join(self)
        return self._return

def generate_qr(data:any):
    try:
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=20,
            border=4
        )
        token_id =data["tokenId"]
        data = json.dumps(data)
        qr.add_data(data)
        qr.make(fit=True)
        img = qr.make_image()
        img = img.convert("RGB")
        logger.info('test')
        img.save("qr-codes/"+token_id+".png")
        logger.info(data)
        return upload_file1("qr-codes/"+token_id+".png", token_id)
    except Exception as ex:
        logger.exception(ex)
        raise MutationError("Error Generating QR Code")

def generate_qr1(data:any,token_id:str):
    try:
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=20,
            border=4
        )
        # token_id =data["tokenId"]
        # data = json.dumps(data)
        qr.add_data(data)
        qr.make(fit=True)
        img = qr.make_image()
        img = img.convert("RGB")
        logger.info('test')
        img.save("qr-codes/"+token_id+".png")
        logger.info(data)
        return upload_file1("qr-codes/"+token_id+".png", token_id)
    except Exception as ex:
        logger.exception(ex)
        raise MutationError("Error Generating QR Code")
def upload_file(data:any, tokenId:any):
    s3_client = boto3.client(
        's3',
        region_name='ap-south-1',
        aws_access_key_id=os.environ['AWS_ACCESS_KEY_ID'],
        aws_secret_access_key=os.environ['AWS_SECRET_ACCESS_KEY'],
        config=Config(s3={'addressing_style': 'path'})
    )
    object_url=None
    try:
        bucket_name='achalasmarthospital'
        file, file_name = decode_base64_file(data,tokenId)
        file_name_with_extention='png'
        s3_client.upload_fileobj(
            file,
            bucket_name,
            file_name,
            ExtraArgs={ "ContentType": "image/png"}
        )
        return f"https://{bucket_name}.s3.amazonaws.com/{file_name}"
        # response = s3_client.upload_fileobj(
        #    data, '/achalasmarthospital/dev/qr-code', tokenId+".png"
        # )
    except ClientError as e:
        logger.warning("Error while uploading file: %s", e)
        return ''
def upload_file1(file_path:any, tokenId:any):
    s3_client = boto3.client(
        's3',
        region_name='ap-south-1',
        aws_access_key_id=os.environ['AWS_ACCESS_KEY_ID'],
        aws_secret_access_key=os.environ['AWS_SECRET_ACCESS_KEY'],
        config=Config(s3={'addressing_style': 'path'})
    )
    object_url=None
    try:
        bucket_name='achalasmarthospital'
        file_name='ahsmhp-dev/aig-hosp/qr-code/'+tokenId+".png"
        with open(file_path, 'rb') as data1:
            s3_client.upload_fileobj(data1,   bucket_name,
            file_name,
            ExtraArgs={ "ContentType": "image/png"})
        return f"https://{bucket_name}.s3.amazonaws.com/{file_name}"
        # response = s3_client.upload_fileobj(
        #    data, '/achalasmarthospital/dev/qr-code', tokenId+".png"
        # )
    except ClientError as e:
        logger.warning("Error while uploading file: %s", e)
        return ''

def decode_base64_file(data,tokenId):
    """
    Fuction to convert base 64 to readable IO bytes and auto-generate file name with extension
    :param data: base64 file input
    :return: tuple containing IO bytes file and filename
    """
    # Check if this is a base64 string
    if isinstance(data, six.string_types):
        # Check if the base64 string is in the "data:" format
        if 'data:' in data and ';base64,' in data:
            # Break out the header from the base64 content
            header, data = data.split(';base64,')

        # Try to decode the file. Return validation error if it fails.
        try:
            decoded_file = base64.b64decode(data)
        except TypeError:
            TypeError('invalid_image')

        # Generate file name:
        file_name = f'dev/qr-code/{tokenId}'  # 12 characters are more than enough.
        # Get the file name extension:
        file_extension = 'png'

        complete_file_name = "%s.%s" % (file_name, file_extension,)

        return io.BytesIO(decoded_file), complete_file_name
    
def format_datetime(dt):
    if dt is not None:
        dt = dt.astimezone(pytz.timezone('Asia/Kolkata'))
        return dt.strftime("%Y-%m-%d %H:%M")
    return None


def format_datetime_ist(dt):
    if dt is not None:
        dt = dt.astimezone(pytz.timezone('Asia/Kolkata'))
        return dt.strftime("%Y-%m-%d %I:%M %p")
    return None
    
def generate_visa_qr(reference_id: str):
    try:
        url = f"{os.environ['FRONTEND_URL']}/{reference_id}"
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=20,
            border=4
        )
        qr.add_data(url)
        qr.make(fit=True)
        img = qr.make_image()
        img = img.convert("RGB")
        img.save("data/visa_letter/qr-imgs/"+reference_id+".png")
        return "data/visa_letter/qr-imgs/"+reference_id+".png"
    except Exception as e:
        logger.exception(e)



def generate_unique_token():
    # Get current date
    # Generate 2 alphanumeric characters
    alphanumeric_part = ''.join(secrets.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ') for _ in range(2))

    # Generate 2 numeric characters
    numeric_part = ''.join(secrets.choice('0123456789') for _ in range(2))

    # Combine parts to create the unique token
    unique_token = alphanumeric_part + numeric_part

    return unique_token
def get_unique_random_value(assigned_values):
    while True:
        new_value = generate_unique_token()
        if new_value not in assigned_values:
            return new_value
        else:
            print(f"Value exists{new_value}")
            
def set_redis_key(key:str, value:str):
    client.set(key, value)

def get_redis_value(key:str):
    old_value=client.get(key)
    if old_value is None and key=="HIS_BILLING_SYNC":
        time = datetime.now(pytz.timezone('Asia/Kolkata'))
        date_format = time.strftime("%Y-%m-%d")
        old_value=date_format
    return old_value
def get_unique_token():
# Example usage
    with RedisLock(client_lock, "queue_token", blocking_timeout=60):
        # Example key for storing assigned values
        time = datetime.now(pytz.timezone('Asia/Kolkata'))
        date_format = time.strftime("%d%m%y")
        hour_format = time.strftime("%H")  # Get current hour in 24-hour format

        # Create a unique key for the current hour
        key = f"token:{date_format}:{hour_format}:counter"

        # Increment the counter for the current hour
        token_id = client.incr(key)

        # Format the token with padding
        token_id_padded = f"{token_id:03}"  # Ensures it's always 3 digits (e.g., 001, 002)

        # Construct the final token (e.g., 10-001 for 10 AM, 15-002 for 3 PM)
        token = f"{hour_format:02}-{token_id_padded}"
        # assigned_values_key = f"{date_format}assigned_values"
        # # Get the set of already assigned values from Redis
        # assigned_values = set(client.smembers(assigned_values_key))
        # # Generate a new random value excluding the assigned ones
        # new_value = get_unique_random_value(assigned_values)
        # # Add the new value to the set of assigned values
        # client.sadd(assigned_values_key, new_value)
        # today = date.today()
        # Create a unique key for today
        # key = f"token:{today}:counter"
        # logger.info(f"key:{key}")
        # Increment the counter for today
        # token_id = client.incr(key)
        # token_id_padded = f"{token_id:03}"
        # Combine the date and the counter to create the token
        # token = f"T-{token_id_padded}"
        return token

def get_unique_digit_code():
    import random
    with RedisLock(client_lock, "queue_4digit_code_lock", blocking_timeout=60):
        time = datetime.now(pytz.timezone('Asia/Kolkata'))
        date_format = time.strftime("%Y-%m-%d")
        assigned_values_key = f"assigned_tokens:{date_format}"
        
        # Calculate seconds until midnight Asia/Kolkata
        midnight = (time + timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
        seconds_until_midnight = int((midnight - time).total_seconds())
        
        # Set expiry for the key to seconds until midnight if not already set
        if client.ttl(assigned_values_key) == -1:
            client.expire(assigned_values_key, seconds_until_midnight)
            logger.info(f"Set expiry for {assigned_values_key} to {seconds_until_midnight} seconds")
        
        while True:
            # Generate a random 4-digit numeric code as string
            code = f"{random.randint(0, 9999):04}"
            logger.info(f"Generated code: {code}")
            # Check if code is already assigned
            if not client.sismember(assigned_values_key, code):
                # Add code to assigned set
                client.sadd(assigned_values_key, code)
                logger.info(f"Assigned new code: {code}")
                return code

def remove_token(old_token):
    with RedisLock(client_lock, "queue_token", blocking_timeout=60):
        # Example key for storing assigned values
        time = datetime.now(pytz.timezone('Asia/Kolkata'))
        date_format = time.strftime("%d%m%y")
        assigned_values_key = f"{date_format}assigned_values"
        client.lrem(assigned_values_key, 0, old_token)
        return old_token
    
def remove_assigned_tokens_list():
    with RedisLock(client_lock, "queue_token", blocking_timeout=60):
        # Example key for storing assigned values
        one_day = timedelta(days=1)
        time = datetime.now(pytz.timezone('Asia/Kolkata'))-one_day
        date_format = time.strftime("%d%m%y")
        assigned_values_key = f"{date_format}assigned_values"
        client.delete(assigned_values_key)
        return True
    
def generate_unique_location_token():
    with RedisLock(client_lock, "queue_loc_token", blocking_timeout=60):
        today = date.today()
        key = f"location_token:{today}:counter"
        token_id = client.incr(key)
        token_id_padded = f"{token_id:03}"
        token = f"P-{token_id_padded}"
        return token
    
def pdf_to_base64(url):
    try:
        headers = {'User-Agent': 'Mozilla/5.0'}
        response = requests.get(url, headers=headers)
        response.raise_for_status()
       
        if 'application/pdf' not in response.headers.get('Content-Type', ''):
            raise ValueError("URL does not point to a valid PDF file")
        base64_pdf = base64.b64encode(response.content).decode('utf-8')
        return base64_pdf
   
    except requests.exceptions.RequestException as e:
        print(f"HTTP error fetching the PDF: {e}")
        return None
    except ValueError as ve:
        print(f"Value error: {ve}")
        return None