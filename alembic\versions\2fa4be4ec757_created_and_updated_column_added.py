"""created and updated column added

Revision ID: 2fa4be4ec757
Revises: da5c609e6227
Create Date: 2023-08-24 05:25:22.715579

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2fa4be4ec757'
down_revision = 'da5c609e6227'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('printer', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True))
    op.add_column('printer', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('printer', 'updated_at')
    op.drop_column('printer', 'created_at')
    # ### end Alembic commands ###
