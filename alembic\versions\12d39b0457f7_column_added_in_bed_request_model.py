"""column added in bed request model

Revision ID: 12d39b0457f7
Revises: e9b0b02759ef
Create Date: 2023-11-29 14:19:00.395363

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '12d39b0457f7'
down_revision = 'e9b0b02759ef'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bed_request', sa.Column('mismatch_duration', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('bed_request', 'mismatch_duration')
    # ### end Alembic commands ###
