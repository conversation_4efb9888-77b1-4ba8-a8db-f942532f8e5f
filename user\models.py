import enum
from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON>umn, Date<PERSON><PERSON>, Enum, <PERSON><PERSON>ey, SmallInteger, Text, String, Time, UniqueConstraint, Integer, Numeric, Boolean,Date, Float, JSON
from sqlalchemy.sql import func
import strawberry
from sqlalchemy.orm import relationship
from database.db_conf import Base


@strawberry.enum
class StatusEnum(enum.Enum):
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"

@strawberry.enum
class UserStatusEnum(enum.Enum):
    PENDING = "PENDING"
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
@strawberry.enum
class EntityTypeEnum(enum.Enum):
    HOSPITAL = "HOSPITAL"
    PATIENT = "PATIENT"
    STAFF = "STAFF"

@strawberry.enum
class GenderEnum(enum.Enum):
    MALE = "MALE"
    FEMALE = "FEMALE"

@strawberry.enum
class DayOfWeekEnum(enum.Enum):
    SUNDAY = "SUNDAY"
    MONDAY = "MONDAY"
    TUESDAY = "TUESDAY"
    WEDNESDAY = "WEDNESDAY"
    THURSDAY = "THURSDAY"
    FRIDAY = "FRIDAY"
    SATURDAY = "SATURDAY"

@strawberry.enum
class UserRefIdTypeEnum(enum.Enum):
    PHONE_NUMBER = "PHONE_NUMBER"
    AADHAR = "AADHAR"
    HEALTH_ID = "HEALTH_ID"

@strawberry.enum
class OTPTypeEnum(enum.Enum):
   BOOK_APPOINTMENT = "BOOK_APPOINTMENT"
   REGISTER = "REGISTER"
   CHECK_IN = "CHECK_IN"
   PAYMENT = "PAYMENT"
   REPORTS = "REPORTS"
   OTHER = "OTHER"
   HEALTH_PACKAGE = "HEALTH_PACKAGE"

@strawberry.enum
class RefTypeEnum(enum.Enum):
    PHONE_NUMBER = "PHONE_NUMBER"
    UHID = "UHID"
    
@strawberry.enum
class RegistrationTypeEnum(enum.Enum):
    MOBILE_REGISTRATION = "MOBILE_REGISTRATION"
    AADHAR_REGISTRATION = "AADHAR_REGISTRATION"
    VITAL = "VITAL"
@strawberry.enum 
class DeviceTypeEnum(enum.Enum):
    KIOSK = "KIOSK"
    STAFF_STATION = "STAFF_STATION"
    APP = "APP"

@strawberry.enum
class SubDeviceTypeEnum(enum.Enum):
    PRINER_ENABLED = "PRINTER_ENABLED"
    PRINTER_DISABLED = "PRINTER_DISABLED"
    TV = "TV"
    QUEUE_COUNTER = "QUEUE_COUNTER"

class User(Base):
    __tablename__ = "user"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    umr_no = Column(String)
    name = Column(String)
    phone_number = Column(String)
    his_api_time_taken= Column(Float,nullable=True)
    registration_type=Column(Enum(RegistrationTypeEnum),nullable=True)
    accept_terms= Column(Boolean,nullable=True)
    accepted_by= Column(String,nullable=True)
    accepted_rel= Column(String,nullable=True)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    device_id = Column(String)

    __table_args__ = (UniqueConstraint('umr_no', name='user_umr_no'),)

    def __repr__(self) -> str:
        return "<User %r>" % self.id

class UserType(Base):
    __tablename__ = "user_type"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String, nullable=False)
    code = Column(String, nullable=False)
    entity_type = Column(Enum(EntityTypeEnum), default=EntityTypeEnum.HOSPITAL, nullable=False)
    status = Column(Enum(StatusEnum), default=StatusEnum.ACTIVE, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(String)
    updated_by = Column(String)

    rel_resource = relationship("RelUserTypeResource")
    def __repr__(self) -> str:
        return "<UserType %r>" % self.id


class UserOTP(Base):
    __tablename__ = "user_otp"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    ref_id = Column(String , nullable = False)
    ref_id_type = Column(Enum(UserRefIdTypeEnum), nullable = False)
    sent_at = Column(DateTime(timezone=True), server_default=func.now())
    otp = Column(String , nullable = False)
    otp_type = Column(Enum(OTPTypeEnum), nullable = False)
    request_id = Column(String,unique=True)
    count = Column(Integer)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    __table__args__= (UniqueConstraint('request_id',name='user_otp_request_id'),)

    def __repr__(self) -> str:
        return "<UserOTP %r>" % self.id


class Screen(Base):
    __tablename__='screen'
    id = Column(Integer, primary_key=True, autoincrement=True)
    screen_code = Column(String,unique=True)
    screen_name = Column(String)
    initial_action =Column(String)
    is_final=Column(SmallInteger)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    __table__args__= (UniqueConstraint('screen_code',name='screen_screen_code'),)

    def __repr__(self) -> str:
        return "<Screen %r>" % self.id
    
class trackScreen(Base):
    __tablename__='track_screen'
    id = Column(Integer, primary_key=True, autoincrement=True)
    request_id = Column(String,unique=True)
    initial_action =Column(String)
    action=Column(String,ForeignKey("screen.screen_code", name="track_screen_action_fk"))
    is_final=Column(SmallInteger)
    device_id=Column(String)
    lang = Column(String,server_default='en')
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    screen = relationship("Screen")


    __table__args__= (UniqueConstraint('request_id',name='track_screen_request_id'),)
    def __repr__(self) -> str:
        return "<trackScreen %r>" % self.id

class trackScreenLogs(Base):
    __tablename__='track_screen_logs'
    id = Column(Integer, primary_key=True, autoincrement=True)
    track_screen_id = Column(BigInteger, ForeignKey("track_screen.id", name="track_screen_logs_track_screen_id_fk"))
    request_id = Column(String)
    action=Column(String)
    is_final=Column(SmallInteger)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    def __repr__(self) -> str:
        return "<trackScreenLogs %r>" % self.id

class SmsSendDetail(Base):
    __tablename__='sms_send_detail'
    id = Column(Integer, primary_key=True, autoincrement=True)
    transaction_id = Column(String, nullable=False)
    sent_from = Column(String)
    sent_to = Column(String)
    status = Column(String)
    count = Column(Integer)
    message = Column(String)
    data = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    def __repr__(self) -> str:
        return "<SmsSendDetail %r>" % self.id

class UserOtpLogs(Base):
    __tablename__='user_otp_logs'
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    Phone_number= Column(String, nullable=False)
    otp_status = Column(String)
    request_id = Column(String)
    created_at= Column(DateTime(timezone=True),server_default=func.now() )
    updated_at = Column(DateTime(timezone=True),onupdate=func.now())
    
    def __repr__(self)->str:
        return "<UserOtpLogs %r>" %self.id
    
class Printer(Base):
    __tablename__='printer'
    id = Column(Integer, primary_key=True, autoincrement=True)
    printer_name=Column(String)
    test_order_pat_id=Column(String)
    cups_job_id=Column(Integer)
    cups_job_status=Column(String)
    type=Column(String)
    uhid=Column(String, index=True)
    device_id = Column(String)
    print_start_time = Column(DateTime(timezone=True))
    print_end_time = Column(DateTime(timezone=True))
    job_media_sheets_completed = Column(Integer)
    total_pages = Column(Integer)
    request_id = Column(String)
    group_id=Column(String)
    retry_count=Column(Integer)
    created_at= Column(DateTime(timezone=True),server_default=func.now() )
    updated_at = Column(DateTime(timezone=True),onupdate=func.now())


    def __repr__(self) -> str:
        return "<Printer %r>" % self.id

class Device(Base):
    __tablename__ ="device"
    id = Column(Integer, primary_key=True, autoincrement=True)
    device_code = Column(String,unique=True)
    device_name = Column(String)
    status = Column(Enum(StatusEnum), default=StatusEnum.ACTIVE, nullable=False)
    printer_name = Column(String)
    hospital_code = Column(String,nullable=False)
    login_pin = Column(Integer,nullable=True)
    created_at = Column(DateTime(timezone=True),server_default=func.now())
    updated_at = Column(DateTime(timezone=True),onupdate=func.now())
    device_type = Column(Enum(DeviceTypeEnum), nullable=True)
    sub_device_type = Column(Enum(SubDeviceTypeEnum), nullable=True)
    queue_counter= Column(Integer, nullable=True)
    printer_ip=Column(String)
    vital_print_enabled= Column(Boolean, default=False)
    
    def __repr__(self) -> str:
        return "<Device %r>" % self.id
    
class Module(Base):
    __tablename__="module"
    id = Column(Integer, primary_key=True,autoincrement=True)
    module_code=Column(String)
    module_name = Column(String)
    created_at = Column(DateTime(timezone=True),server_default=func.now())
    updated_at = Column(DateTime(timezone=True),onupdate=func.now())
    base_url = Column(String,nullable=True)
    menus = relationship("RelModuleMenu")
    
    def __repr__(self) -> str:
        return "<Module %r>" % self.id

class RelDeviceModule(Base):
    __tablename__ = "rel_device_module"
    id = Column(Integer, primary_key=True, autoincrement=True)
    device_id = Column(Integer,ForeignKey("device.id",name="rel_device_module_device_id_fk"))
    module_id = Column(Integer,ForeignKey("module.id",name="rel_device_module_module_id_fk"))
    resource_id = Column(Integer,ForeignKey("resource.id",name="rel_device_module_resource_id_fk"))
    created_at=Column(DateTime(timezone=True),server_default=func.now())
    updated_at= Column(DateTime(timezone=True),onupdate=func.now())
    
    device=relationship("Device")
    module= relationship("Module")
    resource = relationship("Resource")
    
    def __repr__(self) -> str:
        return "<RelDeviceModule %r>" % self.id

class MandatoryFields(Base):
    __tablename__="mandatory_fields"
    id = Column(Integer, primary_key=True, autoincrement=True)
    name=Column(String)
    is_mandatory =Column(Integer)
    created_at=Column(DateTime(timezone=True),server_default=func.now())
    updated_at= Column(DateTime(timezone=True),onupdate=func.now())
    
  
class Relation(Base):
    __tablename__="relation"
    id = Column(Integer, primary_key=True, autoincrement=True)
    relation_name=Column(String,nullable=False)
    relation_code=Column(String,nullable=False)
    priority = Column(Integer)
    created_at=Column(DateTime(timezone=True),server_default=func.now())
    updated_at= Column(DateTime(timezone=True),onupdate=func.now())
