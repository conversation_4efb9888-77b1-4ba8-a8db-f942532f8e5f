from urllib.parse import quote
import database.db_conf as db_config
import os
from logging.config import fileConfig

from sqlalchemy import engine_from_config, pool

# Should import models here to enable auto migrations
from user.models import User, UserType, UserOtpLogs, Screen
from operation.models import Operation, OperationRelUserType
from feedback.models import UserFeedback
from alembic import context
from sms_mail_notification.models import SMSMailNotificationConfig
from quick_guide.models import MedicalServices,KeyLocations,Specialtity
from abdm.models import AadharOtp
from language.models import Language
from service.models import Service
from config.models import Config
from bill.models import UserService, UserToken
from queues.models import Queue, Cluster,UserQueueStep
from staff_user.models import StaffUser
from bms.models import BedRequest
from visa_letter.models import Country
from assessment_vital.models import AssessmentCategory, VitalUser
from menu.models import Menu
from resources.models import Resource
config = context.config

password = os.environ["DB_PASSWORD"]
db_url_escaped = os.environ["DATABASE_URL"]% quote(password).replace('%', '%%')
config.set_main_option('sqlalchemy.url', db_url_escaped)
fileConfig(config.config_file_name)


target_metadata = db_config.Base.metadata


# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline():
    """Run migrations in 'offline' mode.
    
    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online():
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.

    """
    def include_object(object, name, type_, reflected, compare_to):
        # Check for table and schema to ignore
        schema=""
        if type_ == "table":
            schema =object.schema
        elif type_ == "index":
            schema =object.table.schema
        elif type_ == "foreign_key_constraint":
            schema =object.table.schema
        elif type_ == 'column':
            schema =object.table.schema
        # if schema in ["queue"]:
        #     return True
        # Optionally, handle other types such as foreign keys, indexes, etc.
        return True
    connectable = engine_from_config(
        config.get_section(config.config_ini_section),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, 
            target_metadata=target_metadata,
            include_schemas=True,
            include_object=include_object,
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
