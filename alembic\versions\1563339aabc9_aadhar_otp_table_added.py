"""aadhar otp table added

Revision ID: 1563339aabc9
Revises: e846132ee534
Create Date: 2023-10-03 07:55:16.969391

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1563339aabc9'
down_revision = 'e846132ee534'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('aadhar_otp',
    sa.<PERSON>umn('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('txn_id', sa.String(), nullable=True),
    sa.Column('client_id', sa.String(), nullable=True),
    sa.Column('aadhar_number', sa.String(), nullable=False),
    sa.Column('phone_number', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('txn_id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('aadhar_otp')
    # ### end Alembic commands ###
