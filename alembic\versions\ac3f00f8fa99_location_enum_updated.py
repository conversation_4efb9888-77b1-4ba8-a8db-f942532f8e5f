"""location enum updated

Revision ID: ac3f00f8fa99
Revises: 862174f5376a
Create Date: 2024-08-02 16:41:17.246859

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ac3f00f8fa99'
down_revision = '862174f5376a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TYPE locationbedstatusenum ADD VALUE 'UNAVAILABLE'")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
