import enum
from sqlalchemy import BigInteger, Column, DateTime, Enum, ForeignKey, SmallInteger, Text, String, Time, UniqueConstraint, Integer, Numeric, Boolean,Date, Float
from sqlalchemy.sql import func
import strawberry
from sqlalchemy.orm import relationship
from database.db_conf import Base

class AadharOtp(Base):
    __tablename__ = "aadhar_otp"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    txn_id = Column(String,unique=True)
    client_id= Column(String)
    aadhar_number= Column(String,nullable=False)
    phone_number=Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    __table__args__= (UniqueConstraint('tnx_id',name='aadhar_otp_tnx_id'),)

    def __repr__(self) -> str:
        return "<AadharOtp %r>" % self.id