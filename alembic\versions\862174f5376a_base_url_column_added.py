"""base_url column added

Revision ID: 862174f5376a
Revises: 45be6b359359
Create Date: 2024-07-23 11:34:39.344476

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '862174f5376a'
down_revision = '45be6b359359'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('module', sa.Column('base_url', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('module', 'base_url')
    # ### end Alembic commands ###
