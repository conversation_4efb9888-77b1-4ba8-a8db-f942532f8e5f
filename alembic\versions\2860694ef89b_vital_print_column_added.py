"""vital print column added

Revision ID: 2860694ef89b
Revises: 6d5344edcd64
Create Date: 2025-06-20 12:43:20.392585

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2860694ef89b'
down_revision = '6d5344edcd64'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('device', sa.Column('vital_print_enabled', sa.<PERSON>(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('device', 'vital_print_enabled')
    # ### end Alembic commands ###
