"""doctor image added

Revision ID: b742d23756a0
Revises: 7c38d5dcb9e4
Create Date: 2025-03-21 17:48:23.175192

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b742d23756a0'
down_revision = '7c38d5dcb9e4'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('queue', sa.Column('assignment', sa.ARRAY(sa.String()), nullable=True), schema='queue')
    op.add_column('queue_counter', sa.Column('doctor_image_name', sa.String(), nullable=True), schema='queue')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('queue_counter', 'doctor_image_name', schema='queue')
    op.drop_column('queue', 'assignment', schema='queue')
    # ### end Alembic commands ###
