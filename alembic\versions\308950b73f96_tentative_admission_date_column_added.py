"""tentative_admission_date column added

Revision ID: 308950b73f96
Revises: 4b93ebb59812
Create Date: 2024-01-30 10:32:11.760819

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '308950b73f96'
down_revision = '4b93ebb59812'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TYPE requeststatus ADD VALUE 'PENDING'")
    op.add_column('bed_request', sa.Column('tentative_admission_date', sa.Date(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('bed_request', 'tentative_admission_date')
    # ### end Alembic commands ###
