import os
from typing import Optional
import pytz
from sqlalchemy import ARRAY, Integer, String, cast, desc, distinct, func, or_, and_, select
from database.db_conf import SessionLocal
from sqlalchemy.orm import Session, aliased
import logging
from resources.models import Resource as ResourceModel, RelUserTypeResource as RelUserTypeResourceModel
from user.resolvers import get_access_token
from util.globals import handle_request
logger = logging.getLogger()
from exceptions.exceptions import MutationError
from menu.models import  Menu as MenuModel, RelModuleMenu as RelModuleMenuModel
from user.models import Module as ModuleModel, UserType as UserTypeModel
from datetime import datetime, date
from sqlalchemy.exc import DataError, NoResultFound
import openpyxl
import python_lang as lang
import io
from sqlalchemy.orm import joinedload,with_loader_criteria,contains_eager
# from sqlalchemy.sql.expression import coalesce

def get_module_menus_resources(db:Session, module_id: int):
    query = db.query(RelModuleMenuModel)
    if module_id is not None:
        query = query.filter(RelModuleMenuModel.module_id == module_id)
    return query.all() 

def get_modules_data(db:Session):
    return db.query(ModuleModel).all()

def get_all_module_menus_resources(db:Session,module_id: int):
    try:
        query = (
        db.query(ModuleModel, RelModuleMenuModel)
        .outerjoin(RelModuleMenuModel, ModuleModel.id == RelModuleMenuModel.module_id)
        ).order_by(ModuleModel.id,RelModuleMenuModel.menu_id)
        if module_id is not None:
            query = query.filter(ModuleModel.id == module_id)
        query = query.all()
        # logger.info(query)
        module_rel_module_menus_dict = {}
        for module, rel_module_menu in query:
            if module not in module_rel_module_menus_dict:
                module_rel_module_menus_dict[module] = []

            if rel_module_menu is not None:
                module_rel_module_menus_dict[module].append(rel_module_menu)
        return module_rel_module_menus_dict
    except Exception as e:
        logger.exception(e)
