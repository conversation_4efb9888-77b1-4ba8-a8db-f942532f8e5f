"""vital user table created

Revision ID: a6099f711666
Revises: ba050261dd98
Create Date: 2024-08-20 12:34:53.321049

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a6099f711666'
down_revision = 'ba050261dd98'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
