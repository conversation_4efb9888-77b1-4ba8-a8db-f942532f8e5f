"""queue counter table added

Revision ID: 59387b3dfcce
Revises: da664c845d5d
Create Date: 2023-11-14 07:38:39.122113

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '59387b3dfcce'
down_revision = 'da664c845d5d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('queue_counter',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('queue_id', sa.Integer(), nullable=True),
    sa.Column('counter', sa.Integer(), nullable=True),
    sa.Column('counter_name', sa.String(), nullable=True),
    sa.Column('counter_code', sa.String(), nullable=True),
    sa.Column('status', sa.Enum('ALLOTED', 'UNALLOTED', 'INACTIVE', name='queuecounterstatusenum'), nullable=False),
    sa.Column('priority', sa.Numeric(), server_default=sa.text('1'), nullable=False),
    sa.ForeignKeyConstraint(['queue_id'], ['queue.id'], name='rel_user_service_queue_queue_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('user_queue', sa.Column('counter', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_queue', 'counter')
    op.drop_table('queue_counter')
    # ### end Alembic commands ###
