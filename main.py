
from contextvars import ContextVar
import datetime
import re
from typing import Optional
from auth import get_auth_user_context
from database.db_conf import <PERSON><PERSON><PERSON><PERSON>
from database.aig_his import SessionLocalHis
from fastapi import FastAPI, Request
from logging.config import dictConfig
from language.resolvers import load_languages
from language.schema import Query as LanguageQuery
from strawberry.extensions import Extension
import logging
import uuid
logger = logging.getLogger()
import strawberry
from strawberry.fastapi import GraphQLRouter
from strawberry.tools import merge_types
from user.schema import Mutation as UserMutation, Query as UserQuery,  Subscription as UserSubscription
import yaml
from strawberry.fastapi import GraphQLRouter
from feedback.schema import Query as FeedbackQuery, Mutation as FeedbackMutation
from quick_guide.schema import Query as QuickGuideQuery
from queues.schema import Query as QueueQuery, Mutation as QueueMutation, Subscription as QueueSubscription
from staff_user.schema import Query as StaffUserQuery, Mutation as StaffUserMutation
from fastapi.middleware.cors import CORSMiddleware
from constants import origins, methods, headers
from abdm.schema import Mutation as ABDMMutation
from bill.schema import Mutation as BillMutation, Query as BillQuery
from bms.schema import Mutation as BmsMutation, Query as BmsQuery
from assessment_vital.schema import Mutation as AssessmentVitalMutation, Query as AssessmentVitalQuery

from visa_letter.schema import Mutation as VisaLetterMutation, Query as VisaLetterQuery
from menu.schema import Query as MenuQuery
import python_lang as lang
from strawberry.http import GraphQLHTTPResponse
from strawberry.types import ExecutionResult
from restapi.sms import router as SmsRouter
from restapi.pacs import router as LabReportRouter
from restapi.auth import router as AuthRouter
from bms.restapi import router as BmsRouster
from visa_letter.restapi import router as visaRouter
from queues.restapi import router as queueRouter
from operation.schema import Mutation as OperationMutation
_ = lang.get
import cups
from cups_notify import Subscriber, event

try:
    load_languages()
except Exception as ex:
    logger.exception(ex)


with open('logging_config.yaml', 'rt') as f:
    config = yaml.safe_load(f.read())
    config.get("handlers")["file_handler"]["filename"] = '/tmp/{:%Y-%m-%d}.log'.format(datetime.datetime.now())
    dictConfig(config)

# Create a CUPS connection
def my_callback(evt):
    logger.info(evt.guid)
    logger.info(evt.description)
    logger.info(evt.title)
    logger.info(evt.printer)
    if evt.title=='CUPS_EVT_JOB_COMPLETED' or evt.title=='CUPS_EVT_JOB_STOPPED':
        match = re.search(r'-(\d+)\s', evt.description)
        # Check if a match is found
        if match:
            number = match.group(1)
            logger.info(number)
        else:
            logger.info("No number found in the text.")

# Create a new subscriber
# sub = Subscriber(cups.Connection())

# # Subscribe the callback
# sub.subscribe(my_callback, [event.CUPS_EVT_JOB_CREATED,
#                             event.CUPS_EVT_JOB_COMPLETED,
#                             event.CUPS_EVT_JOB_STOPPED,event.CUPS_EVT_JOB_STATE_CHANGED,event.CUPS_EVT_JOB_STOPPED])
# Create a new subscriber
class SQLAlchemySession(Extension):
    def on_request_start(self):
        self.execution_context.context["db"] = SessionLocal()
        try:
            self.execution_context.context["db_aig_his"] = SessionLocalHis()
        except Exception as ex:
            logger.exception(ex)
    def on_request_end(self):
        self.execution_context.context["db"].close()
        try:
            self.execution_context.context["db_aig_his"].close()
        except Exception as ex:
            logger.exception(ex)

class MyGraphQLRouter(GraphQLRouter):
    async def process_result(self, request: Request, result: ExecutionResult) -> GraphQLHTTPResponse:
        if result.errors:
            logger.info(result.errors)
            return GraphQLHTTPResponse({"data" : {"success":False,"data":None,"message":"Unexpected Error occured."}})
        else:
            for obj in result.data.keys():
                try:
                    if result.data[obj] is not None and not isinstance(result.data[obj],list) and not isinstance(result.data[obj],str)  and "message" in result.data[obj].keys():
                        lang.select(request.headers.get("lang") if request.headers.get("lang") is not None else 'en')
                        try:
                            result.data[obj]["message"]=_(result.data[obj]["message"])
                        except Exception as e:
                            logger.exception(f"Error occured:{e}")
                except Exception as e:
                        logger.exception(f"Error occured:{e}")
            return GraphQLHTTPResponse({"data" : result.data})

Query = merge_types("Query", (UserQuery, FeedbackQuery, QuickGuideQuery, LanguageQuery,StaffUserQuery,BillQuery,QueueQuery,BmsQuery,VisaLetterQuery,MenuQuery,AssessmentVitalQuery))
Mutation = merge_types(
    "Mutation", (UserMutation,FeedbackMutation, ABDMMutation,StaffUserMutation,QueueMutation,BillMutation,BmsMutation,VisaLetterMutation, OperationMutation,AssessmentVitalMutation))
Subscription = merge_types("Subscription", (QueueSubscription, UserSubscription))

schema = strawberry.Schema(Query, Mutation, Subscription, extensions=[SQLAlchemySession])

graphql_app = MyGraphQLRouter(schema,context_getter=get_auth_user_context)
app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=methods,
    allow_headers=headers,
)

app.include_router(graphql_app, prefix="/graphql")
app.include_router(SmsRouter)
app.include_router(LabReportRouter)
app.include_router(AuthRouter)
app.include_router(BmsRouster)
app.include_router(visaRouter)
app.include_router(queueRouter)