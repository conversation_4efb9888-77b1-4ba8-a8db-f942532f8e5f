
import csv
from datetime import date, datetime, timedelta
import time
import uuid, random
import pytz
from constants import SEND_WHATSAPP_MSG
from sms_mail_notification.schema import UserDetails
from sqlalchemy import desc, case, func, cast, Date
from bms.resolvers import mismatch_data, process_allocated_beds, send_bed_request_whatsapp_msg, store_bed_status_history, update_discharged_beds, update_pending_bed_request_status
from database.aig_his import SessionLocalH<PERSON>
from database.db_conf import SessionLocal
from database.achala import AchalaSessionLocal

import os
from celery import Celery
import logging
from celery.schedules import crontab
from user.models import Screen as ScreenModel, trackScreen as trackScreenModel, User as UserModel
from user.resolvers import send_aig_otp, update_stats
from util.email import send_email
from queues.resolvers import call_next, sync_loc_count, update_counter_priority,exit_user_queue, force_exit_user_from_queue, reset_queues_user_queues, add_user_queue, test_exit_user_from_queue, update_user_queue_manual, update_user_queue_step, update_user_queue_step_dynamically
# from bill.resolvers import save_user_service
from graphql_types import BillDetail, QRDetail
from queues.models import Queue as QueueModel, QueueTypeEnum, Tag as TagModel, UserQueue as UserQueueModel, UserQueueStatusEnum
from service.models import Service as ServiceModel
from config.models import Config as ConfigModel
from util.globals import handle_request1
logger = logging.getLogger()
celery = Celery(__name__)
celery.conf.broker_url = os.environ.get("CELERY_BROKER_URL")
celery.conf.result_backend = os.environ.get("CELERY_BROKER_BACKEND")
SQLALCHEMY_DATABASE_URL = os.environ["DATABASE_URL"]

@celery.on_after_configure.connect
def setup_periodic_tasks(sender, **kwargs):
    sender.add_periodic_task(
        crontab(hour="0", minute="0"),
        daily_report.s('daily report'),
    )
    # sender.add_periodic_task(
    #     crontab(minute='*'),  
    #     force_exit_user_queue.s(),
    # )
    # sender.add_periodic_task(
    #     crontab(hour="0", minute="0"),
    #     reset_queues.s(),
    # )
    # sender.add_periodic_task(
    #     crontab(minute='*'),  
    #     generate_user_bill.s(),
    # )
    # sender.add_periodic_task(
    #     crontab(minute='*'),  
    #     dynamically_entry_user.s(),
    # )
    # sender.add_periodic_task(
    #     crontab(minute='*'),  
    #     dynamically_exit_user.s(),
    # )
    # sender.add_periodic_task(
    #     crontab(minute='*/5'),  
    #     update_counter.s(),
    # )

    # sender.add_periodic_task(
    #     crontab(minute='*/5'),  
    #     generate_user_bill_endo.s(),
    # )
    # sender.add_periodic_task(
    #     crontab(minute='*'),  
    #     dynamically_freeze_user_endo.s(),
    # )
    # sender.add_periodic_task(
    #     crontab(minute='*'),  
    #     dynamically_assign_tags.s(),
    # )
    # sender.add_periodic_task(
    #     crontab(minute='*/5'),  
    #     dynamically_assign_locations.s(),
    # )

    sender.add_periodic_task(
        crontab(minute='*/5'),  
        update_alloted_beds.s(),
    )
    sender.add_periodic_task(
        crontab(hour="0", minute="0"),  
        update_mismatch_bed_classes.s(),
    )
    sender.add_periodic_task(
        crontab(hour="*",minute="0"),  
        update_bed_status.s(),
    )
    sender.add_periodic_task(
        crontab(minute='*/5'),  
        update_discharge_history.s(),
    )
    sender.add_periodic_task(
        crontab(hour="0", minute="0"),  
        update_bed_status_history.s(),
    )
    # sender.add_periodic_task(
    #     crontab(minute="*"),  
    #     sync_loc_count_1.s(),
    # )


@celery.task(bind=True)
def daily_report(self, args):
    db=None
    try:
        html_page=''
        db = SessionLocal()
        achala_db= AchalaSessionLocal()
        update_stats(db, achala_db)
        current_time = datetime.utcnow()
        one_day_back = current_time - timedelta(days=1)
        flow_types=db.query(trackScreenModel.initial_action,func.count(trackScreenModel.id).label("count")).filter(cast(trackScreenModel.created_at, Date)==one_day_back.date()).group_by(trackScreenModel.initial_action).all()
        total_count=0
        html_page+=f'<b>{os.environ["TYPE"]}</b><br>'
        html_page+=f'<html><body>Report Generated at {current_time}<br>'
        html_page+=f'Report Generated for {one_day_back.date()}<br>'
        html_page+="<table border=1 width='100%' style='border-collapse: collapse;'><tr><th width='30%'>Flow</th><th width='30%'>Screen</th><th width='10%'>Count</th><th width='10%'>Min Time</th><th width='10%'>Max Time</th><th width='10%'>Avg Time</th></tr>"
        sum=0
        failure=0
        success=0
        for initial_action in flow_types:
            total_count+=initial_action[1]
            print(initial_action)
            print(len(flow_types))
            screens=db.query(func.count(trackScreenModel.id).label("screen_count"),ScreenModel.screen_name,trackScreenModel.is_final,func.min(0 if trackScreenModel.updated_at is None else func.extract("epoch", (trackScreenModel.updated_at - trackScreenModel.created_at))),func.max(0 if trackScreenModel.updated_at is None else func.extract("epoch", (trackScreenModel.updated_at - trackScreenModel.created_at))),func.avg(0 if trackScreenModel.updated_at is None else func.extract("epoch", (trackScreenModel.updated_at - trackScreenModel.created_at)))).filter(trackScreenModel.initial_action==initial_action[0]).join(trackScreenModel.screen).filter(cast(trackScreenModel.created_at, Date)==one_day_back.date()).group_by(trackScreenModel.action,trackScreenModel.is_final,ScreenModel.screen_name).all()
            if len(screens)>0:
                html_page+=f"<tr><td rowspan={len(screens)}>{initial_action[0]}</td>"
            for screen in screens:
                success+=screen[0] if screen[2]==1 else 0
                failure+=screen[0] if screen[2]==0 else 0
                color='green' if screen[2]==1 else '#FFBF00'
                html_page+=f"<td style='color:{color};'>{screen[1]}</td><td>{screen[0]}</td><td>{0 if screen[3] is None else round(screen[3], 2)}</td><td>{0 if screen[4] is None else round(screen[4], 2)}</td><td>{0 if screen[5] is None else round(screen[5], 2)}</td></tr><tr>"
            if len(screens)>0:
                html_page+="</tr>"
        html_page+=f"<tr><td>Total count: </td><td>&nbsp;</td><td colspan='4'>{failure+success} success({success}) failure ({failure})</td></tr>"
        html_page+="</table></body></html>"
        send_email([os.environ["DIALY_REPORT_MAIL"]],"Aig daily report",html_page,os.environ["MAIL_ID"],os.environ["PASSWORD"])
    except Exception as Ex:
        logger.exception("exception while sending data")
    finally:
        if db is not None :db.close()
        if achala_db is not None :achala_db.close()
    # db.commit()


@celery.task(bind=True)
def force_exit_user_queue(self):
    try:
        db= SessionLocal()
        force_exit_user_from_queue(db)
        print("Function executed Successfully")
    except Exception as ex:
        logger.exception(ex)
    finally:
        if db is not None: db.close()

@celery.task(bind=True)
def reset_queues(self):
    try:
        db= SessionLocal()
        reset_queues_user_queues(db)
        print("Function executed Successfully")
    except Exception as ex:
        logger.exception(ex)
    finally:
        if db is not None: db.close()

# @celery.task(bind=True)
# def generate_user_bill(self):
#     try:
#         db= SessionLocal()
#         moring_services = ["FBS","LP","USG ABDOMEN","LFT",
#                        "RFT","BGRH","CUE","CBP","ESR","TSH",
#                        "HB CLIA","HBA1C","X_RAY","ECG","2D ECHO","URINE"]
#         moring_weights = [10,10,2,2,1,1,1,1,1,1,1,1,1,2,1.5,1]
#         afternoon_services= ["USG ABDOMEN","LFT","RFT",
#                          "BGRH","CUE","CBP","ESR","TSH",
#                          "HB CLIA","HBA1C","X_RAY","ECG",
#                          "2D ECHO","URINE"]
#         afternoon_weights=[1,1,2,3,2,3,2,2,2,1,1,2,1.5,2]
#         data={
#             6:{
#                 "services":moring_services,
#                 "weights":moring_weights,
#                 "count_per_min":2,
#             },
#             7:{
#                 "services":moring_services,
#                 "weights":moring_weights,
#                 "count_per_min":5,
#             },
#             8:{
#                 "services":moring_services,
#                 "weights":moring_weights,
#                 "count_per_min":8,
#             },
#             9:{
#                 "services":moring_services,
#                 "weights":moring_weights,
#                 "count_per_min":7,
#             },
#             10:{
#                 "services":moring_services,
#                 "weights":moring_weights,
#                 "count_per_min":6,
#             },
#             11:{
#                 "services":moring_services,
#                 "weights":moring_weights,
#                 "count_per_min":6,
#             },
#             12:{
#                 "services":moring_services,
#                 "weights":moring_weights,
#                 "count_per_min":4,
#             },
#             13:{
#                 "services":afternoon_services,
#                 "weights":afternoon_weights,
#                 "count_per_min":2,
#             },
#             14:{
#                 "services":afternoon_services,
#                 "weights":afternoon_weights,
#                 "count_per_min":3,
#             },
#             15:{
#                 "services":afternoon_services,
#                 "weights":afternoon_weights,
#                 "count_per_min":10,
#             },
#             16:{
#                 "services":afternoon_services,
#                 "weights":afternoon_weights,
#                 "count_per_min":10,
#             },
#             17:{
#                 "services":afternoon_services,
#                 "weights":afternoon_weights,
#                 "count_per_min":8,
#             },
#             18:{
#                 "services":afternoon_services,
#                 "weights":afternoon_weights,
#                 "count_per_min":8,
#             },
#             19:{
#                 "services":afternoon_services,
#                 "weights":afternoon_weights,
#                 "count_per_min":1,
#             },
#         }
        
#         names = ["Monika", "Lohith", "Jyothish", "Sandeep", "Praneeth", "Pavani", "Ganesh", "Jagan", "Joel", "Sweetha",
#          "Latha", "Niharika", "Sravani", "Kalyan", "Poojitha", "Hema", "Sindhu", "Gopi", "Sophia", "Tyler"]
#         name = random.choice(names)
#         phone_nos= ["6281567912","7013292857","8179466871","9542302610","9394265087"]
#         ist_timezone = pytz.timezone('Asia/Kolkata')
#         current_time_in_ist = datetime.now(ist_timezone)
#         current_hour_in_ist = current_time_in_ist.hour
#         if(data.get(current_hour_in_ist)):
#             for n in range(data[current_hour_in_ist]["count_per_min"]):
#                 logger.info(n)
#                 bill_no= str(uuid.uuid4())
#                 uhid = 'AIGG.' + str(uuid.uuid4().int)[:8]
#                 phone_number = random.choice(phone_nos)
#                 logger.info(data[current_hour_in_ist])
#                 service_codes = random.choices(data[current_hour_in_ist]["services"], k=random.choice([2,3,4,5,6,7,8]), weights=data[current_hour_in_ist]["weights"])
#                 logger.info(service_codes)
#                 bill_details = BillDetail(bill_no = bill_no, umr_no= uhid, name =name, phone_number=phone_number, service_codes=service_codes)
#                 save_user_service(db,bill_details)
#         print("Bill Generated Successfully")
#     except Exception as ex:
#         text = f"""Exception : {ex}"""
#         # send_email([os.environ["DIALY_REPORT_MAIL"]],"Bill Generation",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
#         logger.exception(ex)
#     finally:
#         if db is not None: db.close()

# @celery.task(bind=True)
# def generate_user_bill_endo(self):
#     try:
#         db= SessionLocal()
#         moring_services = ["ENDOSCOPY","COLONOSCOPY"]
#         moring_weights = [1,1]
#         afternoon_services= ["ENDOSCOPY","COLONOSCOPY"]
#         afternoon_weights=[1,1]
#         data={
#             6:{
#                 "services":moring_services,
#                 "weights":moring_weights,
#                 "count_per_min":1,
#             },
#             7:{
#                 "services":moring_services,
#                 "weights":moring_weights,
#                 "count_per_min":1,
#             },
#             8:{
#                 "services":moring_services,
#                 "weights":moring_weights,
#                 "count_per_min":2,
#             },
#             9:{
#                 "services":moring_services,
#                 "weights":moring_weights,
#                 "count_per_min":2,
#             },
#             10:{
#                 "services":moring_services,
#                 "weights":moring_weights,
#                 "count_per_min":2,
#             },
#             11:{
#                 "services":moring_services,
#                 "weights":moring_weights,
#                 "count_per_min":1,
#             },
#             12:{
#                 "services":moring_services,
#                 "weights":moring_weights,
#                 "count_per_min":1,
#             },
#             13:{
#                 "services":afternoon_services,
#                 "weights":afternoon_weights,
#                 "count_per_min":2,
#             },
#             14:{
#                 "services":afternoon_services,
#                 "weights":afternoon_weights,
#                 "count_per_min":2,
#             },
#             15:{
#                 "services":afternoon_services,
#                 "weights":afternoon_weights,
#                 "count_per_min":1,
#             },
#             16:{
#                 "services":afternoon_services,
#                 "weights":afternoon_weights,
#                 "count_per_min":2,
#             },
#             17:{
#                 "services":afternoon_services,
#                 "weights":afternoon_weights,
#                 "count_per_min":10,
#             },
#             18:{
#                 "services":afternoon_services,
#                 "weights":afternoon_weights,
#                 "count_per_min":10,
#             },
#             19:{
#                 "services":afternoon_services,
#                 "weights":afternoon_weights,
#                 "count_per_min":0,
#             },
#         }
        
#         names = ["Monika", "Lohith", "Jyothish", "Sandeep", "Praneeth", "Pavani", "Ganesh", "Jagan", "Joel", "Sweetha",
#          "Latha", "Niharika", "Sravani", "Kalyan", "Poojitha", "Hema", "Sindhu", "Gopi", "Sophia", "Tyler"]
#         name = random.choice(names)
#         phone_nos= ["6281567912","7013292857","8179466871","9542302610","9394265087"]
#         ist_timezone = pytz.timezone('Asia/Kolkata')
#         current_time_in_ist = datetime.now(ist_timezone)
#         current_hour_in_ist = current_time_in_ist.hour
#         if(data.get(current_hour_in_ist)):
#             for n in range(data[current_hour_in_ist]["count_per_min"]):
#                 logger.info(n)
#                 bill_no= str(uuid.uuid4())
#                 uhid = 'AIGG.' + str(uuid.uuid4().int)[:8]
#                 phone_number = random.choice(phone_nos)
#                 logger.info(data[current_hour_in_ist])
#                 service_codes = random.choices(data[current_hour_in_ist]["services"], k=random.choice([2,3,4,5,6,7,8]), weights=data[current_hour_in_ist]["weights"])
#                 logger.info(service_codes)
#                 bill_details = BillDetail(bill_no = bill_no, umr_no= uhid, name =name, phone_number=phone_number, service_codes=service_codes)
#                 save_user_service(db,bill_details)
#         print("Bill Generated Successfully")
#     except Exception as ex:
#         text = f"""Exception : {ex}"""
#         # send_email([os.environ["DIALY_REPORT_MAIL"]],"Bill Generation",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
#         logger.exception(ex)
#     finally:
#         if db is not None: db.close()
        
@celery.task(bind=True)
def dynamically_entry_user(self):
    try:
        db= SessionLocal()
        today = date.today()
        random_numbers=[300,400,700,800,900,1000]
        user_queues = (
            db.query(UserQueueModel.queue_id, UserQueueModel.token_id, UserModel.umr_no).join(UserQueueModel.user).join(UserQueueModel.queue).filter(QueueModel.queue_type==QueueTypeEnum.AUTO).filter(UserQueueModel.date == today)
            .filter(UserQueueModel.status == UserQueueStatusEnum.CHECKIN).filter(func.extract("epoch", (func.now() - UserQueueModel.created_at))>random.choice(random_numbers))
            .all())
        for user in user_queues:
            try:
                qr_detail = QRDetail(uhid= user.umr_no)
                add_user_queue(db,user.queue_id, qr_detail,None)
            except Exception as ex:
                text = f"""Exception : {ex}"""
                send_email([os.environ["DIALY_REPORT_MAIL"]],"Entry User",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
                logger.exception(ex)
        print("User Entered Successfully")
    except Exception as ex:
        text = f"""Exception : {ex}"""
        # send_email([os.environ["DIALY_REPORT_MAIL"]],"Entry User",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
        logger.exception(ex)
    finally:
        if db is not None: db.close()

@celery.task(bind=True)
def dynamically_exit_user(self):
    try:
        db= SessionLocal()
        test_exit_user_from_queue(db)
        print("User Exited Successfully")
    except Exception as ex:
        text = f"""Exception : {ex}"""
        # send_email([os.environ["DIALY_REPORT_MAIL"]],"Exit User Queue",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
        logger.exception(ex)
    finally:
        if db is not None: db.close()

@celery.task(bind=True)
def update_alloted_beds(self):
    try:
        db= SessionLocal()
        his_db = SessionLocalHis()
        process_allocated_beds(db,his_db)
        print("Proccessed Allocated Beds")
    except Exception as ex:
        logger.exception(ex)
    finally:
        if db is not None: db.close()
        if his_db is not None: his_db.close()

@celery.task(bind=True)
def update_mismatch_bed_classes(self):
    try:
        db= SessionLocal()
        his_db = SessionLocalHis()
        mismatch_data(db,his_db)
        print("Proccessed mismatched data")
    except Exception as ex:
        logger.exception(ex)
    finally:
        if db is not None: db.close()
        if his_db is not None: his_db.close()      

@celery.task(bind=True)
def update_bed_status(self):
    try:
        db= SessionLocal()
        update_pending_bed_request_status(db)
        print("Updated bed status")
    except Exception as ex:
        logger.exception(ex)
    finally:
        if db is not None: db.close()
        
@celery.task(bind=True)
def update_counter(self):
    try:
        db= SessionLocal()
        update_counter_priority(db)
        print("Counter Updated Successfully")
    except Exception as ex:
        text = f"""Exception : {ex}"""
        send_email([os.environ["DIALY_REPORT_MAIL"]],"Update Counter priority",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
        logger.exception(ex)
    finally:
        if db is not None: db.close()

@celery.task(bind=True)
def dynamically_entry_user_endo(self):
    try:
        db= SessionLocal()
        today = date.today()
        random_numbers=[300,400,700,800,900,1000]
        user_queues = (
            db.query(UserQueueModel.queue_id, UserQueueModel.token_id,UserQueueModel.user).join(UserQueueModel.queue).filter(UserQueueModel.date == today)
            .filter(UserQueueModel.status == UserQueueStatusEnum.CHECKIN).filter(func.extract("epoch", (func.now() - UserQueueModel.created_at))>random.choice(random_numbers))
            .all())
        for user in user_queues:
            try:
                qr_detail = QRDetail(uhid= user.user.umr_no)
                add_user_queue(db,user.queue_id, qr_detail,None)
            except Exception as ex:
                text = f"""Exception : {ex}"""
                send_email([os.environ["DIALY_REPORT_MAIL"]],"Entry User",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
                logger.exception(ex)
        print("User Entered Successfully")
    except Exception as ex:
        text = f"""Exception : {ex}"""
        # send_email([os.environ["DIALY_REPORT_MAIL"]],"Entry User",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
        logger.exception(ex)
    finally:
        if db is not None: db.close()
        
@celery.task(bind=True)
def dynamically_freeze_user_endo(self):
    try:
        db= SessionLocal()
        today = date.today()
        random_numbers=[300,400,700,800,900,1000]
        queue= db.query(QueueModel.id).filter(QueueModel.queue_code=='10').one_or_none()
        try:
            if queue is not None:
                logger.info(f'start time: {time.time()}')
                call_next(db,queue.id, None)
                logger.info(f'end time: {time.time()}')
        except Exception as ex:
            text = f"""Exception : {ex}"""
            # send_email([os.environ["DIALY_REPORT_MAIL"]],"FREEZE User ENDO",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
            logger.exception(ex)
        print("User Entered Successfully")
    except Exception as ex:
        text = f"""Exception : {ex}"""
        # send_email([os.environ["DIALY_REPORT_MAIL"]],"Entry User",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
        logger.exception(ex)
    finally:
        if db is not None: db.close()
    
@celery.task(bind=True)
def dynamically_assign_tags(self):
    try:
        db= SessionLocal()
        today = date.today()
        random_numbers=[10,20,30,40,50,60]
        try:
            user_queues = (
                db.query(UserQueueModel.id, UserQueueModel.queue_id, UserQueueModel.token_id).join(UserQueueModel.queue).filter(UserQueueModel.date == today)
                .filter(UserQueueModel.status == UserQueueStatusEnum.FREEZED).filter(func.extract("epoch", (func.now() - UserQueueModel.freezed_at))>random.choice(random_numbers))
                .all())
            for user_queue in user_queues:
                tag= db.query(TagModel).filter(TagModel.id.not_in(db.query(UserQueueModel.tag_id).filter(UserQueueModel.tag_id.is_not(None)))).first()
                if tag is not None:
                    logger.info(f'assign tags start time: {time.time()}')
                    data = update_user_queue_step(db, user_queue.id, None,"from cron","CHECKOUT",None,tag.code, "TRANSITION",None, None)
                    logger.info(f'assign tags end time: {time.time()}')
        except Exception as ex:
            text = f"""Exception : {ex}"""
            send_email([os.environ["DIALY_REPORT_MAIL"]],"FREEZE User ENDO",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
            logger.exception(ex)
        print("User Entered Successfully")
    except Exception as ex:
        text = f"""Exception : {ex}"""
        # send_email([os.environ["DIALY_REPORT_MAIL"]],"Entry User",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
        logger.exception(ex)
    finally:
        if db is not None: db.close()

@celery.task(bind=True)
def dynamically_assign_locations(self):
    try:
        db= SessionLocal()
        update_user_queue_step_dynamically(db)
        print("Queue Step Updated")
    except Exception as ex:
        logger.exception(ex)
    finally:
        if db is not None: db.close()
        
@celery.task(bind=True)
def update_discharge_history(self):
    try:
        db= SessionLocal()
        his_db = SessionLocalHis()
        update_discharged_beds(db,his_db)
        print("Updated bed status")
    except Exception as ex:
        logger.exception(ex)
    finally:
        if db is not None: db.close()
        if his_db is not None: his_db.close()

@celery.task(bind=True)
def update_bed_status_history(self):
    try:
        db= SessionLocal()
        his_db = SessionLocalHis()
        store_bed_status_history(db,his_db)
        print("Updated bed status")
    except Exception as ex:
        logger.exception(ex)
    finally:
        if db is not None: db.close()
        if his_db is not None: his_db.close()
# @celery.task(bind=True)
# def get_random(self):    
    # Define a range of numbers and their corresponding weights
    # moring_services = ["FBS","LP","USG ABDOMEN","LFT",
    #                    "RFT","BGRH","CUE","CBP","ESR","TSH",
    #                    "HB CLIA","HBA1C","X_RAY","ECG","2D ECHO","URINE"]
    # moring_weights = [10,10,1,1,1,1,1,1,1,1,1,1,1,2,1.5,1]
    # afternoon_services= ["USG ABDOMEN","LFT","RFT",
    #                      "BGRH","CUE","CBP","ESR","TSH",
    #                      "HB CLIA","HBA1C","X_RAY","ECG",
    #                      "2D ECHO","URINE"]
    # afternoon_weights=[1,1,1,1,1,1,1,1,1,1,1,2,1.5,1]
    # data={
    #     7:{
    #         services:moring_services,
    #         weights:moring_weights
    #     },
    #     8:{
    #         services:moring_services,
    #         weights:moring_weights
    #     },
    #     9:{
    #         services:moring_services,
    #         weights:moring_weights
    #     },
    #     10:{
    #         services:moring_services,
    #         weights:moring_weights
    #     },
    #     11:{
    #         services:moring_services,
    #         weights:moring_weights
    #     },
    #     12:{
    #         services:moring_services,
    #         weights:moring_weights
    #     },
    #     13:{
    #         services:afternoon_services,
    #         weights:afternoon_weights
    #     },
    #     14:{
    #         services:afternoon_services,
    #         weights:afternoon_weights
    #     },
    #     15:{
    #         services:afternoon_services,
    #         weights:afternoon_weights
    #     },
    #     16:{
    #         services:afternoon_services,
    #         weights:afternoon_weights
    #     },
    #     17:{
    #         services:afternoon_services,
    #         weights:afternoon_weights
    #     },
    #     18:{
    #         services:afternoon_services,
    #         weights:afternoon_weights
    #     },
    #     19:{
    #         services:afternoon_services,
    #         weights:afternoon_weights
    #     },
    #     20:{
    #         services:afternoon_services,
    #         weights:afternoon_weights
    #     },
    # }
    # ist_timezone = pytz.timezone('Asia/Kolkata')
    # # Get the current time in IST
    # current_time_in_ist = datetime.now(ist_timezone)
    # # Extract the current hour in IST
    # current_hour_in_ist = current_time_in_ist.hour
    # services = ["USG ABDOMEN","LFT","RFT","FBS","BGRH","CUE","CBP","ESR","TSH","HB CLIA","LP","HBA1C","X_RAY","ECG","2D ECHO","URINE"]
    # weights = [1,1,1,1,2,1,1,1,1,1,1,2,1,1,1,1]
    # csv_file_path = 'local_data/patients.csv'
    # data_list=[]
    # with open(csv_file_path, newline='') as csvfile:
    #     reader = csv.reader(csvfile)
    #     data_list = list(reader)
    # # Use random.choices to pick a number based on weights
    # services=data[current_hour_in_ist]
    # random_number = random.choices(services, weights)[0]
    # patient_name = random.choice(data_list)
    # print(f"Patient Name: {patient_name['FIRST']+ patient_name['LAST']}")
    # print(f"Randomly selected number: {random_number}")
@celery.task(name="create_watsapp_msg")
def call_whatsapp_msg_task(headers, body):
    res = handle_request1(os.environ["FACEBOOK_BASE_URL"]+SEND_WHATSAPP_MSG, headers,body)
    if res.status_code == 200:
        logger.info("Message sent successfully")
    else:
        logger.info("Failed to sent message")

@celery.task(bind=True)       
def sync_loc_count_1(self):
    try:
        db= SessionLocal()
        sync_loc_count(db)
        print("Updated loc count")
    except Exception as ex:
        logger.exception(ex)
    finally:
        if db is not None: db.close()
        
@celery.task(name="send_sms_msg_task")
def send_sms_msg_task(message,phone_number, event_code,user_type):        
    user_details = [
            UserDetails(
                phone_number=phone_number, event_code=event_code, user_type=user_type
            )
        ]
    try:
        db= SessionLocal()
        print("send_sms_msg_task start")
        send_aig_otp(message, user_details, db)
        print("send_sms_msg_task end")
    except Exception as ex:
        logger.exception(ex)
    finally:
        if db is not None: db.close()

@celery.task(name="send_bed_request_msg")
def send_bed_request_msg(phone_number, event_code,var1,var2):        
    try:
        print("send_bed_request_msg start")
        send_bed_request_whatsapp_msg(phone_number, event_code,var1,var2)
        print("send_bed_request_msg end")
    except Exception as ex:
        logger.exception(ex)
