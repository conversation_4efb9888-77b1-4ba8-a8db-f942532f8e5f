
import csv
from datetime import date, datetime, timedelta
import time
import uuid, random
import pytz
from bill.resolvers import save_user_service, save_user_service_1
from constants import SEND_WHATSAPP_MSG, SEND_AIRTEL_WHATSAPP
from sms_mail_notification.schema import UserDetails
from sqlalchemy import desc, case, func, cast, Date
from bms.resolvers import mismatch_data, process_allocated_beds, store_bed_status_history, update_discharged_beds, update_pending_bed_request_status
from database.aig_his import SessionLocalHis
from database.db_conf import SessionLocal
import os
from celery import Celery
import logging
from celery.schedules import crontab
from user.models import Screen as ScreenModel, trackScreen as trackScreenModel, User as UserModel, StatusEnum as QueueStatuEnum
from user.resolvers import send_aig_otp
from util.email import send_email
from queues.resolvers import call_next, handle_cancelled_services, sync_loc_count, update_counter_priority,exit_user_queue, force_exit_user_from_queue, reset_queues_user_queues, add_user_queue, test_exit_user_from_queue, update_data_from_emr, update_user_queue_manual, update_user_queue_step, update_user_queue_step_dynamically,get_order_details, get_sample_collection_time
from bill.models import ServiceStatusEnum
from graphql_types import BillDetail, BillDetail1, QRDetail, UserDetail
from queues.models import Queue as QueueModel, QueueTypeEnum, Tag as TagModel, UserQueue as UserQueueModel, UserQueueStatusEnum, UserService as UserServiceModel
from service.models import Service as ServiceModel, StatusEnum
from config.models import Config as ConfigModel
from util.globals import handle_request1, set_redis_key, get_redis_value
logger = logging.getLogger()
celery = Celery(__name__)
celery.conf.broker_url = os.environ.get("CELERY_BROKER_URL")
celery.conf.result_backend = os.environ.get("CELERY_BROKER_BACKEND")
SQLALCHEMY_DATABASE_URL = os.environ["DATABASE_URL"]
from itertools import groupby

@celery.on_after_configure.connect
def setup_periodic_tasks(sender, **kwargs):
    # sender.add_periodic_task(
    #     crontab(hour="0", minute="0"),
    #     daily_report.s('daily report'),
    # )
    # sender.add_periodic_task(
    #     crontab(minute='*'),  
    #     force_exit_user_queue.s(),
    # )
    sender.add_periodic_task(
        crontab(hour="0", minute="0"),
        reset_queues.s(),
    )
    sender.add_periodic_task(
        crontab(minute='*/1'),  
        generate_user_bill.s(),
    )
    sender.add_periodic_task(
        crontab(minute='*/1'),  
        update_order_details.s(),
    )
    # sender.add_periodic_task(
    #     crontab(minute='*/1'),  
    #     dynamically_entry_user.s(),
    # )
    # sender.add_periodic_task(
    #     crontab(minute='*/1'),  
    #     dynamically_exit_user.s(),
    # )
    sender.add_periodic_task(
        crontab(minute='*/5'),  
        update_counter.s(),
    )
    # sender.add_periodic_task(
    #     crontab(minute='*/5'),  
    #     update_alloted_beds.s(),
    # )
    # sender.add_periodic_task(
    #     crontab(minute='*/5'),  
    #     generate_user_bill_endo.s(),
    # )
    # sender.add_periodic_task(
    #     crontab(minute='*'),  
    #     dynamically_freeze_user_endo.s(),
    # )
    # sender.add_periodic_task(
    #     crontab(minute='*'),  
    #     dynamically_assign_tags.s(),
    # )
    # sender.add_periodic_task(
    #     crontab(minute='*/5'),  
    #     dynamically_assign_locations.s(),
    # )
    # sender.add_periodic_task(
    #     crontab(hour="0", minute="0"),  
    #     update_mismatch_bed_classes.s(),
    # )
    # sender.add_periodic_task(
    #     crontab(hour="*",minute="0"),  
    #     update_bed_status.s(),
    # )
    # sender.add_periodic_task(
    #     crontab(minute='*/5'),  
    #     update_discharge_history.s(),
    # )
    # sender.add_periodic_task(
    #     crontab(hour="0", minute="0"),  
    #     update_bed_status_history.s(),
    # )
    sender.add_periodic_task(
        crontab(minute="*"),  
        sync_loc_count_1.s(),
    )
    sender.add_periodic_task(
        crontab(minute="*"),  
        update_from_emr.s(),
    )
    sender.add_periodic_task(
        crontab(minute='*/1'),  
        sync_cancelled_service.s(),
    )


@celery.task(bind=True)
def daily_report(self, args):
    db=None
    try:
        html_page=''
        db = SessionLocal()
        current_time = datetime.utcnow()
        one_day_back = current_time - timedelta(days=1)
        flow_types=db.query(trackScreenModel.initial_action,func.count(trackScreenModel.id).label("count")).filter(cast(trackScreenModel.created_at, Date)==one_day_back.date()).group_by(trackScreenModel.initial_action).all()
        total_count=0
        html_page+=f'<b>{os.environ["TYPE"]}</b><br>'
        html_page+=f'<html><body>Report Generated at {current_time}<br>'
        html_page+=f'Report Generated for {one_day_back.date()}<br>'
        html_page+="<table border=1 width='100%' style='border-collapse: collapse;'><tr><th width='30%'>Flow</th><th width='30%'>Screen</th><th width='10%'>Count</th><th width='10%'>Min Time</th><th width='10%'>Max Time</th><th width='10%'>Avg Time</th></tr>"
        sum=0
        failure=0
        success=0
        for initial_action in flow_types:
            total_count+=initial_action[1]
            print(initial_action)
            print(len(flow_types))
            screens=db.query(func.count(trackScreenModel.id).label("screen_count"),ScreenModel.screen_name,trackScreenModel.is_final,func.min(0 if trackScreenModel.updated_at is None else func.extract("epoch", (trackScreenModel.updated_at - trackScreenModel.created_at))),func.max(0 if trackScreenModel.updated_at is None else func.extract("epoch", (trackScreenModel.updated_at - trackScreenModel.created_at))),func.avg(0 if trackScreenModel.updated_at is None else func.extract("epoch", (trackScreenModel.updated_at - trackScreenModel.created_at)))).filter(trackScreenModel.initial_action==initial_action[0]).join(trackScreenModel.screen).filter(cast(trackScreenModel.created_at, Date)==one_day_back.date()).group_by(trackScreenModel.action,trackScreenModel.is_final,ScreenModel.screen_name).all()
            if len(screens)>0:
                html_page+=f"<tr><td rowspan={len(screens)}>{initial_action[0]}</td>"
            for screen in screens:
                success+=screen[0] if screen[2]==1 else 0
                failure+=screen[0] if screen[2]==0 else 0
                color='green' if screen[2]==1 else '#FFBF00'
                html_page+=f"<td style='color:{color};'>{screen[1]}</td><td>{screen[0]}</td><td>{0 if screen[3] is None else round(screen[3], 2)}</td><td>{0 if screen[4] is None else round(screen[4], 2)}</td><td>{0 if screen[5] is None else round(screen[5], 2)}</td></tr><tr>"
            if len(screens)>0:
                html_page+="</tr>"
        html_page+=f"<tr><td>Total count: </td><td>&nbsp;</td><td colspan='4'>{failure+success} success({success}) failure ({failure})</td></tr>"
        html_page+="</table></body></html>"
        send_email([os.environ["DIALY_REPORT_MAIL"]],"Aig daily report",html_page,os.environ["MAIL_ID"],os.environ["PASSWORD"])
    except Exception as Ex:
        logger.exception("exception while sending data")
    finally:
        if db is not None :db.close()
    # db.commit()


@celery.task(bind=True)
def force_exit_user_queue(self):
    try:
        db= SessionLocal()
        force_exit_user_from_queue(db)
        print("Function executed Successfully")
    except Exception as ex:
        logger.exception(ex)
    finally:
        if db is not None: db.close()

@celery.task(bind=True)
def reset_queues(self):
    try:
        db= SessionLocal()
        reset_queues_user_queues(db)
        print("Function executed Successfully")
    except Exception as ex:
        logger.exception(ex)
    finally:
        if db is not None: db.close()

@celery.task(bind=True)
def sync_cancelled_service(self):
    try:
        db= SessionLocal()
        his_db = SessionLocalHis()
        handle_cancelled_services(db,his_db)
        print("cancelled services completed Successfully")
    except Exception as ex:
        logger.exception(ex)
    finally:
        if db is not None: db.close()
        if his_db is not None: his_db.close()

@celery.task(bind=True)
def generate_user_bill(self):
    try:
        db= SessionLocal()
        his_db = SessionLocalHis()
        # moring_services = ["FBS","LP","USG ABDOMEN","LFT",
        #                "RFT","BGRH","CUE","CBP","ESR","TSH",
        #                "HB CLIA","HBA1C","X_RAY","ECG","2D ECHO","URINE"]
        # moring_weights = [10,10,2,2,1,1,1,1,1,1,1,1,1,2,1.5,1]
        # moring_services= ["USG ABDOMEN","LFT","RFT",
        #                  "CBP","HBA1C","X_RAY","ECG",
        #                  "2D ECHO","INTERNAL_MED","CLINICAL_NUTRITION"]
        # moring_weights=[1,1,2,3,2,3,2,2,2,1]
        # afternoon_services= ["USG ABDOMEN","LFT","RFT",
        #                  "BGRH","CUE","CBP","ESR","TSH",
        #                  "HB CLIA","HBA1C","X_RAY","ECG",
        #                  "2D ECHO","URINE"]
        # afternoon_services= ["USG ABDOMEN","LFT","RFT",
        #                  "CBP","HBA1C","X_RAY","ECG",
        #                  "2D ECHO","INTERNAL_MED","CLINICAL_NUTRITION"]
        # afternoon_weights=[1,1,2,3,2,3,2,2,2,1]
        # data={
        #     6:{
        #         "services":moring_services,
        #         "weights":moring_weights,
        #         "count_per_min":1,
        #     },
        #     7:{
        #         "services":moring_services,
        #         "weights":moring_weights,
        #         "count_per_min":1,
        #     },
        #     8:{
        #         "services":moring_services,
        #         "weights":moring_weights,
        #         "count_per_min":1,
        #     },
        #     9:{
        #         "services":moring_services,
        #         "weights":moring_weights,
        #         "count_per_min":1,
        #     },
        #     10:{
        #         "services":moring_services,
        #         "weights":moring_weights,
        #         "count_per_min":1,
        #     },
        #     11:{
        #         "services":moring_services,
        #         "weights":moring_weights,
        #         "count_per_min":1,
        #     },
        #     12:{
        #         "services":moring_services,
        #         "weights":moring_weights,
        #         "count_per_min":1,
        #     },
        #     13:{
        #         "services":afternoon_services,
        #         "weights":afternoon_weights,
        #         "count_per_min":1,
        #     },
        #     14:{
        #         "services":afternoon_services,
        #         "weights":afternoon_weights,
        #         "count_per_min":1,
        #     },
        #     15:{
        #         "services":afternoon_services,
        #         "weights":afternoon_weights,
        #         "count_per_min":1,
        #     },
        #     16:{
        #         "services":afternoon_services,
        #         "weights":afternoon_weights,
        #         "count_per_min":1,
        #     },
        #     17:{
        #         "services":afternoon_services,
        #         "weights":afternoon_weights,
        #         "count_per_min":1,
        #     },
        #     18:{
        #         "services":afternoon_services,
        #         "weights":afternoon_weights,
        #         "count_per_min":1,
        #     },
        #     19:{
        #         "services":afternoon_services,
        #         "weights":afternoon_weights,
        #         "count_per_min":1,
        #     },
        # }
        
        # names = ["Monika", "Lohith", "Jyothish", "Sandeep", "Praneeth", "Pavani", "Ganesh", "Jagan", "Joel", "Sweetha",
        #  "Latha", "Niharika", "Sravani", "Kalyan", "Poojitha", "Hema", "Sindhu", "Gopi", "Sophia", "Tyler"]
        # name = random.choice(names)
        # phone_nos= ["6281567912","7013292857","9398652032","9542302610"]
        ist_timezone = pytz.timezone('Asia/Kolkata')
        emp_list = db.query(ServiceModel.code).join(ServiceModel.queues).filter(
            QueueModel.status == QueueStatuEnum.ACTIVE,
            QueueModel.service_type == "CONSULTATIONS",
            ServiceModel.status == StatusEnum.ACTIVE
        ).distinct().all()
        emp_list = [e.code for e in emp_list]  # Extract only the code values

        # For NON-CONSULTATIONS
        test_list = db.query(ServiceModel.code).join(ServiceModel.queues).filter(
            QueueModel.status == QueueStatuEnum.ACTIVE,
            QueueModel.service_type != "CONSULTATIONS",
            ServiceModel.status == StatusEnum.ACTIVE
        ).distinct().all()
        test_list = [t.code for t in test_list]
        emp_str = ",".join(f"'{code}'" for code in emp_list)
        emp_str="''" if not emp_str else emp_str
        test_str = ",".join(f"'{code}'" for code in test_list)
        test_str="'0'" if not test_str else test_str
        current_time_in_ist = datetime.now(ist_timezone)
        current_hour_in_ist = current_time_in_ist.hour
        time= get_redis_value("HIS_BILLING_SYNC")
        sql_select_Query = """
            SELECT op.Datetime, dp.serviceId,ld.serviceId AS mServiceId, cast(dp.itemId as varchar) as itemId,cast(ld.itemId as varchar) AS mItemId, cast(me.EmployeeId as varchar) as EmployeeId, op.id as oId, dp.id as dId, CONCAT(TRIM(op.IACode), '.', op.RegistrationNo) AS uhId, CONCAT(mp.Title,' ', TRIM(mp.Firstname), ' ', mp.LastName) AS name, mp.pCellNo,od.FromDateTime,
            CASE WHEN mf.deleted = 0 AND mf.RateContract = (SELECT id FROM M_Company WHERE name = 'Platinum') THEN 1 ELSE 0 END AS is_platinum, op.BillNo as bill_no, CONCAT(cast(mp.Age as varchar), ' ', mat.Name) as age, ms.NAME as gender
            FROM O_OPBill op LEFT JOIN d_opbill dp ON op.Id=dp.OPBillId
            JOIN M_Patient mp ON op.IACode=mp.IACode AND op.RegistrationNo=mp.Registrationno
            LEFT JOIN M_Sex ms ON ms.ID=mp.Sex
            LEFT JOIN M_AgeType mat ON mat.Id=mp.Agetype
            LEFT JOIN L_Mhcdetail ld ON ld.HCUID=dp.ItemId AND dp.serviceId=26
            LEFT JOIN M_Employee me ON (ld.itemId= me.id OR dp.itemId=me.id) AND (ld.serviceId=25 OR dp.serviceId= 25)
            LEFT JOIN o_doctorschedule od ON (op.RegistrationNo = od.IPIDOPID AND dp.ItemId = od.DoctorId AND dp.OPBillId = od.opbillid and od.Deleted=0)
            OUTER APPLY (
                    SELECT TOP 1 *
                    FROM l_PatientFinancialDetails mf
                    WHERE mf.Iacode=mp.IACode AND mf.RegNo=mp.Registrationno AND (PolicyvalidTill>= GETDATE() OR PolicyvalidTill IS NULL)
                    ORDER BY mf.UpdateOn DESC
                ) mf
            WHERE op.Datetime>='""" + time + """' and op.Datetime<='""" + current_time_in_ist.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3] + """' AND dp.Cancelled='false' AND op.Cancelled='false' and op.HSPLocationId=7
            AND (
                (dp.serviceId = 25 AND me.EmployeeId IN (""" + emp_str + """))
                OR 
                (dp.serviceId <> 25 AND ISNULL(ld.itemId, dp.itemId) IN (""" + test_str + """))
            )
            ORDER BY op.id ASC 
        """
        logger.info(sql_select_Query)
        res_data = his_db.execute(sql_select_Query).all()
        grouped_dict = {key: list(group) for key, group in groupby(res_data, key=lambda x: x["oId"])}
        logger.info(grouped_dict)
        for uhid, records in grouped_dict.items():
            try:
                logger.info(records)
                bill_details= [BillDetail1(package_id= x.itemId if x.mItemId is not None else 0,detail_bill_id=x.dId,bill_no=x.oId, service_code=x.EmployeeId if x.EmployeeId is not None else x.mItemId if x.mItemId is not None else x.itemId, billed_at=x.Datetime,appointment_date_time = x.FromDateTime,bill=x.bill_no,test_id=x.itemId) for x in records]
                user_details = UserDetail(uhid=records[0].uhId,name=records[0].name,phone_number=records[0].pCellNo,bill_detail=bill_details,weightage_id=1,doctor_name="",patient_type="OP",remarks="",is_platinum=records[0].is_platinum,age=records[0].age,gender=records[0].gender)
                logger.info(bill_details)
                save_user_service_1(db,None,user_details,False,None,True, True)
            except Exception as ex:
                logger.exception(ex)
        print("Bill Generated Successfully")
        safe_time = current_time_in_ist - timedelta(seconds=1)
        set_redis_key("HIS_BILLING_SYNC",safe_time.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3])
    except Exception as ex:
        text = f"""Exception : {ex}"""
        # send_email([os.environ["DIALY_REPORT_MAIL"]],"Bill Generation",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
        logger.exception(ex)
    finally:
        if db is not None: db.close()
        if his_db is not None: his_db.close()


@celery.task(bind=True)
def update_order_details(self):
    db = None
    try:
        from collections import defaultdict
        db = SessionLocal()

        services = db.query(UserServiceModel.id, UserServiceModel.bill, UserServiceModel.test_id, UserModel.umr_no.label('uhid')).join(
            UserModel, UserServiceModel.user_id == UserModel.id
        ).join(ServiceModel, UserServiceModel.service_id == ServiceModel.id).filter(
            UserServiceModel.status.in_([ServiceStatusEnum.PENDING, ServiceStatusEnum.ON_PROGRESS]),
            ServiceModel.service_type == "INVESTIGATIONS",
            func.date(UserServiceModel.created_at) == date.today(),
            UserServiceModel.bill.isnot(None),
            UserServiceModel.test_id.isnot(None),
            (UserServiceModel.order_details.is_(None)) | (UserServiceModel.order_details == {})
        ).all()

        # Group by bill and process
        bill_groups = defaultdict(list)
        for s in services:
            bill_groups[(s.bill, s.uhid)].append(s)

        updates = []
        for (bill_no, uhid), bill_services in bill_groups.items():
            try:
                order_details = get_order_details("1100", uhid, bill_no)
                lookup = {od.get('testId'): od for od in order_details or [] if od.get('testId')}
                updates.extend([
                    {'id': s.id, 'order_details': lookup[s.test_id]}
                    for s in bill_services if s.test_id in lookup
                ])
            except Exception as ex:
                logger.exception(f"Error processing bill {bill_no}: {ex}")

        # Batch update
        if updates:
            try:
                [db.query(UserServiceModel).filter(UserServiceModel.id == u['id']).update(
                    {UserServiceModel.order_details: u['order_details']}) for u in updates]
                db.commit()
                logger.info(f"Updated {len(updates)} services")
            except Exception as ex:
                logger.exception(f"Batch update failed: {ex}")
                db.rollback()
        else:
            logger.info("No matching order details found")

    except Exception as ex:
        logger.exception(ex)
    finally:
        if db: db.close()

# @celery.task(bind=True)
# def generate_user_bill_endo(self):
#     try:
#         db= SessionLocal()
#         moring_services = ["ENDOSCOPY","COLONOSCOPY"]
#         moring_weights = [1,1]
#         afternoon_services= ["ENDOSCOPY","COLONOSCOPY"]
#         afternoon_weights=[1,1]
#         data={
#             6:{
#                 "services":moring_services,
#                 "weights":moring_weights,
#                 "count_per_min":1,
#             },
#             7:{
#                 "services":moring_services,
#                 "weights":moring_weights,
#                 "count_per_min":1,
#             },
#             8:{
#                 "services":moring_services,
#                 "weights":moring_weights,
#                 "count_per_min":2,
#             },
#             9:{
#                 "services":moring_services,
#                 "weights":moring_weights,
#                 "count_per_min":2,
#             },
#             10:{
#                 "services":moring_services,
#                 "weights":moring_weights,
#                 "count_per_min":2,
#             },
#             11:{
#                 "services":moring_services,
#                 "weights":moring_weights,
#                 "count_per_min":1,
#             },
#             12:{
#                 "services":moring_services,
#                 "weights":moring_weights,
#                 "count_per_min":1,
#             },
#             13:{
#                 "services":afternoon_services,
#                 "weights":afternoon_weights,
#                 "count_per_min":2,
#             },
#             14:{
#                 "services":afternoon_services,
#                 "weights":afternoon_weights,
#                 "count_per_min":2,
#             },
#             15:{
#                 "services":afternoon_services,
#                 "weights":afternoon_weights,
#                 "count_per_min":1,
#             },
#             16:{
#                 "services":afternoon_services,
#                 "weights":afternoon_weights,
#                 "count_per_min":2,
#             },
#             17:{
#                 "services":afternoon_services,
#                 "weights":afternoon_weights,
#                 "count_per_min":10,
#             },
#             18:{
#                 "services":afternoon_services,
#                 "weights":afternoon_weights,
#                 "count_per_min":10,
#             },
#             19:{
#                 "services":afternoon_services,
#                 "weights":afternoon_weights,
#                 "count_per_min":0,
#             },
#         }
        
#         names = ["Monika", "Lohith", "Jyothish", "Sandeep", "Praneeth", "Pavani", "Ganesh", "Jagan", "Joel", "Sweetha",
#          "Latha", "Niharika", "Sravani", "Kalyan", "Poojitha", "Hema", "Sindhu", "Gopi", "Sophia", "Tyler"]
#         name = random.choice(names)
#         phone_nos= ["6281567912","7013292857","8179466871","9542302610","9394265087"]
#         ist_timezone = pytz.timezone('Asia/Kolkata')
#         current_time_in_ist = datetime.now(ist_timezone)
#         current_hour_in_ist = current_time_in_ist.hour
#         if(data.get(current_hour_in_ist)):
#             for n in range(data[current_hour_in_ist]["count_per_min"]):
#                 logger.info(n)
#                 bill_no= str(uuid.uuid4())
#                 uhid = 'AIGG.' + str(uuid.uuid4().int)[:8]
#                 phone_number = random.choice(phone_nos)
#                 logger.info(data[current_hour_in_ist])
#                 service_codes = random.choices(data[current_hour_in_ist]["services"], k=random.choice([2,3,4,5,6,7,8]), weights=data[current_hour_in_ist]["weights"])
#                 logger.info(service_codes)
#                 bill_details = BillDetail(bill_no = bill_no, umr_no= uhid, name =name, phone_number=phone_number, service_codes=service_codes)
#                 save_user_service(db,bill_details)
#         print("Bill Generated Successfully")
#     except Exception as ex:
#         text = f"""Exception : {ex}"""
#         # send_email([os.environ["DIALY_REPORT_MAIL"]],"Bill Generation",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
#         logger.exception(ex)
#     finally:
#         if db is not None: db.close()
        
@celery.task(bind=True)
def dynamically_entry_user(self):
    try:
        db= SessionLocal()
        today = date.today()
        random_numbers=[180,240,290,300,360,400]
        user_queues = (
            db.query(UserQueueModel.queue_id, UserQueueModel.token_id, UserModel.umr_no).join(UserQueueModel.user).join(UserQueueModel.queue).filter(QueueModel.queue_type==QueueTypeEnum.AUTO).filter(UserQueueModel.date == today)
            .filter(UserQueueModel.status == UserQueueStatusEnum.CHECKIN).filter(func.extract("epoch", (func.now() - UserQueueModel.created_at))>random.choice(random_numbers))
            .all())
        for user in user_queues:
            try:
                qr_detail = QRDetail(uhid= user.umr_no)
                add_user_queue(db,user.queue_id, qr_detail,None)
            except Exception as ex:
                text = f"""Exception : {ex}"""
                send_email([os.environ["DIALY_REPORT_MAIL"]],"Entry User",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
                logger.exception(ex)
        print("User Entered Successfully")
    except Exception as ex:
        text = f"""Exception : {ex}"""
        # send_email([os.environ["DIALY_REPORT_MAIL"]],"Entry User",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
        logger.exception(ex)
    finally:
        if db is not None: db.close()

@celery.task(bind=True)
def dynamically_exit_user(self):
    try:
        db= SessionLocal()
        test_exit_user_from_queue(db)
        print("User Exited Successfully")
    except Exception as ex:
        text = f"""Exception : {ex}"""
        # send_email([os.environ["DIALY_REPORT_MAIL"]],"Exit User Queue",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
        logger.exception(ex)
    finally:
        if db is not None: db.close()

@celery.task(bind=True)
def update_alloted_beds(self):
    try:
        db= SessionLocal()
        his_db = SessionLocalHis()
        process_allocated_beds(db,his_db)
        print("Proccessed Allocated Beds")
    except Exception as ex:
        logger.exception(ex)
    finally:
        if db is not None: db.close()
        if his_db is not None: his_db.close()

@celery.task(bind=True)
def update_mismatch_bed_classes(self):
    try:
        db= SessionLocal()
        his_db = SessionLocalHis()
        mismatch_data(db,his_db)
        print("Proccessed mismatched data")
    except Exception as ex:
        logger.exception(ex)
    finally:
        if db is not None: db.close()
        if his_db is not None: his_db.close()      

@celery.task(bind=True)
def update_bed_status(self):
    try:
        db= SessionLocal()
        update_pending_bed_request_status(db)
        print("Updated bed status")
    except Exception as ex:
        logger.exception(ex)
    finally:
        if db is not None: db.close()
        
@celery.task(bind=True)
def update_counter(self):
    try:
        db= SessionLocal()
        update_counter_priority(db)
        print("Counter Updated Successfully")
    except Exception as ex:
        text = f"""Exception : {ex}"""
        send_email([os.environ["DIALY_REPORT_MAIL"]],"Update Counter priority",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
        logger.exception(ex)
    finally:
        if db is not None: db.close()

@celery.task(bind=True)
def dynamically_entry_user_endo(self):
    try:
        db= SessionLocal()
        today = date.today()
        random_numbers=[300,400,700,800,900,1000]
        user_queues = (
            db.query(UserQueueModel.queue_id, UserQueueModel.token_id,UserQueueModel.user).join(UserQueueModel.queue).filter(UserQueueModel.date == today)
            .filter(UserQueueModel.status == UserQueueStatusEnum.CHECKIN).filter(func.extract("epoch", (func.now() - UserQueueModel.created_at))>random.choice(random_numbers))
            .all())
        for user in user_queues:
            try:
                qr_detail = QRDetail(uhid= user.user.umr_no)
                add_user_queue(db,user.queue_id, qr_detail,None)
            except Exception as ex:
                text = f"""Exception : {ex}"""
                send_email([os.environ["DIALY_REPORT_MAIL"]],"Entry User",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
                logger.exception(ex)
        print("User Entered Successfully")
    except Exception as ex:
        text = f"""Exception : {ex}"""
        # send_email([os.environ["DIALY_REPORT_MAIL"]],"Entry User",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
        logger.exception(ex)
    finally:
        if db is not None: db.close()
        
@celery.task(bind=True)
def dynamically_freeze_user_endo(self):
    try:
        db= SessionLocal()
        today = date.today()
        random_numbers=[300,400,700,800,900,1000]
        queue= db.query(QueueModel.id).filter(QueueModel.queue_code=='10').one_or_none()
        try:
            if queue is not None:
                logger.info(f'start time: {time.time()}')
                call_next(db,queue.id, None)
                logger.info(f'end time: {time.time()}')
        except Exception as ex:
            text = f"""Exception : {ex}"""
            # send_email([os.environ["DIALY_REPORT_MAIL"]],"FREEZE User ENDO",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
            logger.exception(ex)
        print("User Entered Successfully")
    except Exception as ex:
        text = f"""Exception : {ex}"""
        # send_email([os.environ["DIALY_REPORT_MAIL"]],"Entry User",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
        logger.exception(ex)
    finally:
        if db is not None: db.close()
    
@celery.task(bind=True)
def dynamically_assign_tags(self):
    try:
        db= SessionLocal()
        today = date.today()
        random_numbers=[10,20,30,40,50,60]
        try:
            user_queues = (
                db.query(UserQueueModel.id, UserQueueModel.queue_id, UserQueueModel.token_id).join(UserQueueModel.queue).filter(UserQueueModel.date == today)
                .filter(UserQueueModel.status == UserQueueStatusEnum.FREEZED).filter(func.extract("epoch", (func.now() - UserQueueModel.freezed_at))>random.choice(random_numbers))
                .all())
            for user_queue in user_queues:
                tag= db.query(TagModel).filter(TagModel.id.not_in(db.query(UserQueueModel.tag_id).filter(UserQueueModel.tag_id.is_not(None)))).first()
                if tag is not None:
                    logger.info(f'assign tags start time: {time.time()}')
                    data = update_user_queue_step(db, user_queue.id, None,"from cron","CHECKOUT",None,tag.code, "TRANSITION",None, None)
                    logger.info(f'assign tags end time: {time.time()}')
        except Exception as ex:
            text = f"""Exception : {ex}"""
            send_email([os.environ["DIALY_REPORT_MAIL"]],"FREEZE User ENDO",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
            logger.exception(ex)
        print("User Entered Successfully")
    except Exception as ex:
        text = f"""Exception : {ex}"""
        # send_email([os.environ["DIALY_REPORT_MAIL"]],"Entry User",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
        logger.exception(ex)
    finally:
        if db is not None: db.close()

@celery.task(bind=True)
def dynamically_assign_locations(self):
    try:
        db= SessionLocal()
        update_user_queue_step_dynamically(db)
        print("Queue Step Updated")
    except Exception as ex:
        logger.exception(ex)
    finally:
        if db is not None: db.close()
def update_discharge_history(self):
    try:
        db= SessionLocal()
        his_db = SessionLocalHis()
        update_discharged_beds(db,his_db)
        print("Updated bed status")
    except Exception as ex:
        logger.exception(ex)
    finally:
        if db is not None: db.close()
        if his_db is not None: his_db.close()

@celery.task(bind=True)
def update_bed_status_history(self):
    try:
        db= SessionLocal()
        his_db = SessionLocalHis()
        store_bed_status_history(db,his_db)
        print("Updated bed status")
    except Exception as ex:
        logger.exception(ex)
    finally:
        if db is not None: db.close()
        if his_db is not None: his_db.close()
# @celery.task(bind=True)
# def get_random(self):    
    # Define a range of numbers and their corresponding weights
    # moring_services = ["FBS","LP","USG ABDOMEN","LFT",
    #                    "RFT","BGRH","CUE","CBP","ESR","TSH",
    #                    "HB CLIA","HBA1C","X_RAY","ECG","2D ECHO","URINE"]
    # moring_weights = [10,10,1,1,1,1,1,1,1,1,1,1,1,2,1.5,1]
    # afternoon_services= ["USG ABDOMEN","LFT","RFT",
    #                      "BGRH","CUE","CBP","ESR","TSH",
    #                      "HB CLIA","HBA1C","X_RAY","ECG",
    #                      "2D ECHO","URINE"]
    # afternoon_weights=[1,1,1,1,1,1,1,1,1,1,1,2,1.5,1]
    # data={
    #     7:{
    #         services:moring_services,
    #         weights:moring_weights
    #     },
    #     8:{
    #         services:moring_services,
    #         weights:moring_weights
    #     },
    #     9:{
    #         services:moring_services,
    #         weights:moring_weights
    #     },
    #     10:{
    #         services:moring_services,
    #         weights:moring_weights
    #     },
    #     11:{
    #         services:moring_services,
    #         weights:moring_weights
    #     },
    #     12:{
    #         services:moring_services,
    #         weights:moring_weights
    #     },
    #     13:{
    #         services:afternoon_services,
    #         weights:afternoon_weights
    #     },
    #     14:{
    #         services:afternoon_services,
    #         weights:afternoon_weights
    #     },
    #     15:{
    #         services:afternoon_services,
    #         weights:afternoon_weights
    #     },
    #     16:{
    #         services:afternoon_services,
    #         weights:afternoon_weights
    #     },
    #     17:{
    #         services:afternoon_services,
    #         weights:afternoon_weights
    #     },
    #     18:{
    #         services:afternoon_services,
    #         weights:afternoon_weights
    #     },
    #     19:{
    #         services:afternoon_services,
    #         weights:afternoon_weights
    #     },
    #     20:{
    #         services:afternoon_services,
    #         weights:afternoon_weights
    #     },
    # }
    # ist_timezone = pytz.timezone('Asia/Kolkata')
    # # Get the current time in IST
    # current_time_in_ist = datetime.now(ist_timezone)
    # # Extract the current hour in IST
    # current_hour_in_ist = current_time_in_ist.hour
    # services = ["USG ABDOMEN","LFT","RFT","FBS","BGRH","CUE","CBP","ESR","TSH","HB CLIA","LP","HBA1C","X_RAY","ECG","2D ECHO","URINE"]
    # weights = [1,1,1,1,2,1,1,1,1,1,1,2,1,1,1,1]
    # csv_file_path = 'local_data/patients.csv'
    # data_list=[]
    # with open(csv_file_path, newline='') as csvfile:
    #     reader = csv.reader(csvfile)
    #     data_list = list(reader)
    # # Use random.choices to pick a number based on weights
    # services=data[current_hour_in_ist]
    # random_number = random.choices(services, weights)[0]
    # patient_name = random.choice(data_list)
    # print(f"Patient Name: {patient_name['FIRST']+ patient_name['LAST']}")
    # print(f"Randomly selected number: {random_number}")
@celery.task(name="create_watsapp_msg")
def call_whatsapp_msg_task(headers, body):
    res = handle_request1(os.environ["FACEBOOK_BASE_URL"]+SEND_WHATSAPP_MSG, headers,body)
    logger.info(res)
    if res.status_code == 200:
        logger.info("Message sent successfully")
    else:
        logger.info("Failed to sent message")
    
@celery.task(name="create_airtel_whatsapp_msg")
def call_airtel_whatsapp_msg_task(headers, body):
    res = handle_request1(os.environ["AIRTEL_WHATSAPP_URL"] + SEND_AIRTEL_WHATSAPP, headers,body)
    logger.info(res.json())
    if res.status_code == 200:
        logger.info("Message sent successfully")
    else:
        logger.info("Failed to sent message")

@celery.task(bind=True)       
def sync_loc_count_1(self):
    try:
        db= SessionLocal()
        sync_loc_count(db)
        print("Updated loc count")
    except Exception as ex:
        logger.exception(ex)
    finally:
        if db is not None: db.close()
        
@celery.task(name="send_sms_msg_task")
def send_sms_msg_task(message,phone_number, event_code,user_type,config={}):
    user_details = [
            UserDetails(
                phone_number=phone_number, event_code=event_code, user_type=user_type
            )
        ]
    try:
        db= SessionLocal()
        print("send_sms_msg_task start")
        send_aig_otp(message, user_details, db, config)
        print("send_sms_msg_task end")
    except Exception as ex:
        logger.exception(ex)
    finally:
        if db is not None: db.close()

@celery.task(name="sample_collection_time_task")
def sample_collection_time_task(self, user_service_ids, update_time):
    """Celery task to update sample collection time asynchronously"""
    try:
        db = SessionLocal()
        get_sample_collection_time(db, user_service_ids, update_time)
        logger.info(f"Sample collection time task completed for {len(user_service_ids)} services")
    except Exception as ex:
        logger.exception(f"Sample collection time task failed: {ex}")
    finally:
        if db is not None:
            db.close()
        
@celery.task(bind=True)
def update_from_emr(self):
    try:
        db= SessionLocal()
        update_data_from_emr(db)
        print("Updated from emr")
    except Exception as ex:
        logger.exception(ex)
    finally:
        if db is not None: db.close()