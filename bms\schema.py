import calendar
from datetime import datetime, timedelta
import logging
from typing import List, Optional
import strawberry
from bms.models import BedRequest as BedRequestModel, BedRemarks as BedRemarksModel,MasterHashTags as MasterHashTagsModel, RemarksTypeEnum
from bms.resolvers import( change_bed_request_priority, download_bed360_reports, get_avg_time_of_er_beds, get_bed_requests, get_bed_requests_in_date_range, get_common_remarks, get_er_bed_details, get_er_beds, get_hash_tags, get_mismatch_bed_classes, get_nurse_stations, get_patient_details, get_priority, initiate_bed_request, assign_bed,
                          get_bed_status, get_departments, get_doctor_by_department, get_case_type, get_patient_category,
                          get_bed_classes, get_bed_numbers, get_request_status, get_user_discharges, update_admission_date, update_bed_request_status, update_hash_tags,
                            withdraw_allocation, cancel_request, get_bed_details, get_modules, get_supporting_care_services, add_common_remarks
                            )
from graphql_types import MutationResponse, QueryResponse,BedAllotRequest, AssignBed
from exceptions.exceptions import MutationError
from util.globals import format_datetime
logger = logging.getLogger()
from fastapi import Response
import os
import calendar
# from user.schema import User

@strawberry.type
class Remarks:
    id: int
    remarks: str
    role: Optional[str] = None
    created_by: Optional[str] = None
    created_at: Optional[str] = None
    
    instance = strawberry.Private[BedRemarksModel]
    @classmethod
    def from_instance(cls, instance:BedRemarksModel):
        return cls(
            id= instance.id,
            remarks=instance.remarks,
            role = instance.user_role,
            created_by = instance.created_by,
            created_at = format_datetime(instance.created_at)
        )
 
@strawberry.type
class HashTag:
    id:int
    name:str
    code:str
    instance = strawberry.Private[MasterHashTagsModel]

    @classmethod
    def from_instance(cls,instance: MasterHashTagsModel):
        return cls(id=instance.id,name=instance.name,code=instance.code)
@strawberry.type
class BedRequest:
    id: int
    request_type :str
    wl_no :str
    uhid :str
    case_type :Optional[str]
    patient_category:Optional[str]
    user_contact_no:Optional[str]
    patient_name :Optional[str]
    department :Optional[str]
    doctor_name :Optional[str]
    requested_bed_class:Optional[str]
    req_from:Optional[str]
    estimation :Optional[str]
    remarks:Optional[List[Remarks]]
    bed_no:Optional[str]
    bed_status :Optional[str]
    alloted_time :Optional[str]
    admission_status:Optional[str]
    shifting_time:Optional[str]
    informed_to:Optional[str]
    status:Optional[str]
    created_by:Optional[str]
    created_at :Optional[str]
    updated_at :Optional[str]
    referred_by: Optional[str]
    source: Optional[str]
    rate_of_contract: Optional[str]
    priority: Optional[str]
    admission_no: Optional[str]
    alloted_bed_class: Optional[str] = None
    billing_bed_class: Optional[str] = None
    floor: Optional[str] = None
    cancellation_remarks: Optional[str] = None
    estimated_by: Optional[str] = None
    reason: Optional[str] = None
    tentative_admission_date : Optional[str] = None
    nurse_station: Optional[str] = None
    vacated_at:Optional[str]=None
    admitted_at: Optional[str]=None
    alloted_by: Optional[str]=None
    hash_tags:Optional[List[HashTag]]
    hash_names: Optional[List[str]] = None
    supporting_care_services: Optional[List[str]] = None

    instance = strawberry.Private[BedRequestModel]

    @classmethod
    def from_instance(cls, instance: BedRequestModel):
        return cls(
           id=instance.id,
            request_type =instance.type.name,
            wl_no  =instance.wl_no,
            uhid  =instance.uhid,
            case_type  = instance.case_type.name if instance.case_type is not None else None,
            patient_category =instance.patient_category,
            user_contact_no =instance.user_contact_no,
            patient_name  =instance.patient_name,
            department =instance.department,
            doctor_name  =instance.doctor_name,
            requested_bed_class =instance.requested_bed_class,
            req_from =instance.req_from,
            estimation  =instance.estimation,
            remarks =[Remarks.from_instance(remarks)for remarks in instance.bed_remarks] if len(instance.bed_remarks)> 0 else [],
            bed_no =instance.bed_no,
            bed_status  =instance.bed_status,
            alloted_time  = format_datetime(instance.alloted_time),
            admission_status =instance.admission_status,
            shifting_time =format_datetime(instance.shifting_time),
            informed_to=instance.informed_to,
            status=instance.status.name,
            created_by=instance.created_by,
            created_at =format_datetime(instance.created_at),
            updated_at =format_datetime(instance.updated_at),
            referred_by =  instance.referred_by,
            source = instance.source,
            rate_of_contract = instance.rate_of_contract,
            priority = instance.priority.name if instance.priority is not None else None,
            admission_no = instance.admission_no,
            alloted_bed_class = instance.alloted_bed_class,
            billing_bed_class = instance.billing_bed_class,
            floor = instance.floor,
            cancellation_remarks = instance.cancellation_remarks,
            estimated_by = instance.estimated_by,
            reason = instance.reason,
            tentative_admission_date = instance.tentative_admission_date,
            nurse_station = instance.nurse_station,
            vacated_at = format_datetime(instance.discharged_time),
            admitted_at = format_datetime(instance.completed_at),
            alloted_by = instance.alloted_by,
            hash_tags =[HashTag.from_instance(obj)for obj in instance.bed_hash_tags] if len(instance.bed_hash_tags)> 0 else [],
            hash_names =[obj.name for obj in instance.bed_hash_tags] if len(instance.bed_hash_tags)> 0 else [],
            supporting_care_services = instance.supporting_care_services
        )

@strawberry.type
class ERBed:
    bed_no: str
    bed_status: Optional[str]=None
    user_name: Optional[str]=None
    umr_no: Optional[str]=None
    ip_no: Optional[str]= None
    admitted_time :Optional[str]=None
    transfer_request:Optional[BedRequest]=None

    @classmethod
    def from_instance(cls, bed_no: str,bed_status:str,user_name:str,umr_no:str,admitted_time:str,ip_no:str,transfer_request:BedRequestModel):
        return cls(
            bed_no=bed_no,
            bed_status=bed_status,
            user_name=user_name,
            umr_no=umr_no,
            admitted_time=admitted_time,
            ip_no=ip_no,
            transfer_request=BedRequest.from_instance(transfer_request) if transfer_request is not None else None
        )
@strawberry.type
class PatientInfo:
    name: Optional[str] = None
    phone_number: Optional[str] = None
    uhid: Optional[str] = None
    age: Optional[str] = None
    gender: Optional[str] = None
    
    @classmethod
    def from_instance(cls, phone_number: str, name: str, uhid: str, gender: str, age: str):
        return cls(
            name = name,
            phone_number =phone_number,
            uhid = uhid,
            gender = gender,
            age = age
        )

@strawberry.type
class UserDischargeStatus:
    id: Optional[str] = None
    uhid :Optional[str] = None
    admission_no :Optional[str] = None
    patient_name :Optional[str] = None
    bed:Optional[str] = None
    admission_date:Optional[str] = None
    requested_discharge_date :Optional[str] = None
    nursing_clearance_date: Optional[str] = None
    blood_bank_clearance_date :Optional[str] = None
    discharge_summary:Optional[str] = None
    f_and_b_clearance_date:Optional[str] = None
    pharmacy_clearance_date :Optional[str] = None
    audit_clearance_date :Optional[str] = None
    billing_ack_date:Optional[str] = None
    bill_ready_date:Optional[str] = None
    discharge_date:Optional[str] = None
    ot_clearance_date: Optional[str] = None
    bed_class: Optional[str] = None
    floor: Optional[str] = None

    @classmethod
    def from_instance(cls, data):
        floor=None
        try:
            floor =data["Nurse Station"].split('-')[1].strip()
        except Exception as e:
            floor='-'
        return cls(
            id=data["Bed"],
            uhid = data["UHID"],
            admission_no = data["IP Number"],
            patient_name = data["Patient Name"],
            bed = data["Bed"],
            admission_date = format_datetime(data["Admission Date Time"]),
            requested_discharge_date = format_datetime(data["Requested Discharge Date"]),
            nursing_clearance_date = format_datetime(data["nurseclearancedatetime"]),
            blood_bank_clearance_date = format_datetime(data["bloodbankclearancedatetime"]),
            discharge_summary =data["Discharge Summary DateTime"],
            f_and_b_clearance_date = format_datetime(data["fnbclearancedatetime"]),
            pharmacy_clearance_date = format_datetime(data["pharmacyclearancedatetime"]),
            audit_clearance_date = format_datetime(data["auditclrsavedatetime"]),
            billing_ack_date = data["Billing Ack Date Time"],
            bill_ready_date = format_datetime(data["Bill Ready Date Time"]),
            discharge_date= format_datetime(data["clinicaldischarge"]),
            ot_clearance_date= format_datetime(data["OTClearanceDateTime"]),
            bed_class = data["Bed Class"],
            floor=floor
        )
    

@strawberry.type
class BedStatus:
    id: Optional[str] = None
    bed_no :Optional[str] = None
    bed_class :Optional[str] = None
    bed_status :Optional[str] = None
    floor :Optional[str] = None
    tower:Optional[str] = None
    nurse_station: Optional[str] = None
    uhid:Optional[str] = None
    admission_number: Optional[str] = None
    patient_name:Optional[str] = None
    speciality: Optional[str] = None
    primary_consultant:Optional[str] = None
    secondary_consultant:Optional[str] = None
    contact_number:Optional[str] = None
    req_bed_class: Optional[str] = None
    billable_bed_class: Optional[str] = None

    @classmethod
    def from_instance(cls, data):
        floor=tower=nurse_station=None
        try:
            floor =data["Nurse Station"].split('-')[1].strip()
            tower=data["Nurse Station"].split('-')[0].strip()
            nurse_station=data["Nurse Station"].split('-')[2].lstrip()
        except Exception as e:
            # logger.exception(e)
            floor='-'
            tower='-'
            nurse_station=data["Nurse Station"]
        return cls(
            id = data["Id"],
            bed_no =data["Bed No"],
            bed_class =data["Bed Class"],
            bed_status =data["Bed Status"],
            floor =floor,
            tower=tower,
            nurse_station=nurse_station,
            uhid=data["UHID"],
            admission_number=data["IP Number"],
            patient_name=data["Patient Name"],
            speciality = data["Speciality"],
            primary_consultant=data["Primary Consultant"],
            secondary_consultant=data["Secondary Consultant"],
            contact_number=data["Contact number"],
            req_bed_class = data["Req BedType"],
            billable_bed_class = data["Billable BedType"]
        )

@strawberry.type
class BedAllotment:
    id: int
    ward_name :Optional[str] = None
    admission_type :Optional[str] = None
    bed_status :Optional[str] = None
    bed_no :Optional[str] = None
    bed_class :Optional[str] = None
    floor :Optional[str] = None
    tower:Optional[str] = None

    @classmethod
    def from_instance(cls, id:int, ward_name: str, admission_type:str, bed_status:str, bed_no:str, bed_class:str, floor:str, tower:str):
        return cls(
            id=id,
            ward_name = ward_name,
            admission_type = admission_type,
            bed_status = bed_status,
            bed_no = bed_no,
            bed_class = bed_class,
            floor = floor,
            tower = tower            
        )

@strawberry.type 
class BedDetails:
    uhid: str
    bed_class: str
    patient_name: str
    contact_no: str
    doctor_name: str
    specialization: str
    specialized_doctors: Optional[List[str]]
    @classmethod
    def from_instance(cls, uhid: str,bed_class: str, patient_name: str, contact_no: str, doctor_name: str, specialization: str,specialized_doctors: List[str]):
        return cls(uhid=uhid, bed_class=bed_class, patient_name=patient_name,
                   contact_no = contact_no,doctor_name = doctor_name, specialization = specialization,specialized_doctors=specialized_doctors)
    
@strawberry.type
class BedNo:
    bed_no: str
    bed_status: str

    @classmethod
    def from_instance(cls, bed_no:str, bed_status: str):
        return cls(
            bed_no = bed_no, 
            bed_status = "Discharge Initiated" if bed_status == "Occupied" else bed_status
            )

@strawberry.type
class BedClass:
    requested_bed_class: str
    alloted_bed_class: str
    billing_bed_class: str

    instance = strawberry.Private[BedRequestModel]
    @classmethod
    def from_instance(cls, instance:BedRequestModel):
        return cls(
            request_bed_class = instance.requested_bed_class,
            alloted_bed_class = instance.alloted_bed_class,
            billing_bed_class = instance.billing_bed_class
        )
    
@strawberry.type
class MismatchData:
    id: int
    requested_bed_class: str
    alloted_bed_class: str
    billing_bed_class: str
    mismatch_duration : Optional[str] = None
    uhid : str
    patient_name :str
    contact_no : str
    remarks:Optional[List[Remarks]] = None
    reason: Optional[str] = None
    status: Optional[str] = None
    admission_date: Optional[str] = None
    vacated_date: Optional[str] = None

    instance = strawberry.Private[BedRequestModel]
    @classmethod
    def from_instance(cls, instance:BedRequestModel):
        total_time= None
        if instance.mismatch_duration is not None:
            days, remaining_minutes = divmod(instance.mismatch_duration, 24 * 60)
            hours, minutes = divmod(remaining_minutes, 60)
            days = f"{days} days," if days > 1 else  f"{days} day," if days > 0 else ""
            hours = f"{hours} hours," if hours > 1 else f"{hours} hour," if hours > 0 else ""
            minutes = f"{minutes} minutes" if minutes > 0 else ""
            total_time = f"{days}{hours}{minutes}"
        return cls(
            id = instance.id,
            requested_bed_class = instance.requested_bed_class,
            alloted_bed_class = instance.alloted_bed_class,
            billing_bed_class = instance.billing_bed_class,
            mismatch_duration = total_time,
            uhid = instance.uhid,
            patient_name = instance.patient_name,
            contact_no = instance.user_contact_no,
            remarks =[Remarks.from_instance(remarks)for remarks in instance.bed_remarks] if len(instance.bed_remarks)> 0 else [],
            reason = instance.reason,
            status= instance.status.name,
            admission_date = format_datetime(instance.completed_at),
            vacated_date = format_datetime(instance.discharged_time)
        )

@strawberry.type
class AvgTimePerDay:
    floor:Optional[str]=None
    avg_time: Optional[str]=None
    avg_hrs:Optional[str]=None
    date: Optional[str]=None
    no_of_patients: Optional[int] = None

    @classmethod
    def from_instance(cls,avg_time:str,date:str,floor:str,count: int):
        duration_timedelta = timedelta(seconds=float(avg_time))
        days = duration_timedelta.days
        hours, remainder = divmod(duration_timedelta.seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        total_time=f"{days}:{hours}:{minutes}" if days > 0 else f"{hours}:{minutes}"
        return cls(avg_time=total_time,date=date,floor=floor,avg_hrs=int(avg_time/3600),no_of_patients=count)
@strawberry.type
class AvgTimePerMonth:
    floor:Optional[str]=None
    avg_time: Optional[str]=None
    avg_hrs:Optional[str]=None
    month: Optional[str]=None
    no_of_patients: Optional[int] = None

    @classmethod
    def from_instance(cls,avg_time:str,month:str,floor:str,count: int):
        duration_timedelta = timedelta(seconds=float(avg_time))
        days = duration_timedelta.days
        hours, remainder = divmod(duration_timedelta.seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        total_time=f"{days}:{hours}:{minutes}" if days > 0 else f"{hours}:{minutes}"
        return cls(avg_time=total_time,month=month,floor=floor,avg_hrs=int(avg_time/3600),no_of_patients=count)

@strawberry.type
class AvgTimeInBed:
    day_wise:Optional[List[AvgTimePerDay]] = None
    month_wise:Optional[List[AvgTimePerMonth]] = None

    @classmethod
    def from_instance(cls,day_wise,month_wise):
        return cls(
            day_wise =[AvgTimePerDay.from_instance(request.avg_time,request.date,request.floor,request.count) for request in day_wise],
            month_wise =[AvgTimePerMonth.from_instance(request.avg_time,calendar.month_name[int(request.month)],request.floor,request.count) for request in month_wise]

        )

@strawberry.type
class ErBedDetails:
    id: int
    uhid:Optional[str]=None
    patient_name:Optional[str]=None
    bed_no:Optional[str]=None
    admitted_time:Optional[str]=None
    discharged_time:Optional[str]=None
    duration: str

    @classmethod
    def from_instance(cls,uhid:str,patient_name:str,bed_no:str,admitted_time:str,discharged_time:str,id:int):
        duration=str(discharged_time-admitted_time)
        split_index = duration.find(':')+3
        split_duration = duration[:split_index].replace(':',' hr ')+' m'
        return cls(uhid=uhid,patient_name=patient_name,bed_no=bed_no,admitted_time=admitted_time,discharged_time=discharged_time,id=id,duration= split_duration)


@strawberry.type
class ModuleDataList:
    name: str
    url: str

@strawberry.type
class Query:
    
    @strawberry.field
    def get_bed_requests(self, info,type:Optional[str]=None, status:Optional[str]=None) -> QueryResponse[List[BedRequest]]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            bed_requests = get_bed_requests(db,status,type, staff_user)
            return QueryResponse.from_status_flag(True, "list fetched successfully",[BedRequest.from_instance(bed_request) for bed_request in bed_requests])
        except MutationError as ex:
                return QueryResponse.from_status_flag(False, ex.message, None)

    @strawberry.field
    def get_patient_details(self, info, uhid: Optional[str] = None)-> QueryResponse[PatientInfo]:
        try:
            details = get_patient_details(uhid)
            logger.info(details)
            return QueryResponse.from_status_flag(True, "list fetched successfully",PatientInfo.from_instance("",details["patientName"],details["patientID"], details["gender"], details["age"]))
        except MutationError as ex:
                return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_bed_status(self, info)-> QueryResponse[List[BedStatus]]:
        try:
            his_db = info.context["db_aig_his"]
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            details = get_bed_status(his_db,db,staff_user)
            return QueryResponse.from_status_flag(True, "list fetched successfully",[BedStatus.from_instance(data)for data in details] if details is not None else None)
        except MutationError as ex:
                return QueryResponse.from_status_flag(False, ex.message, None)
            
    @strawberry.field
    def get_departments(self,info)-> QueryResponse[List[str]]:
        try:
            his_db = info.context["db_aig_his"]
            data = get_departments(his_db)
            return QueryResponse.from_status_flag(True, "List Fetched Successfully", data)
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message,None)
    
    @strawberry.field
    def get_doctor_by_department(self,info, dep_name: str)->QueryResponse[List[str]]:
        try:
            his_db = info.context["db_aig_his"]
            data = get_doctor_by_department(his_db,dep_name)
            return QueryResponse.from_status_flag(True, "List Fetched Successfully", data)
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message,None)
        
    @strawberry.field
    def get_case_type(self, info) -> QueryResponse[List[str]]:
        try:
            db = info.context["db"]
            data = get_case_type(db)
            return QueryResponse.from_status_flag(True, "List fetched successfully", data)
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.field
    def get_patient_category(self, info) -> QueryResponse[List[str]]:
        try:
            db = info.context["db"]
            data = get_patient_category(db)
            return QueryResponse.from_status_flag(True, "List fetched successfully", data)
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_bed_classes(self, info, uhid: Optional[str] = None) -> QueryResponse[List[str]]:
        try:
            his_db = info.context["db_aig_his"]
            data = get_bed_classes(his_db,uhid)
            return QueryResponse.from_status_flag(True, "List fetched successfully", data)
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_bed_numbers(self, info, bed_class:Optional[str]=None)-> QueryResponse[List[BedNo]]:
        try:
            his_db = info.context["db_aig_his"]
            db= info.context["db"]
            result = get_bed_numbers(db,his_db,bed_class)
            return QueryResponse.from_status_flag(True, "List fetched successfully", [BedNo.from_instance(obj[0],obj[1]) for obj in result])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None) 
        
    @strawberry.field
    def get_request_status(self, info)-> QueryResponse[List[str]]:
        try:
            db = info.context["db"]
            result = get_request_status(db)
            return QueryResponse.from_status_flag(True, "List fetched successfully", result if result is not None else [])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_user_discharges(self, info) -> QueryResponse[List[UserDischargeStatus]]:
        try:
            his_db = info.context["db_aig_his"]
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            user_discharges = get_user_discharges(his_db,db,staff_user)
            return QueryResponse.from_status_flag(True, "list fetched successfully",[UserDischargeStatus.from_instance(user) for user in user_discharges])
        except MutationError as ex:
                return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_bed_details(self, info, admission_no: str) -> QueryResponse[BedDetails]:
        try:
            his_db = info.context["db_aig_his"]
            obj = get_bed_details(his_db, admission_no)
            doctors= get_doctor_by_department(his_db,obj[5])
            return QueryResponse.from_status_flag(True, "list fetched successfully",BedDetails.from_instance(obj[0],obj[1],obj[2],obj[3],obj[4],obj[5],doctors))
        except MutationError as ex:
                return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_priority(self, info) -> QueryResponse[List[str]]:
        try:
            db = info.context["db"]
            data = get_priority(db)
            return QueryResponse.from_status_flag(True, "List fetched successfully", data)
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.field 
    def get_modules(self,info) -> QueryResponse[List[ModuleDataList]]:
        try:
            db = info.context["db"]
            data = get_modules(db)
            return QueryResponse.from_status_flag(True, "List fetched successfully", [ModuleDataList(name = d.get("name"), url = d.get("url")) for d in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.field 
    def get_mismatch_bed_classes(self,info) -> QueryResponse[List[MismatchData]]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            data = get_mismatch_bed_classes(db,staff_user)
            return QueryResponse.from_status_flag(True, "List fetched successfully", [MismatchData.from_instance(obj) for obj in data] if len(data)>0 else [])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_bed_requests_in_date_range(self, info, from_date: str, to_date: str) -> QueryResponse[List[BedRequest]]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            bed_requests = get_bed_requests_in_date_range(db,from_date,to_date,staff_user)
            return QueryResponse.from_status_flag(True, "list fetched successfully",[BedRequest.from_instance(bed_request) for bed_request in bed_requests])
        except MutationError as ex:
                return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field 
    def get_common_remarks(self,info, type:Optional[str]= None) -> QueryResponse[List[str]]:
        try:
            db = info.context["db"]
            data = get_common_remarks(db,type)
            return QueryResponse.from_status_flag(True, "List fetched successfully", data)
        except MutationError as ex:
                return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field 
    def get_nurse_stations(self,info) -> QueryResponse[List[str]]:
        try:
            his_db = info.context["db_aig_his"]
            data = get_nurse_stations(his_db)
            return QueryResponse.from_status_flag(True, "List fetched successfully", data)
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_er_beds(self, info) -> QueryResponse[List[ERBed]]:
        try:
            db = info.context["db"]
            his_db = info.context["db_aig_his"]
            data = get_er_beds(db,his_db)
            return QueryResponse.from_status_flag(True, "list fetched successfully",[ERBed.from_instance(obj.get("bed_no"),obj.get("bed_status"),obj.get("user_name"),obj.get("umr_no"),obj.get("admitted_time"),obj.get("ip_no"),obj.get('transfer_request')) for obj in data])
        except MutationError as ex:
                return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_avg_time_of_er_beds(self,info) -> QueryResponse[AvgTimeInBed]:
        try:
            db= info.context["db"]
            his_db=info.context["db_aig_his"]
            data = get_avg_time_of_er_beds(db,his_db)
            return QueryResponse.from_status_flag(True,"Data fetched Successfully",AvgTimeInBed.from_instance(data[0],data[1]))
        except MutationError as ex:
                return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_er_bed_details(self,info,date:str,floor:str) -> QueryResponse[List[ErBedDetails]]:
        try:
            db= info.context["db"]
            data = get_er_bed_details(db,date,floor)
            return QueryResponse.from_status_flag(True,"Data fetched Successfully",[ErBedDetails.from_instance(obj.uhid,obj.patient_name,obj.bed_no,obj.completed_at,obj.discharged_time,obj.id) for obj in data])
        except MutationError as ex:
                return QueryResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.field
    def download_bed360_reports(self,info,start_date: str, end_date: str) -> QueryResponse[str]:
        try:
            db= info.context["db"]
            data = download_bed360_reports(db,start_date,end_date)
            return QueryResponse.from_status_flag(True,"Data fetched Successfully",data)
        except MutationError as ex:
                return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_hash_tags(self,info) -> QueryResponse[List[HashTag]]:
        try:
            db= info.context["db"]
            data = get_hash_tags(db)
            return QueryResponse.from_status_flag(True,"Data fetched Successfully",[HashTag.from_instance(obj) for obj in data])
        except MutationError as ex:
                return QueryResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.field
    def get_supporting_care_services(self, info) -> QueryResponse[List[str]]:
        try:
            db = info.context["db"]
            data = get_supporting_care_services(db)
            return QueryResponse.from_status_flag(True,"Data fetched Successfully",[obj[0] for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
        
@strawberry.type
class Mutation:
    
    @strawberry.mutation
    def initiate_bed_request(self, info, request_type: str, data: BedAllotRequest) -> MutationResponse[List[BedRequest]]:
        try:
            db = info.context["db"]
            his_db = info.context["db_aig_his"]
            staff_user = info.context["staff_user"]
            bed_requests,msg = initiate_bed_request(db, staff_user, request_type, data,his_db)
            return MutationResponse.from_status_flag(True, msg, [BedRequest.from_instance(bed_request) for bed_request in bed_requests])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.mutation
    def assign_bed(self, info, request_id: int, data: AssignBed) -> MutationResponse[List[BedRequest]]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            his_db = info.context["db_aig_his"]
            bed_requests = assign_bed(his_db, db, staff_user, request_id, data)
            return MutationResponse.from_status_flag(True, "Bed Alloted successfully", [BedRequest.from_instance(bed_request) for bed_request in bed_requests])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.mutation
    def download_excel() -> MutationResponse[str]:
        try:
            file_url = f"{os.environ['SERVER']}:{os.environ['PORT']}/bed/download-excel"
            return MutationResponse.from_status_flag(True, "url sent successfully", file_url)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.mutation
    def withdraw_allocation(self, info, request_id: int, remarks: str,hash_ids:Optional[List[int]]=None) -> MutationResponse[List[BedRequest]]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            bed_requests = withdraw_allocation(db, request_id, remarks, staff_user,hash_ids)
            return MutationResponse.from_status_flag(True, "Bed withdrawal was successful.", [BedRequest.from_instance(bed_request) for bed_request in bed_requests])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.mutation
    def cancel_request(self, info, request_id: int, remarks: str, cancellation_remarks: str, status: Optional[str]=None,hash_ids:Optional[List[int]]=None) -> MutationResponse[List[BedRequest]]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            bed_requests = cancel_request(db, request_id, remarks, staff_user,cancellation_remarks,status,hash_ids)
            return MutationResponse.from_status_flag(True, "Bed request cancelled successfully", [BedRequest.from_instance(bed_request) for bed_request in bed_requests])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.mutation
    def change_bed_request_priority(self, info, request_id: int, remarks: str, priority: str) -> MutationResponse[List[BedRequest]]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            bed_requests = change_bed_request_priority(db, request_id, remarks,priority, staff_user)
            return MutationResponse.from_status_flag(True, "Priority Updated successfully", [BedRequest.from_instance(bed_request) for bed_request in bed_requests])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.mutation
    def update_bed_request_status(self, info, request_id: int, remarks: str, status: str,hash_ids:Optional[List[int]]=None) -> MutationResponse[List[BedRequest]]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            bed_requests = update_bed_request_status(db, request_id, remarks, staff_user, status,hash_ids)
            return MutationResponse.from_status_flag(True, "Bed request updated successfully", [BedRequest.from_instance(bed_request) for bed_request in bed_requests])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.mutation
    def update_admission_date(self, info, request_id: int, remarks: str, admission_date: str) -> MutationResponse[List[BedRequest]]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            bed_requests = update_admission_date(db, request_id, remarks, staff_user,admission_date)
            return MutationResponse.from_status_flag(True, "Admission Date Updated successfully", [BedRequest.from_instance(bed_request) for bed_request in bed_requests])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.mutation
    def update_hash_tags(self, info, request_id: int, remarks: Optional[str]=None,hash_ids:Optional[List[int]]=None,status:Optional[str]=None) -> MutationResponse[List[BedRequest]]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            bed_requests = update_hash_tags(db, request_id, remarks,hash_ids, staff_user,status)
            return MutationResponse.from_status_flag(True, "Admission Date Updated successfully", [BedRequest.from_instance(bed_request) for bed_request in bed_requests])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
        
    
    @strawberry.mutation
    def add_common_remarks(self,info,remark:str,type:RemarksTypeEnum) -> MutationResponse[None]:
        try:
            db = info.context["db"]
            success, msg = add_common_remarks(db,remark,type)
            return MutationResponse.from_status_flag(success, msg, None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)