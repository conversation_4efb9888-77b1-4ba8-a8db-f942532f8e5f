version: "3.8"

services:

  # aig_opd_db:
  #   container_name: aig_opd_db
  #   image: postgres
  #   restart: always
  #   ports:
  #     - 5190:5432
  #   environment:
  #     - POSTGRES_USER=${DB_USER}
  #     - POSTGRES_PASSWORD=${DB_PASSWORD}
  #     - POSTGRES_DB=${DB_NAME}
  #   env_file:
  #     - env-uat
  #   volumes:
  #     - ../data/aig_opd_db:/var/lib/postgresql/data
  opd_redis:
    container_name: opd_redis
    image: redis:6.2-alpine
    env_file:
      - .env-dev
    volumes:
      - ../data/opd_app/redis:/var/lib/redis/data
    command: 
      ["redis-server", "--requirepass", "${REDIS_PASSWORD}", "--appendonly", "yes"]

  opd_app:
    container_name: opd_app
    build: .
    privileged: true
    command: bash -c "hypercorn main:app --bind 0.0.0.0:8001 --workers 2"
    # --keyfile=privkey-dev.pem --certfile=fullchain-dev.pem
    volumes:
      - .:/opd_app
      - ../data/visa_letter:/opd_app/data/visa_letter
    ports:
      - 8001:8001
    env_file:
      - .env-dev
    restart: always
    depends_on:
      - opd_redis

  public_opd_app:
    container_name: public_opd_app
    build: .
    command: bash -c "hypercorn public_main:app --keyfile-password=Aigh@123 --bind 0.0.0.0:443 --insecure-bind 0.0.0.0:8145 --workers 2 --keyfile=public_key.pem --certfile=public_cert.pem"
    volumes:
      - .:/opd_app
    ports:
      - 8145:8145
    env_file:
      - .env-dev
    restart: always
    depends_on:
      # - opd_db
      - opd_redis

  opd_celery_worker:
    container_name: opd_celery_worker
    build: 
      context: .
    command: celery -A celery_worker worker --loglevel=info
    restart: always
    volumes:
      - .:/opd_app
    environment:
      - CELERY_BROKER_URL=${CELERY_BROKER_URL}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
    env_file:
      - .env-dev
    depends_on:
      - opd_app
      - opd_redis
  # opd_flower:
  #   container_name: opd_flower
  #   restart: always
  #   command: celery -A celery_worker flower --port=5557 --basic_auth=admin:admin --persistent=True
  #   volumes:
  #     - ../data/opd_app/flower:/data
  #   ports:
  #     - 5558:5557
  #   environment:
  #     - CELERY_BROKER_URL=${CELERY_BROKER_URL}
  #     - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
  #   env_file:
  #     - env-uat
  #   depends_on:
  #     - opd_celery_worker
    
  opd_celery_beat:
    container_name: opd_celery_beat
    restart: always
    build: 
      context: .
    depends_on:
      - opd_celery_worker
    environment:
        - CELERY_BROKER_URL=${CELERY_BROKER_URL}
        - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND}
    env_file:
      - .env-dev
    command: celery -A celery_worker beat -l INFO --scheduler celery_sqlalchemy_scheduler.schedulers:DatabaseScheduler
    