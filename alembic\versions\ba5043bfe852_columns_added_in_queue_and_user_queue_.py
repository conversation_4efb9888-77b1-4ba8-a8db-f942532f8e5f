"""columns added in queue and user queue models

Revision ID: ba5043bfe852
Revises: 10dccc60bed5
Create Date: 2023-10-11 12:49:06.367962

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ba5043bfe852'
down_revision = '10dccc60bed5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('queue', sa.Column('capacity', sa.Integer(), nullable=True))
    op.add_column('user_queue', sa.Column('count', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_queue', 'count')
    op.drop_column('queue', 'capacity')
    # ### end Alembic commands ###
