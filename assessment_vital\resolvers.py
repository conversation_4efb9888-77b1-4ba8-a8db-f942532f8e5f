import logging
import os
from assessment_vital.models import AssessmentCategory as AssessmentCategoryModel, AssessmentQuestion as AssessmentQuestionModel, VitalAccuracyModule as VitalAccuracyModel, UserVitalDetail as UserVitalDetailModel, AllergyMaster as AllergyMasterModel, VitalUser as VitalUserModel, VitalUserStatusEnum
from config.models import Config as ConfigModel
from constants import GET_VISIT_DETAILS, EMR_API_KEY, POST_ASSESSMENT_DETAILS, POST_PRISM_ASSESSMENT_DETAILS, GET_PRISM_VISIT_DETAILS
from exceptions.exceptions import MutationError 
from user.models import StatusEnum, Device as DeviceModel
# from ai4bharat.transliteration import XlitEngine
from sqlalchemy.orm import Session
from typing import Optional
import json
from datetime import datetime
from util.globals import handle_request1,handle_get_request
from sqlalchemy import func



logger = logging.getLogger()

def get_assessment_category(db:Session,device_id:str):
    assessment_categories = (
        db.query(AssessmentCategoryModel)
        .join(ConfigModel, AssessmentCategoryModel.config_id == ConfigModel.id)
        .join(DeviceModel, ConfigModel.hospital_code == DeviceModel.hospital_code)
        .filter(
            DeviceModel.device_code == device_id,
            AssessmentCategoryModel.status == StatusEnum.ACTIVE
        )
        .order_by(AssessmentCategoryModel.priority)
        .all()
    )
    return assessment_categories

def get_vital_type(db,device_detail):
    config_data = db.query(ConfigModel.data).filter(ConfigModel.hospital_code==device_detail.hospital_code).one()
    config_data= json.loads(config_data.data)
    return config_data.get("vital_type")


def post_prism_assessment(db, patient_data, assessment_list, token):
    body = {
        "id": 1,
        "uhid": (patient_data.uhid).upper(),
        "encounterId": patient_data.encounter_id,
        "visitId": patient_data.encounter_id,
        "doctorId": patient_data.doctor_id,
        "doctorName": patient_data.doctor_name,
        "patientName": patient_data.patient_name,
        "patientAge": patient_data.age,
        "patientGender": patient_data.gender,
	    "status": True,
        "patientVitalsSection":{}
    }
    assessment_question_ids = [item.assessment_question_id for item in assessment_list]
    temp = {}
    for item in assessment_list:
        temp.update({item.assessment_question_id : item.value})
    results= db.query(AssessmentQuestionModel.id, AssessmentQuestionModel.code).filter(AssessmentQuestionModel.id.in_(assessment_question_ids)).filter(AssessmentQuestionModel.status==StatusEnum.ACTIVE).all()
    code_to_key = {
        "VB0099": "temp",
        "WEIGHTKG001": "weight",
        "SYSHEIGHT": "height",
        "VB0003": "diastolic",
        "VB0002": "systolic",
        "CI0000000000013": "spo2",
        "VB0005": "rr",
        "VB0001": "pulse",
    }
    for result in results:
        if result.code in code_to_key:
            body["patientVitalsSection"][code_to_key[result.code]] = temp.get(result.id)
    print(body)
    headers = {"Authorization": token}
    res1=handle_request1(os.environ["ONE_AIG_BASE_URL"]+POST_PRISM_ASSESSMENT_DETAILS,headers,body)
    print(res1.json())
    print(res1.status_code)
    if res1.status_code != 200:
        raise MutationError("Assessment Details not posted")


def save_assessment(db,assessment_list,user_id,device_detail,patient_data):
    config_data = db.query(ConfigModel.data).filter(ConfigModel.hospital_code==device_detail.hospital_code).one()
    config_data= json.loads(config_data.data)
    token = "Bearer " + config_data.get("one_aig_token","")
    vital_user = db.query(VitalUserModel).filter_by(uhid=patient_data.uhid).order_by(VitalUserModel.created_at.desc()).first()
    try:
        if config_data.get("vital_type") == "PRISM":
            post_prism_assessment(db,patient_data,assessment_list,token)
        else:
            post_assessment(db,patient_data,assessment_list,device_detail)
        if vital_user:
            vital_user.updated_at = func.now()
            vital_user.status = VitalUserStatusEnum.COMPLETED.value
            db.commit()
    except MutationError as ex:
        if vital_user:
            vital_user.updated_at = func.now()
            vital_user.status = VitalUserStatusEnum.FAILED.value
            vital_user.failure_reason = str(ex.message)
            db.commit()
        raise ex

        
def save_vitals(db,vital_list,user_id):
    list=[]
    for vital in vital_list:
        detail_obj=UserVitalDetailModel(
            user_id=user_id,
            vital_code=vital.vital_code,
            value= vital.value
        )
        list.append(detail_obj)
    db.bulk_save_objects(list)
    db.commit()


def get_vital_accuracy_data(db: Session,device_id: Optional[str] = None):
    try:
        if device_id is not None:
            device_id: DeviceModel =db.query(DeviceModel.id).filter(DeviceModel.device_code==device_id).first()
        db_data=db.query(VitalAccuracyModel).filter(VitalAccuracyModel.device_id==device_id[0]).first()
        data=json.loads(db_data.data)
        return data
    except Exception as e:
        logger.exception(e)
        return []
    
    
def save_assessement_categories(db:Session):
    with open('./assess.json') as f:
        json_array = json.load(f)
    for i,json_data in enumerate(json_array):
        ass_cat= db.query(AssessmentCategoryModel).filter(AssessmentCategoryModel.code==json_data.get("id")).one_or_none()
        conditions_cate={
            "conditionalFieldCode":json_data.get("conditionalFieldCode"),
            "conditionalFieldValue":json_data.get("conditionalFieldValue"),
            "operation":json_data.get("operation")
        }
        ass_cat= AssessmentCategoryModel(
            code=json_data.get("id"),
            name=json_data.get("topic"),
            domain_code=json_data.get("domainCode"),
            multiple= json_data.get("multiple"),
            category_conditions= conditions_cate,
            priority = i,
            description = ""
        )
        db.add(ass_cat)
        db.flush()
        logger.info(json_data.get("id"))
        questions=[]
        for j, question in enumerate(json_data.get("sections")):
            que= db.query(AssessmentQuestionModel).filter(AssessmentQuestionModel.assessment_category_id==ass_cat.id).filter(AssessmentQuestionModel.code==question.get("domainCode")).one_or_none()
            logger.info(question)
            if que is None:
                validations={}
                question_options=[]
                results=[]
                conditions={
                        "conditionalFieldCode":question.get("conditionalFieldCode"),
                        "conditionalFieldValue":question.get("conditionalFieldValue"),
                        "operation": question.get("operation")
                    }
                if question.get("type")!='schematics':
                    hideIn= question.get("hideIn")
                    validations= {
                        "mandatory": question.get("mandatory"),
                        "minValue": question.get("domainControls",{}).get("minValue"),
                        "maxValue": question.get("domainControls",{}).get("maxValue"),
                        "normalHigh": question.get("domainControls",{}).get("normalHigh"),
                        "normalLow": question.get("domainControls",{}).get("normalLow"),
                        "hide": "false" if hideIn is None else ("false" if hideIn.get("value")=='N' else "true")
                    }
                    if question.get("type")=='masterLinked':
                        masterCode = question.get("domainControls",{}).get("masterLinked",{}).get("masterCode","")
                        allergy_master_data = db.query(AllergyMasterModel.short_desc).filter(AllergyMasterModel.domain_code==masterCode).all()
                        question_options = [item[0] for item in allergy_master_data]
                    else:
                        question_options= question.get("domainControls",{}).get("answer") if question.get("domainControls",{}).get("answer") is not None else []
                    results= question.get("results")
                questions.append({
                    "assessment_category_id": ass_cat.id,
                    "code": question.get("domainCode"),
                    "name":  question.get("question")[0]["text"] if question.get("type") == 'caption' else question.get("domainName"),
                    "type" :question.get("type"),
                    "question_options" :question_options ,
                    "question_validations": validations,
                    "results": results,
                    "priority": j,
                    "question_conditions":conditions,
                    "formula_variables":question.get("formulaVariables"),
                    "formula":question.get("formula")}
                )
                # question= AssessmentQuestionModel(
                #     assessment_category_id= ass_cat.id,
                #     code= question.get("domainCode"),
                #     name= question.get(""),
                #     type =  question.get("type"),
                #     question_options = question.get("domainControls").get("answer"),
                #     question_validations = validations,
                #     priority= j,
                #     question_conditions = conditions,
                #     formula_variables= question.get("formulaVariables"),
                #     formula= question.get("formula")
                # )
            else:
                que.conditions={
                        "conditionalFieldCode":question.get("conditionalFieldCode"),
                        "conditionalFieldValue":question.get("conditionalFieldValue"),
                        "operation": question.get("operation")
                    }
        if len(questions)>0:
            db.bulk_insert_mappings(AssessmentQuestionModel, questions) 
        db.commit()       


def save_visit_detail(uhid):
    headers = {
            f'{EMR_API_KEY}': f'{os.environ["EMR_API_KEY_VALUE"]}'
        }
    res1=handle_request1(f'{os.environ["EMR_BASE_URL"]}'+
        GET_VISIT_DETAILS,headers,{
                "patientId": uhid
            })
    if res1.status_code == 200:
        visit_list = (res1.json()["data"]).get("visitDetails", [])
        patient_details = (res1.json()["data"]).get("patientDetails", {})
        print(visit_list)
        return visit_list, patient_details
    else:
        return [], {}

def get_prism_visit_details(db, uhid, device_detail):
    config_data = db.query(ConfigModel.data).filter(ConfigModel.hospital_code==device_detail.hospital_code).one()
    config_data= json.loads(config_data.data)
    token = "Bearer " + config_data.get("one_aig_token","")
    headers = {"Authorization": token}
    res1=handle_get_request(
        os.environ["ONE_AIG_BASE_URL"]+GET_PRISM_VISIT_DETAILS,
        {
            "InterfaceID": device_detail.hospital_code,
            "uhId": uhid
        },
        headers
    )
    print(res1.json())
    print(res1.status_code)
    return res1.json()


def post_assessment(db, patient_data, assessment_list, device_detail):
    #_,patient_details = save_visit_detail(patient_data.uhid)
    facilityId=device_detail.hospital_code
    dob = datetime.strptime(patient_data.dob, '%d/%m/%Y').strftime('%Y-%m-%d')
    body = {
        "patientId": (patient_data.uhid).upper(),
        "encounterId": patient_data.encounter_id,
	    "patientClass": "OP",
	    "facilityId": facilityId,
	    "source": "VITALKIOSK",
        "userId": "AchalaKiosk_User",
        "SYS_PAT_AGE": patient_data.age,
        "DONCVF001": dob
    }
    assessment_question_ids = [item.assessment_question_id for item in assessment_list]
    temp = {}
    for item in assessment_list:
        temp.update({item.assessment_question_id : item.value})
    results= db.query(AssessmentQuestionModel.id, AssessmentQuestionModel.code).filter(AssessmentQuestionModel.id.in_(assessment_question_ids)).filter(AssessmentQuestionModel.status==StatusEnum.ACTIVE).all()
    for result in results:
        if result.code and result.code != "DOBCVF00001":
            body[result.code] = temp.get(result.id)
        if result.code == "AIGPAINLO90_ADD" and temp.get(result.id):
            body["AIGPAINLO90"] = temp.get(result.id)
    body.pop('AIGPAINLO90_ADD', None)
    print(body)
    headers = {
            f'{EMR_API_KEY}': f'{os.environ["EMR_API_KEY_VALUE"]}'
        }
    print(os.environ["EMR_BASE_URL"])
    res1=handle_request1(f'{os.environ["EMR_BASE_URL"]}'+
        POST_ASSESSMENT_DETAILS,headers,body)
    if res1.status_code != 200:
        raise MutationError("Assessment Details not posted")
    
    
def vital_user_login(db: Session, patient_name: str, uhid: str, encounter_id: str, device_code: str):
    vital_user = VitalUserModel(
        patient_name=patient_name,
        uhid=uhid,
        created_at=func.now(),
        encounter_id=encounter_id,   
        device_code= device_code,
        status= VitalUserStatusEnum.IN_PROGRESS.value
    )
    db.add(vital_user)
    db.commit()
    
