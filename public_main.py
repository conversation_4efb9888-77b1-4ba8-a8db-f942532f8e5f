
import datetime
from fastapi import FastAP<PERSON>
from logging.config import dictConfig
import logging
logger = logging.getLogger()
import yaml
from fastapi.middleware.cors import CORSMiddleware
from constants import origins, methods, headers

from public.vil.restapi import router as visaRouter



with open('logging_config.yaml', 'rt') as f:
    config = yaml.safe_load(f.read())
    config.get("handlers")["file_handler"]["filename"] = '/tmp/{:%Y-%m-%d}.log'.format(datetime.datetime.now())
    dictConfig(config)


app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=methods,
    allow_headers=headers,
)

# app.include_router(graphql_app, prefix="/graphql")
# app.include_router(SmsRouter)
# app.include_router(QueueRouter)
# app.include_router(StaffUserRouter)
# app.include_router(LabReportRouter)
# app.include_router(AuthRouter)
# app.include_router(BmsRouster)
app.include_router(visaRouter)