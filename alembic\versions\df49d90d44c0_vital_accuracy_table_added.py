"""Vital Accuracy Table added

Revision ID: df49d90d44c0
Revises: 0d415e93581a
Create Date: 2024-07-10 06:24:47.328875

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'df49d90d44c0'
down_revision = '0d415e93581a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('vital_accuracy',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.Integer(), nullable=True),
    sa.Column('data', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['device_id'], ['device.id'], name='rel_device_module_device_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('vital_accuracy')
    # ### end Alembic commands ###
