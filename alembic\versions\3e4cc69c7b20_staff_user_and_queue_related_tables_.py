""" staff user and queue related tables added

Revision ID: 3e4cc69c7b20
Revises: 07c5a5d1fd3e
Create Date: 2023-10-10 07:00:01.978563

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '3e4cc69c7b20'
down_revision = '07c5a5d1fd3e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('cluster',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('tower', sa.String(), nullable=True),
    sa.Column('floor', sa.String(), nullable=True),
    sa.Column('cluster', sa.String(), nullable=True),
    sa.Column('status', postgresql.ENUM('ACTIVE', 'INACTIVE', name='statusenum',create_type=False), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('staff_user',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('emp_id', sa.String(), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('phone_number', sa.String(), nullable=True),
    sa.Column('user_type_id', sa.Integer(), nullable=True),
    sa.Column('status', postgresql.ENUM('ACTIVE', 'INACTIVE', name='statusenum',create_type=False), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['staff_user.id'], name='staff_user_created_by_fk'),
    sa.ForeignKeyConstraint(['updated_by'], ['staff_user.id'], name='staff_user_updated_by_fk'),
    sa.ForeignKeyConstraint(['user_type_id'], ['user_type.id'], name='staff_user_user_type_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('queue',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('queue_name', sa.String(), nullable=True),
    sa.Column('queue_code', sa.String(), nullable=True),
    sa.Column('avg_procedure_time', sa.Float(), nullable=True),
    sa.Column('cluster_id', sa.Integer(), nullable=True),
    sa.Column('upcoming_patients', sa.Integer(), nullable=True),
    sa.Column('status', postgresql.ENUM('ACTIVE', 'INACTIVE', name='statusenum',create_type=False), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['cluster_id'], ['cluster.id'], name='queue_cluster_id_fk'),
    sa.ForeignKeyConstraint(['created_by'], ['staff_user.id'], name='queue_created_by_fk'),
    sa.ForeignKeyConstraint(['updated_by'], ['staff_user.id'], name='queue_updated_by_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('queue_audit_logs',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('data', sa.Text(), nullable=True),
    sa.Column('action', sa.String(), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['staff_user.id'], name='queue_created_by_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('staff_user_audit_logs',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('data', sa.Text(), nullable=True),
    sa.Column('action', sa.String(), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['staff_user.id'], name='staff_user_audit_logs_created_by_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('rel_staff_user_queue',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('staff_user_id', sa.Integer(), nullable=True),
    sa.Column('queue_id', sa.Integer(), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['staff_user.id'], name='rel_staff_user_queue_created_by_fk'),
    sa.ForeignKeyConstraint(['queue_id'], ['queue.id'], name='rel_staff_user_queue_queue_id_fk'),
    sa.ForeignKeyConstraint(['staff_user_id'], ['staff_user.id'], name='rel_staff_user_queue_staff_user_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('rel_staff_user_queue')
    op.drop_table('staff_user_audit_logs')
    op.drop_table('queue_audit_logs')
    op.drop_table('queue')
    op.drop_table('staff_user')
    op.drop_table('cluster')
    # ### end Alembic commands ###
