"""columns added

Revision ID: 511131ba03cf
Revises: 3e4cc69c7b20
Create Date: 2023-10-10 11:37:51.421369

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '511131ba03cf'
down_revision = '3e4cc69c7b20'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(None, 'queue', ['queue_name'])
    op.add_column('queue_audit_logs', sa.Column('queue_code', sa.String(), nullable=True))
    op.create_unique_constraint(None, 'staff_user', ['emp_id'])
    op.add_column('staff_user_audit_logs', sa.Column('emp_id', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('staff_user_audit_logs', 'emp_id')
    op.drop_constraint(None, 'staff_user', type_='unique')
    op.drop_column('queue_audit_logs', 'queue_code')
    op.drop_constraint(None, 'queue', type_='unique')
    # ### end Alembic commands ###
