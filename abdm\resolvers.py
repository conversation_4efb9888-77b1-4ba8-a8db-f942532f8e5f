import base64
import os
import uuid
import requests
from constants import AADHAR_RESEND_OTP, ABDM_RSA_PUBLIC_KEY, AUTH_ACCOUNT_HEALTH_CARD, AUTH_ACCOUNT_PROFILE, AUTH_ACCOUNT_QR_CODE, AUTH_CONFIRM_AADHAR_OTP, AUTH_INIT, <PERSON><PERSON><PERSON>NT_ID, CLIENT_SECRET, CREATE_HEALTHID_BY_AADHAAR, GET_PATIENT_DETAILS_BY_PHONE_NO, HEALTH_ID_BASE_URL, REGIS_AADHAAR_GENERATE_OTP, REGIS_AADHAAR_VERIFY_OTP, REGIS_AADHAR_LINK_MOBILE, RESEND_MOBILE_OTP, SESSION_API, VERFIY_AADHAR_LINK_MOBILE, NAME_SPLITTER, SUREPASS_GENERATE_OTP, SUREPASS_SUBMIT_OTP
from user.resolvers import get_access_token#get_new_access_token
from util.globals import calculate_age, handle_request, ThreadWithReturnValue, handle_request1
from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_v1_5
from typing import Optional
from user.models import User as UserModel
from abdm.models import AadharOtp as AadharOtpModel
from exceptions.exceptions import MutationError
from sqlalchemy.orm import Session
import logging,datetime,pytz
from collections import Counter
from sqlalchemy import desc
logger = logging.getLogger()

def generate_otp_aadhar_health_id(db: Session, ref_id: str, phone_number:str):
    ref_id_1 = ref_id.replace("-", "")
    # user=db.query(UserModel).filter(UserModel.phone_number==phone_number).one_or_none()
    # if user is not None:
    if ref_id.__len__() == 12:
        message = ref_id.encode('utf-8')
        res = get_token()
        token = "Bearer "+res.json()["accessToken"]
        headers = {
            "Authorization": token
        }
        res1=handle_request1(HEALTH_ID_BASE_URL +
        REGIS_AADHAAR_GENERATE_OTP,headers,{
                "aadhaar": encrypt_with_public_key(message),
                # "consent":True
            })
        if res1.status_code == 200:
            return (res1.json()["txnId"], res1.json()["mobileNumber"],"REGISTER")
        else:
            try:
                details= res1.json().get("details")
                message = res1.json()["details"][0]["message"] if (details is not None and len(details)>0) else res1.json()["message"]
            except:
                logger.exception("Error occured while calling ABDM server")
                raise MutationError("Error occured while calling ABDM server")
            logger.exception(message)
            raise MutationError(message)
    elif ref_id_1.__len__() == 14:
        res = get_token()
        health_id=ref_id_1[:2]+"-"+ref_id_1[2:6]+"-"+ref_id_1[6:10]+"-"+ref_id_1[10:]
        token = "Bearer "+res.json()["accessToken"]
        headers = {
            "Authorization": token
        }
        res1=handle_request1(HEALTH_ID_BASE_URL+
        AUTH_INIT,headers,{
                "healthid": health_id,
                "authMethod": "AADHAAR_OTP"
                # "consent":True
            })
        if res1.status_code == 200:
            logger.info("generated otp")
            return (res1.json()["txnId"], "","REGISTER")
        else:
            try:
                details= res1.json().get("details")
                message = res1.json()["details"][0]["message"] if (details is not None and len(details)>0) else res1.json()["message"]
            except:
                logger.exception("Error occured while calling ABDM server")
                raise MutationError("Error occured while calling ABDM server")
            logger.exception(message)
            raise MutationError(message)
    # else:
    #     logger.info("NOT_REGISTERED")
    #     return (None,None,"NOT_REGISTERED")
    
def get_token():
    res=handle_request1(SESSION_API,None,{
            "clientId": CLIENT_ID,
            "clientSecret": CLIENT_SECRET,
            "grantType": "client_credentials"
        })
    return res

def encrypt_with_public_key(a_message):
    x509pem = ABDM_RSA_PUBLIC_KEY.encode('utf-8')
    public_key = RSA.import_key(base64.b64decode(x509pem))
    encryptor = PKCS1_v1_5.new(public_key)
    encrypted_msg = encryptor.encrypt(a_message)
    encoded_encrypted_msg = base64.b64encode(encrypted_msg)
    return encoded_encrypted_msg.decode('utf-8')

def verify_otp_aadhar_health_id(db: Session,his_db:Session, txn_id, otp, ref_id, phone_number, flow):
    try:
        # user=db.query(UserModel).filter(UserModel.phone_number==phone_number).one_or_none()
        # if user is not None:
            ref_id_1 = ref_id.replace("-", "")
            if ref_id.__len__() == 12:
                res = get_token()
                token = "Bearer "+res.json()["accessToken"]
                headers = {
                    "Authorization": token
                }
                res1=handle_request1(HEALTH_ID_BASE_URL +
                    REGIS_AADHAAR_VERIFY_OTP,headers,{
                        "otp": encrypt_with_public_key(otp.encode('utf-8')),
                        "txnId": txn_id
                        # "consent":True
                    })
                qr_code=None
                if res1.status_code==200:
                    obj=res1.json()
                    logger.info(obj)
                    if obj["healthIdNumber"] is None:
                        if flow =='AADHAR_OTP_ONLY_VERIFY':
                            date_of_birth=obj["birthdate"]
                            age=calculate_age(date_of_birth,1)
                            address=''
                            if obj["house"] is not None:
                                address=address+""+ obj["house"]+" "
                            if obj["street"] is not None:
                                address= address + obj["street"]+" "
                            # if obj["locality"] is not None:
                            #     address= address + obj["locality"]+" "
                            # if obj["villageTownCity"] is not None:
                            #     address= address + obj["villageTownCity"]+" "
                            can_proceed,his_city=get_his_city(his_db,obj['villageTownCity'],obj['pincode'],obj['locality'])
                            first_name,last_name=split_name(obj['name'])
                            res_aig = get_access_token()
                            token = "Bearer " + res_aig["access_token"]
                            headers = {"Authorization": token}
                            res_aig1 = handle_request(
                                os.environ["AIG_BASE_URL"] + GET_PATIENT_DETAILS_BY_PHONE_NO,
                                headers,{"phoneNo": phone_number})
                            # if res_aig1.status_code==401 or res_aig1.status_code==403:
                            #     headers = get_new_access_token()
                            #     res_aig1 = handle_request(
                            #         os.environ["AIG_BASE_URL"] + GET_PATIENT_DETAILS_BY_PHONE_NO,
                            #         headers,{"phoneNo": phone_number})
                            users=res_aig1.json()['response']
                            gender = "Female" if obj["gender"] == "F" else "Male" if obj["gender"] == "M" else "Others"             
                            return (first_name.upper(),address.upper(),age,None,gender,ref_id,obj["healthIdNumber"],obj["txnId"],qr_code,date_of_birth,None,None,"MOBILE_LINKED", obj["state"].upper(), obj["villageTownCity"].upper(), obj["pincode"].upper(),users,can_proceed,his_city.upper(),last_name.upper(),obj['locality'].upper())
                        else:
                           obj1 = create_health_id_from_aadhar(db,obj["txnId"],phone_number,ref_id)
                           obj1[13] = obj["state"]
                           obj1[14] = obj["villageTownCity"]
                           obj1[15] = obj["pincode"]
                           obj1[16] = []
                           obj1[17]=False
                           obj1[18]=None
                           obj1[19]=None
                           return obj1
                    else:
                        if(obj["jwtResponse"] is not None):
                            qr_code=get_qr_code(token,obj["jwtResponse"]["token"])
                            health_card = get_health_card(token,obj["jwtResponse"]["token"])
                        date_of_birth=obj["birthdate"]
                        age=calculate_age(date_of_birth,1)
                        address=''
                        if obj["house"] is not None:
                            address=address+""+ obj["house"]+" "
                        if obj["street"] is not None:
                            address= address + obj["street"]+" "
                        # if obj["locality"] is not None:
                        #     address= address + obj["locality"]+" "
                        # if obj["villageTownCity"] is not None:
                        #     address= address + obj["villageTownCity"]+" "
                    can_proceed,his_city=get_his_city(his_db,obj['villageTownCity'],obj['pincode'],obj['locality'])
                    first_name,last_name=split_name(obj['name'])
                    res_aig = get_access_token()
                    token = "Bearer " + res_aig["access_token"]
                    headers = {"Authorization": token}
                    res_aig1 = handle_request(
                        os.environ["AIG_BASE_URL"] + GET_PATIENT_DETAILS_BY_PHONE_NO,
                        headers,{"phoneNo": phone_number})
                    # if res_aig1.status_code==401 or res_aig1.status_code==403:
                    #     headers = get_new_access_token()
                    #     res_aig1 = handle_request(
                    #         os.environ["AIG_BASE_URL"] + GET_PATIENT_DETAILS_BY_PHONE_NO,
                    #         headers,{"phoneNo": phone_number})
                        
                    users=res_aig1.json()['response']
                    gender = "Female" if obj["gender"] == "F" else "Male" if obj["gender"] == "M" else "Others"
                    return (first_name.upper(),address.upper(),age,None,gender,ref_id,obj["healthIdNumber"],obj["txnId"],qr_code,date_of_birth,None,health_card,"MOBILE_LINKED", obj["state"].upper(), obj["villageTownCity"].upper(), obj["pincode"],users,can_proceed,his_city.upper(),last_name.upper(),obj['locality'].upper())
                else:
                    try:
                        details= res1.json().get("details")
                        message = res1.json()["details"][0]["message"] if (details is not None and len(details)>0) else res1.json()["message"]
                    except:
                        logger.exception("Error occured while calling ABDM server")
                        raise MutationError("Error occured while calling ABDM server")
                    logger.exception(message)
                    raise MutationError(message)
            elif ref_id_1.__len__() == 14:
                health_id=ref_id_1[:2]+"-"+ref_id_1[2:6]+"-"+ref_id_1[6:10]+"-"+ref_id_1[10:]
                res = get_token()
                token = "Bearer "+res.json()["accessToken"]
                headers = {
                    "Authorization": token
                }
                # res1 = requests.post(
                #     HEALTH_ID_BASE_URL +
                #     AUTH_CONFIRM_AADHAR_OTP,
                #     json={
                #         "otp": encrypt_with_public_key(otp.encode('utf-8')),
                #         "txnId": txn_id
                #         # "consent":True
                #     },
                #     headers=headers
                # )
                res1=handle_request1(HEALTH_ID_BASE_URL +
                    AUTH_CONFIRM_AADHAR_OTP,headers,{
                        "otp": encrypt_with_public_key(otp.encode('utf-8')),
                        "txnId": txn_id,
                        "consent":True
                    })
                if res1.status_code==200:
                    if res1.status_code==200:
                        try:
                            headers = {
                            "Authorization": token,
                            "X-Token":"Bearer "+res1.json()["token"]
                            }
                            res2 = requests.get(
                            HEALTH_ID_BASE_URL+
                            AUTH_ACCOUNT_PROFILE,
                            headers=headers
                            )

                            obj=res2.json()
                            age=calculate_age(obj["dayOfBirth"]+"-"+obj["monthOfBirth"]+"-"+obj["yearOfBirth"],1)
                            gender = "Female" if obj["gender"] == "F" else "Male" if obj["gender"] == "M" else "Others"
                            return (obj["name"].upper(),obj["address"].upper(),age,None,gender,None,obj["healthIdNumber"],None,None,None,None,None,None, None,None,None,[],False,None,None,obj['locality'].upper())
                        except:
                            logger.exception("Error occured while calling ABDM server")
                            raise MutationError("Error occured while calling ABDM server")
                    else:
                        try:
                            details= res1.json().get("details")
                            message = res1.json()["details"][0]["message"] if (details is not None and len(details)>0) else res1.json()["message"]
                        except:
                            logger.exception("Error occured while calling ABDM server")
                            raise MutationError("Error occured while calling ABDM server")
                    logger.exception(message)
                    raise MutationError(message)
                    # res1 = requests.get(
                    #     HEALTH_ID_BASE_URL +
                    #     AUTH_ACCOUNT_QR_CODE,
                    #     headers=headers
                    # )
                else:
                    try:
                        details= res1.json().get("details")
                        message = res1.json()["details"][0]["message"] if (details is not None and len(details)>0) else res1.json()["message"]
                    except:
                        logger.exception("Error occured while calling ABDM server")
                        raise MutationError("Error occured while calling ABDM server")
                    logger.exception(message)
                    raise MutationError(message)
    except MutationError as ex:
        logger.exception(ex.message)
        if (ex.message == ("You have reached the maximum verify attempts. Exit your browser and try again.You have reached the maximum verify attempts. Exit your browser and try again.")):
            ex.message = "You have reached the maximum verify attempts. Exit your browser and try again."
        elif(ex.message == "Invalid OTP value."):
            ex.message = ex.message.replace(".","")
        raise MutationError(ex.message)
    except:
        logger.exception("Error occured while calling ABDM server")
        raise MutationError("Error occured while calling ABDM server")
def create_health_id_from_aadhar(db:Session,txn_id:str,phone_number:str,ref_id:str):
    # user=db.query(UserModel).filter(UserModel.phone_number==phone_number).one_or_none()
    # if(user is None):
    #     logger.exception("Mobile number is not Registered")
    #     raise MutationError("Mobile number is not Registered")
    res = get_token()
    token = "Bearer "+res.json()["accessToken"]
    headers = {
        "Authorization": token
    }
    res1 = requests.post(
   HEALTH_ID_BASE_URL +
   REGIS_AADHAR_LINK_MOBILE,
    json={
        "txnId": txn_id,
        "mobile":phone_number
        # "consent":True
    },
    headers=headers
    )
    if res1.status_code==200:
        # res1 = requests.post(
        # HEALTH_ID_BASE_URL +
        # CREATE_HEALTHID_BY_AADHAAR,
        # json={
        #     "txnId": txn_id,
        #     # "consent":True
        # },
        # headers=headers
        # )
        mobile_linked = res1.json()['mobileLinked']
        if(mobile_linked == False):
            return [None,None,None,None,None,None,None,txn_id,None,None,None,None,"MOBILE_NOT_LINKED",None,None,None]
        res1=handle_request1(HEALTH_ID_BASE_URL +
        CREATE_HEALTHID_BY_AADHAAR,headers,{
            "txnId": txn_id,
            "consent":True
        })
        if res1.status_code==200:
            obj=res1.json()
            # user.health_id=obj["healthIdNumber"]
            db.commit()
            age=calculate_age(obj["dayOfBirth"]+"-"+obj["monthOfBirth"]+"-"+obj["yearOfBirth"],1)
            dateOfBirth= obj["dayOfBirth"]+"-"+obj["monthOfBirth"]+"-"+obj["yearOfBirth"]
            qr_code = None
            try:
                qr_code = get_qr_code(token,res1.json()["token"])
                health_card = get_health_card(token,res1.json()["token"])
            except:
                pass
            gender = "Female" if obj["gender"] == "F" else "Male" if obj["gender"] == "M" else "Others"
            return [obj["name"],None,age,None,gender,ref_id,obj["healthIdNumber"],txn_id,qr_code,dateOfBirth,obj["kycPhoto"],health_card,"MOBILE_LINKED",None,None,None]
        else:
            try:
                details= res1.json().get("details")
                message = res1.json()["details"][0]["message"] if (details is not None and len(details)>0) else res1.json()["message"]
            except:
                logger.exception("Error occured while calling ABDM server")
                raise MutationError("Error occured while calling ABDM server")
            logger.exception(message)
            raise MutationError(message)
    else:
        try:
            details= res1.json().get("details")
            message = res1.json()["details"][0]["message"] if (details is not None and len(details)>0) else res1.json()["message"]
        except:
            logger.exception("Error occured while calling ABDM server")
            raise MutationError("Error occured while calling ABDM server")
        logger.exception(message)
        raise MutationError(message)
def get_health_card(token:str,xtoken:str):
    headers = {
                    "Authorization": token,
                    "X-Token":"Bearer "+xtoken
                }
    res2 = requests.get(
                    HEALTH_ID_BASE_URL +
                    AUTH_ACCOUNT_HEALTH_CARD,
                    headers=headers
                    )
    if res2.content is not None:
            # logger.info(res2.headers["Content-Type"])
            # qr_code=base64.b64encode(res2.content)
            # base64.b64encode(response.content)
        return str(base64.b64encode(res2.content).decode("utf-8"))
        # return "data:" + res2.headers['Content-Type'] + ";" +"base64," + base64.b64encode(res2.content)
    else :
        return None
def get_qr_code(token:str,xtoken:str):
    headers = {
                    "Authorization": token,
                    "X-Token":"Bearer "+xtoken
                }
    res2 = requests.get(
                    HEALTH_ID_BASE_URL +
                    AUTH_ACCOUNT_QR_CODE,
                    headers=headers
                    )
    qr_code=None
    if res2.content is not None:
            # logger.info(res2.headers["Content-Type"])
            # qr_code=base64.b64encode(res2.content)
            # base64.b64encode(response.content)
        return str(base64.b64encode(res2.content).decode("utf-8"))
        # return "data:" + res2.headers['Content-Type'] + ";" +"base64," + base64.b64encode(res2.content)
    else :
        None


def verify_aadhar_link_mobile_otp(db:Session,txn_id:str,otp:str,ref_id:str,phone_number:str):
    try:
        user=db.query(UserModel).filter(UserModel.phone_number==phone_number).one_or_none()
        if user is not None:
            res = get_token()
            token = "Bearer "+res.json()["accessToken"]
            headers = {
            "Authorization": token
            }
            res1 = requests.post(
            HEALTH_ID_BASE_URL +
            VERFIY_AADHAR_LINK_MOBILE,
            json={
                "otp": encrypt_with_public_key(otp.encode('utf-8')),
                "txnId": txn_id
            # "consent":True
            },
            headers=headers
            )
            logger.info(res1.json())
            if res1.status_code==200:
                txn_id = res1.json()["txnId"]
                res2=handle_request1(HEALTH_ID_BASE_URL +CREATE_HEALTHID_BY_AADHAAR,headers,
                {
                "txnId": txn_id,
                "consent":True
                 })
                if res2.status_code==200:
                    obj=res2.json()
                    logger.info(obj)
                    user.health_id=obj["healthIdNumber"]
                    db.commit()
                    age=calculate_age(obj["dayOfBirth"]+"-"+obj["monthOfBirth"]+"-"+obj["yearOfBirth"],1)
                    dateOfBirth= obj["dayOfBirth"]+"-"+obj["monthOfBirth"]+"-"+obj["yearOfBirth"]
                    qr_code = None
                    try:
                        qr_code = get_qr_code(token,res2.json()["token"])
                        health_card = get_health_card(token,res2.json()["token"])
                    except:
                        pass
                    gender = "Female" if obj["gender"] == "F" else "Male" if obj["gender"] == "M" else "Others"
                    return (obj["name"].upper(),None,age,None,gender,ref_id,obj["healthIdNumber"],txn_id,qr_code,dateOfBirth,obj["kycPhoto"],health_card,"MOBILE_LINKED", None, None, None,[],False,None,None,obj['locality'].upper())
                else:
                    try:
                        details= res2.json().get("details")
                        message = res2.json()["details"][0]["message"] if (details is not None and len(details)>0) else res2.json()["message"]
                    except:
                        logger.exception("Error occured while calling ABDM server")
                        raise MutationError("Error occured while calling ABDM server")
                    logger.exception(message)
                    raise MutationError(message)
            else:
                try:
                    details= res1.json().get("details")
                    message = res1.json()["details"][0]["message"] if (details is not None and len(details)>0) else res1.json()["message"]
                except:
                    logger.exception("Error occured while calling ABDM server")
                    raise MutationError("Error occured while calling ABDM server")
                logger.exception(message)
                raise MutationError(message)
        else:
            return (None,None,None,"NOT_REGISTERED",None,ref_id,None,None,None,None,None,None,None,None,None,None,[],False,None,None)
        
    except MutationError as ex:
        logger.exception(ex.message)
        if (ex.message == ("You have reached the maximum verify attempts. Exit your browser and try again.You have reached the maximum verify attempts. Exit your browser and try again.")):
            ex.message = "You have reached the maximum verify attempts. Exit your browser and try again."
        elif(ex.message == "Invalid OTP value."):
            ex.message = ex.message.replace(".","")
        raise MutationError(ex.message)
    except:
        logger.exception("Error occured while calling ABDM server")
        raise MutationError("Error occured while calling ABDM server")
    
def resend_aadhar_otp(db: Session, txn_id : str):
    try:
        res = get_token()
        token = "Bearer "+res.json()["accessToken"]
        headers = {
        "Authorization": token
        }
        res1 = requests.post(
        HEALTH_ID_BASE_URL +
        AADHAR_RESEND_OTP,
        json={
        "txnId": txn_id,
        # "consent":True
        },
        headers=headers
        )
        if res1.status_code==200:
            logger.info("otp sent")
            return (res1.json()["txnId"])
        else:
            try:
                details= res1.json().get("details")
                message = res1.json()["details"][0]["message"] if (details is not None and len(details)>0) else res1.json()["message"]
            except:
                logger.exception("Error occured while calling ABDM server")
                raise MutationError("Error occured while calling ABDM server")
            logger.exception(message)
            raise MutationError(message)
    except MutationError as ex:
        logger.exception(ex.message)
        raise MutationError(ex.message)
    except:
        logger.exception("Error occured while calling ABDM server")
        raise MutationError("Error occured while calling ABDM server")

def resend_mobile_otp(db: Session, txn_id : str):
    try:
        res = get_token()
        token = "Bearer "+res.json()["accessToken"]
        headers = {
        "Authorization": token
        }
        res1 = requests.post(
        HEALTH_ID_BASE_URL +
        RESEND_MOBILE_OTP,
        json={
        "txnId": txn_id,
        # "consent":True
        },
        headers=headers
        )
        if res1.status_code==200:
            if(res1.json()):
                logger.info("otp sent")
                return (txn_id)
        else:
            try:
                details= res1.json().get("details")
                message = res1.json()["details"][0]["message"] if (details is not None and len(details)>0) else res1.json()["message"]
            except:
                logger.exception("Error occured while calling ABDM server")
                raise MutationError("Error occured while calling ABDM server")
            logger.exception(message)
            raise MutationError(message)
    except MutationError as ex:
        logger.exception(ex.message)
        raise MutationError(ex.message)
    except:
        logger.exception("Error occured while calling ABDM server")
        raise MutationError("Error occured while calling ABDM server")

def split_name(full_name:str):
    name=full_name.split(" ")
    if len(name) == 2:
        first_name=name[0]
        last_name=name[1]
    else:
        try:
            headers={
                "client":"X-API-KEY",
                "X-API-KEY":os.environ["X_API_KEY"]
            }
            uuid_name = str(uuid.uuid4())
            result=handle_request1(os.environ["NAME_SPLITTER_BASE_URL"]+NAME_SPLITTER,headers,
                        { "personalNames": [{
                            "id": uuid_name,
                            "countryIso2":"IN",
                            "name": full_name}]
                        })
            name=result.json()["personalNames"][0]["firstLastName"]
            first_name=name["firstName"]
            last_name=name["lastName"]
        except Exception as e:
            logger.info(f"Error occured while splitting name {e}")
            first_name=' '.join(name[:-1])
            last_name=name[-1]
    return first_name.upper(),last_name.upper()

def get_his_city(his_db,city,pincode,locality):
    can_proceed=False
    his_city=""
    try:
        sql_query=f"SELECT City_Name FROM dbo.GetCityMasterforKiosk where upper(City_Name) ='{city.upper()}'"
        res_data = his_db.execute(sql_query).all()
        if len(res_data) !=0:
            can_proceed=True
            his_city=city
        else:
            sql_query=f"SELECT City_Name FROM dbo.GetLocalityMasterforKiosk where PinCode ='{pincode}' and upper(Locality_Name) ='{locality.upper()}'"
            res_data = his_db.execute(sql_query).all()
            if len(res_data) !=0:
                can_proceed=True
                his_city=res_data[0][0]
    except Exception as e:
        logger.info(f"Query execution failed: {e}")
    return (can_proceed,his_city)

def surepass_generate_otp(db: Session, ref_id: str, phone_number:str):
    try:
        if ref_id.__len__() == 12:
            token = "Bearer "+os.environ['SUREPASS_TOKEN']
            headers = {"Authorization": token}
            res1=handle_request1(os.environ['SUREPASS_BASE_URL'] +
            SUREPASS_GENERATE_OTP,headers,{
                            "id_number": ref_id
                })
            if res1.status_code == 200:
                txn_id=str(uuid.uuid4())
                data=AadharOtpModel(txn_id=txn_id,client_id=res1.json()["data"].get('client_id'),aadhar_number=ref_id,phone_number=phone_number)
                db.add(data)
                db.commit()
                return (txn_id, "","REGISTER")
            elif res1.status_code == 429:
                raise MutationError("You have reached the maximum allowable attempts. Please try again after sometime")
            else:
                raise MutationError("Invalid Aadhar Number")
        else:
            raise MutationError("Invalid Aadhar Number")
    except MutationError as ex:
        logger.exception(ex.message)
        raise MutationError(ex.message)
    except Exception as e:
        logger.exception(f"Error occured: {e}")
        raise MutationError("Error occured while calling SUREPASS server")

def surepass_submit_otp(db: Session,his_db:Session, txn_id, otp, ref_id, phone_number, flow):
    try:
        otp_data=db.query(AadharOtpModel.client_id,AadharOtpModel.phone_number).filter(AadharOtpModel.txn_id==txn_id).order_by(desc(AadharOtpModel.created_at)).first()
        if otp_data is None:
            raise MutationError("Invalid request")
        token = "Bearer "+os.environ['SUREPASS_TOKEN']
        headers = {"Authorization": token}
        res1=handle_request1(os.environ['SUREPASS_BASE_URL'] +
        SUREPASS_SUBMIT_OTP,headers,{
                        "client_id": otp_data.client_id,
                        "otp":otp
                    })
        qr_code=None
        if res1.status_code==200:
            db.query(AadharOtpModel).filter(AadharOtpModel.txn_id == txn_id).delete()
            db.commit()
            obj=res1.json()['data']
            if flow =='AADHAR_OTP_ONLY_VERIFY':
                date_of_birth=datetime.datetime.strptime(obj["dob"], "%Y-%m-%d").strftime('%d-%m-%Y')
                age=calculate_age(date_of_birth,1)
                obj1=obj['address']
                address=''
                if obj1["house"] is not None:
                    address=address+""+ obj1["house"]+" "
                if obj1["street"] is not None:
                    address= address + obj1["street"]+" "
                res_aig = get_access_token()
                token = "Bearer " + res_aig["access_token"]
                headers = {"Authorization": token}
                thread1=ThreadWithReturnValue(target=get_his_city,args=(his_db,obj1['vtc'],obj['zip'],obj1['loc']))
                thread2=ThreadWithReturnValue(target=split_name,args=(obj['full_name'],))
                thread3=ThreadWithReturnValue(target=handle_request,args=(os.environ["AIG_BASE_URL"] + GET_PATIENT_DETAILS_BY_PHONE_NO,headers,{"phoneNo": otp_data.phone_number}))
                thread1.start()
                thread2.start()
                thread3.start()
                result1 = thread1.join()
                result2 = thread2.join()
                result3=thread3.join()
                can_proceed,his_city = result1[0],result1[1]
                first_name,last_name = result2[0],result2[1]
                users=result3.json()['response']
                # if res_aig1.status_code==401 or res_aig1.status_code==403:
                #     headers = get_new_access_token()
                #     res_aig1 = handle_request(
                #         os.environ["AIG_BASE_URL"] + GET_PATIENT_DETAILS_BY_PHONE_NO,
                #         headers,{"phoneNo": phone_number})
                locality=obj1["vtc"].upper() if not obj1['loc'] else obj1['loc'].upper()
                gender = "Female" if obj["gender"] == "F" else "Male" if obj["gender"] == "M" else "Others"
                return (first_name.upper(),address.upper(),age,None,gender,obj["aadhaar_number"],None,obj["client_id"],qr_code,date_of_birth,None,None,"MOBILE_LINKED", obj1["state"].upper(), obj1["vtc"].upper(), obj["zip"].upper(),users,can_proceed,his_city.upper(),last_name.upper(),locality)
        elif res1.status_code==404:
            raise MutationError("Invalid request")
        elif res1.status_code==422:
            if res1.json()['message_code'] is not None:
                raise MutationError("Invalid OTP")
            else:
                raise MutationError("OTP has expired")
    except MutationError as ex:
        logger.exception(ex.message)
        raise MutationError(ex.message)
    except Exception as e:
        logger.exception(f"Error occured: {e}")
        raise MutationError("Error occured while calling SUREPASS server")
    
def surepass_resend_otp(db: Session, txn_id:str):
    try:
        otp_obj=db.query(AadharOtpModel.aadhar_number).filter(AadharOtpModel.txn_id==txn_id).order_by(desc(AadharOtpModel.created_at)).first()
        if otp_obj is None:
            raise MutationError("Invalid request")
        token = "Bearer "+os.environ['SUREPASS_TOKEN']
        headers = {"Authorization": token}
        res1=handle_request1(os.environ['SUREPASS_BASE_URL'] +
        SUREPASS_GENERATE_OTP,headers,{
                        "id_number": otp_obj.aadhar_number
            })
        if res1.status_code == 200:
            db.query(AadharOtpModel).filter(AadharOtpModel.txn_id==txn_id).update(
                               {AadharOtpModel.client_id:res1.json()["data"].get('client_id')})
            db.commit()
            return (txn_id)
        elif res1.status_code == 429:
            raise MutationError("You have reached the maximum allowable attempts. Please try again after sometime")
        else:
            raise MutationError("Invalid Aadhar Number")
    except MutationError as ex:
        logger.exception(ex.message)
        raise MutationError(ex.message)
    except Exception as e:
        logger.exception(f"Error occured: {e}")
        raise MutationError("Error occured while calling SUREPASS server")