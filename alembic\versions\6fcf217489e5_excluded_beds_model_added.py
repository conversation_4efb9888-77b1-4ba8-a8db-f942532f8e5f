"""excluded beds model added

Revision ID: 6fcf217489e5
Revises: a4a845125579
Create Date: 2023-12-07 11:39:13.233011

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6fcf217489e5'
down_revision = 'a4a845125579'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('exclude_beds',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('bed_no', sa.String(), nullable=True),
    sa.Column('tower', sa.String(), nullable=True),
    sa.Column('floor', sa.String(), nullable=True),
    sa.Column('bed_class', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('exclude_beds')
    # ### end Alembic commands ###
