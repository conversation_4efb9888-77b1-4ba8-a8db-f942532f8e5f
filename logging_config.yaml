
version: 1
disable_existing_loggers: true
formatters:
  brief:
    format: '[%(levelname)s] %(name)s: %(message)s'
    datefmt: "%I:%M:%S"
  standard:
    format: '[%(asctime)s] [%(levelname)s] %(name)s: [%(filename)s:%(funcName)s:%(lineno)d]: %(message)s'
    datefmt: "%I:%M:%S"
  multi_process:
    class: 'logging.Formatter'
    format: '[%(asctime)s] [%(levelname)s] [%(thread)s] %(name)s: [%(filename)s:%(funcName)s:%(lineno)d]: %(message)s'
  multi_thread:
    class: 'logging.Formatter'
    format: '[%(asctime)s] [%(levelname)s] [%(process)d]: %(name)s: [%(filename)s:%(funcName)s:%(lineno)d]: %(message)s'
  verbose:
    class: 'logging.Formatter'
    format: '[%(asctime)s] [%(levelname)s] [%(process)d - %(thread)s]: %(name)s: [%(filename)s:%(funcName)s:%(lineno)d]: %(message)s'
handlers:
  console:
    level: 'DEBUG'
    class: 'logging.StreamHandler'
    formatter: 'verbose'
    stream : 'ext://sys.stdout'
  file_handler:
    level: 'INFO'
    class: 'logging.handlers.WatchedFileHandler'
    formatter: 'verbose'
    filename: '/tmp/edgar_errors.log'
    mode: 'a+'
    encoding: 'utf-8'
loggers:
  mypkg:
    level: 'DEBUG'
    propagate: false
    handlers:
      - 'console'
  requests:
    level: 'WARNING'
root:
  level: 'DEBUG'
  handlers:
    - 'console'
    - 'file_handler'