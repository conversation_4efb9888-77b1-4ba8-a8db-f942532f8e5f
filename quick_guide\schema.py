from datetime import datetime
import logging
from typing import List, Optional

import strawberry
from quick_guide.models import MedicalServices as MedicalServicesModel, KeyLocations as KeyLocationsModel, Specialtity as SpecialtityModel
from quick_guide.resolvers import list_key_locations, list_medical_services, list_specialties

logger = logging.getLogger()
# from user.schema import User


@strawberry.type
class MedicalServices:
    id: int
    investigations: str
    tower: str
    floor: str
    location: str
    timings: str

    instance = strawberry.Private[MedicalServicesModel]

    @classmethod
    def from_instance(cls, instance: MedicalServicesModel):
        return cls(
            id=instance.id,
            investigations=instance.investigations,
            tower=instance.tower,
            floor=instance.floor,
            location=instance.location,
            timings=instance.timings
        )
    
@strawberry.type
class Specialtity:
    id: int
    name: str
    tower: str
    floor: str
    location: str

    instance = strawberry.Private[SpecialtityModel]

    @classmethod
    def from_instance(cls, instance: SpecialtityModel):
        return cls(
            id=instance.id,
            name=instance.name,
            tower=instance.tower,
            floor=instance.floor,
            location=instance.location
        )

@strawberry.type
class KeyLocations:
    id: int
    name: str
    code: str
    description: List[str]
    icon: Optional[str]

    instance = strawberry.Private[KeyLocationsModel]

    @classmethod
    def from_instance(cls, instance: KeyLocationsModel):
        return cls(
            id=instance.id,
            name=instance.name,
            code=instance.code,
            icon=instance.icon,
            description=instance.description.split('<$>')

        )
    
@strawberry.type
class Query:
    
    @strawberry.field
    def list_medical_services(self, info) -> List[MedicalServices]:
        db = info.context["db"]
        data = list_medical_services(db=db)
        return [MedicalServices.from_instance(obj) for obj in data]
    
    @strawberry.field
    def list_key_locations(self, info) -> List[KeyLocations]:
        db = info.context["db"]
        data = list_key_locations(db=db)
        return [KeyLocations.from_instance(obj) for obj in data]
    
    @strawberry.field
    def list_specialties(self, info) -> List[Specialtity]:
        db = info.context["db"]
        data = list_specialties(db=db)
        return [Specialtity.from_instance(obj) for obj in data]

