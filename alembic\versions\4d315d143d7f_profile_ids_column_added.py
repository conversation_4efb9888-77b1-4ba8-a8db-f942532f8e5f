"""profile ids column added

Revision ID: 4d315d143d7f
Revises: 5892e7913959
Create Date: 2024-04-22 09:39:04.999501

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '4d315d143d7f'
down_revision = '5892e7913959'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('service', sa.Column('profile_ids', sa.ARRAY(sa.Integer()), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('service', 'profile_ids')
    # ### end Alembic commands ###
