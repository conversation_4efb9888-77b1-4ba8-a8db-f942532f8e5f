"""queue schema added

Revision ID: 0f716e64129a
Revises: df49d90d44c0
Create Date: 2024-07-18 08:55:05.180045

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0f716e64129a'
down_revision = 'df49d90d44c0'
branch_labels = None
depends_on = None


def upgrade():
    op.drop_constraint('device_queue_counter_fk', 'device', type_='foreignkey')
    op.drop_index('ix_user_queue_step_user_queue_id', table_name='user_queue_step')
    op.drop_index('ix_queue_step_queue_id', table_name='queue_step')
    op.drop_index('ix_user_queue_date', table_name='user_queue')
    op.drop_index('ix_user_queue_queue_id', table_name='user_queue')
    op.drop_index('ix_user_queue_status', table_name='user_queue')
    op.drop_index('ix_user_queue_logs_user_queue_id', table_name='user_queue_logs')
    op.drop_table('rel_user_service_queue')
    op.drop_table('user_queue_step')
    op.drop_table('user_service_prerequisite')
    op.drop_table('user_service')
    op.drop_table('user_queue_logs')
    op.drop_table('user_queue')

    
    op.drop_table('queue_weightage_action')
    op.drop_table('rel_queue_service')
    op.drop_table('rel_device_queue')
    op.drop_table('queue_audit_logs')
    op.drop_table('queue_counter')
    op.drop_table('rel_staff_user_queue')
    op.drop_table('rel_queue_step_location')
    op.drop_table('queue_step')
    op.drop_table('queue')
    op.drop_table('location')
    op.drop_table('cluster')
    op.drop_table('tag')
    
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('cluster',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('tower', sa.String(), nullable=True),
    sa.Column('floor', sa.String(), nullable=True),
    sa.Column('cluster', sa.String(), nullable=True),
    sa.Column('status', postgresql.ENUM('ACTIVE', 'INACTIVE', name='statusenum', create_type=False), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    schema='queue'
    )
    op.create_table('location',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('code', sa.String(), nullable=True),
    sa.Column('priority', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('parent_location_id', sa.Integer(), nullable=True),
    sa.Column('status', postgresql.ENUM('ALLOTED', 'AVAILABLE', 'OCCUPIED', name='locationbedstatusenum', create_type=False), nullable=True),
    sa.Column('alloted_count', sa.Integer(), nullable=True),
    sa.Column('occupied_count', sa.Integer(), nullable=True),
    sa.Column('iot_code', sa.String(), nullable=True),
    sa.Column('total_count', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['parent_location_id'], ['queue.location.id'], name='location_parent_location_id_fk'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code'),
    schema='queue'
    )
    op.create_table('queue_weightage_action',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('code', sa.String(), nullable=True),
    sa.Column('weightage', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    schema='queue'
    )
    op.create_table('tag',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('code', sa.String(), nullable=True),
    sa.Column('status', postgresql.ENUM('ACTIVE', 'INACTIVE', name='statusenum', create_type=False), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code'),
    schema='queue'
    )
    op.create_table('user_service',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('service_id', sa.Integer(), nullable=True),
    sa.Column('bill_no', sa.String(), nullable=True),
    sa.Column('priority', sa.Numeric(), server_default=sa.text('1'), nullable=False),
    sa.Column('status', postgresql.ENUM('PENDING', 'ON_PROGRESS', 'COMPLETED', 'CANCELLED', name='servicestatusenum', create_type= False), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['service_id'], ['service.id'], name='user_service_service_id_fk'),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], name='user_service_user_id_fk'),
    sa.PrimaryKeyConstraint('id'),
    schema='queue'
    )
    op.create_table('queue',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('queue_name', sa.String(), nullable=True),
    sa.Column('queue_code', sa.String(), nullable=True),
    sa.Column('avg_procedure_time', sa.Float(), nullable=True),
    sa.Column('cluster_id', sa.Integer(), nullable=True),
    sa.Column('upcoming_patients', sa.Integer(), nullable=True),
    sa.Column('service_type', sa.String(), nullable=True),
    sa.Column('status', postgresql.ENUM('ACTIVE', 'INACTIVE', name='statusenum', create_type = False), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('show_patient_name', sa.Boolean(), nullable=True),
    sa.Column('capacity', sa.Integer(), nullable=True),
    sa.Column('total_count', sa.Integer(), server_default=sa.text('0'), nullable=True),
    sa.Column('ongoing_count', sa.Integer(), server_default=sa.text('0'), nullable=True),
    sa.Column('completed_count', sa.Integer(), server_default=sa.text('0'), nullable=True),
    sa.Column('cancelled_count', sa.Integer(), server_default=sa.text('0'), nullable=True),
    sa.Column('freezed_count', sa.Integer(), server_default=sa.text('0'), nullable=True),
    sa.Column('buffer_time', sa.Float(), nullable=True),
    sa.Column('deviation_rate', sa.Float(), server_default=sa.text('1'), nullable=True),
    sa.Column('queue_type', postgresql.ENUM('MANUAL', 'AUTO', name='queuetypeenum', create_type= False), nullable=True),
    sa.Column('allocate_counter_at', postgresql.ENUM('CHECKIN', 'ENTRY', 'EXIT', 'HOLD', 'ARRIVED', 'PURGED', 'FREEZED', name='userqueuestatusenum', create_type= False), nullable=True),
    sa.Column('waiting_capacity', sa.Integer(), nullable=True),
    sa.Column('pseudo_capacity', sa.Integer(), server_default=sa.text('0'), nullable=True),
    sa.Column('pre_check_req', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['cluster_id'], ['queue.cluster.id'], name='queue_cluster_id_fk'),
    sa.ForeignKeyConstraint(['created_by'], ['staff_user.id'], name='queue_created_by_fk'),
    sa.ForeignKeyConstraint(['updated_by'], ['staff_user.id'], name='queue_updated_by_fk'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('queue_name'),
    schema='queue'
    )
    op.create_table('queue_audit_logs',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('queue_code', sa.String(), nullable=True),
    sa.Column('data', sa.Text(), nullable=True),
    sa.Column('action', sa.String(), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['staff_user.id'], name='queue_audit_logs_created_by_fk'),
    sa.PrimaryKeyConstraint('id'),
    schema='queue'
    )
    op.create_table('user_service_prerequisite',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('user_service_id', sa.Integer(), nullable=True),
    sa.Column('pre_req_user_service_id', sa.Integer(), nullable=True),
    sa.Column('status', postgresql.ENUM('HOLD', 'WAITING', 'IGNORE', name='userprereqstatusenum', create_type= False), nullable=False),
    sa.Column('un_hold_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['pre_req_user_service_id'], ['queue.user_service.id'], name='user_service_prerequisite_pre_req_user_service_id_fk'),
    sa.ForeignKeyConstraint(['user_service_id'], ['queue.user_service.id'], name='user_service_prerequisite_user_service_id_fk'),
    sa.PrimaryKeyConstraint('id'),
    schema='queue'
    )
    op.create_table('queue_counter',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('queue_id', sa.Integer(), nullable=True),
    sa.Column('counter', sa.Integer(), nullable=True),
    sa.Column('counter_name', sa.String(), nullable=True),
    sa.Column('counter_code', sa.String(), nullable=True),
    sa.Column('status', postgresql.ENUM('ALLOTED', 'UNALLOTED', 'INACTIVE', name='queuecounterstatusenum', create_type= False), nullable=False),
    sa.Column('freeze_count', sa.Integer(), nullable=True),
    sa.Column('upcoming_capacity', sa.Integer(), nullable=True),
    sa.Column('priority', sa.Numeric(), server_default=sa.text('1'), nullable=False),
    sa.Column('counter_status', postgresql.ENUM('ACTIVE', 'INACTIVE', name='statusenum', create_type =False), server_default='ACTIVE', nullable=True),
    sa.ForeignKeyConstraint(['queue_id'], ['queue.queue.id'], name='rel_queue_counter_queue_id_fk'),
    sa.PrimaryKeyConstraint('id'),
    schema='queue'
    )
    op.create_table('queue_step',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('code', sa.String(), nullable=True),
    sa.Column('priority', sa.Integer(), nullable=True),
    sa.Column('avg_step_time', sa.Numeric(), nullable=True),
    sa.Column('queue_id', sa.Integer(), nullable=True),
    sa.Column('checkin_status', postgresql.ENUM('CHECKIN', 'ENTRY', 'EXIT', 'HOLD', 'ARRIVED', 'PURGED', 'FREEZED', name='userqueuestatusenum', create_type= False), nullable=True),
    sa.Column('checkout_status', postgresql.ENUM('CHECKIN', 'ENTRY', 'EXIT', 'HOLD', 'ARRIVED', 'PURGED', 'FREEZED', name='userqueuestatusenum', create_type= False), nullable=True),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('checkin_name', sa.String(), nullable=True),
    sa.Column('checkout_name', sa.String(), nullable=True),
    sa.Column('checkin_description', sa.String(), nullable=True),
    sa.Column('checkout_description', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['queue_id'], ['queue.queue.id'], name='queue_step_queue_id_fk'),
    sa.PrimaryKeyConstraint('id'),
    schema='queue'
    )
    op.create_index(op.f('ix_queue_queue_step_queue_id'), 'queue_step', ['queue_id'], unique=False, schema='queue')
    op.create_table('rel_queue_service',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('test_id', sa.Integer(), nullable=True),
    sa.Column('queue_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['queue_id'], ['queue.queue.id'], name='rel_queue_service_queue_id_fk'),
    sa.ForeignKeyConstraint(['test_id'], ['service.id'], name='rel_queue_service_test_id_fk'),
    sa.PrimaryKeyConstraint('id'),
    schema='queue'
    )
    op.create_table('rel_staff_user_queue',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('staff_user_id', sa.Integer(), nullable=True),
    sa.Column('queue_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['queue_id'], ['queue.queue.id'], name='rel_staff_user_queue_queue_id_fk'),
    sa.ForeignKeyConstraint(['staff_user_id'], ['staff_user.id'], name='rel_staff_user_queue_staff_user_id_fk'),
    sa.PrimaryKeyConstraint('id'),
    schema='queue'
    )
    op.create_table('rel_queue_step_location',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('location_id', sa.Integer(), nullable=True),
    sa.Column('queue_step_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['location_id'], ['queue.location.id'], name='rel_queue_step_location_location_id_fk'),
    sa.ForeignKeyConstraint(['queue_step_id'], ['queue.queue_step.id'], name='rel_queue_step_location_queue_step_id_fk'),
    sa.PrimaryKeyConstraint('id'),
    schema='queue'
    )
    op.create_table('user_queue',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('queue_id', sa.Integer(), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('token_no', sa.String(), nullable=True),
    sa.Column('date', sa.Date(), nullable=True),
    sa.Column('status', postgresql.ENUM('CHECKIN', 'ENTRY', 'EXIT', 'HOLD', 'ARRIVED', 'PURGED', 'FREEZED', name='userqueuestatusenum', create_type= False), nullable=False),
    sa.Column('pre_check_status', postgresql.ENUM('PENDING', 'COMPLETED', name='userqueueprecheckstatusenum', create_type= False), nullable=True),
    sa.Column('weightage_id', sa.Integer(), nullable=True),
    sa.Column('start_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('estimated_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('end_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('allocate_counter_at', postgresql.ENUM('CHECKIN', 'ENTRY', 'EXIT', 'HOLD', 'ARRIVED', 'PURGED', 'FREEZED', name='userqueuestatusenum', create_type= False), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('freezed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('arrived_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('force_exit', sa.Boolean(), nullable=True),
    sa.Column('counter', sa.Integer(), nullable=True),
    sa.Column('queue_step_id', sa.Integer(), nullable=True),
    sa.Column('location_id', sa.Integer(), nullable=True),
    sa.Column('next_location_id', sa.Integer(), nullable=True),
    sa.Column('step_start_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('next_step_start_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('tag_id', sa.Integer(), nullable=True),
    sa.Column('prerequisites_conditions', sa.ARRAY(sa.String()), nullable=True),
    sa.ForeignKeyConstraint(['counter'], ['queue.queue_counter.id'], name='user_queue_counter_fk'),
    sa.ForeignKeyConstraint(['created_by'], ['staff_user.id'], name='user_queue_created_by_fk'),
    sa.ForeignKeyConstraint(['location_id'], ['queue.location.id'], name='user_queue_location_id_fk'),
    sa.ForeignKeyConstraint(['next_location_id'], ['queue.location.id'], name='user_queue_next_location_id_fk'),
    sa.ForeignKeyConstraint(['queue_id'], ['queue.queue.id'], name='user_queue_queue_id_fk'),
    sa.ForeignKeyConstraint(['queue_step_id'], ['queue.queue_step.id'], name='user_queue_queue_step_id_fk'),
    sa.ForeignKeyConstraint(['tag_id'], ['queue.tag.id'], name='user_queue_tag_id_fk'),
    sa.ForeignKeyConstraint(['updated_by'], ['staff_user.id'], name='user_queue_updated_by_fk'),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], name='user_queue_user_id_fk'),
    sa.ForeignKeyConstraint(['weightage_id'], ['queue.queue_weightage_action.id'], name='user_queue_weightage_id_fk'),
    sa.PrimaryKeyConstraint('id'),
    schema='queue'
    )
    op.create_index(op.f('ix_queue_user_queue_date'), 'user_queue', ['date'], unique=False, schema='queue')
    op.create_index(op.f('ix_queue_user_queue_queue_id'), 'user_queue', ['queue_id'], unique=False, schema='queue')
    op.create_index(op.f('ix_queue_user_queue_status'), 'user_queue', ['status'], unique=False, schema='queue')
    op.create_table('rel_device_queue',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.Integer(), nullable=True),
    sa.Column('queue_id', sa.Integer(), nullable=True),
    sa.Column('is_updated', sa.Boolean(), nullable=True),
    sa.Column('subscription_name', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['device_id'], ['device.id'], name='rel_device_queue_device_id_fk'),
    sa.PrimaryKeyConstraint('id'),
    schema='queue'
    )
    op.create_table('rel_user_service_queue',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('user_service_id', sa.Integer(), nullable=True),
    sa.Column('queue_id', sa.Integer(), nullable=True),
    sa.Column('user_queue_id', sa.Integer(), nullable=True),
    sa.Column('status', postgresql.ENUM('PENDING', 'ON_PROGRESS', 'COMPLETED', 'CANCELLED', name='servicestatusenum', create_type= False), nullable=True),
    sa.ForeignKeyConstraint(['queue_id'], ['queue.queue.id'], name='rel_user_service_queue_queue_id_fk'),
    sa.ForeignKeyConstraint(['user_queue_id'], ['queue.user_queue.id'], name='rel_user_service_queue_user_queue_id_fk'),
    sa.ForeignKeyConstraint(['user_service_id'], ['queue.user_service.id'], name='rel_user_service_queue_user_service_id_fk'),
    sa.PrimaryKeyConstraint('id'),
    schema='queue'
    )
    op.create_table('user_queue_logs',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('user_queue_id', sa.Integer(), nullable=True),
    sa.Column('status', postgresql.ENUM('CHECKIN', 'ENTRY', 'EXIT', 'HOLD', 'ARRIVED', 'PURGED', 'FREEZED', name='userqueuestatusenum',create_type= False), nullable=True),
    sa.Column('pre_check_status', postgresql.ENUM('PENDING', 'COMPLETED', name='userqueueprecheckstatusenum',create_type= False), nullable=True),
    sa.Column('weightage_id', sa.Integer(), nullable=True),
    sa.Column('counter', sa.Integer(), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['counter'], ['queue.queue_counter.id'], name='user_queue_counter_fk'),
    sa.ForeignKeyConstraint(['created_by'], ['staff_user.id'], name='user_queue_created_by_fk'),
    sa.ForeignKeyConstraint(['user_queue_id'], ['queue.user_queue.id'], name='user_queue_logs_user_queue_id_fk'),
    sa.ForeignKeyConstraint(['weightage_id'], ['queue.queue_weightage_action.id'], name='user_queue_weightage_id_fk'),
    sa.PrimaryKeyConstraint('id'),
    schema='queue'
    )
    op.create_index(op.f('ix_queue_user_queue_logs_user_queue_id'), 'user_queue_logs', ['user_queue_id'], unique=False, schema='queue')
    op.create_table('user_queue_step',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('queue_step_id', sa.Integer(), nullable=True),
    sa.Column('user_queue_id', sa.Integer(), nullable=True),
    sa.Column('location_id', sa.Integer(), nullable=True),
    sa.Column('remarks', sa.Text(), nullable=True),
    sa.Column('status', postgresql.ENUM('PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'HOLD', 'SKIPPED', name='userqueuestepstatusenum',create_type= False), nullable=True),
    sa.Column('action_type', sa.String(), nullable=True),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_location', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('tag_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['location_id'], ['queue.location.id'], name='user_queue_step_location_id_fk'),
    sa.ForeignKeyConstraint(['queue_step_id'], ['queue.queue_step.id'], name='user_queue_step_queue_step_id_fk'),
    sa.ForeignKeyConstraint(['tag_id'], ['queue.tag.id'], name='user_queue_tag_id_fk'),
    sa.ForeignKeyConstraint(['user_queue_id'], ['queue.user_queue.id'], name='user_queue_step_user_queue_id_fk'),
    sa.PrimaryKeyConstraint('id'),
    schema='queue'
    )
    op.create_index(op.f('ix_queue_user_queue_step_user_queue_id'), 'user_queue_step', ['user_queue_id'], unique=False, schema='queue')
    op.create_foreign_key('device_queue_counter_fk', 'device', 'queue_counter', ['queue_counter'], ['id'], referent_schema='queue')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('device_queue_counter_fk', 'device', type_='foreignkey')
    op.create_foreign_key('device_queue_counter_fk', 'device', 'queue_counter', ['queue_counter'], ['id'])
    op.create_table('tag',
    sa.Column('id', sa.BIGINT(), server_default=sa.text("nextval('tag_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('code', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('status', postgresql.ENUM('ACTIVE', 'INACTIVE', name='statusenum'), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='tag_pkey'),
    sa.UniqueConstraint('code', name='tag_code_key'),
    postgresql_ignore_search_path=False
    )
    op.create_table('user_queue_step',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('queue_step_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('user_queue_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('remarks', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('status', postgresql.ENUM('PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'HOLD', 'SKIPPED', name='userqueuestepstatusenum'), autoincrement=False, nullable=True),
    sa.Column('created_by', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_by', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('action_type', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('location_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_location', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('description', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('tag_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['location_id'], ['location.id'], name='user_queue_step_location_id_fk'),
    sa.ForeignKeyConstraint(['queue_step_id'], ['queue_step.id'], name='user_queue_step_queue_step_id_fk'),
    sa.ForeignKeyConstraint(['tag_id'], ['tag.id'], name='user_queue_tag_id_fk'),
    sa.ForeignKeyConstraint(['user_queue_id'], ['user_queue.id'], name='user_queue_step_user_queue_id_fk'),
    sa.PrimaryKeyConstraint('id', name='user_queue_step_pkey')
    )
    op.create_index('ix_user_queue_step_user_queue_id', 'user_queue_step', ['user_queue_id'], unique=False)
    op.create_table('location',
    sa.Column('id', sa.BIGINT(), server_default=sa.text("nextval('location_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('code', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('priority', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('parent_location_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('status', postgresql.ENUM('ALLOTED', 'AVAILABLE', 'OCCUPIED', name='locationbedstatusenum'), autoincrement=False, nullable=True),
    sa.Column('alloted_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('occupied_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('iot_code', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('total_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['parent_location_id'], ['location.id'], name='location_parent_location_id_fk'),
    sa.PrimaryKeyConstraint('id', name='location_pkey'),
    sa.UniqueConstraint('code', name='location_code_key'),
    postgresql_ignore_search_path=False
    )
    op.create_table('rel_queue_step_location',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('location_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('queue_step_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['location_id'], ['location.id'], name='rel_queue_step_location_location_id_fk'),
    sa.ForeignKeyConstraint(['queue_step_id'], ['queue_step.id'], name='rel_queue_step_location_queue_step_id_fk'),
    sa.PrimaryKeyConstraint('id', name='rel_queue_step_location_pkey')
    )
    op.create_table('user_service',
    sa.Column('id', sa.BIGINT(), server_default=sa.text("nextval('user_service_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('service_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('priority', sa.NUMERIC(), server_default=sa.text('1'), autoincrement=False, nullable=False),
    sa.Column('status', postgresql.ENUM('PENDING', 'ON_PROGRESS', 'COMPLETED', 'CANCELLED', name='servicestatusenum'), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('token_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('bill_no', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['service_id'], ['service.id'], name='user_service_service_id_fk'),
    sa.ForeignKeyConstraint(['token_id'], ['user_token.id'], name='user_service_user_token_id_fk'),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], name='user_service_user_id_fk'),
    sa.PrimaryKeyConstraint('id', name='user_service_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_table('rel_staff_user_queue',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('staff_user_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('queue_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['queue_id'], ['queue.id'], name='rel_staff_user_queue_queue_id_fk'),
    sa.ForeignKeyConstraint(['staff_user_id'], ['staff_user.id'], name='rel_staff_user_queue_staff_user_id_fk'),
    sa.PrimaryKeyConstraint('id', name='rel_staff_user_queue_pkey')
    )
    op.create_table('queue_audit_logs',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('data', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('action', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('created_by', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('queue_code', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['staff_user.id'], name='queue_created_by_fk'),
    sa.PrimaryKeyConstraint('id', name='queue_audit_logs_pkey')
    )
    op.create_table('rel_device_queue',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('queue_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('is_updated', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('subscription_name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['device_id'], ['device.id'], name='rel_device_queue_device_id_fk'),
    sa.PrimaryKeyConstraint('id', name='rel_device_queue_pkey')
    )
    op.create_table('rel_queue_service',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('test_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('queue_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['queue_id'], ['queue.id'], name='rel_queue_service_queue_id_fk'),
    sa.ForeignKeyConstraint(['test_id'], ['service.id'], name='rel_queue_service_test_id_fk'),
    sa.PrimaryKeyConstraint('id', name='rel_queue_service_pkey')
    )
    op.create_table('rel_user_service_queue',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('user_service_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('queue_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('user_queue_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('status', postgresql.ENUM('PENDING', 'ON_PROGRESS', 'COMPLETED', 'CANCELLED', name='servicestatusenum'), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['queue_id'], ['queue.id'], name='rel_user_service_queue_queue_id_fk'),
    sa.ForeignKeyConstraint(['user_queue_id'], ['user_queue.id'], name='rel_user_service_queue_user_queue_id_fk'),
    sa.ForeignKeyConstraint(['user_service_id'], ['user_service.id'], name='rel_user_service_queue_user_service_id_fk'),
    sa.PrimaryKeyConstraint('id', name='rel_user_service_queue_pkey')
    )
    op.create_table('user_queue_logs',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('user_queue_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('status', postgresql.ENUM('CHECKIN', 'ENTRY', 'EXIT', 'HOLD', 'ARRIVED', 'PURGED', 'FREEZED', name='userqueuestatusenum'), autoincrement=False, nullable=True),
    sa.Column('pre_check_status', postgresql.ENUM('PENDING', 'COMPLETED', name='userqueueprecheckstatusenum'), autoincrement=False, nullable=True),
    sa.Column('weightage_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('counter', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_by', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['counter'], ['queue_counter.id'], name='user_queue_counter_fk'),
    sa.ForeignKeyConstraint(['created_by'], ['staff_user.id'], name='user_queue_created_by_fk'),
    sa.ForeignKeyConstraint(['user_queue_id'], ['user_queue.id'], name='user_queue_logs_user_queue_id_fk'),
    sa.ForeignKeyConstraint(['weightage_id'], ['queue_weightage_action.id'], name='user_queue_weightage_id_fk'),
    sa.PrimaryKeyConstraint('id', name='user_queue_logs_pkey')
    )
    op.create_index('ix_user_queue_logs_user_queue_id', 'user_queue_logs', ['user_queue_id'], unique=False)
    op.create_table('user_service_prerequisite',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('user_service_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('pre_req_user_service_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('status', postgresql.ENUM('HOLD', 'WAITING', 'IGNORE', name='userprereqstatusenum'), autoincrement=False, nullable=False),
    sa.Column('un_hold_time', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['pre_req_user_service_id'], ['user_service.id'], name='user_service_prerequisite_pre_req_user_service_id_fk'),
    sa.ForeignKeyConstraint(['user_service_id'], ['user_service.id'], name='user_service_prerequisite_user_service_id_fk'),
    sa.PrimaryKeyConstraint('id', name='user_service_prerequisite_pkey')
    )
    op.create_table('queue',
    sa.Column('id', sa.BIGINT(), server_default=sa.text("nextval('queue_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('queue_name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('queue_code', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('avg_procedure_time', postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('cluster_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('upcoming_patients', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('status', postgresql.ENUM('ACTIVE', 'INACTIVE', name='statusenum'), autoincrement=False, nullable=False),
    sa.Column('created_by', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('updated_by', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('deleted_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('show_patient_name', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('capacity', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('total_count', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=True),
    sa.Column('ongoing_count', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=True),
    sa.Column('completed_count', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=True),
    sa.Column('cancelled_count', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=True),
    sa.Column('buffer_time', postgresql.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('deviation_rate', postgresql.DOUBLE_PRECISION(precision=53), server_default=sa.text('1'), autoincrement=False, nullable=True),
    sa.Column('freezed_count', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=True),
    sa.Column('waiting_capacity', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('pseudo_capacity', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=True),
    sa.Column('allocate_counter_at', postgresql.ENUM('CHECKIN', 'ENTRY', 'EXIT', 'HOLD', 'ARRIVED', 'PURGED', 'FREEZED', name='userqueuestatusenum'), autoincrement=False, nullable=True),
    sa.Column('queue_type', postgresql.ENUM('MANUAL', 'AUTO', name='queuetypeenum'), autoincrement=False, nullable=True),
    sa.Column('pre_check_req', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('service_type', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['cluster_id'], ['cluster.id'], name='queue_cluster_id_fk'),
    sa.ForeignKeyConstraint(['created_by'], ['staff_user.id'], name='queue_created_by_fk'),
    sa.ForeignKeyConstraint(['updated_by'], ['staff_user.id'], name='queue_updated_by_fk'),
    sa.PrimaryKeyConstraint('id', name='queue_pkey'),
    sa.UniqueConstraint('queue_name', name='queue_queue_name_key'),
    postgresql_ignore_search_path=False
    )
    op.create_table('user_queue',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('queue_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('token_no', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('date', sa.DATE(), autoincrement=False, nullable=True),
    sa.Column('status', postgresql.ENUM('CHECKIN', 'ENTRY', 'EXIT', 'HOLD', 'ARRIVED', 'PURGED', 'FREEZED', name='userqueuestatusenum'), autoincrement=False, nullable=False),
    sa.Column('weightage_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('start_time', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('end_time', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('created_by', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('updated_by', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('freezed_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('token_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('estimated_time', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('arrived_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('force_exit', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('counter', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('pre_check_status', postgresql.ENUM('PENDING', 'COMPLETED', name='userqueueprecheckstatusenum'), autoincrement=False, nullable=True),
    sa.Column('allocate_counter_at', postgresql.ENUM('CHECKIN', 'ENTRY', 'EXIT', 'HOLD', 'ARRIVED', 'PURGED', 'FREEZED', name='userqueuestatusenum'), autoincrement=False, nullable=True),
    sa.Column('queue_step_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('location_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('tag_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('next_location_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('step_start_time', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('next_step_start_time', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('prerequisites_conditions', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['counter'], ['queue_counter.id'], name='user_queue_counter_fk'),
    sa.ForeignKeyConstraint(['created_by'], ['staff_user.id'], name='user_queue_created_by_fk'),
    sa.ForeignKeyConstraint(['location_id'], ['location.id'], name='user_queue_location_id_fk'),
    sa.ForeignKeyConstraint(['next_location_id'], ['location.id'], name='user_queue_next_location_id_fk'),
    sa.ForeignKeyConstraint(['queue_id'], ['queue.id'], name='user_queue_queue_id_fk'),
    sa.ForeignKeyConstraint(['queue_step_id'], ['queue_step.id'], name='user_queue_queue_step_id_fk'),
    sa.ForeignKeyConstraint(['tag_id'], ['tag.id'], name='user_queue_tag_id_fk'),
    sa.ForeignKeyConstraint(['token_id'], ['user_token.id'], name='user_queue_user_token_id_fk'),
    sa.ForeignKeyConstraint(['updated_by'], ['staff_user.id'], name='user_queue_updated_by_fk'),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], name='user_queue_user_id_fk'),
    sa.ForeignKeyConstraint(['weightage_id'], ['queue_weightage_action.id'], name='user_queue_weightage_id_fk'),
    sa.PrimaryKeyConstraint('id', name='user_queue_pkey')
    )
    op.create_index('ix_user_queue_status', 'user_queue', ['status'], unique=False)
    op.create_index('ix_user_queue_queue_id', 'user_queue', ['queue_id'], unique=False)
    op.create_index('ix_user_queue_date', 'user_queue', ['date'], unique=False)
    op.create_table('cluster',
    sa.Column('id', sa.BIGINT(), server_default=sa.text("nextval('cluster_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('tower', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('floor', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('cluster', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('status', postgresql.ENUM('ACTIVE', 'INACTIVE', name='statusenum'), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='cluster_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_table('queue_weightage_action',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('code', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('weightage', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='queue_weightage_action_pkey')
    )
    op.create_table('queue_counter',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('queue_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('counter', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('counter_name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('counter_code', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('status', postgresql.ENUM('ALLOTED', 'UNALLOTED', 'INACTIVE', name='queuecounterstatusenum'), autoincrement=False, nullable=False),
    sa.Column('priority', sa.NUMERIC(), server_default=sa.text('1'), autoincrement=False, nullable=False),
    sa.Column('counter_status', postgresql.ENUM('ACTIVE', 'INACTIVE', name='statusenum'), server_default=sa.text("'ACTIVE'::statusenum"), autoincrement=False, nullable=True),
    sa.Column('freeze_count', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('upcoming_capacity', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['queue_id'], ['queue.id'], name='rel_user_service_queue_queue_id_fk'),
    sa.PrimaryKeyConstraint('id', name='queue_counter_pkey')
    )
    op.create_table('queue_step',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('code', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('priority', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('avg_step_time', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('queue_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('checkin_status', postgresql.ENUM('CHECKIN', 'ENTRY', 'EXIT', 'HOLD', 'ARRIVED', 'PURGED', 'FREEZED', name='userqueuestatusenum'), autoincrement=False, nullable=True),
    sa.Column('checkout_status', postgresql.ENUM('CHECKIN', 'ENTRY', 'EXIT', 'HOLD', 'ARRIVED', 'PURGED', 'FREEZED', name='userqueuestatusenum'), autoincrement=False, nullable=True),
    sa.Column('created_by', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('checkin_name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('checkout_name', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('checkin_description', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('checkout_description', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['queue_id'], ['queue.id'], name='queue_step_queue_id_fk'),
    sa.PrimaryKeyConstraint('id', name='queue_step_pkey')
    )
    op.create_index('ix_queue_step_queue_id', 'queue_step', ['queue_id'], unique=False)
    op.drop_index(op.f('ix_queue_user_queue_step_user_queue_id'), table_name='user_queue_step', schema='queue')
    op.drop_table('user_queue_step', schema='queue')
    op.drop_index(op.f('ix_queue_user_queue_logs_user_queue_id'), table_name='user_queue_logs', schema='queue')
    op.drop_table('user_queue_logs', schema='queue')
    op.drop_table('rel_user_service_queue', schema='queue')
    op.drop_table('rel_device_queue', schema='queue')
    op.drop_index(op.f('ix_queue_user_queue_status'), table_name='user_queue', schema='queue')
    op.drop_index(op.f('ix_queue_user_queue_queue_id'), table_name='user_queue', schema='queue')
    op.drop_index(op.f('ix_queue_user_queue_date'), table_name='user_queue', schema='queue')
    op.drop_table('user_queue', schema='queue')
    op.drop_table('rel_queue_step_location', schema='queue')
    op.drop_table('rel_staff_user_queue', schema='queue')
    op.drop_table('rel_queue_service', schema='queue')
    op.drop_index(op.f('ix_queue_queue_step_queue_id'), table_name='queue_step', schema='queue')
    op.drop_table('queue_step', schema='queue')
    op.drop_table('queue_counter', schema='queue')
    op.drop_table('user_service_prerequisite', schema='queue')
    op.drop_table('queue_audit_logs', schema='queue')
    op.drop_table('queue', schema='queue')
    op.drop_table('user_service', schema='queue')
    op.drop_table('tag', schema='queue')
    op.drop_table('queue_weightage_action', schema='queue')
    op.drop_table('location', schema='queue')
    op.drop_table('cluster', schema='queue')
    # ### end Alembic commands ###
