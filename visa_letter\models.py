import enum
from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON>umn, DateTime, Enum, ForeignKey, SmallInteger, Text, String, Time, UniqueConstraint, Integer, Numeric, Boolean,Date, Float, case
from sqlalchemy.sql import func
import strawberry
from sqlalchemy.orm import relationship
from database.db_conf import Base


@strawberry.enum
class PdfFormatEnum(enum.Enum):
    TABLE = "TABLE"
    NORMAL = "NORMAL"

@strawberry.enum
class PdfGenerationEnum(enum.Enum):
    PENDING = "PENDING"
    GENERATED = "GENERATED"
    CANCELLED = "CANCELLED"

@strawberry.enum
class AttendantTypeEnum(enum.Enum):
    ATTENDANT = "ATTENDANT"
    DONOR = "DONOR"

class Country(Base):
    __tablename__ = "country"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    country_name = Column(String)
    country_code = Column(String)
    subject = Column(Text)
    sub_body_part_1 = Column(Text)
    sub_body_part_2 = Column(Text)
    sub_body_part_3 = Column(Text)
    conclusion = Column(Text)
    note = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self) -> str:
        return "<country %r>" % self.id

class CountryEmbassy(Base):
    __tablename__ = "country_embassy"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    country_id = Column(Integer, ForeignKey(
        "country.id", name = "country_embassy_country_id_fk"
    ))
    embassy = Column(String)
    embassy_address = Column(Text)
    pdf_format = Column(Enum(PdfFormatEnum), default = PdfFormatEnum.TABLE)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    country = relationship("Country")

    def __repr__(self) -> str:
        return "<country_embassy %r>" % self.id   

class UserVisaAttendant(Base):
    __tablename__ = 'user_visa_attendant'
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_visa_id = Column(Integer, ForeignKey(
        "user_visa_data.id", name = "user_visa_attendant_user_visa_id_fk"
    ))
    attendant = Column(String)
    attendant_passport = Column(String)
    type = Column(Enum(AttendantTypeEnum))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self) -> str:
        return "<UserVisaAttendant %r>" % self.id
    
class UserVisaData(Base):
    __tablename__ = "user_visa_data"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    patient_name = Column(String)
    passport_no = Column(String)
    provisional_diagnosis = Column(String)
    treatment_duration = Column(String)
    hospital_signatory = Column(String)
    contact_details = Column(String)
    country_id = Column(Integer, ForeignKey(
        "country.id", name = "user_visa_data_country_id_fk"
    ))
    reference_id = Column(String)
    pdf_file_path = Column(String)
    embassy_id = Column(Integer, ForeignKey(
        "country_embassy.id", name = "user_visa_data_embassy_id_fk"
    ))
    status = Column(Enum(PdfGenerationEnum))
    appointment_date = Column(Date)
    appointment_slot = Column(String)
    ref_no = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    patient_passport_file1 = Column(String)
    patient_passport_file2 = Column(String)
    expiry_date = Column(Date)
    additions = Column(Text)
    remarks = Column(Text)
    created_by = Column(String)
    generated_by = Column(String)
    last_updated_by = Column(String)
    country = relationship("Country")
    embassy = relationship("CountryEmbassy")
    attendant = relationship("UserVisaAttendant",order_by=case(value=UserVisaAttendant.type, whens={'DONOR': 0}, else_=1))
    doctor = relationship("UserVisaDoctor")
    def __repr__(self) -> str:
        return "<UserVisaData %r>" % self.id 

class AuthorizedDoctor(Base):
    __tablename__ = "authorized_doctor"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    doctor_name = Column(String)
    doctor_position = Column(Text)
    mobile_no = Column(String)
    email = Column(String)
    signature_path = Column(String)
    stamp_path = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    def __repr__(self) -> str:
        return "<AuthorizedDoctor %r>" % self.id

class UserVisaDoctor(Base):
    __tablename__ = 'user_visa_doctor'
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_visa_id = Column(Integer, ForeignKey(
        "user_visa_data.id", name = "user_visa_doctor_user_visa_id_fk"
    ))
    doctor_name = Column(String)
    doctor_specalization = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self) -> str:
        return "<UserVisaDoctor %r>" % self.id

class Doctor(Base):
    __tablename__ = 'doctor'
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String)
    speciality = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    def __repr__(self) -> str:
        return "<Doctor %r>" % self.id

class UserVisaScannedDetails(Base):
    __tablename__ = 'user_visa_scanned_details'
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    reference_id = Column(String)
    status = Column(String)
    scanned_at = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    def __repr__(self) -> str:
        return "<UserVisaScannedDetails %r>" % self.id