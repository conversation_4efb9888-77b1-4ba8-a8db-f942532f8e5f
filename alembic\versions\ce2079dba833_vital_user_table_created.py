"""vital user table created

Revision ID: ce2079dba833
Revises: de2114c0d49d
Create Date: 2024-08-20 12:52:15.013439

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ce2079dba833'
down_revision = 'de2114c0d49d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
