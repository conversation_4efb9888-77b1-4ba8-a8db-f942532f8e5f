# import ezdxf
# import geojson

# def convert_to_geojson(filename):
#     dwg = ezdxf.readfile(filename)
#     msp = dwg.modelspace()

#     features = []
#     for entity in msp:
#         try:
#             if entity.dxftype() == 'LINE':
#                 start_point = entity.dxf.start
#                 end_point = entity.dxf.end
#                 line = geojson.LineString([(start_point[0]/138188, start_point[1]/138188), (end_point[0]/138188, end_point[1]/138188)])
#                 features.append(geojson.Feature(geometry=line))
#             elif entity.dxftype() == 'LWPOLYLINE':
#                 points = []
#                 print(entity)
#                 try:
#                     points = [(v[0]/138188, v[1]/138188) for v in entity.vertices()]
#                     polyline = geojson.LineString(points)
#                     print(polyline)
#                     features.append(geojson.Feature(geometry=polyline))
#                 except Exception as ex:
#                     print(ex)
#         except Exception as ex:
#             pass
#         # Add other elif blocks for different entity types as needed

#     feature_collection = geojson.FeatureCollection(features)
#     return feature_collection
# def save_geojson_to_file(geojson_data, output_file):
#     with open(output_file, 'w') as f:
#         geojson.dump(geojson_data, f, sort_keys=True)
# # Usage example
# dwg_file = 'example1.dxf'
# geojson_data = convert_to_geojson(dwg_file)
# # print(coordinates)
# output_file = 'output1.geojson'
# save_geojson_to_file(geojson_data, output_file)

