import re

from exceptions.exceptions import Mutation<PERSON><PERSON>r

def validate_password(password):
    # Define a regular expression pattern for password validation
    # This pattern enforces the following rules:
    # - At least 8 characters
    # - Contains at least one lowercase letter
    # - Contains at least one uppercase letter
    # - Contains at least one digit
    # - Contains at least one special character from the set: !@#$%^&*
    pattern = r"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*]).{8,}$"
    # Compile the regular expression pattern
    regex = re.compile(pattern)
    # Check if the password matches the pattern
    if regex.match(password):
        return True
    else:
        raise MutationError("Password Should Contain min Capital, Small, Digit, Special Character and should contain min 8 Letters")