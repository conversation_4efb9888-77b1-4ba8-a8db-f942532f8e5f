from collections import defaultdict
import json
import math
import random
import threading
import time
from typing import List, Optional
import pytz
from bill.models import ServiceStatusEnum, UserService as UserServiceModel, UserToken as UserTokenModel
from bms.resolvers import get_staff_user
from database.db_conf import SessionLoc<PERSON>
from exceptions.exceptions import MutationError, QueryError
from sms_mail_notification.models import EventCodeEnum, SMSTypeEnum
from sms_mail_notification.resolvers import get_template_msg, get_template_name, get_whatsapp_details, send_whatsapp_msg
from user.models import StatusEnum, Device as DeviceModel
from service.models import Service as ServiceModel
from queues.models import (Cluster as ClusterModel, Location as LocationModel, LocationBedStatusEnum, PatientTypeEnum, QueueCounter as QueueCounterModel, QueueCounterStatusEnum, QueueStep as QueueStepModel, QueueTypeEnum, QueueWeightageAction as QueueWeightageActionModel, 
                           RelQueueService as RelQueueServiceModel, RelUserServiceQueue as RelUserServiceQueueModel, Tag as TagModel, UserPreReqStatusEnum, UserQueue as UserQueueModel, Queue as QueueModel, 
                           RelStaffUserQueue as RelStaffUserQueueModel, QueueAuditLogs as QueueAuditLogsModel, UserQueueLogs as UserQueueLogsModel, UserQueuePreCheckStatusEnum, UserQueueStatusEnum, RelDeviceQueue as RelDeviceQueueModel, UserQueueStep as UserQueueStepModel, 
                           UserQueueStepStatusEnum, UserServicePrerequisite as UserServicePrerequisiteModel)
from sqlalchemy.orm import Session, joinedload,aliased
import logging
from datetime import date, datetime, timedelta
from graphql_types import AcknowledgeDetailsInput, BarCodeDetails, QRDetail, QueueInput, SampleCollectionInput , TagInput
from user.models import User as UserModel
from sqlalchemy import bindparam, update, asc, desc, func, literal_column, or_, and_, text, Integer, case, extract, distinct, cast, String
from sqlalchemy.sql import select
from staff_user.models import StaffUser as StaffUserModel
from staff_user.resolvers import model_to_dict, CustomJSONEncoder
from sqlalchemy.exc import IntegrityError
import os
from jose import jws
import requests
from user.resolvers import get_access_token
from util.email import send_email
logger = logging.getLogger()
import redis
from redis_lock import RedisLock
from util.globals import client_lock, handle_request, remove_assigned_tokens_list,handle_request1,handle_get_request
from constants import CREATE_TAG,DELETE_TAG, GENERATE_BAR_CODE, GET_ACKNOWLEDGE_DETAILS, GET_ORDER_DETAILS_URL, GET_VITAL_STATUS_BY_UHIDS, UPDATE_SAMPLE_COLLECTION, UPDATE_TAG, GET_EMPLOYEE_DETAILS
from sqlalchemy.dialects import postgresql
import uuid
from config.models import Config as ConfigModel

lock = threading.RLock()
lock1 = threading.RLock()

# client = redis.Redis(host="opd_redis", port=6379)

async def add_or_update_queue(db: Session, data: QueueInput):
    with RedisLock(client_lock, "queue_"+str(0 if data.queue_id is None else data.queue_id), blocking_timeout=60):
        try:
            logger.info(data)
            queue_id = None
            json_data = {}
            inital_capacity = None
            final_capacity = None
            pseudo_capacity=0
            queue_data = db.query(QueueModel).filter(
                    QueueModel.id == data.queue_id).one_or_none()
            if data.counters == None or len(data.counters) <= 0:
                raise MutationError("Queue Should have atleast one counter")
            if data.queue_id is None or data.queue_id == 0:
                queue_data = QueueModel(
                    queue_name=data.queue_name,
                    queue_code=data.queue_code,
                    queue_type= data.queue_type,
                    service_type= data.service_type,
                    avg_procedure_time=data.avg_procedure_time,
                    cluster_id=data.cluster_id,
                    upcoming_patients=data.upcoming_patients,
                    show_patient_name=data.show_patient_name if data.show_patient_name is not None else False,
                    capacity = len(data.counters),
                    buffer_time = data.buffer_time if data.buffer_time is not None else 2,
                    waiting_capacity = data.waiting_capacity if data.waiting_capacity is not None else None,
                    assignment = data.assignment_options,
                    allocate_counter_at= UserQueueStatusEnum.FREEZED
                )
                db.add(queue_data)
                db.flush()
                staff_user_dict = model_to_dict(queue_data)
                json_data = json.dumps(staff_user_dict, cls=CustomJSONEncoder)
                action = "ADDED"
                queue_id = queue_data.id
            else:
                inital_capacity = queue_data.capacity
                queue_data.queue_name = data.queue_name
                queue_data.service_type= data.service_type
                queue_data.queue_type= data.queue_type
                queue_data.avg_procedure_time = data.avg_procedure_time
                queue_data.cluster_id = data.cluster_id
                queue_data.upcoming_patients = data.upcoming_patients
                queue_data.buffer_time = data.buffer_time if data.buffer_time is not None else 2
                queue_data.waiting_capacity = data.waiting_capacity if data.waiting_capacity is not None else None
                queue_data.show_patient_name=data.show_patient_name if data.show_patient_name is not None else False
                queue_data.assignment = data.assignment_options
                json_data["queue_code"] = data.queue_code
                json_data["avg_procedure_time"] = data.avg_procedure_time
                json_data["cluster_id"] = data.cluster_id
                json_data["upcoming_patients"] = data.upcoming_patients
                json_data = json.dumps(json_data)
                action = "UPDATED"
                queue_id = data.queue_id
                final_capacity = queue_data.capacity
            queue_counters = db.query(QueueCounterModel).filter(QueueCounterModel.queue_id==queue_id).all()
            counters_list = list(map(lambda x: x.counter, queue_counters))
            directory = "data/doctor_images"
            if not os.path.exists(directory):
                os.makedirs(directory)
            for counter in data.counters:
                if counter.id is not None and counter.id != 0:
                    queue_counters = db.query(QueueCounterModel).filter(QueueCounterModel.id==counter.id).first()
                    queue_counters.upcoming_capacity=math.ceil(queue_data.upcoming_patients/queue_data.capacity)
                    if counter.status == 'INACTIVE'and (queue_counters.status == QueueCounterStatusEnum.ALLOTED and queue_counters.counter_status == StatusEnum.ACTIVE):
                        pseudo_capacity += 1
                        queue_counters.counter_status= "INACTIVE"
                    else:
                        queue_counters.counter_status= StatusEnum(counter.status)
                    if counter.queue_counter_name:
                        queue_counters.counter_name = counter.queue_counter_name
                if counter.doctor_image is not None:
                    reference_id = str(uuid.uuid4())
                    file_extn = counter.doctor_image.filename.split('.')[-1]
                    with open(f"data/doctor_images/{reference_id}_1.{file_extn}", 'wb') as new_file:
                        new_file.write(await counter.doctor_image.read())
                    counter.image_name = f"{reference_id}_1.{file_extn}"
                    if counter.id != 0:
                        queue_counters = db.query(QueueCounterModel).filter(QueueCounterModel.id==counter.id).first()
                        queue_counters.doctor_image_name = f"{reference_id}_1.{file_extn}"
            db.add_all(
                QueueCounterModel(queue_id=queue_id, counter=counter.number, counter_name = counter.queue_counter_name, doctor_image_name = counter.image_name, counter_code = f"COUNTER_{counter.number}",status= QueueCounterStatusEnum.UNALLOTED, counter_status = StatusEnum(counter.status),upcoming_capacity =math.ceil(queue_data.upcoming_patients/queue_data.capacity), freeze_count=0)
                for counter in data.counters 
                if int(counter.number) not in counters_list
            )
            db.query(QueueModel).filter(QueueModel.id==queue_id).update(
                    {QueueModel.pseudo_capacity:pseudo_capacity
                        },
                    synchronize_session="fetch",
                )
            db.flush()
            final_capacity = db.query(QueueCounterModel).filter(QueueCounterModel.queue_id == queue_id , QueueCounterModel.counter_status != StatusEnum.INACTIVE)
            queue_data.capacity = final_capacity.count()
            if inital_capacity is not None and  inital_capacity < queue_data.capacity:
                for queue_count in final_capacity.all():
                    if queue_count.status==QueueCounterStatusEnum.UNALLOTED:
                        ongoing_count,freezed_count=entry_or_freeze_next_users(db, queue_data,queue_count.id)
                        db.query(QueueModel).filter(QueueModel.id==queue_id).update(
                            {QueueModel.ongoing_count: QueueModel.ongoing_count+ongoing_count,
                                QueueModel.freezed_count: QueueModel.freezed_count+freezed_count
                                },
                            synchronize_session="fetch",
                        )
                        db.flush()
            db.query(RelStaffUserQueueModel).filter(
                RelStaffUserQueueModel.queue_id == queue_data.id).delete()
            if data.staff_user_ids is not None:
                for staff_user_id in data.staff_user_ids:
                    rel_queue_user_data = RelStaffUserQueueModel(
                        staff_user_id=staff_user_id,
                        queue_id=queue_id
                    )
                    db.add(rel_queue_user_data)
            db.query(RelQueueServiceModel).filter(
                RelQueueServiceModel.queue_id == queue_data.id).delete()
            if data.test_ids is not None:
                for test_id in data.test_ids:
                    rel_queue_service = RelQueueServiceModel(
                        test_id=test_id,
                        queue_id=queue_id
                    )
                    db.add(rel_queue_service)
            log_data = QueueAuditLogsModel(
                queue_code=data.queue_code,
                data=json_data,
                action=action
            )
            db.add(log_data)
            db.commit()
            return get_queues(db)
        except IntegrityError as e:
            logger.exception(e)
            raise MutationError("Queue already exists with this name")
        except MutationError as e:
            logger.exception(e)
            raise MutationError(e.message)
        except Exception as e:
            logger.exception(e)
            raise MutationError("Error occured While saving data")
        
def add_or_update_tag(db: Session,data : TagInput):
    try:
        if data.rfid_code is None or data.rfid_code=="":
            raise MutationError("rfid Code is required")
        tag_data = db.query(TagModel).filter((TagModel.id==data.id)).one_or_none()
        if tag_data != None:
            db.query(TagModel).filter(TagModel.id == data.id).update({
                TagModel.name : data.name,
                TagModel.code:data.code,
                TagModel.rfid_code : data.rfid_code,
                TagModel.status : data.status
            })
            db.commit()
            return "Updated Successfully"
        add_tag = TagModel(
            name= data.name,
            code= data.code,
            rfid_code= data.rfid_code,
            status = data.status
        )
        db.add(add_tag)
        db.commit()
        return "Created Successfully"
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occurred While Saving data")



def get_queues(db: Session):
    return db.query(QueueModel).filter(QueueModel.deleted_at == None).order_by(QueueModel.created_at).all()

def get_patient_queues(db: Session, staff_user, uhid):
    data = (
        db.query(UserQueueModel)
        .join(UserModel, UserModel.id == UserQueueModel.user_id)
        .join(QueueModel, QueueModel.id == UserQueueModel.queue_id)
        .join(RelStaffUserQueueModel, RelStaffUserQueueModel.queue_id == QueueModel.id)
        .filter(UserModel.umr_no == uhid)
        .filter(RelStaffUserQueueModel.staff_user_id == staff_user)
        .filter(UserQueueModel.status.in_(['HOLD', 'CHECKIN', 'ARRIVED', 'FREEZED', 'ENTRY']))
        .first()
    )
    if not data:
        raise MutationError("No patient found")
    if data.status in [UserQueueStatusEnum.ARRIVED,UserQueueStatusEnum.FREEZED,UserQueueStatusEnum.ENTRY]:
        raise MutationError("Patient already arrived")
    return data


def delete_queue(db: Session, queue_id: int):
    try:
        queue = db.query(QueueModel).filter(
            QueueModel.id == queue_id).one_or_none()
        queue.deleted_at = datetime.now() if queue is not None else None
        db.commit()
    except MutationError as e:
        logger.exception(e)
        raise MutationError(e.message)
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occurred While deleting queue")


def get_user_queue(db: Session, queue_id: int, pre_check_status:str, assignment_option:Optional[str]=None, queue_code: str= None):
    today = date.today()
    if queue_id is not None and queue_id!=0:
        queue_query = db.query(QueueModel).filter(QueueModel.id == queue_id)
    else:
        queue_query = db.query(QueueModel).filter(QueueModel.queue_code == queue_code)
    if assignment_option is not None:
        queue_query = queue_query.filter(QueueModel.assignment.any(assignment_option))
    queue: QueueModel = queue_query.one_or_none()
    if queue is None:
        return []

    query=db.query(UserQueueModel).join(UserQueueModel.queue_weightage_action).join(UserQueueModel.queue).filter(UserQueueModel.date == today)
    if pre_check_status is not None:
        query=query.filter(QueueModel.assignment.any("PRECHECK")).filter(UserQueueModel.pre_check_status==UserQueuePreCheckStatusEnum.PENDING, UserQueueModel.status==UserQueueStatusEnum.ARRIVED)
    query=query.filter(
        UserQueueModel.queue_id == queue.id)    
    list1 = query.filter(
            or_(UserQueueModel.status == UserQueueStatusEnum.ARRIVED,
                UserQueueModel.status == UserQueueStatusEnum.CHECKIN,
                UserQueueModel.status == UserQueueStatusEnum.HOLD,
                UserQueueModel.status == UserQueueStatusEnum.FREEZED,
                UserQueueModel.status == UserQueueStatusEnum.ENTRY)).order_by(
                    asc(UserQueueModel.freezed_at),
        text("user_queue.status='FREEZED' desc"),
        desc((QueueModel.avg_procedure_time*QueueWeightageActionModel.weightage)+(func.extract("epoch",
             (func.now() - UserQueueModel.order_by_date))/60)),
        asc(UserQueueModel.created_at)).group_by(UserQueueModel.id, QueueWeightageActionModel.weightage,QueueModel.avg_procedure_time )
    # if pre_check_status is not None:
    #     list1=list1.limit(queue.upcoming_patients)
    return list1.all()

def get_user_queue_all(db: Session, queue_code: str= None):
    today = date.today()
    queue_query = db.query(QueueModel).filter(QueueModel.queue_code == queue_code)
    queue: QueueModel = queue_query.one_or_none()
    if queue is None:
        return []
    query=db.query(UserQueueModel, func.max(UserServiceModel.bill_no)).join(UserQueueModel.queue_weightage_action).join(UserQueueModel.queue).join(RelUserServiceQueueModel, RelUserServiceQueueModel.user_queue_id==UserQueueModel.id).join(RelUserServiceQueueModel.user_service).filter(UserQueueModel.date == today)
    query=query.filter(
        UserQueueModel.queue_id == queue.id) 
    list1= query.order_by(
    case(
        [
            (UserQueueModel.status == 'ENTRY', 1),
            (UserQueueModel.status == 'FREEZED', 2),
            (UserQueueModel.status == 'ARRIVED', 3),
            (UserQueueModel.status == 'CHECKIN', 3),
            (UserQueueModel.status == 'HOLD', 3),
            (UserQueueModel.status == 'EXIT', 4),
        ],
        else_=5  # This sets any unhandled status to the last position
    ).asc(),
        asc(UserQueueModel.freezed_at),
        desc((QueueModel.avg_procedure_time*QueueWeightageActionModel.weightage) + (
            func.extract("epoch", func.now() - UserQueueModel.order_by_date) / 60
        )),
        asc(UserQueueModel.created_at)
    ).group_by(UserQueueModel.id, QueueWeightageActionModel.weightage, QueueModel.avg_procedure_time) 
    return list1.all()

def get_user_queue_vitals(db: Session, queue_id: List[int], pre_check_status:str, assignment_option:Optional[str]=None):
    today = date.today()
    query = (
        db.query(UserQueueModel)
        .join(UserQueueModel.queue_weightage_action)
        .join(QueueModel, UserQueueModel.queue_id == QueueModel.id)
        .filter(UserQueueModel.date == today, UserQueueModel.queue_id.in_(queue_id))
    )
    if assignment_option is not None:
        query = query.filter(QueueModel.assignment.any(assignment_option))
    if assignment_option=="VITALS":
        query = query.filter(UserQueueModel.vitals_datetime.is_(None))
    elif assignment_option=="PHYSICIANASSISTANT":
        query = query.filter(UserQueueModel.phy_ass_datetime.is_(None)).filter(UserQueueModel.status == UserQueueStatusEnum.ARRIVED)
    # if pre_check_status is not None:
    #     query=query.filter(UserQueueModel.pre_check_status==UserQueuePreCheckStatusEnum.PENDING, UserQueueModel.status==UserQueueStatusEnum.ARRIVED) 
    list1 = query.filter(
            or_(UserQueueModel.status == UserQueueStatusEnum.ARRIVED,
                UserQueueModel.status == UserQueueStatusEnum.CHECKIN,
                UserQueueModel.status == UserQueueStatusEnum.FREEZED,
                UserQueueModel.status == UserQueueStatusEnum.HOLD
                )).order_by(
                    asc(UserQueueModel.freezed_at),
        text("user_queue.status='FREEZED' desc"),
        desc((QueueModel.avg_procedure_time*QueueWeightageActionModel.weightage)+(func.extract("epoch",
             (func.now() - UserQueueModel.order_by_date))/60)),
        asc(UserQueueModel.created_at)).group_by(UserQueueModel.id, QueueWeightageActionModel.weightage, QueueModel.avg_procedure_time)
    logger.info(list1)
    return list1.all()


def get_current_user_queue(db: Session, queue_id: int):
    today = date.today()
    list1 = db.query(UserQueueModel).join(UserQueueModel.queue_weightage_action).join(UserQueueModel.queue).filter(
        UserQueueModel.queue_id == queue_id).filter(UserQueueModel.date == today).filter(
        UserQueueModel.status == UserQueueStatusEnum.ENTRY,
    ).order_by(
        asc(UserQueueModel.freezed_at),
        text("user_queue.status='FREEZED' desc"),
        desc((QueueWeightageActionModel.weightage*QueueModel.avg_procedure_time)+(func.extract("epoch",
             (func.now() - UserQueueModel.order_by_date))/60)),
        asc(UserQueueModel.created_at)).group_by(UserQueueModel.id, QueueWeightageActionModel.weightage).all()
    return list1

def get_queue_counter(db:Session, queue_id:int, counter):
    try:
        query= db.query(QueueCounterModel).filter(QueueCounterModel.queue_id==queue_id).filter(
            QueueCounterModel.status==QueueCounterStatusEnum.UNALLOTED, QueueCounterModel.counter_status == StatusEnum.ACTIVE)
        if counter is not None:
            query=query.filter(QueueCounterModel.id==counter)
        counter=query.order_by(
                asc(QueueCounterModel.priority)).first()
        if counter==None:
            return  None
        else:
            return counter
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error in queue counters")

def get_queue_counter_freeze(db:Session, queue:QueueModel, user_queue: str, condition, counter_id):
    try:
        query_1= db.query(QueueCounterModel).filter(QueueCounterModel.queue_id==queue.id)
        freezed_count=0
        # if condition =="FREEZED":
        #     query_1=query_1.filter(QueueCounterModel.status==QueueCounterStatusEnum.ALLOTED)
        if condition == "ENTRY" or condition == "ENTRY_ACTIVE":
            if condition == "ENTRY":
                freezed_count=-1
            query_1=query_1.filter(QueueCounterModel.status==QueueCounterStatusEnum.UNALLOTED)
            count=case(
            [
                (
                    QueueCounterModel.status==QueueCounterStatusEnum.ALLOTED, True
                )
            ],else_=(QueueCounterModel.upcoming_capacity-QueueCounterModel.freeze_count>=0)
        )
            query_1=query_1.filter(
                count
            )
        if counter_id is not None:
            query_1=query_1.filter(QueueCounterModel.id==counter_id)
        if condition == "FREEZED" or condition == "ENTRY_ACTIVE" or condition is None or counter_id is None:
            query_1=query_1.filter(
            QueueCounterModel.counter_status == StatusEnum.ACTIVE)
        counter=query_1.order_by(desc(QueueCounterModel.status),desc(QueueCounterModel.upcoming_capacity-QueueCounterModel.freeze_count),asc(QueueCounterModel.priority)).first()
        logger.info(counter)
        status=None
        start_time= None
        freezed_at = None
        allocate_counter_at = None
        if counter==None:
            return  None, None
        else:                                        
            if counter.status==QueueCounterStatusEnum.ALLOTED or (condition!="ENTRY" and condition!="ENTRY_ACTIVE" and queue.queue_type==QueueTypeEnum.SEMIAUTO):
                count= db.query(QueueCounterModel).filter(QueueCounterModel.id==counter.id).filter(QueueCounterModel.upcoming_capacity-QueueCounterModel.freeze_count>0).update({
                    QueueCounterModel.freeze_count: QueueCounterModel.freeze_count+1
                },synchronize_session="fetch")
                if count>0:
                    phone_number = user_queue.user.phone_number
                    user_name = user_queue.user.name
                    # tmp_name = get_template_name(db, SMSTypeEnum.WHATSAPP.name,EventCodeEnum.NEXT_TOKEN_CALL.name)      
                    # params = [{"type":"text","text":user_name},
                    #         {"type":"text","text":queue.service_type}]
                    # send_msg = send_whatsapp_msg(db, tmp_name, phone_number, params,None)
                    status= UserQueueStatusEnum.FREEZED
                    if queue.allocate_counter_at ==UserQueueStatusEnum.FREEZED:
                        counter_id=counter.id
                        allocate_counter_at=UserQueueStatusEnum.FREEZED
                    freezed_at= func.now()
                else:
                    return None, None
                # user_queue.counter=counter.id
            else:
                value=db.query(QueueCounterModel).filter(QueueCounterModel.id==counter.id).filter(QueueCounterModel.status==QueueCounterStatusEnum.UNALLOTED).update({
                    QueueCounterModel.status: QueueCounterStatusEnum.ALLOTED,
                    QueueCounterModel.freeze_count:QueueCounterModel.freeze_count+freezed_count
                },synchronize_session="fetch")
                logger.info("Allocate Queue counter")
                status = UserQueueStatusEnum.ENTRY
                phone_number = user_queue.user.phone_number
                user_name = user_queue.user.name
                # tmp_name = get_template_name(db, SMSTypeEnum.WHATSAPP.name,EventCodeEnum.SERVING_TOKEN_CALL.name)      
                # params = [{"type":"text","text":user_name},
                #         {"type":"text","text":counter.counter_name}]
                # send_msg = send_whatsapp_msg(db, tmp_name, phone_number, params,None)
                if user_queue.counter== None:
                    if queue.allocate_counter_at ==UserQueueStatusEnum.FREEZED or queue.allocate_counter_at ==UserQueueStatusEnum.ENTRY:
                        counter_id=counter.id
                        allocate_counter_at=UserQueueStatusEnum.ENTRY
                    # user_queue.counter=counter.id
                start_time=func.now()
            queue_step_id,location_id = add_user_queue_step(db,None,user_queue.id,"entry or freeze", "CHECKIN",status,queue, None,None,None, None)
            db.query(UserQueueModel).filter(UserQueueModel.id==user_queue.id).update(
                {
                    UserQueueModel.status:status,
                    UserQueueModel.start_time:UserQueueModel.start_time if start_time is None else start_time,
                    UserQueueModel.counter:counter_id,
                    UserQueueModel.freezed_at: UserQueueModel.freezed_at if freezed_at is None else freezed_at,
                    UserQueueModel.allocate_counter_at : UserQueueModel.allocate_counter_at if allocate_counter_at is None else allocate_counter_at,
                    UserQueueModel.queue_step_id: queue_step_id,
                    UserQueueModel.location_id: location_id,
                    UserQueueModel.step_start_time: func.now(),
                    UserQueueModel.remarks: None,
                    UserQueueModel.next_step_start_time: None,
                },synchronize_session="fetch"
            )
            return counter, status
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error in queue counters")
    
def arrived_msg(db:Session,queue: QueueModel,user_queue_id,phone_number,status,counter):
    try:
        query=db.query(UserQueueModel).join(UserQueueModel.queue_weightage_action).join(UserQueueModel.queue).filter(UserQueueModel.date == date.today())
        query=query.filter(UserQueueModel.queue_id == queue.id)
        query=query.filter(or_(UserQueueModel.counter==None, UserQueueModel.counter_obj.has(counter_status=StatusEnum.ACTIVE))) 
        list1 = query.filter(
                or_(UserQueueModel.status == UserQueueStatusEnum.ARRIVED,
                    UserQueueModel.status == UserQueueStatusEnum.CHECKIN,
                    UserQueueModel.status == UserQueueStatusEnum.HOLD,
                    UserQueueModel.status == UserQueueStatusEnum.FREEZED,
                    UserQueueModel.status == UserQueueStatusEnum.ENTRY)).order_by(
            text("user_queue.status='FREEZED' desc"),
            asc(UserQueueModel.freezed_at),
            desc((QueueModel.avg_procedure_time*QueueWeightageActionModel.weightage)+(func.extract("epoch",
                (func.now() - UserQueueModel.order_by_date))/60)),
            asc(UserQueueModel.created_at)).group_by(UserQueueModel.id, QueueWeightageActionModel.weightage,QueueModel.avg_procedure_time).all()
        status_queues = {
            status: [q for q in list1 if q.status == status]
            for status in [UserQueueStatusEnum.FREEZED, UserQueueStatusEnum.ARRIVED]
        }
        logger.info(status_queues)
        people_ahead = 0
        text_msg = "remain seated and wait for your turn."
        user_queue = db.query(UserQueueModel).filter(UserQueueModel.id == user_queue_id).order_by(UserQueueModel.created_at.desc()).first()
        if user_queue:
            if user_queue.vitals_datetime is None and "VITALS" in queue.assignment:
                text_msg = "Proceed to Vital Counter"
            elif user_queue.phy_ass_datetime is None and "PHYSICIANASSISTANT" in queue.assignment:
                text_msg = "Proceed to Physician Assistant Counter"
            else:
                logger.info(status)
                if status in status_queues:
                    queue_list = status_queues[status]
                    logger.info(queue_list)
                    index = next((i for i, q in enumerate(queue_list) if q.id == user_queue_id), -1)
                    logger.info(index)
                    if index >= 0:
                        if status == UserQueueStatusEnum.FREEZED:
                            people_ahead = index
                            if people_ahead == 0 or people_ahead == 1:
                                text_msg = "note, you are next in line."
                            else:
                                text_msg = f'note, you are {get_ordinal(people_ahead)} in line.'
                        elif status == UserQueueStatusEnum.ARRIVED:
                            index = 1
                            people_ahead = index + (index if index in [1, 2] else 3)
                            if queue.capacity >0:
                                index + (len(status_queues.get(UserQueueStatusEnum.FREEZED, []))/queue.capacity)
                            text_msg = f'note, you are {get_ordinal(people_ahead)} in line.'
                    elif status == UserQueueStatusEnum.ARRIVED:
                        index = index + 1
                        people_ahead = index + (index if index in [1, 2] else 3)
                        if queue.capacity >0:
                            index + (len(status_queues.get(UserQueueStatusEnum.FREEZED, []))/queue.capacity)
                        text_msg = f'note, you are {get_ordinal(people_ahead)} in line.'
                elif status == UserQueueStatusEnum.ENTRY:
                    counter_name = db.query(QueueCounterModel.counter_name).filter(
                        QueueCounterModel.id == counter
                    ).scalar()
                    text_msg = f"kindly proceed to the {counter_name}"
        # tmp_name = get_template_name(db, SMSTypeEnum.WHATSAPP.name,EventCodeEnum.PATIENT_ARRIVAL_CONFIRMATION.name)
        
        # params = [{"type":"text","text":f"Tower {queue.cluster.tower}->Floor {queue.cluster.floor}->Location {queue.cluster.cluster}"},
        #         {"type":"text","text":text_msg}]
        # send_msg = send_whatsapp_msg(db, tmp_name, phone_number, params,None)
    except Exception as e:
        logger.exception(e)
        pass
   
def get_opt_queue(db: Session, services, token_id):
    if len(services)==0:
        return None
    services1= db.scalars(db.query(UserServiceModel.service_id).join(UserServicePrerequisiteModel,UserServiceModel.id==UserServicePrerequisiteModel.user_service_id).filter(UserServiceModel.token_id==token_id,or_(UserServicePrerequisiteModel.status==UserPreReqStatusEnum.HOLD,UserServicePrerequisiteModel.status==UserPreReqStatusEnum.WAITING))).all()
    queue= db.query(QueueModel).join(QueueModel.services).filter(
                ServiceModel.id.in_(services)).filter(ServiceModel.id.not_in(services1)).filter(
                    QueueModel.status == StatusEnum.ACTIVE).order_by(
                        queue_query_filter).group_by(
                        QueueModel.id).first()
    logger.info("total Queues order")
    total_queues=db.query(QueueModel).join(QueueModel.services).filter(
                ServiceModel.id.in_(services)).filter(
                    QueueModel.status == StatusEnum.ACTIVE).order_by(
                        queue_query_filter).group_by(
                        QueueModel.id).all()
    # logger.info("selected Queue"+queue.queue_name)
    for queue1 in total_queues:
        logger.info("Queue Name: "+queue1.queue_name)
        logger.info("Estimated Time (min): "+
            str(queue1.deviation_rate * queue1.avg_procedure_time*(queue1.total_count-(
                                            (queue1.cancelled_count+queue1.completed_count)))/queue1.capacity)
        )
        logger.info("Queue Capacity: "+str(queue1.capacity))
        logger.info("Queue Length: "+str(queue1.total_count-(
                                            (queue1.cancelled_count+queue1.completed_count))))
        pre_con= len(set(list(map(lambda x: x.id,queue1.services))).intersection(set(services1)))
        logger.info({"Pre Requisites : YES" if pre_con>0 else "Pre Requisites : NO"})
        # logger.info(f"pre requesite condition: {"Yes" if pre_con>0 else "No"}")
    logger.info("Excluded services")
    logger.info(services1)
    
    if queue is None:
        services1= db.scalars(db.query(UserServiceModel.service_id).join(UserServicePrerequisiteModel,UserServiceModel.id==UserServicePrerequisiteModel.user_service_id).filter(UserServiceModel.token_id==token_id,UserServicePrerequisiteModel.status==UserPreReqStatusEnum.HOLD)).all()
        logger.info(services1)
        queue= db.query(QueueModel).join(QueueModel.services).filter(
                    ServiceModel.id.in_(services)).filter(ServiceModel.id.not_in(services1)).filter(
                        QueueModel.status == StatusEnum.ACTIVE).order_by(queue_query_filter).group_by(
                            QueueModel.id
                            ).first()
    return queue, sorted(set(services)-set(services1))

def queue_query_filter():
    return asc((func.min(QueueModel.deviation_rate *
                                        QueueModel.avg_procedure_time*(QueueModel.total_count-(
                                            (QueueModel.cancelled_count+QueueModel.completed_count)))/QueueModel.capacity)+QueueModel.buffer_time))
            

def add_user_queue(db: Session, queue_id, qr_details: QRDetail,staff_user:Optional[int]=None):
    with RedisLock(client_lock, "queue_"+str(queue_id), blocking_timeout=180):
        try:
            logger.info(f"queue_id:{queue_id}")
            logger.info(qr_details)
            queue = db.query(QueueModel).filter(
                QueueModel.id == queue_id,QueueModel.status==StatusEnum.ACTIVE).one_or_none()
            if queue is None:
                raise MutationError("Queue is inactive")
            # user_token = db.query(UserTokenModel).filter(
            #     UserTokenModel.id == qr_details.token_id).one_or_none()
            if qr_details.token_id is not None and qr_details.token_id !="":
                token_id = int(qr_details.token_id)
            elif qr_details.uhid is not None and qr_details.uhid !="":
                token = db.query(UserTokenModel.id).join(UserTokenModel.user).join(UserTokenModel.user_services).filter(or_(UserServiceModel.status==ServiceStatusEnum.PENDING,UserServiceModel.status==ServiceStatusEnum.ON_PROGRESS)).filter(UserModel.umr_no == qr_details.uhid).order_by(desc(UserTokenModel.created_at)).first()
                if token is None:
                    raise MutationError("Patient data is not available")
                token_id= token.id
            user_queue: UserQueueModel = db.query(UserQueueModel).filter(
                UserQueueModel.token_id == token_id).filter(UserQueueModel.status !=UserQueueStatusEnum.EXIT, UserQueueModel.status !=UserQueueStatusEnum.PURGED).one_or_none()
            logger.info(user_queue)
            flag=False
            if user_queue is None:
                # raise MutationError("Invalid action")
                flag=True
            elif user_queue.queue_id != queue_id:
                purge_exit_user_queue(db, user_queue,staff_user)
                flag=True
            if flag:
                all_services_ids, user_id, token_id, phone_number,token_no = get_all_services(db, token.id, queue_id)
                if len(all_services_ids)>0:
                    user_queue, time = checkin_user_into_queue(db, queue, user_id[0], token_no[0], token_id[0], all_services_ids,phone_number[0], None,staff_user)
                else:
                    raise MutationError("Patient is not eligible for this queue")
            if user_queue.status == UserQueueStatusEnum.CHECKIN or user_queue.status == UserQueueStatusEnum.HOLD:
                pass
            elif user_queue.queue_id == queue_id:
                if user_queue.status==UserQueueStatusEnum.EXIT:
                    raise MutationError("Patient already completed the services")
                else:
                    raise MutationError("Patient already arrived")
            arrived_at = None
            start_time = None
            freezed_at = None
            assignment_to_field = {
                "VITALS": user_queue.vitals_datetime,
                "PHYSICIANASSISTANT": user_queue.phy_ass_datetime,
                "PRECHECK": user_queue.phlebotomy_precheck_datetime,
            }
            if all(assignment_to_field.get(task) for task in user_queue.queue.assignment):
                status,queue_counter = freeze_user(db,queue, user_queue,None,None)
                logger.info(status)
                if status != None:
                    if status==UserQueueStatusEnum.FREEZED:
                        db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                                        {QueueModel.freezed_count: QueueModel.freezed_count+1},
                                        synchronize_session="fetch",
                                )
                        freezed_at = func.now()
                    elif status==UserQueueStatusEnum.ENTRY:
                        db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                            {QueueModel.ongoing_count: QueueModel.ongoing_count+1},
                            synchronize_session="fetch",
                        )
                        # db.query(QueueCounterModel).filter(QueueCounterModel.id==queue_counter.id).update(
                        #     {QueueCounterModel.status: QueueCounterStatusEnum.ALLOTED},
                        #     synchronize_session="fetch",
                        # )
                        freezed_at = func.now()
                        start_time = func.now()
                else:
                    status = UserQueueStatusEnum.ARRIVED
            else:
                status = UserQueueStatusEnum.ARRIVED
            queue_step_id= None
            location_id = None
            step_start_time = None
            if user_queue.status==UserQueueStatusEnum.ARRIVED:
                queue_step_id, location_id = add_user_queue_step(db,None,user_queue.id,"arrived", "CHECKIN",status,queue, None,None,None,staff_user)
            if status==UserQueueStatusEnum.ARRIVED:
                arrived_msg(db,queue,user_queue.id,user_queue.user.phone_number,status,user_queue.counter)
                step_start_time = func.now()
            db.query(UserQueueModel).filter(UserQueueModel.id==user_queue.id).update(
                    {UserQueueModel.status: status,
                     UserQueueModel.freezed_at:freezed_at,
                     UserQueueModel.start_time: start_time,
                     UserQueueModel.arrived_at:func.now(),
                     UserQueueModel.updated_by:staff_user,
                     UserQueueModel.queue_step_id:queue_step_id,
                     UserQueueModel.location_id:location_id,
                     UserQueueModel.step_start_time:step_start_time,
                     UserQueueModel.next_step_start_time:None,
                     UserQueueModel.prerequisites_conditions:qr_details.prerequisites_conditions
                     },
               synchronize_session="fetch",
            )
            update_rel_device_queue(db,queue_id, True)
            db.commit()
            return get_user_queue(db, user_queue.queue_id, None, None)
        except MutationError as ex:
            logger.exception(ex)
            raise MutationError(ex.message)
        except Exception as ex:
            logger.exception("Exception in add user queue")


def get_clusters(db: Session):
    return db.query(ClusterModel).all()


def update_user_queue(db: Session, user_queue_id, queue_weightage_action_id,queue_weightage_action,staff_user, service_status, remarks=None):
    try:
        logger.info(queue_weightage_action)
        if queue_weightage_action_id is not None:
            action:QueueWeightageActionModel=db.query(QueueWeightageActionModel).filter(QueueWeightageActionModel.id==queue_weightage_action_id).one()
        elif queue_weightage_action is not None and queue_weightage_action!= '':
            action:QueueWeightageActionModel=db.query(QueueWeightageActionModel).filter(QueueWeightageActionModel.code==queue_weightage_action).one()
        
        logger.info(f"user_queue_id:{user_queue_id}")
        user_queue = db.query(UserQueueModel).filter(
            UserQueueModel.id == user_queue_id).one()
        logger.info(action.code)
        queue = db.query(QueueModel).filter(QueueModel.id == user_queue.queue_id).one()
        
        if user_queue is None:
            raise MutationError(f"Provided user is not in queue")
        if user_queue.status==UserQueueStatusEnum.EXIT:
            raise MutationError("Patient exited from queue")  
        elif user_queue.status==UserQueueStatusEnum.PURGED:
            raise MutationError("Patient service is cancelled")  
        if user_queue.status==UserQueueStatusEnum.FREEZED and(action.code!=UserQueueStatusEnum.PURGED.name and action.code!=UserQueueStatusEnum.HOLD.name):
            raise MutationError("Invalid Action")  
        user_queue_status= user_queue.status
        user_queue_counter= user_queue.counter
        if user_queue.status==UserQueueStatusEnum.HOLD:
            if action.code=='PURGED':
                user_queue_status= UserQueueStatusEnum.PURGED
                queue.cancelled_count = queue.cancelled_count+1
            else:
                user_queue_status= (UserQueueStatusEnum.ARRIVED if user_queue.arrived_at is not None else UserQueueStatusEnum.CHECKIN)
                db.query(UserQueueModel).filter(UserQueueModel.id==user_queue.id).update(
                    {
                        UserQueueModel.status: user_queue_status,
                    },synchronize_session="fetch"
                )
                if user_queue.pre_check_status != UserQueuePreCheckStatusEnum.PENDING and user_queue.arrived_at is not None and (queue.queue_type ==QueueTypeEnum.AUTO):
                    status,queue_counter = freeze_user(db,queue, user_queue,None,None)
                    logger.info(status)
                    if status != None:
                        if status==UserQueueStatusEnum.FREEZED:
                            db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                                            {QueueModel.freezed_count: QueueModel.freezed_count+1},
                                            synchronize_session="fetch",
                                    )
                        elif status==UserQueueStatusEnum.ENTRY:
                            db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                                {QueueModel.ongoing_count: QueueModel.ongoing_count+1},
                                synchronize_session="fetch",
                            )
        if action.code=='HOLD':
            if user_queue.status==UserQueueStatusEnum.FREEZED:
                freezed_count=-1
                db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                        {QueueModel.freezed_count: QueueModel.freezed_count+freezed_count},synchronize_session="fetch",)
                db.query(QueueCounterModel).filter(QueueCounterModel.id==user_queue.counter).update({
                    QueueCounterModel.freeze_count: QueueCounterModel.freeze_count+freezed_count},synchronize_session="fetch")
                user_queue_counter=None
                update_arrived_user(db,queue,user_queue.counter)
            if user_queue.status == UserQueueStatusEnum.ENTRY:
                counter=user_queue.counter
                user_queue_counter=None
                update_next_users(db,queue,counter,0)
            user_queue_status=UserQueueStatusEnum.HOLD
        elif action.code=='PURGED':
            if user_queue.status==UserQueueStatusEnum.FREEZED:
                cancelled_count =1
                freezed_count = -1
                db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                            {QueueModel.freezed_count: QueueModel.freezed_count+freezed_count,QueueModel.cancelled_count: QueueModel.cancelled_count+cancelled_count},
                            synchronize_session="fetch",)
                db.query(QueueCounterModel).filter(QueueCounterModel.id==user_queue.counter).update({
                    QueueCounterModel.freeze_count: QueueCounterModel.freeze_count+freezed_count},synchronize_session="fetch")
                user_queue_counter=None
                update_arrived_user(db, queue,None)
            elif user_queue.status == UserQueueStatusEnum.ENTRY:
                cancelled_count =1
                counter=user_queue.counter
                user_queue_counter=None
                update_next_users(db,queue,counter,cancelled_count)
            elif user_queue.status==UserQueueStatusEnum.ARRIVED or user_queue.status==UserQueueStatusEnum.CHECKIN:
                db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                            {QueueModel.cancelled_count: QueueModel.cancelled_count+1},
                            synchronize_session="fetch",)
            db.query(UserServiceModel).filter(UserServiceModel.id.in_(db.query(RelUserServiceQueueModel.user_service_id).filter(RelUserServiceQueueModel.user_queue_id==user_queue.id))).update(
            {UserServiceModel.status: service_status},
                synchronize_session="fetch",
            )
            db.query(RelUserServiceQueueModel).filter(RelUserServiceQueueModel.user_queue_id==user_queue.id).update(
            {RelUserServiceQueueModel.status: ServiceStatusEnum.CANCELLED},
                synchronize_session="fetch",
            )
            user_queue_status=UserQueueStatusEnum.PURGED
        location_id= user_queue.location_id
        next_location_id= user_queue.next_location_id
        weightage_id= user_queue.weightage_id
        if action.code=='PURGED' or action.code=='HOLD':
            location_id= None
            next_location_id= None
            if user_queue.tag_id is not None:
                deleting_tag = delete_tag(user_queue.tag_id,user_queue.tag.code,user_queue.user.umr_no)
        else:
            weightage_id = action.id
        db.query(UserQueueModel).filter(UserQueueModel.id==user_queue.id).update(
            {
                UserQueueModel.weightage_id: weightage_id,
                UserQueueModel.updated_by: staff_user,
                UserQueueModel.remarks: remarks,
                UserQueueModel.location_id: location_id,
                UserQueueModel.next_location_id: next_location_id,
                UserQueueModel.status: user_queue_status,
                UserQueueModel.counter: user_queue_counter,
            },synchronize_session="fetch"
        )
        update_rel_device_queue(db, queue.id, True)
        db.commit()
        return get_user_queue(db, user_queue.queue_id, None, None)
    except MutationError as e:
        logger.exception("Exception in update user queue")
        raise MutationError(e.message)
    except Exception as ex:
        logger.exception("Exception in update user queue")
        raise MutationError("Error occured while updating user queue")


def update_user_queue_timestamp(db: Session, user_queue_id, update_type, staff_user):
    try:
        logger.info(f"user_queue_id:{user_queue_id}")
        update_pre_check_status(db, user_queue_id, None, update_type, staff_user, None)
        # user_queue = db.query(UserQueueModel).filter(
        #     UserQueueModel.id == user_queue_id).one()
        # if user_queue is None:
        #     raise MutationError(f"Provided user is not in queue")
        # if update_type == "VITALS":
        #     user_queue.vitals_datetime = func.now()
        # else:
        #     user_queue.phy_ass_datetime = func.now()
        # arrived_msg(db,user_queue.queue,user_queue.id,user_queue.user.phone_number,user_queue.status,user_queue.counter)
        db.commit()
    except MutationError as e:
        logger.exception("Exception in update user queue")
        raise MutationError(e.message)
    except Exception as ex:
        logger.exception("Exception in update user queue")
        raise MutationError("Error occured while updating user queue")
def update_next_users(db: Session, queue, counter,cancelled_count):
    try:
        ongoing_count=-1
        db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                {QueueModel.ongoing_count: QueueModel.ongoing_count+ongoing_count,QueueModel.cancelled_count: QueueModel.cancelled_count+cancelled_count},
                synchronize_session="fetch",)
        db.query(QueueCounterModel).filter(QueueCounterModel.id==counter).update({
            QueueCounterModel.status: QueueCounterStatusEnum.UNALLOTED},synchronize_session="fetch")
        ongoing_count, freezed_count=entry_or_freeze_next_users(db,queue,counter)
        db.query(QueueModel).filter(QueueModel.id==queue.id).update(
            {QueueModel.ongoing_count: QueueModel.ongoing_count+ongoing_count,
                QueueModel.freezed_count: QueueModel.freezed_count+freezed_count
                },
            synchronize_session="fetch",
        )
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while entring next users")
    
def update_arrived_user(db: Session, queue,counter):
    try:
        freeze_user_queue = db.query(UserQueueModel).join(UserQueueModel.queue).join(UserQueueModel.queue_weightage_action).filter(
                UserQueueModel.queue_id == queue.id).filter(UserQueueModel.date == date.today()).filter(UserQueueModel.status == UserQueueStatusEnum.ARRIVED).filter(or_(
                    UserQueueModel.pre_check_status != UserQueuePreCheckStatusEnum.PENDING,UserQueueModel.pre_check_status == None)).order_by(
                text("user_queue.status='FREEZED' desc"),
                desc((QueueModel.avg_procedure_time*QueueWeightageActionModel.weightage)+(func.extract("epoch",
                    (func.now() - UserQueueModel.order_by_date))/60)),
                asc(UserQueueModel.created_at)).first()
        logger.info(freeze_user_queue)
        if freeze_user_queue is not None :
            status,queue_counter = freeze_user(db,queue, freeze_user_queue,None,counter)
            logger.info(status)
            if status != None:
                if status==UserQueueStatusEnum.FREEZED:
                    db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                                    {QueueModel.freezed_count: QueueModel.freezed_count+1},
                                    synchronize_session="fetch",
                            )
                elif status==UserQueueStatusEnum.ENTRY:
                    db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                        {QueueModel.ongoing_count: QueueModel.ongoing_count+1},
                        synchronize_session="fetch",
                    )
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while updating freeze user")
    
def send_service_completion_msg(db:Session,user_queue:UserQueueModel, user_service_ids):
    try:
        send_to = user_queue.user.phone_number
        user_name = user_queue.user.name
        completed_status_as_int = case([
        (UserServiceModel.status == ServiceStatusEnum.COMPLETED, 1),
        ], else_=0)
        completed_token = db.query(
            UserTokenModel.id
        ).filter(UserTokenModel.id==user_queue.token_id).group_by(UserTokenModel.id).having(
            func.count(UserServiceModel.id) == func.sum(completed_status_as_int)
        ).join(UserTokenModel.user_services).first()
        service = db.query(ServiceModel.type, ServiceModel.reports_collection, ServiceModel.reports_completion_time, ServiceModel.source).join(UserServiceModel.service).filter(
                UserServiceModel.user_id == user_queue.user.id).filter(UserServiceModel.id.in_(user_service_ids), UserServiceModel.service_id==ServiceModel.id).first()
        # if user_queue.queue.service_type=='INVESTIGATIONS' and completed_token is not None:
        #     hsp_phone_number= get_whatsapp_details(db)
        #     tmp_name = get_template_name(db, SMSTypeEnum.WHATSAPP.name,EventCodeEnum.TESTS_COMPLETION_REPORT_COLLECTION.name)      
        #     params = [{"type":"text","text":user_name},
        #               {"type":"text","text":"tests"},
        #               {"type":"text","text":"Reports"},
        #               {"type":"text","text":f"{service.reports_completion_time} hours" if service.reports_completion_time else " "},
        #               {"type":"text","text":f"{service.source} at {service.reports_collection}" if service.source and service.reports_collection else " "},
        #               {"type":"text","text":f"{hsp_phone_number[1]}"}]
        #     send_msg = send_whatsapp_msg(db, tmp_name, send_to, params,None)
        # else:
        #     tmp_name = get_template_name(db, SMSTypeEnum.WHATSAPP.name,EventCodeEnum.SERVICE_COMPLETED_CONFIRMATION.name)      
        #     params = [{"type":"text","text":user_name},{"type":"text","text":service.type},{"type":"text","text":"1800-000-0000"}]
        #     send_msg = send_whatsapp_msg(db, tmp_name, send_to, params,None)
    except Exception as e:
        logger.exception(e)
        pass
def purge_exit_user_queue(db: Session, user_queue:UserQueueModel,staff_user):
    try:
        queue = db.query(QueueModel).filter(QueueModel.id == user_queue.queue_id).one()
        status=ServiceStatusEnum.PENDING
        logger.info(user_queue.status)
        user_queue_status=user_queue.status
        rel_user_queue_status=ServiceStatusEnum.CANCELLED
        if user_queue.status == UserQueueStatusEnum.ENTRY:
            user_service_ids = db.scalars(db.query(RelUserServiceQueueModel.user_service_id).filter(
                RelUserServiceQueueModel.user_queue_id == user_queue.id).filter(RelUserServiceQueueModel.status==ServiceStatusEnum.PENDING)).all()
            # db.query(UserServiceModel).filter(UserServiceModel.id.in_(user_service_ids)).update({
            #     "status": ServiceStatusEnum.COMPLETED.name
            # }, synchronize_session="fetch")
            db.query(UserServicePrerequisiteModel).filter(UserServicePrerequisiteModel.pre_req_user_service_id.in_(user_service_ids)).delete()
            # user_queue.status = UserQueueStatusEnum.EXIT
            # user_queue.end_time = func.now()
            db.query(UserQueueModel).filter(UserQueueModel.id==user_queue.id).update({
                UserQueueModel.status: UserQueueStatusEnum.EXIT,
                UserQueueModel.end_time: func.now(),
                UserQueueModel.force_exit: True,
                UserQueueModel.updated_by: staff_user,
            },synchronize_session="fetch")
            db.query(QueueCounterModel).filter(QueueCounterModel.id == user_queue.counter).update({
                QueueCounterModel.status: QueueCounterStatusEnum.UNALLOTED
            },synchronize_session="fetch",)
            # queue.completed_count = queue.completed_count+1
            # queue.ongoing_count = queue.ongoing_count-1
            # user_queue.force_exit = True
            send_service_completion_msg(db,user_queue, user_service_ids)
            # db.commit()
            # db.refresh(queue)
            db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                {QueueModel.ongoing_count: QueueModel.ongoing_count-1,
                 QueueModel.completed_count: QueueModel.completed_count+1,
                    },
                synchronize_session="fetch",
            )
            ongoing_count, freezed_count=entry_or_freeze_next_users(db, queue,user_queue.counter)
            # queue.ongoing_count=queue.ongoing_count+ongoing_count-1
            # queue.freezed_count=queue.freezed_count+freezed_count
            db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                {QueueModel.ongoing_count: QueueModel.ongoing_count+ongoing_count,
                    QueueModel.freezed_count: QueueModel.freezed_count+freezed_count
                    },
                synchronize_session="fetch",
            )
            status=ServiceStatusEnum.COMPLETED
            rel_user_queue_status=ServiceStatusEnum.CANCELLED
        elif user_queue.status==UserQueueStatusEnum.FREEZED:
            # user_queue.status=UserQueueStatusEnum.PURGED
            cancelled_count = 1
            freezed_count = -1
            db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                        {QueueModel.freezed_count: QueueModel.freezed_count+freezed_count,
                         QueueModel.cancelled_count: QueueModel.cancelled_count+cancelled_count},
                        synchronize_session="fetch",)
            db.query(QueueCounterModel).filter(QueueCounterModel.id==user_queue.counter).update({
                    QueueCounterModel.freeze_count: QueueCounterModel.freeze_count+freezed_count},synchronize_session="fetch")
            # user_queue.counter=None
            db.query(UserQueueModel).filter(UserQueueModel.id==user_queue.id).update({
                UserQueueModel.status: UserQueueStatusEnum.PURGED,
                UserQueueModel.counter: None,
                UserQueueModel.updated_by: staff_user,
            },synchronize_session="fetch")
            update_arrived_user(db,queue,user_queue.counter)
        elif user_queue_status in [
                UserQueueStatusEnum.ARRIVED,
                UserQueueStatusEnum.CHECKIN,
                UserQueueStatusEnum.HOLD,
            ]:
            # user_queue.status=UserQueueStatusEnum.PURGED
            logger.info(user_queue.status)
            db.query(UserQueueModel).filter(UserQueueModel.id==user_queue.id).update({
                UserQueueModel.status: UserQueueStatusEnum.PURGED,
                UserQueueModel.updated_by: staff_user,
            },synchronize_session="fetch")
            db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                        {QueueModel.cancelled_count: QueueModel.cancelled_count+1
                         },
                        synchronize_session="fetch",)
            # queue.cancelled_count = queue.cancelled_count+1
        # user_queue.updated_by=staff_user
        db.query(UserServiceModel).filter(UserServiceModel.id.in_(db.query(RelUserServiceQueueModel.user_service_id).filter(RelUserServiceQueueModel.status==ServiceStatusEnum.PENDING).filter(RelUserServiceQueueModel.user_queue_id==user_queue.id))).update(
            {UserServiceModel.status: status},
                synchronize_session="fetch",
        )
        db.query(RelUserServiceQueueModel).filter(RelUserServiceQueueModel.status==ServiceStatusEnum.PENDING).filter(RelUserServiceQueueModel.user_queue_id==user_queue.id).update(
            {RelUserServiceQueueModel.status: rel_user_queue_status},
                synchronize_session="fetch",
            )
    except MutationError as e:
        logger.exception(e)
        raise MutationError(e.message)
    except Exception as ex:
        logger.exception("Exception in add user queue")
        raise MutationError(ex)
        
def exit_user_queue(db: Session, queue_id: int, qr_details: QRDetail, force_exit=None,cmpltd_user_service_ids: Optional[List[int]]=None,staff_user:Optional[int]=None):
    with RedisLock(client_lock, "queue_"+str(queue_id), blocking_timeout=180):
        try:
            queue : QueueModel = db.query(QueueModel).filter(QueueModel.id == queue_id).one()
            user_token= None
            if queue.queue_type==QueueTypeEnum.SEMIAUTO:
                if qr_details.user_queue_id:
                   user_queue:UserQueueModel = db.query(UserQueueModel).filter(UserQueueModel.queue_id==queue_id).filter(UserQueueModel.id==qr_details.user_queue_id).filter(UserQueueModel.status==UserQueueStatusEnum.ENTRY).first()
                   if user_queue is None:
                       raise MutationError("Patient is not in serving")
                else:
                   user_queue:UserQueueModel = db.query(UserQueueModel).filter(UserQueueModel.queue_id==queue_id).filter(UserQueueModel.status==UserQueueStatusEnum.ENTRY).first()
                if user_queue is not None: 
                    user_token = db.query(UserTokenModel).filter(UserTokenModel.id == user_queue.token_id).one()
            else:
                user_token = db.query(UserTokenModel).filter(
                UserTokenModel.id == qr_details.token_id).one()
                user_queue = db.query(UserQueueModel).filter(UserQueueModel.queue_id == queue_id, UserQueueModel.token_id == qr_details.token_id, UserQueueModel.status == UserQueueStatusEnum.ENTRY).one_or_none()
            logger.info('EXIT')
            logger.info(queue_id)
            logger.info(cmpltd_user_service_ids)
            alloted_counter= None
            counter= None
            if user_queue is None :
                if queue.queue_type==QueueTypeEnum.SEMIAUTO: 
                    pass
                else:    
                    raise MutationError("Patient is not in serving")
            else:
                counter=user_queue.counter
                logger.info(f"counter:{counter}")
                user_service_ids = db.scalars(db.query(RelUserServiceQueueModel.user_service_id).filter(
                    RelUserServiceQueueModel.user_queue_id == user_queue.id).filter(RelUserServiceQueueModel.status==ServiceStatusEnum.PENDING)).all()
                pending_services = set(user_service_ids) - set(cmpltd_user_service_ids) if cmpltd_user_service_ids is not None else []
                completed_services = cmpltd_user_service_ids if cmpltd_user_service_ids is not None else user_service_ids
                db.query(UserServiceModel).filter(UserServiceModel.id.in_(pending_services)).update({
                    UserServiceModel.status: ServiceStatusEnum.PENDING
                }, synchronize_session="fetch")
                db.query(UserServiceModel).filter(UserServiceModel.id.in_(completed_services)).update({
                    UserServiceModel.status: ServiceStatusEnum.COMPLETED
                }, synchronize_session="fetch")
                get_sample_collection_type(db, completed_services, datetime.now().strftime('%d/%b/%Y %H:%M'))
                db.query(RelUserServiceQueueModel).filter(RelUserServiceQueueModel.user_service_id.in_(completed_services)).update({
                    RelUserServiceQueueModel.status: ServiceStatusEnum.COMPLETED
                }, synchronize_session="fetch",)
                db.query(RelUserServiceQueueModel).filter(RelUserServiceQueueModel.user_service_id.in_(pending_services)).update({
                    RelUserServiceQueueModel.status: ServiceStatusEnum.CANCELLED
                }, synchronize_session="fetch",)
                db.query(UserServicePrerequisiteModel).filter(UserServicePrerequisiteModel.pre_req_user_service_id.in_(user_service_ids)).delete()
                # user_queue.status = UserQueueStatusEnum.EXIT
                # user_queue.end_time = func.now()                
                queue_step_id= None
                if queue.queue_type==QueueTypeEnum.AUTO or queue.queue_type==QueueTypeEnum.SEMIAUTO:
                    if counter is not None:
                        db.query(QueueCounterModel).filter(QueueCounterModel.id == counter).update(
                            {
                                QueueCounterModel.status:QueueCounterStatusEnum.UNALLOTED
                            },synchronize_session="fetch",
                        )
                    else:
                        alloted_counter= db.query(QueueCounterModel).filter(QueueCounterModel.queue_id == user_queue.queue_id).filter(
                            QueueCounterModel.status==QueueCounterStatusEnum.ALLOTED
                        ).first()
                        db.query(QueueCounterModel).filter(QueueCounterModel.id == alloted_counter.id).update(
                            {
                                QueueCounterModel.status:QueueCounterStatusEnum.UNALLOTED
                            },synchronize_session="fetch",
                        )
                queue_step_id,queue_location_id = add_user_queue_step(db,None,user_queue.id,"Exited", "CHECKOUT",UserQueueStatusEnum.EXIT,queue, None,None,None,staff_user)
                db.query(UserQueueModel).filter(UserQueueModel.id==user_queue.id).update(
                    {
                        UserQueueModel.status:UserQueueStatusEnum.EXIT,
                        UserQueueModel.end_time: func.now(),
                        UserQueueModel.queue_step_id: queue_step_id,
                        UserQueueModel.force_exit: force_exit,
                        UserQueueModel.location_id: queue_location_id,
                        UserQueueModel.step_start_time: None,
                        UserQueueModel.next_step_start_time: func.now(),
                        UserQueueModel.updated_by: staff_user
                    },
                    synchronize_session="fetch",
                )
                pseudo_capacity = 0
                send_service_completion_msg(db,user_queue, user_service_ids)
                db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                    {QueueModel.ongoing_count: QueueModel.ongoing_count-1,QueueModel.pseudo_capacity: QueueModel.pseudo_capacity+pseudo_capacity,
                       QueueModel.completed_count: QueueModel.completed_count+1
                        },
                    synchronize_session="fetch",
                )
            if queue.queue_type==QueueTypeEnum.AUTO or queue.queue_type==QueueTypeEnum.SEMIAUTO:
                ongoing_count, freezed_count=entry_or_freeze_next_users(db, queue,counter)
                db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                    {QueueModel.ongoing_count: QueueModel.ongoing_count+ongoing_count,
                        QueueModel.freezed_count: QueueModel.freezed_count+freezed_count
                        },
                    synchronize_session="fetch",
                )
                if queue.queue_type==QueueTypeEnum.SEMIAUTO and user_queue is None and ongoing_count==0:
                    raise MutationError("No patients found")
            try:
                if user_token is not None:
                    services= db.scalars(db.query(UserServiceModel.service_id).filter(UserServiceModel.token_id==user_token.id).filter(UserServiceModel.status==ServiceStatusEnum.PENDING)).all()
                    if len(services)>0:
                        logger.info(services)
                        queue1, services=get_opt_queue(db,services,user_token.id)
                        if queue1 is not None:
                            user_queue, time = checkin_user_into_queue(db,queue1,user_token.user_id,user_token.token_no,user_token.id, services, user_queue.user.phone_number,None,None)
                            update_rel_device_queue(db,queue1.id, True)
            except Exception as ex:
                logger.exception("Error checking into other queue")
            update_rel_device_queue(db,queue.id, True)
            db.commit()
            return get_user_queue(db, queue.id, None, None)
        except MutationError as e:
            logger.exception(e)
            raise MutationError(e.message)
        except Exception as ex:
            logger.exception("Exception in Exit user queue")
            raise MutationError(ex)

def update_user_queue_service(db: Session, user_queue_id, user_service_ids,status:Optional[str]=None):
    try:
        user_queue=db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id).update({
                UserQueueModel.pre_check_status:UserQueuePreCheckStatusEnum.COMPLETED
            }, synchronize_session="fetch"
        )
        all_services=db.scalars(db.query(RelUserServiceQueueModel.user_service_id).filter(RelUserServiceQueueModel.user_queue_id==user_queue_id)).all()
        if len(set(all_services)-set(user_service_ids))>0:
            db.query(UserServiceModel).filter(UserServiceModel.id.in_(set(all_services)-set(user_service_ids))).update(
                {
                UserServiceModel.status: ServiceStatusEnum.PENDING
                }, synchronize_session="fetch"
            )
            db.query(RelUserServiceQueueModel).filter(RelUserServiceQueueModel.user_service_id.in_(set(all_services)-set(user_service_ids))).update(
            {RelUserServiceQueueModel.status: ServiceStatusEnum.CANCELLED},
                synchronize_session="fetch",
            )
            # db.query(RelUserServiceQueueModel).filter(RelUserServiceQueueModel.user_queue_id==user_queue_id).filter(RelUserServiceQueueModel.user_service_id.not_in(user_service_ids)).delete()
        user_queue=db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id).one()
        queue = db.query(QueueModel).filter(
                    QueueModel.id == user_queue.queue_id,QueueModel.status==StatusEnum.ACTIVE).one_or_none()
        status,queue_counter = freeze_user(db,queue, user_queue,None,None)
        logger.info(status)
        if status != None:
            if status==UserQueueStatusEnum.FREEZED:
                db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                                {QueueModel.freezed_count: QueueModel.freezed_count+1},
                                synchronize_session="fetch",
                        )
            elif status==UserQueueStatusEnum.ENTRY:
                db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                    {QueueModel.ongoing_count: QueueModel.ongoing_count+1},
                    synchronize_session="fetch",
                )
        db.commit()
        return get_user_queue(db, queue.id, None, None)
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while updating user queue")



def activate_or_deactivate_queue(db: Session, queue_id: int, status: str):
    try:
        if status == 'ACTIVE':
            db.query(QueueModel).filter(QueueModel.id == queue_id).update(
                {QueueModel.status: StatusEnum.ACTIVE}, synchronize_session="fetch")
            msg = "Queue Activated Sucessfully"
        else:
            db.query(QueueModel).filter(QueueModel.id == queue_id).update(
                {QueueModel.status: StatusEnum.INACTIVE}, synchronize_session="fetch",)
            msg = "Queue Deactivated Sucessfully"
        db.commit()
        return msg,get_queues(db)
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to Activate Queue")


def get_queue_weightage(db: Session,codes):
    query=db.query(QueueWeightageActionModel).filter(QueueWeightageActionModel.status == StatusEnum.ACTIVE)
    if len(codes)>0:
        query=query.filter(QueueWeightageActionModel.code.in_(codes))
    else:
        query=query.filter(and_(QueueWeightageActionModel.code != 'HOLD', QueueWeightageActionModel.code != 'PURGED'))
    query = query.order_by(case(
                [
                    (QueueWeightageActionModel.code == 'VIP', 0),
                    (QueueWeightageActionModel.code == 'PREGNANT_WOMEN', 1),
                    (QueueWeightageActionModel.code == 'WHC', 2),
                    (QueueWeightageActionModel.code == 'SENIOR_CITIZEN', 3),
                    (QueueWeightageActionModel.code == 'SPECIALLY_ABLED', 4),
                    (QueueWeightageActionModel.code == 'NORMAL', 5),
                    (QueueWeightageActionModel.code == 'HOLD', 6),
                    (QueueWeightageActionModel.code == 'PURGED', 7),
                ],
                else_=6,
            ))
    return query.all()

def get_prefix_token_case():
    return case(
        (
            UserQueueModel.user.has(is_platinum_user=True),
            "P-" + cast(UserQueueModel.token_no, String)
        ),
        (
            UserQueueModel.appointment_date_time != None,
            "A-" + cast(UserQueueModel.token_no, String)
        ),
        else_="W-" + cast(UserQueueModel.token_no, String)
    )

def get_staff_user_allocated_queues(db: Session, staff_user):
    logger.info(staff_user)
    prefixed_token_case = get_prefix_token_case()
    return db.query(
        QueueModel,
        func.count(case((UserQueueModel.status == UserQueueStatusEnum.ENTRY, 1))).label("entry_count"),
        func.count(case((or_(UserQueueModel.status == UserQueueStatusEnum.FREEZED, UserQueueModel.status == UserQueueStatusEnum.ARRIVED) , 1))).label("waiting_count"),
        func.count(case((or_(UserQueueModel.status == UserQueueStatusEnum.CHECKIN,UserQueueModel.status == UserQueueStatusEnum.HOLD), 1))).label("checkin_count"),
         func.array_agg(prefixed_token_case).filter(
        and_(UserQueueModel.status == UserQueueStatusEnum.FREEZED, UserQueueModel.token_no.isnot(None))
        ).label("freezed_tokens"),
        func.array_agg(prefixed_token_case).filter(
            and_(UserQueueModel.status == UserQueueStatusEnum.ENTRY, UserQueueModel.token_no.isnot(None))
        ).label("entry_tokens"),
    ).join(RelStaffUserQueueModel, QueueModel.id == RelStaffUserQueueModel.queue_id).outerjoin(
        UserQueueModel,
        and_(
            UserQueueModel.queue_id == QueueModel.id,
            UserQueueModel.status != UserQueueStatusEnum.EXIT,
            UserQueueModel.status != UserQueueStatusEnum.PURGED,
        ),
    ).filter(RelStaffUserQueueModel.staff_user_id == staff_user).filter(QueueModel.deleted_at == None).group_by(QueueModel.id).order_by(QueueModel.queue_name).all()


def get_queues_details(db: Session, queue_code:str):
    prefixed_token_case = get_prefix_token_case()
    return db.query(
        QueueModel,
        func.count(case((UserQueueModel.status == UserQueueStatusEnum.ENTRY, 1))).label("entry_count"),
        func.count(case((or_(UserQueueModel.status == UserQueueStatusEnum.FREEZED, UserQueueModel.status == UserQueueStatusEnum.ARRIVED) , 1))).label("waiting_count"),
        func.count(case((or_(UserQueueModel.status == UserQueueStatusEnum.CHECKIN,UserQueueModel.status == UserQueueStatusEnum.HOLD), 1))).label("checkin_count"),
         func.array_agg(prefixed_token_case).filter(
        and_(UserQueueModel.status == UserQueueStatusEnum.FREEZED, UserQueueModel.token_no.isnot(None))
        ).label("freezed_tokens"),
        func.array_agg(prefixed_token_case).filter(
            and_(UserQueueModel.status == UserQueueStatusEnum.ENTRY, UserQueueModel.token_no.isnot(None))
        ).label("entry_tokens"),
    ).outerjoin(
        UserQueueModel,
        and_(
            UserQueueModel.queue_id == QueueModel.id,
            UserQueueModel.status != UserQueueStatusEnum.EXIT,
            UserQueueModel.status != UserQueueStatusEnum.PURGED,
        ),
    ).filter(QueueModel.queue_code == queue_code).filter(QueueModel.deleted_at == None).group_by(QueueModel.id).order_by(QueueModel.queue_name).first()


def get_queue_count(db: Session):
    try:
        today = date.today()
        return db.query(UserQueueModel).filter(UserQueueModel.date == today).all()
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error while fetching data")

def get_services(db: Session,staff_user,service_type):
    try:
        if staff_user is not None:
            query=db.query(ServiceModel).join(ServiceModel.queues).join(RelStaffUserQueueModel, QueueModel.id == RelStaffUserQueueModel.queue_id).filter(RelStaffUserQueueModel.staff_user_id == staff_user).filter(QueueModel.deleted_at == None)
            if service_type is not None:
                query=query.filter(ServiceModel.service_type==service_type)
            return query.all()
        else:
            return db.query(ServiceModel).all()
    except Exception as e:
        logger.exception(e)
        raise MutationError("failed to fetch the data")

def test_exit_user_from_queue(db: Session):
    today = date.today()
    user_queues = db.query(UserQueueModel).join(UserQueueModel.queue).filter(UserQueueModel.date == today).filter(QueueModel.queue_type==QueueTypeEnum.AUTO).filter(
        UserQueueModel.status == UserQueueStatusEnum.ENTRY).filter(
        QueueModel.avg_procedure_time*60<func.extract("epoch", (func.now()-UserQueueModel.start_time))).all()
    for user in user_queues:
        try:
            exit_user_queue(db,user.queue.id,QRDetail(token_id=user.token_id), False,None)
        except Exception as ex:
            text = f"""Exception : {ex}"""
            send_email([os.environ["DIALY_REPORT_MAIL"]],"Entry User",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
            logger.exception(ex)

        
def force_exit_user_from_queue(db: Session):
    try:
        today = date.today()
        user_queues = (
            db.query(UserQueueModel)
            .join(UserQueueModel.queue)
            .filter(UserQueueModel.date == today)
            .filter(UserQueueModel.status == UserQueueStatusEnum.ENTRY)
            .filter(func.extract("epoch", func.now() - UserQueueModel.start_time)/60 > QueueModel.avg_procedure_time*3)
            .all()
        )
        logger.info(user_queues)
        if len(user_queues) > 0:
            for user in user_queues:
                try:
                    exit_user_queue(db,user.queue.id,QRDetail(token_id=user.token_id), True,None)
                except Exception as ex:
                    logger.exception("exception in exit")     
    except Exception as e:
        logger.exception(e)

def reset_queues_user_queues(db: Session):
    try:
        today = date.today()
        user_queues = (
            db.query(UserQueueModel)
        .filter(or_(UserQueueModel.status == UserQueueStatusEnum.CHECKIN,
                UserQueueModel.status == UserQueueStatusEnum.ARRIVED,
                UserQueueModel.status == UserQueueStatusEnum.FREEZED,
                UserQueueModel.status == UserQueueStatusEnum.HOLD,
                UserQueueModel.status == UserQueueStatusEnum.ENTRY))
        .all()
        )
        if len(user_queues)> 0:
            for user in user_queues:
                # if user.status == UserQueueStatusEnum.ENTRY:
                #     user.status = UserQueueStatusEnum.EXIT
                #     user.force_exit = True
                #     # db.query(QueueCounterModel).filter(QueueCounterModel.id == user.counter).update({
                #     # QueueCounterModel.status: QueueCounterStatusEnum.UNALLOTED
                #     # })
                # else:
                user.status = UserQueueStatusEnum.PURGED
                user.force_exit = True
                user.location_id = None
                user.tag_id=None
                user.next_location_id=None
                user_service_ids = db.scalars(db.query(RelUserServiceQueueModel.user_service_id).filter(
                    RelUserServiceQueueModel.user_queue_id == user.id).filter(RelUserServiceQueueModel.status == ServiceStatusEnum.PENDING)).all()
                db.query(UserServiceModel).filter(UserServiceModel.id.in_(user_service_ids)).update({
                    UserServiceModel.status: ServiceStatusEnum.PENDING
                }, synchronize_session="fetch")
                db.query(RelUserServiceQueueModel).filter(RelUserServiceQueueModel.user_service_id.in_(user_service_ids)).update({
                    RelUserServiceQueueModel.status: ServiceStatusEnum.CANCELLED
                }, synchronize_session="fetch",)
        db.query(QueueCounterModel).update({
            QueueCounterModel.status: QueueCounterStatusEnum.UNALLOTED
        })
        queues = db.query(QueueModel).update({
            QueueModel.completed_count: 0,QueueModel.ongoing_count: 0, QueueModel.cancelled_count: 0,
            QueueModel.freezed_count: 0, QueueModel.total_count: 0,
            QueueModel.last_token_called_at: None,
            QueueModel.latest_token_id: None,
        }, synchronize_session="fetch",)
        queues = db.query(QueueCounterModel).update({
            QueueCounterModel.freeze_count: 0,
            QueueCounterModel.status: QueueCounterStatusEnum.UNALLOTED
        }, synchronize_session="fetch",)
        location_beds = db.query(LocationModel).filter(LocationModel.status!= LocationBedStatusEnum.UNAVAILABLE).filter(LocationModel.parent_location_id.isnot(None)).update({
            LocationModel.status: LocationBedStatusEnum.AVAILABLE
        }, synchronize_session="fetch",)
        location_beds = db.query(LocationModel).update({
            LocationModel.alloted_count:0, LocationModel.occupied_count:0
        }, synchronize_session="fetch",)
        remove_assigned_tokens_list()
        db.commit()
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to save changes")    

def entry_user(db: Session, queue, user_queue,counter):
    try:
        queue_counter=get_queue_counter(db,queue.id,counter)
        logger.info(queue_counter)
        if queue_counter !=None:
            # queue_counter.status=QueueCounterStatusEnum.ALLOTED
            # queue.ongoing_count = queue.ongoing_count+1
            status = UserQueueStatusEnum.ENTRY
            # user_queue.counter=queue_counter.id
            user_queue.start_time = func.now()
            return status
        return None
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while entering user")

def freeze_user(db: Session, queue, user_queue,condition,counter):
    try:
        queue_counter, status=get_queue_counter_freeze(db,queue,user_queue,condition,counter)
        if queue_counter !=None:
            # queue_counter.status=QueueCounterStatusEnum.ALLOTED
            return status,queue_counter
        return None,None
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while entering user")

def staff_station_device_login(db: Session,queue_id:int, device_id:str, pin:int):
    try:
        data=db.query(DeviceModel.id).filter(and_(DeviceModel.login_pin==pin,DeviceModel.device_code == device_id)).first()
        if data is None:
            raise MutationError("Invalid Login credentials")
        existing_record = db.query(RelDeviceQueueModel).filter_by(device_id=data.id).one_or_none()
        if existing_record is None:
            device_queue = RelDeviceQueueModel(device_id = data.id, queue_id = queue_id, subscription_name = "getQueueCounters", is_updated = False)
            db.add(device_queue)
        else:
            existing_record.queue_id = queue_id
        db.commit()
        return "Device Login successfully"
    except Exception as e:
        logger.info(f"Error occured: {e}")
        raise MutationError("Invalid Login credentials")

def get_device_queue(db:Session, queue_id: int):
    try:
        data = (
            db.query(RelDeviceQueueModel.queue_id, RelDeviceQueueModel.is_updated)
            .filter(RelDeviceQueueModel.queue_id == queue_id).first()
        )
        return data.queue_id, data.is_updated
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error while fetching data")

def update_rel_device_queue(db: Session, queue_id:int, is_updated: bool):
    db.query(RelDeviceQueueModel).filter(RelDeviceQueueModel.queue_id == queue_id).update(
        {RelDeviceQueueModel.is_updated : is_updated}
    )
    db.commit()

def entry_or_freeze_next_users(db: Session, queue:QueueModel,counter):
    freezed_count=0
    ongoing_count=0
    logger.info(f"Counter: {counter}")
    query = db.query(UserQueueModel).filter(UserQueueModel.queue_id == queue.id, UserQueueModel.date == date.today())
    if counter is not None:
        query= query.filter(UserQueueModel.counter==counter)
    # if queue.queue_type==QueueTypeEnum.SEMIAUTO:
    #     query=query.join(UserQueueModel.queue_weightage_action).filter(UserQueueModel.status == UserQueueStatusEnum.ARRIVED).order_by(
    #             desc(QueueWeightageActionModel.weightage+(func.extract("epoch",
    #                 (func.now() - UserQueueModel.estimated_time))/60)),
    #             asc(UserQueueModel.created_at))
    # else:
    #     query=query.filter(UserQueueModel.status == UserQueueStatusEnum.FREEZED)
    entry_user_queue=query.filter(UserQueueModel.status == UserQueueStatusEnum.FREEZED).order_by(asc(UserQueueModel.freezed_at)).first()
    logger.info('EXIT: ENTRY next user')
    logger.info(entry_user_queue)
    entry_condition="ENTRY"
    if entry_user_queue is None:  
        entry_condition = "ENTRY_ACTIVE"      
        entry_user_queue = db.query(UserQueueModel).join(UserQueueModel.queue).join(UserQueueModel.queue_weightage_action).filter(or_(UserQueueModel.pre_check_status==UserQueuePreCheckStatusEnum.COMPLETED, UserQueueModel.pre_check_status==None)).filter(UserQueueModel.queue_id == queue.id, UserQueueModel.date == date.today(), UserQueueModel.status == UserQueueStatusEnum.ARRIVED).order_by(
                desc((QueueWeightageActionModel.weightage*QueueModel.avg_procedure_time)+(func.extract("epoch",
                    (func.now() - UserQueueModel.order_by_date))/60)),
                asc(UserQueueModel.created_at)).first()
    if entry_user_queue is not None:
        entry_user_queue_status=UserQueueModel.status
        if entry_user_queue.counter is not None:
            counter=entry_user_queue.counter
        status,queue_counter = freeze_user(db,queue, entry_user_queue,entry_condition,counter)
        if status !=None:
            if status==UserQueueStatusEnum.ENTRY:
                ongoing_count=1
                if entry_user_queue_status== UserQueueStatusEnum.FREEZED:
                    freezed_count=-1
            else:
                raise MutationError("Error Freezing data") 
    flag=True
    while flag:
        freeze_user_queue = db.query(UserQueueModel).join(UserQueueModel.queue).join(UserQueueModel.queue_weightage_action).filter(or_(UserQueueModel.pre_check_status==UserQueuePreCheckStatusEnum.COMPLETED, UserQueueModel.pre_check_status==None)).filter(UserQueueModel.queue_id == queue.id, UserQueueModel.date == date.today(), UserQueueModel.status == UserQueueStatusEnum.ARRIVED).order_by(
                desc((QueueWeightageActionModel.weightage*QueueModel.avg_procedure_time)+(func.extract("epoch",
                    (func.now() - UserQueueModel.order_by_date))/60)),
                asc(UserQueueModel.created_at))
        logger.info('EXIT: FREEZE next user')
        logger.info(freeze_user_queue)
        freeze_user_queue=freeze_user_queue.first()
        logger.info(freeze_user_queue)
        if freeze_user_queue is not None :
            status,queue_counter = freeze_user(db,queue, freeze_user_queue,"FREEZED",counter)
            logger.info(status)
            if status != None:
                if status==UserQueueStatusEnum.FREEZED:
                    freezed_count=freezed_count+1
                else:
                    raise MutationError("Error Freezing data")
            if queue_counter is None or queue_counter.upcoming_capacity-queue_counter.freeze_count>=0:
                flag=False
        else:
            flag=False
    return ongoing_count,freezed_count

def get_queue_counters(db: Session, queue_id: int):
    today = date.today()
    queue: QueueModel= db.query(QueueModel.queue_type).filter(QueueModel.id== queue_id).one_or_none()
    if queue is None:
        return []
    else:
        list1=[]
        if queue.queue_type!=QueueTypeEnum.MANUAL:
            list1 = db.query(UserQueueModel).join(UserQueueModel.queue_weightage_action).join(UserQueueModel.queue).filter(
                UserQueueModel.queue_id == queue_id).filter(UserQueueModel.date == today).filter(
                    or_(
                        UserQueueModel.status == UserQueueStatusEnum.FREEZED,
                        UserQueueModel.status == UserQueueStatusEnum.ENTRY)).order_by(
                text("user_queue.status='FREEZED' desc"),
                desc((QueueModel.avg_procedure_time*QueueWeightageActionModel.weightage)+(func.extract("epoch",
                    (func.now() - UserQueueModel.order_by_date))/60)),
                asc(UserQueueModel.created_at)).group_by(UserQueueModel.id, QueueWeightageActionModel.weightage)
        else:
            list1 = db.query(UserQueueModel).join(UserQueueModel.queue).filter(QueueModel.latest_token_id==UserQueueModel.token_id).limit(1)
    return list1.all()

def checkin_msg(db:Session, prerequisites, queue:QueueModel, phone_number, user_name, user_queue_id):
    try:
        query=db.query(UserQueueModel).join(UserQueueModel.queue_weightage_action).join(UserQueueModel.queue).filter(UserQueueModel.date == date.today())
        query=query.filter(UserQueueModel.queue_id == queue.id)  
        list1 = query.filter(
                or_(UserQueueModel.status == UserQueueStatusEnum.ARRIVED,
                    UserQueueModel.status == UserQueueStatusEnum.CHECKIN,
                    UserQueueModel.status == UserQueueStatusEnum.HOLD,
                    UserQueueModel.status == UserQueueStatusEnum.FREEZED,
                    UserQueueModel.status == UserQueueStatusEnum.ENTRY)).order_by(
                        asc(UserQueueModel.freezed_at),
            text("user_queue.status='FREEZED' desc"),
            desc((QueueModel.avg_procedure_time*QueueWeightageActionModel.weightage)+(func.extract("epoch",
                (func.now() - UserQueueModel.order_by_date))/60)),
            asc(UserQueueModel.created_at)).group_by(UserQueueModel.id, QueueWeightageActionModel.weightage,QueueModel.avg_procedure_time).all()
        status_queues = {
            status: [q for q in list1 if q.status == status]
            for status in [UserQueueStatusEnum.CHECKIN]
        }

        people_ahead = 0
        waiting_msg = " "
        if UserQueueStatusEnum.CHECKIN in status_queues:
            queue_list = status_queues[UserQueueStatusEnum.CHECKIN]
            index = next((i for i, q in enumerate(queue_list) if q.id == user_queue_id), -1)

            if index > 0:
                people_ahead = index
                waiting_msg = f"You're currently {people_ahead} in queue"
        prerequisites1=" and ".join(data for data in prerequisites)        
        if prerequisites1.strip() == "" or prerequisites1 == None:
            if queue.service_type == "INVESTIGATIONS":
                prerequisites1 = "Pre-test Instructions: Fast for 8-12 hours before your blood test. Only drink water; avoid coffee, tea, juice, or soda. Take medications as usual unless advised otherwise. Avoid alcohol and smoking 24 hours before the test. Drink water to keep your bladder full, but avoid urinating until after the test"
            if queue.service_type == "CONSULTATIONS":
                prerequisites1 = "Please ensure Vitals and Physician Assessment are completed before proceeding to the consultation."
            else:
                " "
        # tmp_name = get_template_name(db, SMSTypeEnum.WHATSAPP.name,EventCodeEnum.PATIENT_QUEUE_CHECKIN_V1.name)      
        # params = [{"type":"text","text":f"{user_name}"},{"type":"text","text":f"{queue.service_type}"},{"type":"text","text":f"Tower {queue.cluster.tower}->Floor {queue.cluster.floor}->Location {queue.cluster.cluster}"},
        #         {"type":"text","text":"Please follow below instructions"},
        #         {"type":"text","text":f"{prerequisites1}" if prerequisites1.strip() != "" else " "}]
        # send_msg = send_whatsapp_msg(db, tmp_name, phone_number, params,None)
    except Exception as ex:
        logger.exception(ex)
        
def checkin_user_into_queue(db: Session, queue: QueueModel, user_id:int, token_no: str, token_id: int, services, phone_number,weightage_id,staff_user: Optional[int]=None,doctor_name: Optional[str] = None,patient_type: Optional[str] = "OP",is_new_token: Optional[bool]= False):
    try:
        rows = db.query(UserServiceModel).join(UserServiceModel.service).join(ServiceModel.queues).filter(
                UserServiceModel.token_id == token_id,
                UserServiceModel.user_id == user_id,
                UserServiceModel.status == ServiceStatusEnum.PENDING).filter(ServiceModel.id.in_(services)).filter(QueueModel.id==queue.id).order_by(desc(ServiceModel.priority)).all()
        appointment_time = next((row.appointment_date_time for row in rows if row.appointment_date_time is not None),None)
        billed_at = next((row.billed_at for row in rows if row.billed_at is not None),None)
        if appointment_time is not None and appointment_time <= billed_at:
            weightage = db.query(QueueWeightageActionModel).filter(
                        QueueWeightageActionModel.code == 'APPOINTMENT').one()
            weightage_id= weightage.id
        is_platinum = db.query(UserModel.is_platinum_user).filter(UserModel.id == user_id).scalar()
        if is_platinum:
            weightage_id= db.query(QueueWeightageActionModel.id).filter(QueueWeightageActionModel.code=="PLATINUM").scalar() or weightage_id
        if weightage_id is None:
            weightage = db.query(QueueWeightageActionModel).filter(
                        QueueWeightageActionModel.code == 'NORMAL').one()
            weightage_id= weightage.id
        time = (float((queue.avg_procedure_time*(queue.total_count-(queue.cancelled_count +
                queue.completed_count)))/queue.capacity)*queue.deviation_rate)+queue.buffer_time
        user_queue = UserQueueModel(
            queue_id=queue.id,
            user_id=user_id,
            status=UserQueueStatusEnum.CHECKIN,
            date=date.today(),
            pre_check_status= UserQueuePreCheckStatusEnum.PENDING if queue.assignment else None,
            weightage_id=weightage_id,
            token_no=token_no,
            token_id=token_id,
            estimated_time=appointment_time if appointment_time is not None else datetime.now(pytz.timezone('asia/kolkata')) + timedelta(minutes=math.ceil(time)),
            created_by=staff_user,
            doctor_name=doctor_name,
            patient_type=PatientTypeEnum.OP if patient_type == None or patient_type.strip() == "" else patient_type,
            appointment_date_time = appointment_time,
            order_by_date=billed_at
        )
        queue.total_count = queue.total_count+1
        prerequisites = []
        if len(rows)==0:
            raise MutationError("Invalid Action")
        db.add(user_queue)
        db.flush()
        logger.info(user_queue)
        for row in rows:
            rel_user_service = RelUserServiceQueueModel(
                user_service_id=row.id,
                queue_id=queue.id,
                user_queue_id=user_queue.id,
                status = ServiceStatusEnum.PENDING
            )
            db.add(rel_user_service)
            if row.service.prerequisites is not None and row.service.prerequisites.strip()!="":
                prerequisites.append(row.service.prerequisites)
            row.status = ServiceStatusEnum.ON_PROGRESS
        prerequisites=set(prerequisites)
        logger.info(prerequisites)
        user_name = db.query(UserModel.name).filter(UserModel.id == user_id).scalar()
        if not is_new_token:
            checkin_msg(db, prerequisites, queue, phone_number, user_name, user_queue.id)
        queue_step_id= db.query(QueueStepModel.id).filter(QueueStepModel.queue_id==queue.id).filter(QueueStepModel.checkin_status =='CHECKIN').first()
        logger.info(queue_step_id)
        if queue_step_id is not None:
            add_user_queue_step(db,queue_step_id.id,user_queue.id,"Patient Added to Queue","CHECKIN", None, None, None,None,None,staff_user)
        user_queue.step_start_time=None
        user_queue.next_step_start_time=func.now()
        logger.info("CHECKED_IN")
        return user_queue , time
    except Exception as e:
        logger.exception(e)

def get_user_services(db: Session,user_queue_id: int):
    data = db.query(RelUserServiceQueueModel).filter(RelUserServiceQueueModel.status!=ServiceStatusEnum.CANCELLED, 
                RelUserServiceQueueModel.user_queue_id == user_queue_id).all()
    return data

def get_user_pending_services(db: Session,uhid: str):
    pending_service = db.query(UserServiceModel).join(UserModel).join(ServiceModel).filter(
        UserModel.umr_no == uhid,
    )
    service_in_progress = pending_service.filter(UserServiceModel.status == ServiceStatusEnum.ON_PROGRESS).first()
    if service_in_progress:
        raise QueryError("User is already in queue")
    user = db.query(UserModel.name, UserModel.phone_number, UserQueueModel.weightage_id, UserQueueModel.doctor_name).filter(UserModel.umr_no == uhid).join(UserQueueModel, UserQueueModel.user_id == UserModel.id).first()
    return user, db.query(
        UserServiceModel.token_id,
        func.date(UserServiceModel.created_at).label('visit_date'),
        case(
            [
                (
                    func.count().filter(UserServiceModel.status != ServiceStatusEnum.COMPLETED) == 0,
                    'COMPLETED'
                )
            ],
            else_='PENDING'
        ).label('overall_status'),
        func.array_agg(
            func.distinct(ServiceModel.service_type)
        ).label('service_types'),
        func.array_agg(
            func.json_build_object(
                'service_id', UserServiceModel.service_id,
                'service_code', ServiceModel.code,
                'service_name', ServiceModel.name,
                'service_type', ServiceModel.service_type,
                'bill_no', UserServiceModel.bill_no,
                'timestamp', UserServiceModel.created_at,
                'status', UserServiceModel.status,
            )
        ).label('service')
    ).join(UserModel).join(ServiceModel).filter(
        UserModel.umr_no == uhid,
        UserServiceModel.status != ServiceStatusEnum.CANCELLED
    ).group_by(UserServiceModel.token_id, func.date(UserServiceModel.created_at)).order_by(desc(func.date(UserServiceModel.created_at))).all()


def update_counter_priority(db:Session):
    db.query(QueueCounterModel).update({
        "priority": db.query(func.count('*')).filter(UserQueueModel.counter == QueueCounterModel.id).filter(UserQueueModel.date==func.date(func.now())).filter(
            func.extract("epoch",(func.now() - UserQueueModel.start_time))/60<=60).scalar_subquery()
        },synchronize_session="fetch",
    )
    db.commit()

def get_counter_details(db: Session, device_id:str, queue_counter_id: int):
    try:
        today = date.today()
        data={}
        if queue_counter_id:
            counter = (db.query(QueueCounterModel)
                    .filter(QueueCounterModel.id == queue_counter_id)
                    .first())
        else:
            counter = (db.query(QueueCounterModel)
                        .join(DeviceModel, QueueCounterModel.id == DeviceModel.queue_counter)
                        .filter(DeviceModel.device_code == device_id)
                        .first())
        if counter is None:
            raise MutationError("No counter is linked to this device")
        data["counter"] = counter
        counter_user_queue=(db.query(UserQueueModel,UserServiceModel)
                            .join(UserServiceModel, UserServiceModel.token_id == UserQueueModel.token_id)
                            .join(RelUserServiceQueueModel, UserServiceModel.id == RelUserServiceQueueModel.user_service_id)
                            .filter(RelUserServiceQueueModel.user_queue_id == UserQueueModel.id)
                           .filter(UserQueueModel.counter == counter.id).filter(and_(RelUserServiceQueueModel.status!=ServiceStatusEnum.CANCELLED,RelUserServiceQueueModel.status!=ServiceStatusEnum.COMPLETED))
                           .filter(UserQueueModel.date == today).filter(or_(UserQueueModel.status == UserQueueStatusEnum.ENTRY,UserQueueModel.status == UserQueueStatusEnum.FREEZED))
                           .order_by(UserQueueModel.status)
                           .all())
        # logger.info(counter_user_queue)
        user_queue_service = defaultdict(list)
        for user_queue, user_service in counter_user_queue:
            user_queue_service[user_queue].append(user_service)       
        if user_queue_service is not None and len(user_queue_service)>0:
            data["counter_users"] = user_queue_service
        else:
            data["counter_users"]=[]
        return data
    except MutationError as ex:
        logger.info(ex)
        raise MutationError(ex.message)
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to get counter details")


def update_counter(db: Session, counter_id:str, status:str):
    try:
        queue_counter= db.query(QueueCounterModel).filter(QueueCounterModel.id==counter_id).one()
        counter_status= None
        with RedisLock(client_lock, "queue_"+str(0 if queue_counter.queue_id is None else queue_counter.queue_id), blocking_timeout=60):
            if status== StatusEnum.ACTIVE.name:
                if queue_counter.counter_status==StatusEnum.ACTIVE:
                    raise MutationError("Queue is already active")
                counter_status= StatusEnum.ACTIVE
            elif status== StatusEnum.INACTIVE.name:
                if queue_counter.counter_status==StatusEnum.INACTIVE:
                    raise MutationError("Queue is already paused")
                counter_status= StatusEnum.INACTIVE
            db.query(QueueCounterModel).filter(QueueCounterModel.id==counter_id).update(
                    {
                        QueueCounterModel.counter_status:counter_status,
                    }, synchronize_session="fetch",
                )
            if counter_status==StatusEnum.ACTIVE:
                ongoing_count,freezed_count=entry_or_freeze_next_users(db, queue_counter.queue,queue_counter.id)
                db.query(QueueModel).filter(QueueModel.id==queue_counter.queue_id).update(
                        {QueueModel.ongoing_count: QueueModel.ongoing_count+ongoing_count,
                            QueueModel.freezed_count: QueueModel.freezed_count+freezed_count
                            },
                        synchronize_session="fetch",
                    )
            db.commit()
        return counter_status.name if counter_status is not None else None
    except MutationError as ex:
        logger.info(ex)
        raise MutationError(ex.message)
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to update counter")
    
def create_tag(tag,tag_id,umr_no):
    if os.environ["TYPE"] == "DEV":
        return "tag Created Successfully"
    res1 = handle_request1(
        os.environ["TAG_BASE_URL"] + UPDATE_TAG +"?uhId="+umr_no,
        None,
        {
            "tagId": tag,
            "tagName": umr_no,
            "num":tag_id,
            "groupId":"1238549258702848"
        })
    print("Create tag response")
    print(res1.json())
    if res1.status_code == 200:
        return "tag created successfully"
    else:
        raise MutationError("Error Updating Tag")
    
# def upload_all_tags(db:Session):
#     tag_list=db.query(TagModel).all()
#     for tag in tag_list:
#         try:
#             delete_tag(tag.id, tag.code)
#         except Exception as  ex:
#             logger.exception(ex)
# def create_tag_1(tag,tag_id,umr_no):
#     # res1 = requests.get(os.environ["TAG_BASE_URL"] + DELETE_TAG + f"?nums={tag_id}&tagId={tag_id}")
#     res1 = handle_request1(
#         os.environ["TAG_BASE_URL"] + UPDATE_TAG,
#         None,
#         {
#             "tagId": tag,
#             "tagName": umr_no,
#             "num":tag_id,
#         })
#     print("Create tag response")
#     print(res1.json())
#     if res1.status_code == 200:
#         return "tag created successfully"
#     else:
#         raise MutationError("Error Updating Tag")

def delete_tag(tag_id,tag_code, uhid):
    if os.environ["TYPE"] == "DEV":
        return "tag successfully deleted"
    res1 = handle_request1(
        os.environ["TAG_BASE_URL"] + UPDATE_TAG +"?uhId="+uhid,
        None,
        {
            "tagId": tag_code,
            "tagName": tag_code,
            "num":tag_id,
            "groupId":"-1"
        })
    # res1 = requests.get(os.environ["TAG_BASE_URL"] + DELETE_TAG + f"?nums={tag_id}&tagId={tag_code}")
    print("Delete tag response")
    print(res1.json())
    if res1.status_code == 200:
        return "tag successfully deleted"
    else:
        return "issue while deleting tag"

def update_user_queue_step(db:Session, user_queue_id:str, queue_step_id, remarks: str, type,queue_location_id: int,tag:Optional[str]=None, queue_step_code: Optional[str]= None, staff_user: Optional[int]= None, tag_type: Optional[str] = None):
    if queue_step_code is None or queue_step_code=='':
        queue_step: QueueStepModel= db.query(QueueStepModel).filter(QueueStepModel.id==queue_step_id).one()
    else:
        queue_step: QueueStepModel= db.query(QueueStepModel).filter(QueueStepModel.code==queue_step_code).one()
    queue_step_id=queue_step.id
    logger.info(queue_step)
    with RedisLock(client_lock, "queue_manual_"+str(0 if queue_step.queue_id is None else queue_step.queue_id), blocking_timeout=180):
        try:
            user_queue: UserQueueModel = db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id).one()
            status= None
            logger.info(type)
            if type=='CHECKIN':
                status= queue_step.checkin_status
            elif type== 'CHECKOUT':
                status= queue_step.checkout_status
            else:
                raise MutationError("Invalid Type")
            logger.info(status)
            previous_location_id = user_queue.location_id
            if tag is not None:
                if status==UserQueueStatusEnum.FREEZED :
                    if user_queue.tag_id is None:
                        tag_data= get_tag_or_scan_id(db, tag,tag_type)
                    else:
                        raise MutationError("Patient is already assigned with another tag")
                else:
                    tag_data = db.query(TagModel).filter(func.upper(TagModel.rfid_code)==tag.upper()).one_or_none()
                    if tag_data is None:
                        tag_data = db.query(TagModel).filter(func.upper(TagModel.code)==tag.upper()).one_or_none()
                tag_id = tag_data.id
                tag_code = tag_data.code
            else:
                tag_code = user_queue.tag.code
                tag_id = user_queue.tag_id   
            if queue_step.queue.queue_type == QueueTypeEnum.MANUAL:
                if status==UserQueueStatusEnum.EXIT:
                    location_ids =[]
                    if user_queue.location_id is not None:
                        location_ids.append(user_queue.location_id)
                    if user_queue.next_location_id is not None:
                        location_ids.append(user_queue.next_location_id)
                    db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id, UserQueueModel.queue_id==queue_step.queue_id).update({
                                    UserQueueModel.location_id:None, UserQueueModel.next_location_id:None,UserQueueModel.tag_id:None}, synchronize_session="fetch",)
                    db.query(LocationModel).filter(LocationModel.id.in_(location_ids)).update({
                                    LocationModel.occupied_count: LocationModel.occupied_count - 1}, synchronize_session="fetch",)
                    exit_user_queue(db, user_queue.queue_id, QRDetail(token_id=user_queue.token_id), None,None,staff_user)

                    deleting_tag = delete_tag(tag_id,tag_code,user_queue.user.umr_no)
                    print(deleting_tag)
                else:
                    if status==UserQueueStatusEnum.ENTRY and (user_queue.status==UserQueueStatusEnum.ARRIVED or user_queue.status==UserQueueStatusEnum.CHECKIN or user_queue.status==UserQueueStatusEnum.FREEZED):
                        db.query(QueueModel).filter(QueueModel.id==user_queue.queue_id).update({
                            QueueModel.ongoing_count:QueueModel.ongoing_count+1
                        },synchronize_session="fetch"
                        )
                        if user_queue.tag_id is None:
                            creating_tag = create_tag(tag_code,tag_id,user_queue.user.umr_no)
                            print(creating_tag)
                        else:
                            raise MutationError("Tag is already assigned to this patient")
                    start_time_case= case(
                        [
                            (
                                (status == UserQueueStatusEnum.ENTRY) and 
                                (UserQueueModel.start_time.is_(None)), 
                                func.now()
                            )
                        ], 
                        else_=UserQueueModel.start_time
                    )
                    freezed_time_case= case(
                        [
                            (
                                (status == UserQueueStatusEnum.FREEZED) and 
                                (UserQueueModel.freezed_at.is_(None)), 
                                func.now()
                            )
                        ], 
                        else_=UserQueueModel.freezed_at
                    )
                    arrived_time_case= case(
                        [
                            (
                                (status == UserQueueStatusEnum.ARRIVED) and 
                                (UserQueueModel.arrived_at.is_(None)), 
                                func.now()
                            )
                        ], 
                        else_=UserQueueModel.arrived_at
                    )
                    value= db.query(UserQueueModel).filter(UserQueueModel.status!=UserQueueStatusEnum.EXIT, UserQueueModel.status!=UserQueueStatusEnum.PURGED).filter(UserQueueModel.id==user_queue_id, UserQueueModel.queue_id==queue_step.queue_id).update({
                            UserQueueModel.queue_step_id: queue_step_id,
                            UserQueueModel.location_id:queue_location_id if type == 'CHECKIN' else UserQueueModel.location_id,
                            UserQueueModel.status:status,
                            UserQueueModel.freezed_at: freezed_time_case,
                            UserQueueModel.arrived_at: arrived_time_case,
                            UserQueueModel.start_time: start_time_case,
                            UserQueueModel.end_time: func.now() if status==UserQueueStatusEnum.EXIT else UserQueueModel.end_time,
                            UserQueueModel.tag_id:tag_id if tag_id is not None else UserQueueModel.tag_id,
                            UserQueueModel.next_location_id:queue_location_id if type == 'CHECKOUT' else UserQueueModel.next_location_id,
                            UserQueueModel.step_start_time:None if type == 'CHECKOUT' else func.now(),
                            UserQueueModel.next_step_start_time: func.now() if type == 'CHECKOUT' else None,
                        }, synchronize_session="fetch",
                    )
                    if previous_location_id is not None and type == 'CHECKIN':
                        previous_location = db.query(LocationModel).filter(LocationModel.id == previous_location_id).first()
                        previous_location_ids=list(filter(None,[previous_location.id,previous_location.parent_location_id]))
                        db.query(LocationModel).filter(LocationModel.id.in_(previous_location_ids)).update({
                                    LocationModel.occupied_count: LocationModel.occupied_count - 1}, synchronize_session="fetch",)
                    if queue_location_id is not None:
                        queue_location = db.query(LocationModel).filter(LocationModel.id == queue_location_id).first()
                        location_ids=list(filter(None,[queue_location.id,queue_location.parent_location_id]))
                        if (type == "CHECKOUT"):
                            db.query(LocationModel).filter(LocationModel.id.in_(location_ids)).update({
                                    LocationModel.alloted_count: LocationModel.alloted_count + 1}, synchronize_session="fetch",)
                        elif (type== 'CHECKIN'):
                            if user_queue.next_location_id is not None:
                                alloted_location = db.query(LocationModel).filter(LocationModel.id == user_queue.next_location_id).first()
                                alloted_location_ids=list(filter(None,[alloted_location.id,alloted_location.parent_location_id]))
                                db.query(LocationModel).filter(LocationModel.id.in_(alloted_location_ids)).update({
                                    LocationModel.alloted_count: LocationModel.alloted_count - 1}, synchronize_session="fetch",)
                            db.query(LocationModel).filter(LocationModel.id.in_(location_ids)).update({
                                    LocationModel.occupied_count: LocationModel.occupied_count + 1}, synchronize_session="fetch",)
                            db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id, UserQueueModel.queue_id==queue_step.queue_id).update({
                                UserQueueModel.next_location_id:None}, synchronize_session="fetch",)
                    # if (type == "CHECKOUT"):
                    #     if previous_location_id is not None:
                    #         db.query(LocationModel).filter(LocationModel.id == previous_location_id,LocationModel.parent_location_id.isnot(None)).update({
                    #             LocationModel.status: LocationBedStatusEnum.AVAILABLE}, synchronize_session="fetch",)
                    #         db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id, UserQueueModel.queue_id==queue_step.queue_id).update({
                    #                 UserQueueModel.location_id:None}, synchronize_session="fetch",)
                    logger.info(queue_step_id)
                    add_user_queue_step(db,queue_step_id,user_queue_id,remarks,type, None, None, queue_location_id,previous_location_id,tag_id,staff_user)
                    logger.info(value)
                    if value== 0:
                        raise MutationError("Invalid Action")
            else:
                raise MutationError("Queue update not permitted")
            db.commit()
        except MutationError as ex:
            raise MutationError(ex.message)
        except Exception as ex:
            logger.exception(ex)
            raise MutationError("Error occured while checkin")
    
def add_user_queue_step(db,queue_step_id,user_queue_id,remarks,type, status,queue, input_location_id,assigned_location_id,tag_id,staff_user):
    if status is not None:
        queue_step: QueueStepModel= db.query(QueueStepModel).filter(QueueStepModel.queue_id==queue.id)
        if type=='CHECKIN':
           queue_step=queue_step.filter(QueueStepModel.checkin_status==status).first()
        else:
           queue_step=queue_step.filter(QueueStepModel.checkout_status==status).first()
        if queue_step is None:
            return None, None
        queue_step_id = queue_step.id
    queue_step: QueueStepModel= db.query(QueueStepModel).filter(QueueStepModel.id==queue_step_id).first()
    db.query(UserQueueStepModel).filter(UserQueueStepModel.user_queue_id==user_queue_id).filter(
        UserQueueStepModel.updated_at == None
        ).update(
        {
            UserQueueStepModel.updated_by:"SYSTEM",
            UserQueueStepModel.updated_at:func.now(),
            UserQueueStepModel.status: UserQueueStepStatusEnum.COMPLETED
        },synchronize_session="fetch",
    )
    location=None
    location_id = input_location_id if input_location_id is not None else assigned_location_id
    location: LocationModel = db.query(LocationModel).filter(LocationModel.id == location_id).first()
    if input_location_id is not None and type == "CHECKOUT":
        remarks = f"Assigned to {location.name}" if ((remarks is None or remarks =="") and location.parent_location is not None) else f"Assigned to {location.name}" if (remarks is None or remarks =="") else remarks
    if location is not None and (type !="CHECKOUT" or assigned_location_id is not None):
        created_location = location.name if location.parent_location is not None else location.name
    elif type == 'CHECKIN':
        created_location = queue_step.checkin_name
    else:
        created_location=queue_step.checkout_name
    logger.info(f"type:{type}")
    if staff_user is not None:
        staff_user_1: StaffUserModel = get_staff_user(db,staff_user)
    user_queue_step= UserQueueStepModel(
        queue_step_id = queue_step_id,
        user_queue_id = user_queue_id,
        remarks = remarks,
        action_type = type, 
        status = UserQueueStepStatusEnum.IN_PROGRESS if type=='CHECKIN' else UserQueueStepStatusEnum.COMPLETED,
        updated_at = None if type=='CHECKIN' else func.now(),
        location_id = location_id,
        created_by = "SYSTEM" if staff_user is None else staff_user_1.name,
        created_location= created_location,
        description = remarks if remarks is not None and remarks !="" else queue_step.checkin_description if type == 'CHECKIN' else queue_step.checkout_description,
        tag_id = tag_id,
    )
    db.add(user_queue_step)
    return queue_step_id,location_id
    
def add_to_queue(db:Session, uhid:str, queue_step_id, remarks: str, type,staff_user):
    queue_step: QueueStepModel= db.query(QueueStepModel).filter(QueueStepModel.id==queue_step_id).one()
    if queue_step is None:
        raise MutationError("Invalid Action")
    status= None
    if type=='CHECKIN':
        status= queue_step.checkin_status
    elif type== 'CHECKOUT':
        status= queue_step.checkout_status
    else:
        MutationError("Invalid Type")
    user_token= db.query(UserTokenModel).join(UserTokenModel.user).join(UserTokenModel.user_services).filter(or_(UserServiceModel.status==ServiceStatusEnum.PENDING, UserServiceModel.status==ServiceStatusEnum.ON_PROGRESS)).filter(UserModel.umr_no==uhid).first()
    if status==UserQueueStatusEnum.ARRIVED:
        add_user_queue(db, queue_step.queue_id, QRDetail(uhid=user_token.user.umr_no),staff_user)
    elif status ==UserQueueStatusEnum.CHECKIN:
       all_services_ids, user_id, token_id, phone_number,token_no = get_all_services(db, user_token.id, queue_step.queue_id)
       user_queue, time = checkin_user_into_queue(db, queue_step.queue, user_token.user_id, user_token.token_no, user_token.id, all_services_ids,phone_number, None,staff_user)
    db.commit()

def get_all_services(db, token_id, queue_id):
    services1= db.scalars(db.query(UserServiceModel.service_id).join(UserServicePrerequisiteModel,UserServiceModel.id==UserServicePrerequisiteModel.user_service_id).filter(
                            UserServiceModel.token_id==token_id,UserServicePrerequisiteModel.status==UserPreReqStatusEnum.HOLD)).all()
    all_services = db.query(UserServiceModel,UserTokenModel.token_no).join(UserTokenModel).join(UserServiceModel.service).join(ServiceModel.queues).filter(QueueModel.id==queue_id).filter(UserServiceModel.service_id.not_in(services1), UserServiceModel.token_id==token_id).filter(UserServiceModel.status==ServiceStatusEnum.PENDING).filter(UserTokenModel.user_id == UserServiceModel.user_id).all()
    user_services = [user_service[0] for user_service in all_services]
    token_no = list(set(map(lambda x: x[1],all_services)))
    return (
        list(set(map(attr, user_services))) for attr in (lambda x: x.service_id, lambda x: x.user_id, lambda x: x.token_id, lambda x: x.user.phone_number, lambda x: x.user_token.token_no)
    )

def add_token_prefix(token_no, appointment_datetime, is_platinum):
    if is_platinum:
        return f"P-{token_no}"
    elif appointment_datetime:
        return f"A-{token_no}"
    else:
        return f"W-{token_no}"

def get_queue_steps(db:Session,queue_id):
    return db.query(QueueStepModel).filter(QueueStepModel.queue_id==queue_id).order_by(QueueStepModel.priority).all()

def call_next(db:Session, queue_id: int, staff_user:Optional[int]= None):
    queue: QueueModel= db.query(QueueModel).filter(QueueModel.id== queue_id).one()
    with RedisLock(client_lock, "queue_manual_"+str(0 if queue.id is None else queue.id), blocking_timeout=180):
            if queue.queue_type!=QueueTypeEnum.MANUAL:
                raise MutationError("Invalid Action")
            else:
                if db.query(UserQueueModel.id).filter(UserQueueModel.queue_id == queue_id, UserQueueModel.date == date.today(), UserQueueModel.status == UserQueueStatusEnum.FREEZED).count()>=queue.upcoming_patients:
                    raise MutationError("Max calling count reached")
            freeze_user_queue = db.query(UserQueueModel).join(UserQueueModel.queue_weightage_action).filter(or_(UserQueueModel.pre_check_status==UserQueuePreCheckStatusEnum.COMPLETED, UserQueueModel.pre_check_status==None)).filter(UserQueueModel.queue_id == queue_id, UserQueueModel.date == date.today()).filter(or_(UserQueueModel.status == UserQueueStatusEnum.ARRIVED,UserQueueModel.status == UserQueueStatusEnum.CHECKIN)).order_by(
                    desc(QueueWeightageActionModel.weightage),
                    asc(UserQueueModel.created_at))
            freeze_user_queue=freeze_user_queue.first()
            logger.info(freeze_user_queue)
            if freeze_user_queue is not None:
                # queue_step_id= db.query(QueueStepModel.id).filter(QueueStepModel.code == 'TOKEN_ISSUED').first()
                # add_user_queue_step(db,queue_step_id.id,freeze_user_queue.id,"Token called","CHECKOUT", None, None, None,None,None)
                queue_step_id,location_id = add_user_queue_step(db,None,freeze_user_queue.id," Token called", "CHECKIN",UserQueueStatusEnum.FREEZED,queue, None,None,None,staff_user)
                db.query(UserQueueModel).filter(
                        UserQueueModel.id==freeze_user_queue.id
                    ).update({
                        UserQueueModel.status:UserQueueStatusEnum.FREEZED,
                        UserQueueModel.freezed_at:func.now(),
                        UserQueueModel.location_id:location_id,
                        UserQueueModel.queue_step_id:queue_step_id,
                        UserQueueModel.step_start_time: func.now(),
                        UserQueueModel.next_step_start_time: None,
                    }, synchronize_session="fetch",
                    )
                queue.latest_token_id=freeze_user_queue.token_id
                queue.last_token_called_at=func.now()
                phone_number = freeze_user_queue.user.phone_number
                user_name = freeze_user_queue.user.name
                # tmp_name = get_template_name(db, SMSTypeEnum.WHATSAPP.name,EventCodeEnum.NEXT_TOKEN_CALL.name)      
                # params = [{"type":"text","text":user_name},
                #         {"type":"text","text":queue.service_type}]
                # send_msg = send_whatsapp_msg(db, tmp_name, phone_number, params,None)
            else:
                raise MutationError("No Patients In Waiting")
            db.commit()

def update_user_queue_manual(db: Session, user_queue_id, queue_weightage_action_id, staff_user: Optional[int]= None,remarks:Optional[str]= None):
    logger.info(f'start time {time.time()}')
    user_queue= db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id).one()
    location_id=user_queue.location_id
    next_location_id= user_queue.next_location_id
    with RedisLock(client_lock, "queue_manual_"+str(0 if user_queue.queue_id is None else user_queue.queue_id), blocking_timeout=60):
        try:
            action:QueueWeightageActionModel=db.query(QueueWeightageActionModel).filter(QueueWeightageActionModel.id==queue_weightage_action_id).one()
            logger.info(f"user_queue_id:{user_queue_id}")
            if action.code=='HOLD' or action.code=='PURGED':
                if user_queue.status==UserQueueStatusEnum.ENTRY:
                    db.query(QueueModel).filter(QueueModel.id==user_queue.queue_id).update(
                        {
                            QueueModel.ongoing_count:QueueModel.ongoing_count-1,
                            QueueModel.cancelled_count: QueueModel.cancelled_count+1 if action.code=='PURGED' else QueueModel.cancelled_count
                        }, synchronize_session="fetch",
                    )
                if user_queue.tag_id is not None:
                    deleting_tag = delete_tag(user_queue.tag_id,user_queue.tag.code,user_queue.user.umr_no)
                    print(deleting_tag)
                value= db.query(UserQueueModel).filter(UserQueueModel.status!=UserQueueStatusEnum.EXIT, UserQueueModel.status!=UserQueueStatusEnum.PURGED).filter(UserQueueModel.id==user_queue_id).update({
                            UserQueueModel.status:action.code,
                            # UserQueueModel.weightage_id:queue_weightage_action_id,
                            UserQueueModel.location_id:None, 
                            UserQueueModel.next_location_id:None,
                            UserQueueModel.tag_id:None,
                            UserQueueModel.queue_step_id: None
                        }, synchronize_session="fetch",
                    )
            else:
                value= db.query(UserQueueModel).filter(UserQueueModel.status!=UserQueueStatusEnum.EXIT, UserQueueModel.status!=UserQueueStatusEnum.PURGED).filter(UserQueueModel.id==user_queue_id).update({
                            UserQueueModel.status:UserQueueStatusEnum.ARRIVED,
                            UserQueueModel.weightage_id:queue_weightage_action_id,
                        }, synchronize_session="fetch",
                    )
            if action.code=='PURGED':
                user_service_ids = db.scalars(db.query(RelUserServiceQueueModel.user_service_id).filter(
                            RelUserServiceQueueModel.user_queue_id == user_queue_id)).all()
                db.query(UserServiceModel).filter(UserServiceModel.id.in_(user_service_ids)).update({
                            UserServiceModel.status: ServiceStatusEnum.CANCELLED
                        }, synchronize_session="fetch")
                if location_id is not None:
                    db.query(LocationModel).filter(LocationModel.id == user_queue.location_id).update({
                                    LocationModel.occupied_count: LocationModel.occupied_count - 1}, synchronize_session="fetch",)
                if next_location_id is not None:
                    db.query(LocationModel).filter(LocationModel.id == user_queue.next_location_id).update({
                                    LocationModel.alloted_count: LocationModel.alloted_count - 1}, synchronize_session="fetch",)
            staff_user_1 = None
            if staff_user is not None:
                staff_user_1: StaffUserModel = get_staff_user(db,staff_user)
            db.query(UserQueueStepModel).filter(UserQueueStepModel.user_queue_id==user_queue_id).filter(
                UserQueueStepModel.updated_at == None
                ).update(
                {
                    UserQueueStepModel.updated_by:"SYSTEM",
                    UserQueueStepModel.updated_at:func.now(),
                    UserQueueStepModel.status: UserQueueStepStatusEnum.CANCELLED
                },synchronize_session="fetch",
            )
            user_queue_step= UserQueueStepModel(
                    queue_step_id = None,
                    user_queue_id = user_queue_id,
                    remarks = f"user action {action.code}",
                    action_type = "CHECKOUT" if action.code=="PURGED" else"CHECKIN", 
                    status = UserQueueStepStatusEnum.CANCELLED if action.code=="PURGED" else UserQueueStepStatusEnum.IN_PROGRESS,
                    location_id = location_id,
                    created_by = "SYSTEM" if staff_user is None else staff_user_1.name,
                    created_location= None,
                    description = remarks if remarks is not None and remarks !="" else f"Status changed to {action.code}",
                    tag_id = None,
                )  
            db.add(user_queue_step)
            logger.info(value)
            if value== 0:
                raise MutationError("Invalid Action")
            db.commit()
        except Exception as ex:
            logger.exception(ex)
            raise MutationError("Error updating queue")
    
    
def get_queue_step_locations(db:Session, queue_step_id: int,condition: str):
    queue_step= db.query(QueueStepModel).filter(QueueStepModel.id==queue_step_id).one()
    queue_step_id = None
    if condition == "CURRENT":
        queue_step_id = queue_step.id
    elif condition == "NEXT":
        queue_step_id_1=db.query(QueueStepModel).filter(QueueStepModel.queue_id==queue_step.queue_id).filter(QueueStepModel.priority>queue_step.priority).order_by(QueueStepModel.priority,QueueStepModel.id).first()
        queue_step_id = queue_step_id_1.id
    if queue_step_id is None:
        return []
    else:
        # logger.info(queue_step_id_1)
        return db.query(LocationModel).join(LocationModel.queue_step).filter(QueueStepModel.id==queue_step_id).filter(LocationModel.parent_location_id.is_(None)).order_by(LocationModel.priority).all()

def get_locations(db:Session):
    return db.query(LocationModel).all()

def get_procedure_rooms(db:Session):
    now = datetime.now(pytz.timezone('Asia/Kolkata'))
    start_of_day = now.replace(hour=0, minute=0, second=0, microsecond=0)
    return db.query(
        LocationModel.code,
        LocationModel.alloted_count,
        LocationModel.status,
        LocationModel.occupied_count,
        LocationModel.total_count,
        (func.avg(func.extract("epoch", UserQueueStepModel.updated_at - UserQueueStepModel.created_at)) / 60).label('avg_time_mins'),
        (func.count(distinct(UserQueueStepModel.user_queue_id))).label('total_patients_served'),
        (func.min(UserQueueStepModel.created_at)).label('first_arrived_at')
    ).join(
        UserQueueStepModel, UserQueueStepModel.location_id == LocationModel.id
    ).filter(
        UserQueueStepModel.created_at > start_of_day
    ).group_by(
        LocationModel.id, LocationModel.code, LocationModel.alloted_count, LocationModel.status, LocationModel.occupied_count, LocationModel.total_count
    ).all()

def get_censes(db:Session):
    now = datetime.now(pytz.timezone('Asia/Kolkata'))
    start_of_day = now.replace(hour=0, minute=0, second=0, microsecond=0)
    return db.query(
        func.count(UserQueueModel.id).label('total_count'),
        func.sum(case([(UserQueueModel.status == 'EXIT', 1)], else_=0)).label('completed_count'),
        func.sum(case([(UserQueueModel.status.in_(['FREEZED', 'HOLD', 'ARRIVED', 'CHECKIN']), 1)], else_=0)).label('waiting_count'),
        func.sum(case([(UserQueueModel.status == 'ENTRY', 1)], else_=0)).label('inprogress_count')
    ).filter(
        UserQueueModel.created_at > start_of_day
    ).one()

def get_queue_step_logs(db:Session,token_id):
    return db.query(UserQueueStepModel).join(UserQueueStepModel.user_queue).filter(UserQueueModel.token_id==token_id).order_by(
        desc(UserQueueStepModel.created_at)
    ).all()
def get_user_queue_logs(db:Session, token_id):
    return db.query(UserQueueLogsModel).join(UserQueueLogsModel.user_queue).filter(UserQueueModel.token_id==token_id).order_by(
        desc(UserQueueLogsModel.created_at)
    ).all()
def update_loc_1(db:Session,tag_id, location_code):
    with RedisLock(client_lock, "tag_id"+tag_id, blocking_timeout=60):
        location: LocationModel= db.query(LocationModel).filter(LocationModel.iot_code==location_code).first()
        user_queue: UserQueueModel= (db.query(UserQueueModel)
                    .join(TagModel, UserQueueModel.tag_id == TagModel.id)
                    .filter(TagModel.code == tag_id,UserQueueModel.status !=UserQueueStatusEnum.EXIT,UserQueueModel.status != UserQueueStatusEnum.PURGED).first())
        if user_queue is None or location is None:
            if user_queue is not None:
                logger.info(f"user_queue: {user_queue}")
            elif location is not None:
                logger.info(f"location: {location}")
            else:
                logger.info("both user_queue and location id is not found")
            return None
        else:
            priority=0
            if user_queue.queue_step_id is not None:
                queue_step_1=db.query(QueueStepModel).filter(QueueStepModel.id==user_queue.queue_step_id).first()
                priority=queue_step_1.priority
            location_id = location.id if location.parent_location_id is None else location.parent_location_id
            force_update_location = db.query(LocationModel).filter(LocationModel.code=='PROCEDURE').filter(LocationModel.id==location_id).one_or_none()
            if force_update_location:
                priority= 0
            queue_step= db.query(QueueStepModel).join(QueueStepModel.locations).filter(QueueStepModel.queue_id==user_queue.queue_id).filter(QueueStepModel.priority>=priority).filter(LocationModel.id == location_id).order_by(asc(QueueStepModel.priority)).first()
            if queue_step is not None:
                if location.id!=user_queue.location_id:
                    data = update_user_queue_step(db, user_queue.id, queue_step.id,"updated from iot device","CHECKIN",location.id,tag_id, None,None, None)
            db.commit()
            return True
def get_available_tags(db: Session):
    try:
        tags = db.query(TagModel).join(UserQueueModel, UserQueueModel.tag_id == TagModel.id,isouter=True).filter(UserQueueModel.tag_id.is_(None)).filter(TagModel.status == StatusEnum.ACTIVE).all()
        return tags
    except Exception as ex:
        logger.exception(ex)
        raise MutationError("Error While Fteching data")

def get_all_tags(db: Session):
    try:
        tags = db.query(TagModel).all()
        return tags
    except Exception as ex:
        logger.exception(ex)
        raise MutationError("Error While Fetching data")
    
def get_missed_patients(db: Session, start_date: str, end_date: str):
    try:
        if not start_date:
            start_date=None
        if not end_date:
            end_date=None
        max_created_at_subquery = (
            db.query(
                UserQueueStepModel.user_queue_id,
                func.max(UserQueueStepModel.created_at).label('max_created_at')
            )
            .group_by(UserQueueStepModel.user_queue_id)
            .subquery()
        )
        completed_subquery = (
            db.query(distinct(UserQueueModel.user_id))
            .join(UserQueueStepModel.user_queue)
            .join(max_created_at_subquery, 
                    (UserQueueStepModel.user_queue_id == max_created_at_subquery.c.user_queue_id) & 
                    (UserQueueStepModel.created_at == max_created_at_subquery.c.max_created_at))
            .filter(UserQueueStepModel.status == 'COMPLETED').subquery()
        )
        missed_patients = (
            db.query(
                distinct(UserQueueModel.user_id).label("user_id"),
                UserModel.umr_no,
                UserModel.name,
                UserModel.phone_number,
                case(
                    (
                        UserQueueModel.user.has(is_platinum_user=True),
                        "P-" + cast(UserQueueModel.token_no, String)
                    ),
                    (
                        UserQueueModel.appointment_date_time != None,
                        "A-" + cast(UserQueueModel.token_no, String)
                    ),
                    else_="W-" + cast(UserQueueModel.token_no, String)
                ).label("token_no")
            )
            .join(UserQueueStepModel.user_queue)
            .join(UserQueueModel.user)
            .join(max_created_at_subquery, 
                    (UserQueueStepModel.user_queue_id == max_created_at_subquery.c.user_queue_id) & 
                    (UserQueueStepModel.created_at == max_created_at_subquery.c.max_created_at))
            .filter(UserQueueStepModel.status == 'CANCELLED')
            .filter(UserQueueModel.user_id.not_in(completed_subquery))
            .filter(case([(and_(start_date != None, end_date != None),(func.DATE(UserQueueStepModel.created_at).between(func.DATE(start_date),func.DATE(end_date))))],
             else_= True))
            .all()
        )
        return missed_patients
    except Exception as ex:
        logger.exception(ex)
        raise MutationError("Error while fetching data")
       
       

def get_tag_or_scan_id(db: Session, ref_id: str, type: str):
    try:
        query= db.query(TagModel).join(UserQueueModel, UserQueueModel.tag_id == TagModel.id,isouter=True).filter(UserQueueModel.tag_id.is_(None))
        if type=="RFID":
            query=query.filter(func.upper(TagModel.rfid_code)==ref_id.upper())
        else:
            query=query.filter(func.upper(TagModel.code)==ref_id.upper())
        tag = query.filter(TagModel.status == StatusEnum.ACTIVE).first()
        if tag is None:
            raise MutationError("Tag is not available")
        return tag
    except MutationError as ex:
        raise MutationError(ex.message)
    except Exception as ex:
        logger.exception(ex)
        raise MutationError("Error While Fetching data")
    
def checkout_user(db: Session, staff_user: str, ref_id: Optional[str]=None, uhid: Optional[str]=None):
    try:
        if ref_id is not None and ref_id != "":
            data = db.query(
                TagModel.code,
                case(
                    (
                        UserQueueModel.user.has(is_platinum_user=True),
                        "P-" + cast(UserQueueModel.token_no, String)
                    ),
                    (
                        UserQueueModel.appointment_date_time != None,
                        "A-" + cast(UserQueueModel.token_no, String)
                    ),
                    else_="W-" + cast(UserQueueModel.token_no, String)
                ).label("token_no"),
                UserQueueModel.id
            ).join(UserQueueModel, UserQueueModel.tag_id == TagModel.id,isouter=True).filter(or_(UserQueueModel.status == UserQueueStatusEnum.ENTRY,UserQueueModel.status == UserQueueStatusEnum.PAUSED)).filter(func.upper(TagModel.rfid_code)==ref_id.upper()).one_or_none()
        else:
            data = db.query(
                TagModel.code,
                case(
                    (
                        UserQueueModel.user.has(is_platinum_user=True),
                        "P-" + cast(UserQueueModel.token_no, String)
                    ),
                    (
                        UserQueueModel.appointment_date_time != None,
                        "A-" + cast(UserQueueModel.token_no, String)
                    ),
                    else_="W-" + cast(UserQueueModel.token_no, String)
                ).label("token_no"),
                UserQueueModel.id
            ).join(UserQueueModel, UserQueueModel.tag_id == TagModel.id,isouter=True).join(UserModel, UserModel.id == UserQueueModel.user_id,isouter=True).filter(or_(UserQueueModel.status == UserQueueStatusEnum.ENTRY,UserQueueModel.status == UserQueueStatusEnum.PAUSED)).filter(func.upper(UserModel.umr_no) == uhid.upper()).one_or_none()
        print(data)
        if data is None:
            tag= db.query(TagModel).filter(func.upper(TagModel.rfid_code)==ref_id.upper()).first()
            if tag is None:
                raise MutationError("Invalid RF Id code")
            raise MutationError("Already checked out")
        update_user_queue_step(db, data.id, None,"Check out user","CHECKOUT",None,data.code, "DISCHARGE",staff_user, "TAGID")
        return data.token_no
    except MutationError as ex:
        raise MutationError(ex.message)
    except Exception as ex:
        logger.exception(ex)
        raise MutationError("Error While Fetching data")

def pause_unpause_user(db: Session,staff_user: str, user_queue_id: str, type: str):
    try:
        user_queue: UserQueueModel = db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id).one()
        if type == "PAUSED":
            update_user_queue_step(db, user_queue_id, user_queue.queue_step_id, "Tracking paused", "CHECKOUT", None, None, None, staff_user, None)
            user_queue.status = UserQueueStatusEnum.PAUSED.name
            db.commit()
            return "successfully paused"
        if type == "RESUMED":
            update_user_queue_step(db, user_queue_id, None, "Tracking resumed","CHECKOUT", None, None, "TRANSITION",staff_user, None)
            return "successfully unpaused"
        raise MutationError("Invalid type")
    except MutationError as ex:
        raise MutationError(ex.message)
    except Exception as ex:
        logger.exception(ex)
        raise MutationError("Error While Fetching data")

def check_tag_avaible_or_not(db :Session,ref_id):
    tag_code = db.query(TagModel.code).filter(TagModel.rfid_code == ref_id).one_or_none()
    scan_id = db.query(TagModel.rfid_code).filter(TagModel.code == ref_id).one_or_none()
    if tag_code is not None:
        Avaible_tags = db.query(TagModel.code).join(UserQueueModel, UserQueueModel.tag_id == TagModel.id,isouter=True).filter(UserQueueModel.tag_id.is_(None)).filter(TagModel.status == StatusEnum.ACTIVE).all()
        if tag_code not in Avaible_tags:
            # raise MutationError("rfid is already assigned")
            return "rfid is already assigned"
        return tag_code[0]
    if scan_id is not None:
        Avaible_rfid_codes = db.query(TagModel.rfid_code).join(UserQueueModel, UserQueueModel.tag_id == TagModel.id,isouter=True).filter(UserQueueModel.tag_id.is_(None)).filter(TagModel.status == StatusEnum.ACTIVE).all()
        if scan_id not in Avaible_rfid_codes:
            # raise MutationError("tagId is already assigned")
            return "tagid is already assigned"
        return scan_id[0]
    else:
        raise MutationError("refId is not found")
    
def update_user_queue_step_dynamically(db: Session):
    user_queues = (
        db.query(UserQueueModel).join(UserQueueModel.queue).join(QueueStepModel, UserQueueModel.queue_step_id == QueueStepModel.id)
        .filter(UserQueueModel.status == UserQueueStatusEnum.ENTRY,QueueModel.queue_type==QueueTypeEnum.MANUAL)
        .filter(or_(
            (func.extract("epoch", func.now() - UserQueueModel.step_start_time)/60) > QueueStepModel.avg_step_time,
            UserQueueModel.step_start_time.is_(None)))
        .order_by(desc(QueueStepModel.priority))
        .all()
    )
    logger.info(f'locations update {len(user_queues)}')
    if len(user_queues) >0:
        for user in user_queues:
            start_time=time.time()
            logger.info(f'locations update start time : {time.time()}')
            with RedisLock(client_lock, "queue_location_"+str(0 if user.queue_id is None else user.queue_id), blocking_timeout=60):
                try:
                    priority=0
                    if user.queue_step_id is not None:
                        queue_step_1=db.query(QueueStepModel).filter(QueueStepModel.id==user.queue_step_id).first()
                        priority=queue_step_1.priority
                        queue_step= (db.query(QueueStepModel).filter(QueueStepModel.queue_id==user.queue_id)
                        .filter(QueueStepModel.priority>priority).order_by(asc(QueueStepModel.priority)).first())
                        tag=user.tag.code if user.tag is not None else None
                        if queue_step is not None:
                            if queue_step.code == 'PROCEDURE':
                                locations = queue_step.locations[0].child_locations
                            else :
                                locations = queue_step.locations
                            available_locations = list(filter(lambda loc: (loc.alloted_count+ loc.occupied_count) < loc.total_count, locations))
                            assigned_location = random.choice(available_locations) if len(available_locations )>0 else None
                            location_id = assigned_location.id if assigned_location is not None else None
                            if queue_step.code =='DISCHARGE':
                                update_user_queue_step(db, user.id, queue_step.id,"From Cron Server","CHECKOUT",user.location_id,tag, None,None, None)
                            elif location_id is not None:
                                update_user_queue_step(db, user.id, queue_step.id,"From Cron Server","CHECKIN",location_id,tag, None,None,None)
                        else:
                            update_user_queue_step(db, user.id, user.queue_step_id,"From Cron Server","CHECKOUT",user.location_id,tag, None,None,None)
                except Exception as ex:
                    logger.exception(ex)
            logger.info(f'locations update end time : {time.time()}')

def get_user_queue_steps_by_user_queue_id(db:Session, user_queue_id:int):
    return db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id).one_or_none()

def update_pre_check_status(db:Session, user_queue_id:int, user_service_ids, current_assignment, staff_user, update_time):
    user_queue= db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id).one()
    if update_time is None:
        update_time = datetime.now(pytz.timezone('Asia/Kolkata'))
    assignment_to_field = {
        "VITALS": update_time if current_assignment == "VITALS" else user_queue.vitals_datetime,
        "PHYSICIANASSISTANT": update_time if current_assignment == "PHYSICIANASSISTANT" else user_queue.phy_ass_datetime,
        "PRECHECK": update_time if current_assignment == "PRECHECK" else user_queue.phlebotomy_precheck_datetime,
    }
    field_column_map = {
        "VITALS": UserQueueModel.vitals_datetime,
        "PHYSICIANASSISTANT": UserQueueModel.phy_ass_datetime,
        "PRECHECK": UserQueueModel.phlebotomy_precheck_datetime,
    }
    field_column_text = {
        "VITALS": "Vitals",
        "PHYSICIANASSISTANT": "Physisian assesment",
        "PRECHECK": "Phlobatomy pre check",
    }
    if user_service_ids is not None and len(user_service_ids)>0:
        db.query(RelUserServiceQueueModel).filter(RelUserServiceQueueModel.status==ServiceStatusEnum.PENDING).filter(RelUserServiceQueueModel.user_queue_id==user_queue_id).filter(
            RelUserServiceQueueModel.user_service_id.not_in(
                user_service_ids
            )
        ).update(
            {
                RelUserServiceQueueModel.status:ServiceStatusEnum.CANCELLED
            },synchronize_session="fetch",
        )
        cancelled_service_ids=db.query(RelUserServiceQueueModel.user_service_id).filter(RelUserServiceQueueModel.status==ServiceStatusEnum.CANCELLED).filter(RelUserServiceQueueModel.user_queue_id==user_queue_id).filter(
            RelUserServiceQueueModel.user_service_id.not_in(
                user_service_ids
            )
        ).scalar_subquery()
        db.query(UserServiceModel).filter(
            UserServiceModel.id.in_(
                cancelled_service_ids
            )
        ).update({
            UserServiceModel.status:ServiceStatusEnum.PENDING
        },synchronize_session="fetch",
        )
    
# Check that for every task in the assignment, the corresponding datetime is set
    if all(assignment_to_field.get(task) for task in user_queue.queue.assignment):
        db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id).update(
            {
                UserQueueModel.pre_check_status:UserQueuePreCheckStatusEnum.COMPLETED if user_queue.queue.assignment else None,
                field_column_map[current_assignment]: update_time,
                UserQueueModel.updated_by: staff_user,
                UserQueueModel.remarks: f"{field_column_text[current_assignment]} completed",
            },synchronize_session="fetch",
        )
        if user_queue.status == UserQueueStatusEnum.ARRIVED:
            status,queue_counter = freeze_user(db,user_queue.queue, user_queue,None,None)
            logger.info(status)
            if status != None:
                if status==UserQueueStatusEnum.FREEZED:
                    db.query(QueueModel).filter(QueueModel.id==user_queue.queue_id).update(
                                    {QueueModel.freezed_count: QueueModel.freezed_count+1,
                                    QueueModel.latest_token_id: None if user_queue.queue.latest_token_id == user_queue_id else user_queue.queue.latest_token_id},
                                    synchronize_session="fetch",
                            )
                    db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id).update(
                        {
                            UserQueueModel.freezed_at:func.now()
                        },synchronize_session="fetch",
                    )
                elif status==UserQueueStatusEnum.ENTRY:
                    db.query(QueueModel).filter(QueueModel.id==user_queue.queue_id).update(
                        {QueueModel.ongoing_count: QueueModel.ongoing_count+1,QueueModel.latest_token_id: None if user_queue.queue.latest_token_id == user_queue_id else user_queue.queue.latest_token_id},
                        synchronize_session="fetch",
                    )
                    db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id).update(
                        {
                            UserQueueModel.freezed_at:func.now(),
                            UserQueueModel.start_time: func.now()
                        },synchronize_session="fetch",
                    )
                if user_queue.queue.latest_token_id == user_queue_id:
                    db.query(QueueModel).filter(QueueModel.id==user_queue.queue_id).update(
                        {QueueModel.latest_token_id: None},
                        synchronize_session="fetch",
                    )        
    else:
        db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id).update(
            {
                field_column_map[current_assignment]: update_time,
                UserQueueModel.updated_by: staff_user,
                UserQueueModel.remarks: f"{field_column_text[current_assignment]} completed",
            },synchronize_session="fetch",
        )
        if user_queue.queue.latest_token_id == user_queue_id:
            db.query(QueueModel).filter(QueueModel.id==user_queue.queue_id).update(
                {QueueModel.latest_token_id: None},
                synchronize_session="fetch",
            )
    db.commit()

def get_service_categories(db):
    categories=db.query(func.unnest(ServiceModel.prerequisites_conditions).label('category')).distinct().all()
    categories = list(map(lambda x: x.category,categories))
    return categories

def get_average_time(db):
    now = datetime.now(pytz.timezone('Asia/Kolkata'))
    start_of_day = now.replace(hour=0, minute=0, second=0, microsecond=0)
    endo_sub_query= db.query(distinct(UserQueueModel.id)).filter(UserQueueModel.user_service).filter(UserServiceModel.service).filter(ServiceModel.code.in_(["ENDOSCOPY", "COLONOSCOPY"])).filter(UserQueueModel.created_at > start_of_day).subquery()
    subquery2 = db.query(distinct(UserQueueStepModel.user_queue_id).label("user_queue_id")
                            ).group_by(UserQueueStepModel.user_queue_id
                            ).having(func.count(distinct(UserQueueStepModel.queue_step_id)) == db.query(func.count(distinct(QueueStepModel.id))).scalar()).subquery()
    subquery1 = db.query(
    UserQueueStepModel.user_queue_id,
    UserQueueStepModel.queue_step_id,
    func.count(UserQueueStepModel.queue_step_id),
    (func.sum((func.extract('epoch', UserQueueStepModel.updated_at) - func.extract('epoch', UserQueueStepModel.created_at)) / 60)).label('avg_duration_minutes')
    ).join(
        UserQueueStepModel.user_queue
    ).filter(   
        or_(UserQueueModel.doctor_name.is_(None),UserQueueModel.doctor_name ==""),
        or_(UserQueueModel.patient_type =="OP" ,UserQueueModel.patient_type == None),
        UserQueueModel.created_at > start_of_day,
        UserQueueStepModel.updated_at.isnot(None),
        UserQueueStepModel.user_queue_id.in_(endo_sub_query),
        UserQueueStepModel.user_queue_id.in_(subquery2)
        # ServiceModel.code.in_(["ENDOSCOPY", "COLONOSCOPY"])
    ).group_by(
        UserQueueStepModel.user_queue_id,
        UserQueueStepModel.queue_step_id,
    )
    subquery= subquery1.subquery()
    result = db.query(
        QueueStepModel.code,
        subquery.c.queue_step_id,
        func.count(subquery.c.user_queue_id).label('count'),
        (func.avg(subquery.c.avg_duration_minutes)).label('avg_duration_per_user')
    ).join(QueueStepModel, QueueStepModel.id==subquery.c.queue_step_id).group_by(
        subquery.c.queue_step_id,QueueStepModel.code
    ).all()
    return result

def get_overall_average_time(db):
    try:
        now = datetime.now(pytz.timezone('Asia/Kolkata'))
        start_of_day = now.replace(hour=0, minute=0, second=0, microsecond=0)
        sub_query = db.query(LocationModel.id).filter(LocationModel.code == "PROCEDURE").subquery()
        one_hour_ago = now - timedelta(hours=1)
        start_of_last_hour = one_hour_ago.replace(minute=0, second=0, microsecond=0)
        start_of_hour = now.replace(minute=0, second=0, microsecond=0)
        two_hour_ago = now - timedelta(hours=2)
        data = db.query(
            func.ceil(func.abs(func.count(UserQueueStepModel.user_queue_id)/(func.extract('epoch',func.now() - func.min(UserQueueStepModel.created_at))/3600))).label('avg_procedure_per_hour'),
            func.ceil(func.abs(func.count(distinct(case([(LocationModel.code.like('EP%'), UserQueueStepModel.user_queue_id)], else_=None)))/(func.extract('epoch', func.now() - func.min(UserQueueStepModel.created_at)) / 3600))).label('endoscopy_avg_procedure_per_hour'),
            func.ceil(func.abs(func.count(distinct(case([(and_(LocationModel.code.like('EP%'),UserQueueStepModel.created_at<=one_hour_ago), UserQueueStepModel.user_queue_id)], else_=None)))/(func.extract('epoch', func.now() - func.min(UserQueueStepModel.created_at)) / 3600))).label('endoscopy_last_hour_avg_procedure_per_hour'),
            # func.ceil(func.abs(func.count(distinct(case([(start_of_last_hour <= UserQueueStepModel.created_at <= start_of_hour, case([(LocationModel.code.like('EP%'), UserQueueStepModel.user_queue_id)], else_=None))], else_=None))))).label('endoscopy_last_hour_avg_procedure_per_hour'),
            func.ceil(func.abs(func.count(distinct(case([(LocationModel.code.like('CP%'), UserQueueStepModel.user_queue_id)], else_=None)))/(func.extract('epoch', func.now() - func.min(UserQueueStepModel.created_at)) / 3600))).label('colonoscopy_avg_procedure_per_hour'),
            func.ceil(func.abs(func.count(distinct(case([(and_(LocationModel.code.like('CP%'),UserQueueStepModel.created_at<=one_hour_ago), UserQueueStepModel.user_queue_id)], else_=None)))/(func.extract('epoch', one_hour_ago - func.min(UserQueueStepModel.created_at)) / 3600))).label('colonoscopy_last_hour_avg_procedure_per_hour'),

            # func.ceil(func.abs(func.count(distinct(case([(start_of_last_hour <= UserQueueStepModel.created_at <= start_of_hour, case([(LocationModel.code.like('CP%'), UserQueueStepModel.user_queue_id)], else_=None))], else_=None))))).label('colonoscopy_last_hour_avg_procedure_per_hour'),
            # func.ceil(func.abs(func.sum(case([(UserQueueStepModel.created_at <= one_hour_ago, UserQueueStepModel.created_at >= two_hour_ago)], else_=0))/(func.extract('epoch', func.now() - one_hour_ago) / 3600))).label('last_prev_hour_avg_procedure_per_hour'),
            (func.ceil(func.abs(func.count(distinct(case([(and_(UserQueueStepModel.created_at <= one_hour_ago,UserQueueStepModel.created_at >= two_hour_ago), case([(LocationModel.code.like('CP%'), UserQueueStepModel.user_queue_id)], else_=None))], else_=None)))))+
            func.ceil(func.abs(func.count(distinct(case([(and_(UserQueueStepModel.created_at <= one_hour_ago,UserQueueStepModel.created_at >= two_hour_ago), case([(LocationModel.code.like('EP%'), UserQueueStepModel.user_queue_id)], else_=None))], else_=None)))))).label('last_prev_hour_avg_procedure_per_hour'),
            func.count(distinct(case([(LocationModel.code.like('EP%'), UserQueueStepModel.user_queue_id)], else_=None))).label('endoscopy_count'),
            func.count(distinct(case([(LocationModel.code.like('CP%'), UserQueueStepModel.user_queue_id)], else_=None))).label('colonoscopy_count'),
            func.ceil(func.abs(func.count(UserQueueStepModel.user_queue_id)/(func.extract('epoch', func.now() - func.coalesce(func.min(UserQueueStepModel.created_at), func.now())) / 3600))).label('avg_procedure_from_start_of_day'),
            func.ceil(func.abs(((func.sum(func.extract('epoch',UserQueueStepModel.updated_at - UserQueueStepModel.created_at)))*100)/(func.count(distinct(LocationModel.id))* (func.extract('epoch',func.now() - func.min(UserQueueStepModel.created_at)))))).label('procedure_suit_occupancy_rate')
        ).join(UserQueueStepModel.user_queue).join(UserQueueStepModel.queue_step).join(LocationModel,and_(LocationModel.status != "UNAVAILABLE", LocationModel.parent_location_id.in_(sub_query),UserQueueStepModel.location_id ==LocationModel.id)).filter(
            UserQueueStepModel.created_at > start_of_day,
            UserQueueStepModel.updated_at.isnot(None),
            or_(UserQueueModel.doctor_name == None,UserQueueModel.doctor_name == ""),
            QueueStepModel.code == "PROCEDURE"
        ).one_or_none()
        logger.info(data)
        return data
    except Exception as e:
        print(str(e))

def call_next_pre_check(db:Session, queue_id: int, staff_user:Optional[int]= None):
    queue: QueueModel= db.query(QueueModel).filter(QueueModel.id== queue_id).one()
    with RedisLock(client_lock, "queue_manual_"+str(0 if queue.id is None else queue.id), blocking_timeout=180):
        if queue.queue_type == QueueTypeEnum.AUTO:
            freeze_user_queue = db.query(UserQueueModel).join(UserQueueModel.queue).join(UserQueueModel.queue_weightage_action).filter(UserQueueModel.pre_check_status==UserQueuePreCheckStatusEnum.PENDING).filter(UserQueueModel.queue_id == queue_id, UserQueueModel.date == date.today()).filter(UserQueueModel.status == UserQueueStatusEnum.ARRIVED,UserQueueModel.queue_step_id.is_(None)).order_by(
                    desc((QueueModel.avg_procedure_time*QueueWeightageActionModel.weightage)+(func.extract("epoch",
                        (func.now() - UserQueueModel.order_by_date))/60)))
            freeze_user_queue=freeze_user_queue.first()
            logger.info(freeze_user_queue)
            if freeze_user_queue is not None:
                queue_step_id,location_id = add_user_queue_step(db,None,freeze_user_queue.id,"vial kit preparation started", "CHECKOUT",UserQueueStatusEnum.ARRIVED,queue, None,None,None,staff_user)
                db.query(UserQueueModel).filter(
                        UserQueueModel.id==freeze_user_queue.id
                    ).update({
                        UserQueueModel.queue_step_id:queue_step_id,
                    }, synchronize_session="fetch",
                    )
            else:
                raise MutationError("No patient found")
        elif queue.queue_type == QueueTypeEnum.SEMIAUTO and "PHYSICIANASSISTANT" in queue.assignment:
            freeze_user_queue = db.query(UserQueueModel).join(UserQueueModel.queue).join(UserQueueModel.queue_weightage_action).filter(UserQueueModel.pre_check_status==UserQueuePreCheckStatusEnum.PENDING).filter(UserQueueModel.queue_id == queue_id, UserQueueModel.date == date.today()).filter(UserQueueModel.status == UserQueueStatusEnum.ARRIVED).filter(UserQueueModel.phy_ass_datetime.is_(None)).order_by(
                    desc((QueueModel.avg_procedure_time*QueueWeightageActionModel.weightage)+(func.extract("epoch",
                        (func.now() - UserQueueModel.order_by_date))/60))).first()
            if freeze_user_queue is not None:
                logger.info(freeze_user_queue)
                db.query(QueueModel).filter(QueueModel.id==queue_id).update({
                    QueueModel.last_token_called_at: func.now(),
                    QueueModel.latest_token_id: freeze_user_queue.token_id
                    },synchronize_session="fetch"
                )
                db.query(UserQueueModel).filter(UserQueueModel.id==freeze_user_queue.id).update(
                    {
                        UserQueueModel.updated_by:staff_user,
                        UserQueueModel.remarks: "physician assistant called"
                    }
                )
            else:
                raise MutationError("No patient found for physician assistant")
        else:
            raise MutationError("Invalid Request")
        db.commit()
        return freeze_user_queue
    
def sync_loc_count(db:Session):
    try:
        update_stmt_1 = (
        update(LocationModel)
        .values(
            occupied_count=(
                db.query(func.count(UserQueueModel.id))
                .filter(
                    UserQueueModel.status == 'ENTRY',
                    UserQueueModel.location_id == LocationModel.id
                )
                .scalar_subquery()
            )
            )
            )
            # Execute the first update statement
        db.execute(update_stmt_1)
        update_stmt_2 = (
        update(LocationModel)
        .values(
            alloted_count=(
                db.query(func.count(UserQueueModel.id))
                .filter(
                    UserQueueModel.status == 'ENTRY',
                    UserQueueModel.next_location_id == LocationModel.id
                )
                .scalar_subquery()
            )
        )
    )
        # Execute the second update statement
        db.execute(update_stmt_2)
        subquery = db.query(
            LocationModel.parent_location_id.label("parent_location_id"),
            func.sum(LocationModel.occupied_count).label("occupied_count"),
            func.sum(LocationModel.alloted_count).label("alloted_count")
        ).filter(
            LocationModel.parent_location_id.is_not(None)
        ).group_by(
            LocationModel.parent_location_id
        ).subquery()

        # Update statement
        update_stmt = update(LocationModel).values(
            alloted_count=subquery.c.alloted_count,
            occupied_count=subquery.c.occupied_count
        ).where(LocationModel.id == subquery.c.parent_location_id)

        db.execute(update_stmt)
        db.commit()
        logger.info("end")
    except Exception as ex:
        logger.exception(ex)

def update_patient_type(db:Session,id:int,patient_type:str):
    try:
        user_queue_record = db.query(UserQueueModel).filter(UserQueueModel.id == id).one_or_none()
        print(user_queue_record)
        if not user_queue_record:
            raise MutationError("Record not found")
        user_queue_record.patient_type = patient_type
        db.commit()
    except MutationError as ex:
       raise ex

def get_queue_counters_by_queue_id(db: Session, queue_id: int):
    try:
        now = datetime.now(pytz.timezone('Asia/Kolkata'))
        start_of_day = now.replace(hour=0, minute=0, second=0, microsecond=0)
        return db.query(
            QueueCounterModel.counter_name,
            QueueCounterModel.id.label("counter_id"),
            QueueCounterModel.counter_status.label("counter_status")
        ).filter(
            QueueCounterModel.queue_id == queue_id,
        ).group_by(QueueCounterModel.id, QueueCounterModel.counter_name, QueueCounterModel.counter_status).order_by(QueueCounterModel.counter_name).all()
    except Exception as e:
        logger.exception(e)

def get_queue_counters_by_user(db: Session, staff_user: int):
    try:
        now = datetime.now(pytz.timezone('Asia/Kolkata'))
        start_of_day = now.replace(hour=0, minute=0, second=0, microsecond=0)

        # Fetch all queues and counters for the given staff user
        staff_queues = db.query(
            QueueModel.id.label("queue_id"),
            QueueModel.queue_name,
            QueueModel.show_patient_name,
            QueueCounterModel.counter_name,
            QueueCounterModel.doctor_image_name,
            func.coalesce(UserTokenModel.token_no, '-').label("phy_token_no")
        ).join(RelStaffUserQueueModel, QueueModel.id == RelStaffUserQueueModel.queue_id
        ).outerjoin(QueueCounterModel, QueueModel.id == QueueCounterModel.queue_id
        ).outerjoin(
            UserTokenModel, QueueModel.latest_token_id == UserTokenModel.id
        ).filter(
            RelStaffUserQueueModel.staff_user_id == staff_user,
            QueueModel.deleted_at.is_(None)
        ).order_by(QueueModel.queue_name, QueueCounterModel.counter_name).all()

        if not staff_queues:
            return []

        # Fetch user queue tokens for the day
        queue_tokens = db.query(
            UserQueueModel.queue_id,
            QueueCounterModel.counter_name,
            QueueCounterModel.doctor_image_name,
            UserQueueModel.token_no,
            UserQueueModel.status,
            QueueModel.show_patient_name,
            UserModel.name,
            UserModel.phone_number
        ).join(QueueCounterModel, QueueCounterModel.id == UserQueueModel.counter
        ).join(QueueModel, QueueModel.id == UserQueueModel.queue_id
        ).join(UserModel, UserModel.id == UserQueueModel.user_id
        ).filter(
            UserQueueModel.queue_id.in_([q.queue_id for q in staff_queues]),
            UserQueueModel.created_at > start_of_day,
            UserQueueModel.status.in_([UserQueueStatusEnum.ENTRY, UserQueueStatusEnum.ARRIVED, UserQueueStatusEnum.FREEZED]),
        ).filter(or_(UserQueueModel.pre_check_status.is_(None),UserQueueModel.pre_check_status==UserQueuePreCheckStatusEnum.COMPLETED)).order_by(QueueCounterModel.counter_name).all()

        # Ensure all queues and counters are included, even if no tokens exist
        output = defaultdict(lambda: {
            "queue_name": "",
            "show_patient_name": False,
            "counters": defaultdict(lambda: {"doctor_image": "", "tokens": [], "current_token": None, "patient_name": None, "mobile_number": None})
        })

        for queue in staff_queues:
            queue_id = queue.queue_id
            queue_name = queue.queue_name
            show_patient_name = queue.show_patient_name
            counter = queue.counter_name
            doctor_image = queue.doctor_image_name
            phy_token_no = queue.phy_token_no
            if queue_id:
                output[queue_id]["phy_token_no"] = phy_token_no
                output[queue_id]["queue_name"] = queue_name
                output[queue_id]["show_patient_name"] = show_patient_name
                if counter:
                    output[queue_id]["counters"][counter]["doctor_image"] = doctor_image

        # Assign tokens and current_token to the correct counters
        for queue_id, counter, doctor_image, token, status, show_patient_name, patient_name, mobile_number in queue_tokens:
            if status == UserQueueStatusEnum.ENTRY:
                output[queue_id]["counters"][counter]["current_token"] = token
                output[queue_id]["counters"][counter]["patient_name"] = patient_name if show_patient_name else None
                output[queue_id]["counters"][counter]["mobile_number"] = mobile_number if show_patient_name else None
            else:
                output[queue_id]["counters"][counter]["tokens"].append(token)

        final_output = sorted([
            {
                "queueId": queue_id,
                "queueName": details["queue_name"],
                "phyTokenNo": details.get("phy_token_no", "-"),
                "showPatientName": details["show_patient_name"],
                "counters": [
                    {
                        "counterName": counter,
                        "doctorImage": counter_details["doctor_image"] if counter_details["doctor_image"] else None,
                        "tokens": sorted(counter_details["tokens"][:6]) or ["---"],
                        "servingToken": counter_details["current_token"] if counter_details["current_token"] else "---",
                        "patientName": counter_details["patient_name"],
                        "mobileNumber": f'{"*" * (len(counter_details["mobile_number"]) - 4)}{counter_details["mobile_number"][-4:]}' if counter_details["mobile_number"] and len(counter_details["mobile_number"]) >= 4 else None
                    }
                    for counter, counter_details in details["counters"].items()
                ]
            }
            for queue_id, details in output.items()
        ], key=lambda x: x["queueId"])
        return final_output
    except Exception as e:
        logger.exception(e)
    
def cancel_pending_services(db: Session, token_id: int):
    try:
        db.query(UserServiceModel).filter(
            UserServiceModel.token_id == token_id,
            UserServiceModel.status == ServiceStatusEnum.PENDING
        ).update(
            {UserServiceModel.status: ServiceStatusEnum.CANCELLED},
            synchronize_session="fetch"
        )
        db.commit()
    except Exception as e:
        logger.error(f"Error in cancel_pending_services: {e}")

def get_user_opted_service_prerequisites(db: Session,user_queue_id: int):
    data = db.query(ServiceModel.prerequisites).join(RelUserServiceQueueModel.user_service).join(UserServiceModel.service).filter(
        RelUserServiceQueueModel.status!=ServiceStatusEnum.CANCELLED, RelUserServiceQueueModel.user_queue_id == user_queue_id).filter(
            ServiceModel.prerequisites !=None).all()
    prerequisites_list= set(list(map(lambda x: x.prerequisites, data)))
    prerequisites = ", ".join(data for data in prerequisites_list)
    return prerequisites

def get_user_queue_msg(db: Session,uhid :str):
    try:
        msg=""
        user_queue_data: UserQueueModel = db.query(UserQueueModel,UserTokenModel).join(UserTokenModel,UserTokenModel.id == UserQueueModel.token_id ).join(UserQueueModel.user).filter(UserQueueModel.status !=UserQueueStatusEnum.EXIT, UserQueueModel.status !=UserQueueStatusEnum.PURGED).filter(UserModel.umr_no==uhid).filter(UserQueueModel.date == date.today()).one_or_none()
        user_last_queue_data: UserQueueModel = db.query(UserQueueModel,UserTokenModel).join(UserTokenModel,UserTokenModel.id == UserQueueModel.token_id ).join(UserQueueModel.user).filter(UserQueueModel.status ==UserQueueStatusEnum.EXIT).filter(UserModel.umr_no==uhid).filter(UserQueueModel.date == date.today()).order_by(desc(UserQueueModel.created_at)).first()
        # logger.info(f"{user_queue_data}{user_last_queue_data}")
        user_queue, token = user_queue_data if user_queue_data else (None, None)
        user_last_queue, last_token = user_last_queue_data if user_last_queue_data else (None, None)
        token = token or last_token
        logger.info("User Queue: %s, Last Exit Queue: %s", user_queue, user_last_queue)
        service_data = None
        if token is not None:
            service_data= db.query(UserTokenModel).filter(UserTokenModel.id == token.id).first()
        if user_queue is not None:
            location = f"Tower {user_queue.queue.cluster.tower}->Floor {user_queue.queue.cluster.floor}->Location {user_queue.queue.cluster.cluster}"
            queue_name = user_queue.queue.queue_name
            if user_queue.status == UserQueueStatusEnum.CHECKIN:
                prerequisites = get_user_opted_service_prerequisites(db,user_queue.id)
                prerequisites = "Ensure your QR code is scanned by the coordinator upon arrival." if prerequisites.strip() == "" or prerequisites == None  else prerequisites
                msg = get_template_msg(db, SMSTypeEnum.WHATSAPP.name,EventCodeEnum.PATIENT_QUEUE_GUIDANCE.name)
                msg = (msg.replace("<$location>",location).replace("<$queueName>",queue_name).replace("<$prerequisites>",prerequisites))
            if user_queue.status in [UserQueueStatusEnum.ARRIVED,UserQueueStatusEnum.FREEZED,UserQueueStatusEnum.ENTRY,UserQueueStatusEnum.HOLD]:                     
                msg = get_template_msg(db,SMSTypeEnum.WHATSAPP.name,EventCodeEnum.PATIENT_QUEUE_CHECKIN.name)
                msg = msg.replace("<$location>",location)
                if user_queue.vitals_datetime is None and "VITALS" in user_queue.queue.assignment and user_queue.queue.service_type != 'INVESTIGATIONS' and user_queue.status not in [UserQueueStatusEnum.HOLD,UserQueueStatusEnum.ENTRY]:
                    msg = msg.replace("<$msg>","Please Proceed to Vital Counter")  
                elif user_queue.phy_ass_datetime is None and "PHYSICIANASSISTANT" in user_queue.queue.assignment and user_queue.queue.service_type != 'INVESTIGATIONS' and user_queue.status not in [UserQueueStatusEnum.HOLD,UserQueueStatusEnum.ENTRY]:
                    msg = msg.replace("<$msg>","Please Proceed to Physisian Assistant Counter")
                else:
                    query=db.query(UserQueueModel).join(UserQueueModel.queue_weightage_action).join(UserQueueModel.queue).filter(UserQueueModel.date == date.today())
                    query=query.filter(UserQueueModel.queue_id == user_queue.queue_id) 
                    query=query.filter(or_(UserQueueModel.counter==None, UserQueueModel.counter_obj.has(counter_status=StatusEnum.ACTIVE))) 
                    list1 = query.filter(
                            or_(UserQueueModel.status == UserQueueStatusEnum.ARRIVED,
                                UserQueueModel.status == UserQueueStatusEnum.CHECKIN,
                                UserQueueModel.status == UserQueueStatusEnum.HOLD,
                                UserQueueModel.status == UserQueueStatusEnum.FREEZED,
                                UserQueueModel.status == UserQueueStatusEnum.ENTRY)).order_by(
                                    asc(UserQueueModel.freezed_at),
                        text("user_queue.status='FREEZED' desc"),
                        desc((QueueModel.avg_procedure_time*QueueWeightageActionModel.weightage)+(func.extract("epoch",
                            (func.now() - UserQueueModel.order_by_date))/60)),
                        asc(UserQueueModel.created_at)).group_by(UserQueueModel.id, QueueWeightageActionModel.weightage,QueueModel.avg_procedure_time).all()
                    status_queues = {
                        status: [q for q in list1 if q.status == status]
                        for status in [UserQueueStatusEnum.FREEZED, UserQueueStatusEnum.ARRIVED]
                    }
                    people_ahead = 0
                    waiting_msg = "Please remain seated and wait for your turn."

                    if user_queue.status in status_queues:
                        queue_list = status_queues[user_queue.status]
                        index = next((i for i, q in enumerate(queue_list) if q.id == user_queue.id), -1)
                        logger.info(index)
                        if index >= 0:
                            if user_queue.status == UserQueueStatusEnum.FREEZED:
                                people_ahead = index
                                if people_ahead == 0 or people_ahead == 1:
                                    waiting_msg = "Please note, you are next in line."
                                else:
                                    waiting_msg = f'Please note, you are {get_ordinal(people_ahead)} in line.'
                            if user_queue.status == UserQueueStatusEnum.ARRIVED:
                                index = index + 1
                                people_ahead = index + (index if index in [1, 2] else 3)
                                if user_queue.queue.capacity >0:
                                    index + (len(status_queues.get(UserQueueStatusEnum.FREEZED, []))/user_queue.queue.capacity)
                                waiting_msg = f'Please note, you are {get_ordinal(people_ahead)} in line.'

                    if user_queue.status == UserQueueStatusEnum.ENTRY:
                        counter_name = db.query(QueueCounterModel.counter_name).filter(
                            QueueCounterModel.id == user_queue.counter
                        ).scalar()
                        waiting_msg = f"Kindly proceed to the {counter_name}"
                    if user_queue.status == UserQueueStatusEnum.HOLD:
                        waiting_msg = "You are currently on hold in the queue"

                    msg = msg.replace("<$msg>", waiting_msg)
                    if user_queue.status in [UserQueueStatusEnum.FREEZED,UserQueueStatusEnum.ENTRY,UserQueueStatusEnum.HOLD]:
                        msg = waiting_msg
        elif user_last_queue is not None:
            completed_status_as_int = case([
                (UserServiceModel.status == ServiceStatusEnum.COMPLETED, 1),
                ], else_=0)
            completed_token = db.query(
                        UserTokenModel.id
                    ).filter(UserTokenModel.id==user_last_queue.token_id).group_by(UserTokenModel.id).having(
                        func.count(UserServiceModel.id) == func.sum(completed_status_as_int)
                    ).join(UserTokenModel.user_services).first()
            if completed_token is not None:
                service = db.query(ServiceModel.type, ServiceModel.reports_collection, ServiceModel.reports_completion_time, ServiceModel.source,ServiceModel.service_type).join(UserServiceModel.service).filter(
                    UserServiceModel.user_id == user_last_queue.user.id).filter(UserServiceModel.id.in_(
                        db.scalars(db.query(RelUserServiceQueueModel.user_service_id).filter(
                    RelUserServiceQueueModel.user_queue_id == user_last_queue.id))
                    ), UserServiceModel.service_id==ServiceModel.id).first()
                if service.service_type == 'CONSULTATIONS':
                    msg ="Your Consultation is now Complete."
                else:
                    msg = get_template_msg(db,SMSTypeEnum.WHATSAPP.name,EventCodeEnum.PATIENT_EXIT.name)
                    msg = msg.replace("<$time>",f"{int(service.reports_completion_time)} hours").replace("<$location>",service.reports_collection)
        
        return msg,service_data 
    except Exception as e:
        logger.info(e)
        raise MutationError("Error occured while getting user status")
# async def update_data_vitals(db:Session):
#     uhIds = (
#         db.query(UserModel.umr_no)
#         .join(UserQueueModel, UserQueueModel.user_id == UserModel.id)  # Correct join
#         .filter(
#             and_(
#                 UserQueueModel.status != UserQueueStatusEnum.EXIT,
#                 UserQueueModel.status != UserQueueStatusEnum.PURGED
#             )
#         ).filter(
#             UserQueueModel.vitals_datetime.is_(None)
#         )
#         .all()  # Fetch list of umr_no values
#     )
#     uhIds = [row[0] for row in uhIds]
#     logger.info(uhIds)
#     token = "Bearer " + os.environ["ONE_AIG_TOKEN"]
#     headers = {"Authorization": token}
#     res1 = handle_request1(
#         os.environ["ONE_AIG_BASE_URL"] + GET_VITAL_STATUS_BY_UHIDS,
#         headers,{
#             "uhIds":uhIds,
#             "type":"VITALS"
#         })
#     print(res1.status_code)
#     if res1.status_code == 200:
#         print(res1.json())
#         for data in res1.json():
#             print(data)
#             user= db.query(UserModel).filter(UserModel.umr_no==data["patientId"]).one_or_none()
#             print(user)
#             if user is not None:
#                 value= db.query(UserQueueModel).filter(UserQueueModel.user_id==user.id).filter(
#                 and_(
#                     UserQueueModel.status != UserQueueStatusEnum.EXIT,
#                     UserQueueModel.status != UserQueueStatusEnum.PURGED
#                 )
#             ).update(
#                  {UserQueueModel.vitals_datetime:data["startDateTime"],
#                         },
#                     synchronize_session="fetch",
#                 ) 
#                 print(value)
#             db.commit()
#     else:
#         raise MutationError("Error Updating Vitals")
    
def update_data_phy_ass(db:Session):
    uhIds = (
        db.query(UserModel.umr_no)
        .join(UserQueueModel, UserQueueModel.user_id == UserModel.id)  # Correct join
        .join(UserQueueModel.queue).filter(
            and_(
                UserQueueModel.status != UserQueueStatusEnum.EXIT,
                UserQueueModel.status != UserQueueStatusEnum.PURGED
            )
        ).filter(
            UserQueueModel.phy_ass_datetime.is_(None)
        )
        .all()  # Fetch list of umr_no values
    )
    uhIds = [row[0] for row in uhIds]
    logger.info(uhIds)
    config_data = db.query(ConfigModel.data).filter(ConfigModel.hospital_code=="1100").one()
    config_data= json.loads(config_data.data)
    token = "Bearer " + config_data.get("one_aig_token","")
    headers = {"Authorization": token}
    res1 = handle_request1(
        os.environ["ONE_AIG_BASE_URL"] + GET_VITAL_STATUS_BY_UHIDS,
        headers,{
            "uhIds":uhIds,
            "type":"PHY_ASS"
        })
    print(res1.status_code)
    if res1.status_code == 200:
        print(res1.json())
        for data in res1.json():
            user_queue=db.query(UserQueueModel.id).join(UserQueueModel.user).filter(UserModel.umr_no==data["patientId"]).filter(and_(UserQueueModel.status != UserQueueStatusEnum.EXIT,UserQueueModel.status != UserQueueStatusEnum.PURGED)).first()
            update_pre_check_status(db, user_queue[0], None, "PHYSICIANASSISTANT", None, data["startDateTime"])
            # print(data)
            # user= db.query(UserModel).filter(UserModel.umr_no==data["patientId"]).one_or_none()
            # print(user)
            # if user is not None:
            #     value= db.query(UserQueueModel).filter(UserQueueModel.user_id==user.id).filter(
            #     and_(
            #         UserQueueModel.status != UserQueueStatusEnum.EXIT,
            #         UserQueueModel.status != UserQueueStatusEnum.PURGED
            #     )
            # ).update(
            #      {UserQueueModel.phy_ass_datetime:data["startDateTime"],
            #             },
            #         synchronize_session="fetch",
            #     ) 
            #     print(value)
            # db.commit()
            # send_arrival_confirmation_msg(db, user)
    else:
        raise MutationError("Error Updating Physician Assistant")
 

def update_data_vitals(db:Session):
    uhIds = (
        db.query(UserModel.umr_no)
        .join(UserQueueModel, UserQueueModel.user_id == UserModel.id).filter(
            and_(
                UserQueueModel.status != UserQueueStatusEnum.EXIT,
                UserQueueModel.status != UserQueueStatusEnum.PURGED
            )
        ).filter(
            UserQueueModel.vitals_datetime.is_(None)
        )
        .all()  # Fetch list of umr_no values
    )
    uhIds = [row[0] for row in uhIds]
    logger.info(uhIds)
    config_data = db.query(ConfigModel.data).filter(ConfigModel.hospital_code=="1100").one()
    config_data= json.loads(config_data.data)
    token = "Bearer " + config_data.get("one_aig_token","")
    headers = {"Authorization": token}
    res1 = handle_request1(
        os.environ["ONE_AIG_BASE_URL"] + GET_VITAL_STATUS_BY_UHIDS,
        headers,{
            "uhIds":uhIds,
            "type":"VITALS"
        })
    print(res1.status_code)
    if res1.status_code == 200:
        print(res1.json())
        for data in res1.json():
            user_queue=db.query(UserQueueModel.id).join(UserQueueModel.user).filter(UserModel.umr_no==data["patientId"]).filter(and_(UserQueueModel.status != UserQueueStatusEnum.EXIT,UserQueueModel.status != UserQueueStatusEnum.PURGED)).first()
            update_pre_check_status(db, user_queue[0], None, "VITALS", None, data["startDateTime"])
            # send_arrival_confirmation_msg(db, user)
    else:
        raise MutationError("Error Updating Vitals")

def get_ordinal(n):
    n = n + 1
    if 10 <= n % 100 <= 20:
        suffix = "th"
    else:
        suffix = {1: "st", 2: "nd", 3: "rd"}.get(n % 10, "th")
    return f"{n}{suffix}"

def send_arrival_confirmation_msg(db,user):
    user_queue = db.query(UserQueueModel).filter(UserQueueModel.user_id==user.id).filter(or_(
                    UserQueueModel.status == UserQueueStatusEnum.ARRIVED,
                    UserQueueModel.status == UserQueueStatusEnum.FREEZED
                )).order_by(UserQueueModel.created_at.desc()).first()
    if user_queue:
        if user_queue.vitals_datetime is None:
            text_msg = "Proceed to Vital Counter"
        if user_queue.phy_ass_datetime is None:
            text_msg = "Proceed to Physician Assistant Counter"
    queue: QueueModel= db.query(QueueModel).filter(QueueModel.id== user_queue.queue_id).one()
    # tmp_name = get_template_name(db, SMSTypeEnum.WHATSAPP.name,EventCodeEnum.PATIENT_ARRIVAL_CONFIRMATION.name)
    # params = [{"type":"text","text":f"Tower {queue.cluster.tower}->Floor {queue.cluster.floor}->Location {queue.cluster.cluster}"},
    #                 {"type":"text","text":text_msg}]
    # print(params)
    # send_msg = send_whatsapp_msg(db, tmp_name, user.phone_number, params,None)
       
def update_data_from_emr(db:Session):
    update_data_vitals(db)
    update_data_phy_ass(db)
    
def handle_cancelled_services(db: Session, his_db: Session):
    try:
        pending_services= db.scalars(db.query(UserServiceModel.detail_bill_id).filter(UserServiceModel.status==ServiceStatusEnum.PENDING).filter(UserServiceModel.detail_bill_id.is_not(None))).all()
        ongoing_services= db.scalars(db.query(UserServiceModel.detail_bill_id).filter(UserServiceModel.status==ServiceStatusEnum.ON_PROGRESS).filter(UserServiceModel.detail_bill_id.is_not(None))).all()
        if len(pending_services)>0:  
            sql_select_Query= text("""
                SELECT id FROM d_opbill dp
                WHERE dp.id IN :ids AND dp.cancelled = 1
            """).bindparams(bindparam("ids", expanding=True))
            logger.info(sql_select_Query)
            res_data = his_db.execute(sql_select_Query, {"ids": pending_services}).all()
            logger.info(res_data)
            if len(res_data)>0:
                logger.info(res_data)
                cancelled_services= [str(row[0]) for row in res_data]
                db.query(UserServiceModel).filter(UserServiceModel.detail_bill_id.in_(cancelled_services)).update(
                    {UserServiceModel.status: ServiceStatusEnum.CANCELLED
                        },
                    synchronize_session="fetch",
                )
                db.commit()
        if len(ongoing_services)>0:
            # placeholders = ', '.join(['%s'] * len(ongoing_services))
            # sql_select_Query = f"""
            #     SELECT id from d_opbill dp where dp.id in ({placeholders}) and dp.cancelled=1
            # """
            sql_select_Query= text("""
                SELECT id FROM d_opbill dp
                WHERE dp.id IN :ids AND dp.cancelled = 1
            """).bindparams(bindparam("ids", expanding=True))
            logger.info(sql_select_Query)
            res_data = his_db.execute(sql_select_Query, {"ids": ongoing_services}).all()
            # res_data = his_db.execute(sql_select_Query,ongoing_services).all()
            if len(res_data)>0:
                cancelled_services= [str(row[0]) for row in res_data]
                results = db.query(
                    UserQueueModel.id.label("user_queue_id"),
                    func.array_agg(
                        case(
                            [(RelUserServiceQueueModel.status == ServiceStatusEnum.PENDING, RelUserServiceQueueModel.user_service_id)],
                            else_=None
                        )
                    ).label('pending_services_ids'),
                    func.array_agg(
                        case(
                            [(UserServiceModel.detail_bill_id.in_(cancelled_services), RelUserServiceQueueModel.user_service_id)],
                            else_=None
                        )
                    ).label('cancelled_services_ids')
                ).join(
                    RelUserServiceQueueModel, RelUserServiceQueueModel.user_queue_id == UserQueueModel.id
                ).join(
                    RelUserServiceQueueModel.user_service
                ).filter(UserQueueModel.id.in_(db.query(UserQueueModel.id).join(RelUserServiceQueueModel, RelUserServiceQueueModel.user_queue_id == UserQueueModel.id).join(
                    RelUserServiceQueueModel.user_service
                ).filter(UserServiceModel.detail_bill_id.in_(cancelled_services)).scalar_subquery())).group_by(
                    UserQueueModel.id
                ).all()
                for result in results:
                    logger.info(results)
                    try:
                        user_queue_id = result.user_queue_id
                        pending_services_ids = [service_id for service_id in result.pending_services_ids if service_id is not None]
                        cancelled_services_ids = [service_id for service_id in result.cancelled_services_ids if service_id is not None]
                        # Find the difference: pending - cancelled
                        difference = list(set(pending_services_ids) - set(cancelled_services_ids))
                        if len(difference)==0:
                            update_user_queue(db, user_queue_id,None,"PURGED",None,ServiceStatusEnum.CANCELLED)
                        else:
                            db.query(UserServiceModel).filter(UserServiceModel.detail_bill_id.in_(cancelled_services)).update(
                                {UserServiceModel.status: ServiceStatusEnum.CANCELLED
                                    },
                                synchronize_session="fetch",
                            )
                            db.query(RelUserServiceQueueModel).filter(RelUserServiceQueueModel.user_queue_id==user_queue_id).filter(RelUserServiceQueueModel.user_service_id.in_(db.query(UserServiceModel).filter(UserServiceModel).detail_bill_id.in_(cancelled_services).subquery())).update(
                                {RelUserServiceQueueModel.status: ServiceStatusEnum.CANCELLED
                                    },
                                synchronize_session="fetch",
                            )
                        db.commit()
                    except Exception as ex:
                        logger.exception(ex)
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to save changes")    


def get_employee_details(db:Session,employee_id:str):
    config_data = db.query(ConfigModel.data).filter(ConfigModel.hospital_code=="1100").one()
    config_data= json.loads(config_data.data)
    token = "Bearer " + config_data.get("one_aig_token","")
    headers = {"Authorization": token}
    res1 = handle_get_request(
        os.environ["ONE_AIG_BASE_URL"] + GET_EMPLOYEE_DETAILS + employee_id,
        None,
        headers
    )
    print(res1.status_code)
    print(res1.json())
    if res1.status_code == 200:
        data = res1.json()
        mapped_doctors = data.get("mappedDoctors",[])
        if mapped_doctors:
            doc_ids = [doc.get("employeeId") for doc in mapped_doctors]
            queue_ids = db.query(QueueModel.id).filter(QueueModel.queue_code.in_(doc_ids)).all()
            data["mapped_queues"]= [qid[0] for qid in queue_ids]
            return data
        return data
    else:
        error = res1.json().get("message", "Error while fetching employee details")
        raise MutationError(error)
    
def bar_code_generation(db: Session, hosp_code: str, user_service_ids: List[int]):
    try:
        if not user_service_ids:
            return [], "No service IDs provided", {}

        results = db.query(UserServiceModel).join(UserModel).filter(
            UserServiceModel.id.in_(user_service_ids),
            UserServiceModel.sample_no.is_(None),
            UserServiceModel.order_details.isnot(None)
        ).all()

        if not results:
            return [], "Not Generated", {}

        token = "Bearer " + get_access_token()["access_token"]
        body = [{
            "hospCode": hosp_code,
            "episode": "OP",
            "uhid": detail.user.umr_no,
            "billNo": detail.bill,
            "orderId": detail.order_details.get("orderId"),
            "testId": detail.test_id,
            "operatorId": "Itadmin"
        } for detail in results]

        response = handle_request(os.environ["AIG_BASE_URL"] + GENERATE_BAR_CODE,
                                {"Authorization": token}, body)
        response_json = response.json()
        msg = response_json.get("message")
        if response.status_code == 200:
            response_data = response_json.get("response", [])
            sample_map = {item["testId"]: item["sampleNo"] for item in response_data}
            service_id_map = {item.test_id: item.id for item in results}

            updated_count = 0
            for row in results:
                if row.test_id in sample_map:
                    row.sample_no = sample_map[row.test_id]
                    updated_count += 1
            if updated_count > 0:
                db.commit()
                logger.info(f"Updated {updated_count} sample numbers")
            return response_data, msg, service_id_map
        return [], msg, {}
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error while generating bar code")

def get_sample_collection_type(db: Session, user_service_ids: List[int], update_time: str):
    if not user_service_ids:
        return [], []

    results = db.query(UserServiceModel).join(UserModel).filter(
        UserServiceModel.id.in_(user_service_ids),
        UserServiceModel.sample_no.isnot(None),
        UserServiceModel.order_details.isnot(None)
    ).all()

    if not results:
        return [], []

    base = {"hospCode": "1100", "episode": "OP", "operatorId": "Itadmin"}
    pathology_items, other_items, pathology_user_ids, other_user_ids = [], [], [], []

    for obj in results:
        data = {**base, "uhid": obj.user.umr_no, "billNo": obj.bill,
                "orderId": obj.order_details.get("orderId"), "testId": obj.test_id}

        if obj.order_details.get("department") == "PATHOLOGY":
            pathology_items.append({**data, "sampleNo": obj.sample_no, "sampleCollectionDateTime": update_time})
            pathology_user_ids.append(obj.id)
        else:
            other_items.append({**data, "ackDateTime": update_time})
            other_user_ids.append(obj.id)

    def update_services(user_ids, msg, service_type):
        try:
            db.query(UserServiceModel).filter(UserServiceModel.id.in_(user_ids)).update(
                {"test_update_time": update_time, "test_update_msg": msg}, synchronize_session="fetch")
            db.commit()
            logger.info(f"Updated {len(user_ids)} {service_type} services: {msg}")
        except Exception as e:
            logger.exception(f"Failed to update {service_type} services: {e}")
            db.rollback()

    # Process API calls and update database
    if pathology_items:
        update_services(pathology_user_ids, sample_collection_update(pathology_items)[1], "pathology")

    if other_items:
        update_services(other_user_ids, get_acknowledge_test_details(other_items)[1], "acknowledge")

    return pathology_items, other_items
   
   
def _make_api_request(endpoint: str, body: list, operation_name: str):
    try:
        if not body:
            return True, f"No {operation_name} items to process"

        response = handle_request(os.environ["AIG_BASE_URL"] + endpoint,
                                {"Authorization": "Bearer " + get_access_token()["access_token"]}, body)

        result = response.json()
        logger.info(f"{operation_name} API - Processed {len(body)} items: {result}")
        return result.get("isSuccess", False), result.get("message", "Unknown error")

    except Exception as e:
        logger.exception(f"Error in {operation_name}: {e}")
        raise MutationError(f"Error processing {operation_name}")

def sample_collection_update(body):
    return _make_api_request(UPDATE_SAMPLE_COLLECTION, body, "sample collection")

def get_acknowledge_test_details(body):
    return _make_api_request(GET_ACKNOWLEDGE_DETAILS, body, "acknowledge test")

def get_order_details(hosp_code:str,uhid:str,bill_no:str):
    try:
        res = get_access_token()
        token = "Bearer " + res["access_token"]
        headers = {"Authorization": token}
        logger.info("Calling external API for order details...")
 
        response = handle_request(
            os.environ["AIG_BASE_URL"] + GET_ORDER_DETAILS_URL,
            headers,
            {
                "hospCode":hosp_code,
                "episode": "OP",
                "uhid": uhid,
                "billNo": bill_no,
                "operatorId": "Itadmin"
            },
        )
        if response.status_code == 200:
            return response.json().get("response")  
        else:
            raise MutationError("Failed to fetch order details: " + response.text)
 
    except Exception as ex:
        logger.error(f"Error in get_order_details: {ex}")
        raise MutationError("Failed to fetch order details")