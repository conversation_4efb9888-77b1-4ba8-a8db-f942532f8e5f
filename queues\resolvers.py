from collections import defaultdict
import json
import math
import random
import threading
import time
from typing import List, Optional
import pytz
from bill.models import ServiceStatusEnum, UserService as UserServiceModel, UserToken as UserTokenModel
from bms.resolvers import get_staff_user
from database.db_conf import SessionLocal
from exceptions.exceptions import MutationError
from sms_mail_notification.models import EventCodeEnum, SMSTypeEnum
from sms_mail_notification.resolvers import get_template_name, get_whatsapp_details, send_whatsapp_msg
from user.models import StatusEnum, Device as DeviceModel
from service.models import Service as ServiceModel
from queues.models import (Cluster as ClusterModel, Location as LocationModel, LocationBedStatusEnum, QueueCounter as QueueCounterModel, QueueCounterStatusEnum, QueueStep as QueueStepModel, QueueTypeEnum, QueueWeightageAction as QueueWeightageActionModel, 
                           RelQueueService as RelQueueServiceModel, RelUserServiceQueue as RelUserServiceQueueModel, Tag as TagModel, UserPreReqStatusEnum, UserQueue as UserQueueModel, Queue as QueueModel, 
                           RelStaffUserQueue as RelStaffUserQueueModel, QueueAuditLogs as QueueAuditLogsModel, UserQueueLogs as UserQueueLogsModel, UserQueuePreCheckStatusEnum, UserQueueStatusEnum, RelDeviceQueue as RelDeviceQueueModel, UserQueueStep as UserQueueStepModel, 
                           UserQueueStepStatusEnum, UserServicePrerequisite as UserServicePrerequisiteModel)
from sqlalchemy.orm import Session, joinedload,aliased
import logging
from datetime import date, datetime, timedelta
from graphql_types import QRDetail, QueueInput , TagInput
from user.models import User as UserModel
from sqlalchemy import update, asc, desc, func, literal_column, or_, and_, text, Integer, case, extract, distinct
from staff_user.models import StaffUser as StaffUserModel
from staff_user.resolvers import model_to_dict, CustomJSONEncoder
from sqlalchemy.exc import IntegrityError
import os
from jose import jws
import requests
from util.email import send_email
logger = logging.getLogger()
import redis
from redis_lock import RedisLock
from util.globals import client_lock, remove_assigned_tokens_list,handle_request1
from constants import CREATE_TAG,DELETE_TAG, UPDATE_TAG
from sqlalchemy.dialects import postgresql

lock = threading.RLock()
lock1 = threading.RLock()

# client = redis.Redis(host="opd_redis", port=6379)

def add_or_update_queue(db: Session, data: QueueInput):
    with RedisLock(client_lock, "queue_"+str(0 if data.queue_id is None else data.queue_id), blocking_timeout=60):
        try:
            logger.info(data)
            queue_id = None
            json_data = {}
            inital_capacity = None
            final_capacity = None
            pseudo_capacity=0
            queue_data = db.query(QueueModel).filter(
                    QueueModel.id == data.queue_id).one_or_none()
            if data.counters == None or len(data.counters) <= 0:
                raise MutationError("Queue Should have atleast one counter")
            if data.queue_id is None or data.queue_id == 0:
                queue_data = QueueModel(
                    queue_name=data.queue_name,
                    queue_code=data.queue_code,
                    queue_type= data.queue_type,
                    service_type= data.service_type,
                    avg_procedure_time=data.avg_procedure_time,
                    cluster_id=data.cluster_id,
                    upcoming_patients=data.upcoming_patients,
                    show_patient_name=data.show_patient_name if data.show_patient_name is not None else False,
                    capacity = len(data.counters),
                    buffer_time = data.buffer_time if data.buffer_time is not None else 2,
                    waiting_capacity = data.waiting_capacity if data.waiting_capacity is not None else None
                )
                db.add(queue_data)
                db.flush()
                staff_user_dict = model_to_dict(queue_data)
                json_data = json.dumps(staff_user_dict, cls=CustomJSONEncoder)
                action = "ADDED"
                queue_id = queue_data.id
            else:
                inital_capacity = queue_data.capacity
                queue_data.queue_name = data.queue_name
                queue_data.service_type= data.service_type
                queue_data.queue_type= data.queue_type
                queue_data.avg_procedure_time = data.avg_procedure_time
                queue_data.cluster_id = data.cluster_id
                queue_data.upcoming_patients = data.upcoming_patients
                queue_data.buffer_time = data.buffer_time if data.buffer_time is not None else 2
                queue_data.waiting_capacity = data.waiting_capacity if data.waiting_capacity is not None else None
                queue_data.show_patient_name=data.show_patient_name if data.show_patient_name is not None else False
                json_data["queue_code"] = data.queue_code
                json_data["avg_procedure_time"] = data.avg_procedure_time
                json_data["cluster_id"] = data.cluster_id
                json_data["upcoming_patients"] = data.upcoming_patients
                json_data = json.dumps(json_data)
                action = "UPDATED"
                queue_id = data.queue_id
                final_capacity = queue_data.capacity
            queue_counters = db.query(QueueCounterModel).filter(QueueCounterModel.queue_id==queue_id).all()
            counters_list = list(map(lambda x: x.counter, queue_counters))
            for counter in data.counters:
                if counter.id is not None and counter.id != 0:
                    queue_counters = db.query(QueueCounterModel).filter(QueueCounterModel.id==counter.id).first()
                    queue_counters.upcoming_capacity=math.ceil(queue_data.upcoming_patients/queue_data.capacity)
                    if counter.status == 'INACTIVE'and (queue_counters.status == QueueCounterStatusEnum.ALLOTED and queue_counters.counter_status == StatusEnum.ACTIVE):
                        pseudo_capacity += 1
                        queue_counters.counter_status= "INACTIVE"
                    else:
                        queue_counters.counter_status= StatusEnum(counter.status)
            db.add_all(
                QueueCounterModel(queue_id=queue_id, counter=counter.number, counter_name = counter.code
                                , counter_code = f"COUNTER_{counter.number}",status= QueueCounterStatusEnum.UNALLOTED, counter_status = StatusEnum(counter.status),upcoming_patients =math.ceil(queue_data.upcoming_patients/queue_data.capacity))
                for counter in data.counters 
                if int(counter.number) not in counters_list
            )
            db.query(QueueModel).filter(QueueModel.id==queue_id).update(
                    {QueueModel.pseudo_capacity:pseudo_capacity
                        },
                    synchronize_session="fetch",
                )
            db.flush()
            final_capacity = db.query(QueueCounterModel).filter(QueueCounterModel.queue_id == queue_id , QueueCounterModel.counter_status != StatusEnum.INACTIVE)
            queue_data.capacity = final_capacity.count()
            if inital_capacity is not None and  inital_capacity < queue_data.capacity:
                for queue_count in final_capacity.all():
                    if queue_count.status==QueueCounterStatusEnum.UNALLOTED:
                        ongoing_count,freezed_count=entry_or_freeze_next_users(db, queue_data,queue_count.id)
                        db.query(QueueModel).filter(QueueModel.id==queue_id).update(
                            {QueueModel.ongoing_count: QueueModel.ongoing_count+ongoing_count,
                                QueueModel.freezed_count: QueueModel.freezed_count+freezed_count
                                },
                            synchronize_session="fetch",
                        )
                        db.flush()
            db.query(RelStaffUserQueueModel).filter(
                RelStaffUserQueueModel.queue_id == queue_data.id).delete()
            if data.staff_user_ids is not None:
                for staff_user_id in data.staff_user_ids:
                    rel_queue_user_data = RelStaffUserQueueModel(
                        staff_user_id=staff_user_id,
                        queue_id=queue_id
                    )
                    db.add(rel_queue_user_data)
            db.query(RelQueueServiceModel).filter(
                RelQueueServiceModel.queue_id == queue_data.id).delete()
            if data.test_ids is not None:
                for test_id in data.test_ids:
                    rel_queue_service = RelQueueServiceModel(
                        test_id=test_id,
                        queue_id=queue_id
                    )
                    db.add(rel_queue_service)
            log_data = QueueAuditLogsModel(
                queue_code=data.queue_code,
                data=json_data,
                action=action
            )
            db.add(log_data)
            db.commit()
            return get_queues(db)
        except IntegrityError as e:
            logger.exception(e)
            raise MutationError("Queue already exists with this name")
        except MutationError as e:
            logger.exception(e)
            raise MutationError(e.message)
        except Exception as e:
            logger.exception(e)
            raise MutationError("Error occured While saving data")
        
def add_or_update_tag(db: Session,data : TagInput):
    try:
        if data.rfid_code is None or data.rfid_code=="":
            raise MutationError("rfid Code is required")
        tag_data = db.query(TagModel).filter((TagModel.id==data.id)).one_or_none()
        if tag_data != None:
            db.query(TagModel).filter(TagModel.id == data.id).update({
                TagModel.name : data.name,
                TagModel.code:data.code,
                TagModel.rfid_code : data.rfid_code,
                TagModel.status : data.status
            })
            db.commit()
            return "Updated Successfully"
        add_tag = TagModel(
            name= data.name,
            code= data.code,
            rfid_code= data.rfid_code,
            status = data.status
        )
        db.add(add_tag)
        db.commit()
        return "Created Successfully"
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured While Saving data")



def get_queues(db: Session):
    return db.query(QueueModel).filter(QueueModel.deleted_at == None).order_by(QueueModel.created_at).all()


def delete_queue(db: Session, queue_id: int):
    try:
        queue = db.query(QueueModel).filter(
            QueueModel.id == queue_id).one_or_none()
        queue.deleted_at = datetime.now() if queue is not None else None
        db.commit()
    except MutationError as e:
        logger.exception(e)
        raise MutationError(e.message)
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured While deleting queue")


def get_user_queue(db: Session, queue_id: int, pre_check_status:str):
    today = date.today()
    queue: QueueModel= db.query(QueueModel).filter(QueueModel.id==queue_id).one()
    query=db.query(UserQueueModel).join(UserQueueModel.queue_weightage_action).filter(UserQueueModel.date == today)
    if pre_check_status is not None:
        query=query.filter(UserQueueModel.pre_check_status==UserQueuePreCheckStatusEnum.PENDING, UserQueueModel.status==UserQueueStatusEnum.ARRIVED)
    else:
        query=query.filter(
        UserQueueModel.queue_id == queue_id)    
    list1 = query.filter(
            or_(UserQueueModel.status == UserQueueStatusEnum.ARRIVED,
                UserQueueModel.status == UserQueueStatusEnum.CHECKIN,
                UserQueueModel.status == UserQueueStatusEnum.HOLD,
                UserQueueModel.status == UserQueueStatusEnum.FREEZED,
                UserQueueModel.status == UserQueueStatusEnum.ENTRY)).order_by(
                    asc(UserQueueModel.freezed_at),
        text("user_queue.status='FREEZED' desc"),
        desc(QueueWeightageActionModel.weightage+(func.extract("epoch",
             (func.now() - UserQueueModel.estimated_time))/60)),
        asc(UserQueueModel.created_at)).group_by(UserQueueModel.id, QueueWeightageActionModel.weightage)
    if pre_check_status is not None:
        list1=list1.limit(queue.upcoming_patients)
    return list1.all()


def get_current_user_queue(db: Session, queue_id: int):
    today = date.today()
    list1 = db.query(UserQueueModel).join(UserQueueModel.queue_weightage_action).filter(
        UserQueueModel.queue_id == queue_id).filter(UserQueueModel.date == today).filter(
        UserQueueModel.status == UserQueueStatusEnum.ENTRY,
    ).order_by(
        asc(UserQueueModel.freezed_at),
        text("user_queue.status='FREEZED' desc"),
        desc(QueueWeightageActionModel.weightage+(func.extract("epoch",
             (func.now() - UserQueueModel.estimated_time))/60)),
        asc(UserQueueModel.created_at)).group_by(UserQueueModel.id, QueueWeightageActionModel.weightage).all()
    return list1

def get_queue_counter(db:Session, queue_id:int, counter):
    try:
        query= db.query(QueueCounterModel).filter(QueueCounterModel.queue_id==queue_id).filter(
            QueueCounterModel.status==QueueCounterStatusEnum.UNALLOTED, QueueCounterModel.counter_status == StatusEnum.ACTIVE)
        if counter is not None:
            query=query.filter(QueueCounterModel.id==counter)
        counter=query.order_by(
                asc(QueueCounterModel.priority)).first()
        if counter==None:
            return  None
        else:
            return counter
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error in queue counters")

def get_queue_counter_freeze(db:Session, queue:QueueModel, user_queue: str, condition, counter_id):
    try:
        query_1= db.query(QueueCounterModel).filter(QueueCounterModel.queue_id==queue.id)
        freezed_count=0
        if condition =="FREEZED":
            query_1=query_1.filter(QueueCounterModel.status==QueueCounterStatusEnum.ALLOTED)
        if condition == "ENTRY":
            freezed_count=-1
            query_1=query_1.filter(QueueCounterModel.status==QueueCounterStatusEnum.UNALLOTED)
        count=case(
        [
            (
                QueueCounterModel.status==QueueCounterStatusEnum.ALLOTED, True
            )
        ],else_=(QueueCounterModel.upcoming_capacity-QueueCounterModel.freeze_count>=0)
    )
        query_1=query_1.filter(
            count
        )
            # query_1=query_1.filter(
            #     QueueCounterModel.upcoming_capacity-QueueCounterModel.freeze_count>0
            #     )
        if counter_id is not None:
            query_1=query_1.filter(QueueCounterModel.id==counter_id)
        if condition == "FREEZED" or condition is None:
            query_1=query_1.filter(
            QueueCounterModel.counter_status == StatusEnum.ACTIVE)
        counter=query_1.order_by(desc(QueueCounterModel.status),desc(QueueCounterModel.upcoming_capacity-QueueCounterModel.freeze_count),asc(QueueCounterModel.priority)).first()
        logger.info(counter)
        status=None
        start_time= None
        freezed_at = None
        allocate_counter_at = None
        if counter==None:
            return  None, None
        else:                                        
            if counter.status==QueueCounterStatusEnum.ALLOTED:
                count= db.query(QueueCounterModel).filter(QueueCounterModel.id==counter.id).filter(QueueCounterModel.upcoming_capacity-QueueCounterModel.freeze_count>0).update({
                    QueueCounterModel.freeze_count: QueueCounterModel.freeze_count+1
                },synchronize_session="fetch")
                if count>0:
                    status= UserQueueStatusEnum.FREEZED
                    if queue.allocate_counter_at ==UserQueueStatusEnum.FREEZED:
                        counter_id=counter.id
                        allocate_counter_at=UserQueueStatusEnum.FREEZED
                    freezed_at= func.now()
                else:
                    return None, None
                # user_queue.counter=counter.id
            else:
                value=db.query(QueueCounterModel).filter(QueueCounterModel.id==counter.id).filter(QueueCounterModel.status==QueueCounterStatusEnum.UNALLOTED).update({
                    QueueCounterModel.status: QueueCounterStatusEnum.ALLOTED,
                    QueueCounterModel.freeze_count:QueueCounterModel.freeze_count+freezed_count
                },synchronize_session="fetch")
                logger.info("Allocate Queue counter")
                status = UserQueueStatusEnum.ENTRY
                if user_queue.counter== None:
                    if queue.allocate_counter_at ==UserQueueStatusEnum.FREEZED or queue.allocate_counter_at ==UserQueueStatusEnum.ENTRY:
                        counter_id=counter.id
                        allocate_counter_at=UserQueueStatusEnum.ENTRY
                    # user_queue.counter=counter.id
                start_time=func.now()
            queue_step_id,location_id = add_user_queue_step(db,None,user_queue.id,"entry or freeze", "CHECKIN",status,queue, None,None,None, None)
            db.query(UserQueueModel).filter(UserQueueModel.id==user_queue.id).update(
                {
                    UserQueueModel.status:status,
                    UserQueueModel.start_time:UserQueueModel.start_time if start_time is None else start_time,
                    UserQueueModel.counter:counter_id,
                    UserQueueModel.freezed_at: UserQueueModel.freezed_at if freezed_at is None else freezed_at,
                    UserQueueModel.allocate_counter_at : UserQueueModel.allocate_counter_at if allocate_counter_at is None else allocate_counter_at,
                    UserQueueModel.queue_step_id: queue_step_id,
                    UserQueueModel.location_id: location_id,
                    UserQueueModel.step_start_time: func.now(),
                    UserQueueModel.next_step_start_time: None,
                },synchronize_session="fetch"
            )
            return counter, status
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error in queue counters")
    
def arrived_msg(db:Session,queue: QueueModel,estimated_time,phone_number, status):
    try:
        if status== UserQueueStatusEnum.ENTRY:
            total_minutes=0
        elif status== UserQueueStatusEnum.FREEZED:
            total_minutes = queue.freezed_count * queue.avg_procedure_time * queue.deviation_rate
        else:
            estimated_time = estimated_time - datetime.now(pytz.timezone('asia/kolkata'))
            if '-' in str(estimated_time):
                total_minutes = queue.freezed_count * queue.avg_procedure_time * queue.deviation_rate
            else:
                time = str(estimated_time).split(':')
                total_minutes= int(time[0])*60+int(time[1])*1 +float(time[2])/60
            if total_minutes < 0:
                total_minutes =0
        tmp_name = get_template_name(db, SMSTypeEnum.WHATSAPP.name,EventCodeEnum.PATIENT_QUEUE_CHECKIN.name)      
        params = [{"type":"text","text":f"Tower {queue.cluster.tower}->Floor {queue.cluster.floor}->Location {queue.cluster.cluster}"},
                {"type":"text","text":f"{int(total_minutes)} minutes"}]
        send_msg = send_whatsapp_msg(db, tmp_name, phone_number, params,None)
    except Exception as e:
        logger.exception(e)
        pass
   
def get_opt_queue(db: Session, services, token_id):
    if len(services)==0:
        return None
    services1= db.scalars(db.query(UserServiceModel.service_id).join(UserServicePrerequisiteModel,UserServiceModel.id==UserServicePrerequisiteModel.user_service_id).filter(UserServiceModel.token_id==token_id,or_(UserServicePrerequisiteModel.status==UserPreReqStatusEnum.HOLD,UserServicePrerequisiteModel.status==UserPreReqStatusEnum.WAITING))).all()
    queue= db.query(QueueModel).join(QueueModel.services).filter(
                ServiceModel.id.in_(services)).filter(ServiceModel.id.not_in(services1)).filter(
                    QueueModel.status == StatusEnum.ACTIVE).order_by(
                        queue_query_filter).group_by(
                        QueueModel.id).first()
    logger.info("total Queues order")
    total_queues=db.query(QueueModel).join(QueueModel.services).filter(
                ServiceModel.id.in_(services)).filter(
                    QueueModel.status == StatusEnum.ACTIVE).order_by(
                        queue_query_filter).group_by(
                        QueueModel.id).all()
    logger.info("selected Queue"+queue.queue_name)
    for queue1 in total_queues:
        logger.info("Queue Name: "+queue1.queue_name)
        logger.info("Estimated Time (min): "+
            str(queue1.deviation_rate * queue1.avg_procedure_time*(queue1.total_count-(
                                            (queue1.cancelled_count+queue1.completed_count)))/queue1.capacity)
        )
        logger.info("Queue Capacity: "+str(queue1.capacity))
        logger.info("Queue Length: "+str(queue1.total_count-(
                                            (queue1.cancelled_count+queue1.completed_count))))
        pre_con= len(set(list(map(lambda x: x.id,queue1.services))).intersection(set(services1)))
        logger.info({"Pre Requisites : YES" if pre_con>0 else "Pre Requisites : NO"})
        # logger.info(f"pre requesite condition: {"Yes" if pre_con>0 else "No"}")
    logger.info("Excluded services")
    logger.info(services1)
    
    if queue is None:
        services1= db.scalars(db.query(UserServiceModel.service_id).join(UserServicePrerequisiteModel,UserServiceModel.id==UserServicePrerequisiteModel.user_service_id).filter(UserServiceModel.token_id==token_id,UserServicePrerequisiteModel.status==UserPreReqStatusEnum.HOLD)).all()
        logger.info(services1)
        queue= db.query(QueueModel).join(QueueModel.services).filter(
                    ServiceModel.id.in_(services)).filter(ServiceModel.id.not_in(services1)).filter(
                        QueueModel.status == StatusEnum.ACTIVE).order_by(queue_query_filter).group_by(
                            QueueModel.id
                            ).first()
    return queue, sorted(set(services)-set(services1))

def queue_query_filter():
    return asc((func.min(QueueModel.deviation_rate *
                                        QueueModel.avg_procedure_time*(QueueModel.total_count-(
                                            (QueueModel.cancelled_count+QueueModel.completed_count)))/QueueModel.capacity)+QueueModel.buffer_time))

def update_estimated_time(db:Session):
    try:
        subquery = db.query(
        UserQueueModel.id,
        UserQueueModel.queue_id,
        (func.now()+ func.make_interval(minutes= 'row_number')).label("estimated_time"),
        func.row_number().over(partition_by=UserQueueModel.queue_id,order_by=
                        (asc(UserQueueModel.freezed_at),
            text("user_queue.status='FREEZED' desc"),
            desc(QueueWeightageActionModel.weightage+(func.extract("epoch",
                (func.now() - UserQueueModel.estimated_time))/60)),
            asc(UserQueueModel.created_at))).label('row_number')*QueueModel.avg_procedure_time
        ).join(UserQueueModel.queue_weightage_action).join(UserQueueModel.queue).filter(UserQueueModel.status==UserQueueStatusEnum.FREEZED).subquery()
        # Add the calculated timedelta to the current time
        query= db.query(UserQueueModel).where(UserQueueModel.id == subquery.c.id).update({
                        UserQueueModel.estimated_time: subquery.c.estimated_time
        }
        )
        logger.info(query)
        db.commit()
    except Exception as ex:
        logger.exception(ex)
            

    
def add_user_queue(db: Session, queue_id, qr_details: QRDetail,staff_user:Optional[int]=None):
    with RedisLock(client_lock, "queue_"+str(queue_id), blocking_timeout=180):
        try:
            logger.info(f"queue_id:{queue_id}")
            logger.info(qr_details)
            queue = db.query(QueueModel).filter(
                QueueModel.id == queue_id,QueueModel.status==StatusEnum.ACTIVE).one_or_none()
            if queue is None:
                raise MutationError("Queue is inactive")
            # user_token = db.query(UserTokenModel).filter(
            #     UserTokenModel.id == qr_details.token_id).one_or_none()
            if qr_details.token_id is not None and qr_details.token_id !="":
                token_id = int(qr_details.token_id)
            elif qr_details.uhid is not None and qr_details.uhid !="":
                token = db.query(UserTokenModel.id).join(UserTokenModel.user).filter(UserModel.umr_no == qr_details.uhid).order_by(desc(UserTokenModel.created_at)).one_or_none()
                token_id= token.id
            user_queue: UserQueueModel = db.query(UserQueueModel).filter(
                UserQueueModel.token_id == token_id).filter(UserQueueModel.status !=UserQueueStatusEnum.EXIT, UserQueueModel.status !=UserQueueStatusEnum.PURGED).one_or_none()
            logger.info(user_queue)
            flag=False
            if user_queue is None:
                # raise MutationError("Invalid action")
                flag=True
            elif user_queue.queue_id != queue_id:
                purge_exit_user_queue(db, user_queue,staff_user)
                flag=True
            if flag:
                all_services_ids, user_id, token_id, phone_number,token_no = get_all_services(db, token.id, queue_id)
                if len(all_services_ids)>0:
                    user_queue, time = checkin_user_into_queue(db, queue, user_id[0], token_no[0], token_id[0], all_services_ids,phone_number[0], None,staff_user)
                else:
                    raise MutationError("Invalid Action")
            if user_queue.status == UserQueueStatusEnum.CHECKIN:
                pass
            elif user_queue.queue_id == queue_id:
                raise MutationError("Invalid action")
            arrived_at= None
            if user_queue.pre_check_status != UserQueuePreCheckStatusEnum.PENDING and queue.queue_type ==QueueTypeEnum.AUTO:
                status,queue_counter = freeze_user(db,queue, user_queue,None,None)
                logger.info(status)
                if status != None:
                    if status==UserQueueStatusEnum.FREEZED:
                        db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                                        {QueueModel.freezed_count: QueueModel.freezed_count+1},
                                        synchronize_session="fetch",
                                )
                        user_queue.freezed_at = func.now()
                        user_queue.arrived_at = func.now()
                    elif status==UserQueueStatusEnum.ENTRY:
                        db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                            {QueueModel.ongoing_count: QueueModel.ongoing_count+1},
                            synchronize_session="fetch",
                        )
                        # db.query(QueueCounterModel).filter(QueueCounterModel.id==queue_counter.id).update(
                        #     {QueueCounterModel.status: QueueCounterStatusEnum.ALLOTED},
                        #     synchronize_session="fetch",
                        # )
                        user_queue.arrived_at = func.now()
                        user_queue.freezed_at = func.now()
                        user_queue.start_time = func.now()
                else:
                    status = UserQueueStatusEnum.ARRIVED
                    user_queue.arrived_at = func.now()
            else:
                status = UserQueueStatusEnum.ARRIVED
                user_queue.arrived_at = func.now()
            user_queue.status = status
            user_queue.prerequisites_conditions=qr_details.prerequisites_conditions
            if user_queue.status==UserQueueStatusEnum.ARRIVED:
                queue_step_id, location_id = add_user_queue_step(db,None,user_queue.id,"arrived", "CHECKIN",status,queue, None,None,None,staff_user)
                user_queue.queue_step_id = queue_step_id
                user_queue.location_id= location_id
                user_queue.step_start_time = func.now()
                user_queue.next_step_start_time = None
            user_queue.updated_by = staff_user
            update_rel_device_queue(db,queue_id, True)
            db.commit()
            arrived_msg(db,queue,user_queue.estimated_time,user_queue.user.phone_number, status)
            return get_user_queue(db, user_queue.queue_id, None)
        except MutationError as ex:
            logger.exception(ex)
            raise MutationError(ex.message)
        except Exception as ex:
            logger.exception("Exception in add user queue")


def get_clusters(db: Session):
    return db.query(ClusterModel).all()


def update_user_queue(db: Session, user_queue_id, queue_weightage_action_id,queue_weightage_action,staff_user):
    try:
        logger.info(queue_weightage_action)
        if queue_weightage_action_id is not None:
            action:QueueWeightageActionModel=db.query(QueueWeightageActionModel).filter(QueueWeightageActionModel.id==queue_weightage_action_id).one()
        elif queue_weightage_action is not None and queue_weightage_action!= '':
            action:QueueWeightageActionModel=db.query(QueueWeightageActionModel).filter(QueueWeightageActionModel.code==queue_weightage_action).one()
        
        logger.info(f"user_queue_id:{user_queue_id}")
        user_queue = db.query(UserQueueModel).filter(
            UserQueueModel.id == user_queue_id).one()
        logger.info(action.code)
        queue = db.query(QueueModel).filter(QueueModel.id == user_queue.queue_id).one()
        
        if user_queue is None:
            raise MutationError(f"Provided user is not in queue")
        if user_queue.status==UserQueueStatusEnum.EXIT or user_queue.status==UserQueueStatusEnum.PURGED:
            raise MutationError("Invalid Action")  
        if user_queue.status==UserQueueStatusEnum.FREEZED and(action.code!=UserQueueStatusEnum.PURGED.name and action.code!=UserQueueStatusEnum.HOLD.name):
            raise MutationError("Invalid Action")  
        if user_queue.status==UserQueueStatusEnum.HOLD:
            if action.code=='PURGED':
                user_queue.status=UserQueueStatusEnum.PURGED
                queue.cancelled_count = queue.cancelled_count+1
            else:
                user_queue.status= (UserQueueStatusEnum.ARRIVED if user_queue.arrived_at is not None else UserQueueStatusEnum.CHECKIN)
                if user_queue.pre_check_status != UserQueuePreCheckStatusEnum.PENDING:
                    status,queue_counter = freeze_user(db,queue, user_queue,None,None)
                    logger.info(status)
                    if status != None:
                        if status==UserQueueStatusEnum.FREEZED:
                            db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                                            {QueueModel.freezed_count: QueueModel.freezed_count+1},
                                            synchronize_session="fetch",
                                    )
                        elif status==UserQueueStatusEnum.ENTRY:
                            db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                                {QueueModel.ongoing_count: QueueModel.ongoing_count+1},
                                synchronize_session="fetch",
                            )
        if action.code=='HOLD':
            if user_queue.status==UserQueueStatusEnum.FREEZED:
                freezed_count=-1
                db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                        {QueueModel.freezed_count: QueueModel.freezed_count+freezed_count},synchronize_session="fetch",)
                db.query(QueueCounterModel).filter(QueueCounterModel.id==user_queue.counter).update({
                    QueueCounterModel.freeze_count: QueueCounterModel.freeze_count+freezed_count},synchronize_session="fetch")
                user_queue.counter=None
                update_arrived_user(db,queue,user_queue.counter)
            if user_queue.status == UserQueueStatusEnum.ENTRY:
                counter=user_queue.counter
                user_queue.counter=None
                update_next_users(db,queue,counter,0)
            user_queue.status=UserQueueStatusEnum.HOLD
        elif action.code=='PURGED':
            if user_queue.status==UserQueueStatusEnum.FREEZED:
                cancelled_count =1
                freezed_count = -1
                db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                            {QueueModel.freezed_count: QueueModel.freezed_count+freezed_count,QueueModel.cancelled_count: QueueModel.cancelled_count+cancelled_count},
                            synchronize_session="fetch",)
                db.query(QueueCounterModel).filter(QueueCounterModel.id==user_queue.counter).update({
                    QueueCounterModel.freeze_count: QueueCounterModel.freeze_count+freezed_count},synchronize_session="fetch")
                user_queue.counter=None
                update_arrived_user(db, queue,user_queue.counter)
            elif user_queue.status == UserQueueStatusEnum.ENTRY:
                cancelled_count =1
                counter=user_queue.counter
                user_queue.counter=None
                update_next_users(db,queue,counter,cancelled_count)
            elif user_queue.status==UserQueueStatusEnum.ARRIVED or user_queue.status==UserQueueStatusEnum.CHECKIN:
                queue.cancelled_count = queue.cancelled_count+1
            db.query(UserServiceModel).filter(UserServiceModel.id.in_(db.query(RelUserServiceQueueModel.user_service_id).filter(RelUserServiceQueueModel.user_queue_id==user_queue.id))).update(
            {UserServiceModel.status: ServiceStatusEnum.PENDING},
                synchronize_session="fetch",
            )
            db.query(RelUserServiceQueueModel).filter(RelUserServiceQueueModel.user_queue_id==user_queue.id).update(
            {RelUserServiceQueueModel.status: ServiceStatusEnum.CANCELLED},
                synchronize_session="fetch",
            )
            # if user_queue.location_id is not None:
            #     db.query(LocationModel).filter(LocationModel.id==user_queue.location_id,LocationModel.parent_location_id.isnot(None)).update(
            #             {LocationModel.status: LocationBedStatusEnum.AVAILABLE},
            #                 synchronize_session="fetch",
            #             )
            user_queue.status=UserQueueStatusEnum.PURGED
        if action.code=='PURGED' or action.code=='HOLD':
            user_queue.location_id= None
            user_queue.next_location_id= None
            if user_queue.tag_id is not None:
                deleting_tag = delete_tag(user_queue.tag_id,user_queue.tag.code,user_queue.user.umr_no)
        else:
            user_queue.weightage_id = action.id
        user_queue.updated_by = staff_user
        update_rel_device_queue(db, queue.id, True)
        db.commit()
        return get_user_queue(db, user_queue.queue_id, None)
    except MutationError as e:
        logger.exception("Exception in update user queue")
        raise MutationError(e.message)
    except Exception as ex:
        logger.exception("Exception in update user queue")
        raise MutationError("Error occured while updating user queue")

def update_next_users(db: Session, queue, counter,cancelled_count):
    try:
        ongoing_count=-1
        db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                {QueueModel.ongoing_count: QueueModel.ongoing_count+ongoing_count,QueueModel.cancelled_count: QueueModel.cancelled_count+cancelled_count},
                synchronize_session="fetch",)
        db.query(QueueCounterModel).filter(QueueCounterModel.id==counter).update({
            QueueCounterModel.status: QueueCounterStatusEnum.UNALLOTED},synchronize_session="fetch")
        ongoing_count, freezed_count=entry_or_freeze_next_users(db,queue,counter)
        db.query(QueueModel).filter(QueueModel.id==queue.id).update(
            {QueueModel.ongoing_count: QueueModel.ongoing_count+ongoing_count,
                QueueModel.freezed_count: QueueModel.freezed_count+freezed_count
                },
            synchronize_session="fetch",
        )
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while entring next users")
    
def update_arrived_user(db: Session, queue,counter):
    try:
        freeze_user_queue = db.query(UserQueueModel).join(UserQueueModel.queue_weightage_action).filter(
                UserQueueModel.queue_id == queue.id).filter(UserQueueModel.date == date.today()).filter(UserQueueModel.status == UserQueueStatusEnum.ARRIVED).filter(or_(
                    UserQueueModel.pre_check_status != UserQueuePreCheckStatusEnum.PENDING,UserQueueModel.pre_check_status == None)).order_by(
                text("user_queue.status='FREEZED' desc"),
                desc(QueueWeightageActionModel.weightage+(func.extract("epoch",
                    (func.now() - UserQueueModel.estimated_time))/60)),
                asc(UserQueueModel.created_at)).first()
        logger.info(freeze_user_queue)
        if freeze_user_queue is not None :
            status,queue_counter = freeze_user(db,queue, freeze_user_queue,None,counter)
            logger.info(status)
            if status != None:
                if status==UserQueueStatusEnum.FREEZED:
                    db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                                    {QueueModel.freezed_count: QueueModel.freezed_count+1},
                                    synchronize_session="fetch",
                            )
                elif status==UserQueueStatusEnum.ENTRY:
                    db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                        {QueueModel.ongoing_count: QueueModel.ongoing_count+1},
                        synchronize_session="fetch",
                    )
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while updating freeze user")
    
def send_service_completion_msg(db:Session,user_queue:UserQueueModel, user_service_ids):
    try:
        send_to = user_queue.user.phone_number
        completed_status_as_int = case([
        (UserServiceModel.status == ServiceStatusEnum.COMPLETED, 1),
        ], else_=0)
        completed_token = db.query(
            UserTokenModel.id
        ).filter(UserTokenModel.id==user_queue.token_id).group_by(UserTokenModel.id).having(
            func.count(UserServiceModel.id) == func.sum(completed_status_as_int)
        ).join(UserTokenModel.user_services).first()
        service = db.query(ServiceModel.type, ServiceModel.reports_collection, ServiceModel.reports_completion_time, ServiceModel.source).join(UserServiceModel.service).filter(
                UserServiceModel.user_id == user_queue.user.id).filter(UserServiceModel.id.in_(user_service_ids), UserServiceModel.service_id==ServiceModel.id).first()
        tmp_name = get_template_name(db, SMSTypeEnum.WHATSAPP.name,EventCodeEnum.PATIENT_QUEUE_CHECKOUT.name)      
        params = [{"type":"text","text":service.type}]
        send_msg = send_whatsapp_msg(db, tmp_name, send_to, params,None)
        if user_queue.queue.service_type=='INVESTIGATIONS' and completed_token is not None:
            hsp_phone_number= get_whatsapp_details(db)
            tmp_name = get_template_name(db, SMSTypeEnum.WHATSAPP.name,EventCodeEnum.PATIENT_EXIT.name)      
            params = [{"type":"text","text":f"{int(service.reports_completion_time)} hours"},
                        {"type":"text","text":f"{service.source}"},
                        {"type":"text","text":f"{service.reports_collection}"},
                        {"type":"text","text":f"{hsp_phone_number[1]}"}]
            send_msg = send_whatsapp_msg(db, tmp_name, send_to, params,None)
    except Exception as e:
        logger.exception(e)
        pass
def purge_exit_user_queue(db: Session, user_queue:UserQueueModel,staff_user):
    try:
        queue = db.query(QueueModel).filter(QueueModel.id == user_queue.queue_id).one()
        status=ServiceStatusEnum.PENDING
        rel_user_queue_status=ServiceStatusEnum.CANCELLED
        if user_queue.status == UserQueueStatusEnum.ENTRY:
            user_service_ids = db.scalars(db.query(RelUserServiceQueueModel.user_service_id).filter(
                RelUserServiceQueueModel.user_queue_id == user_queue.id)).all()
            # db.query(UserServiceModel).filter(UserServiceModel.id.in_(user_service_ids)).update({
            #     "status": ServiceStatusEnum.COMPLETED.name
            # }, synchronize_session="fetch")
            db.query(UserServicePrerequisiteModel).filter(UserServicePrerequisiteModel.pre_req_user_service_id.in_(user_service_ids)).delete()
            user_queue.status = UserQueueStatusEnum.EXIT
            user_queue.end_time = func.now()
            db.query(QueueCounterModel).filter(QueueCounterModel.id == user_queue.counter).update({
                QueueCounterModel.status: QueueCounterStatusEnum.UNALLOTED
            })
            queue.completed_count = queue.completed_count+1
            # queue.ongoing_count = queue.ongoing_count-1
            user_queue.force_exit = True
            send_service_completion_msg(db,user_queue, user_service_ids)
            # db.commit()
            # db.refresh(queue)
            db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                {QueueModel.ongoing_count: QueueModel.ongoing_count-1,
                    },
                synchronize_session="fetch",
            )
            ongoing_count, freezed_count=entry_or_freeze_next_users(db, queue,user_queue.counter)
            # queue.ongoing_count=queue.ongoing_count+ongoing_count-1
            # queue.freezed_count=queue.freezed_count+freezed_count
            db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                {QueueModel.ongoing_count: QueueModel.ongoing_count+ongoing_count,
                    QueueModel.freezed_count: QueueModel.freezed_count+freezed_count
                    },
                synchronize_session="fetch",
            )
            status=ServiceStatusEnum.COMPLETED
            rel_user_queue_status=ServiceStatusEnum.CANCELLED
        elif user_queue.status==UserQueueStatusEnum.FREEZED:
            user_queue.status=UserQueueStatusEnum.PURGED
            cancelled_count = 1
            freezed_count = -1
            db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                        {QueueModel.freezed_count: QueueModel.freezed_count+freezed_count,QueueModel.cancelled_count: QueueModel.cancelled_count+cancelled_count},
                        synchronize_session="fetch",)
            db.query(QueueCounterModel).filter(QueueCounterModel.id==user_queue.counter).update({
                    QueueCounterModel.freeze_count: QueueCounterModel.freeze_count+freezed_count},synchronize_session="fetch")
            user_queue.counter=None
            update_arrived_user(db,queue,user_queue.counter)
        elif user_queue.status==UserQueueStatusEnum.ARRIVED or user_queue.status==UserQueueStatusEnum.CHECKIN or UserQueueModel.status == UserQueueStatusEnum.HOLD:
            user_queue.status=UserQueueStatusEnum.PURGED
            queue.cancelled_count = queue.cancelled_count+1
        user_queue.updated_by=staff_user
        db.query(UserServiceModel).filter(UserServiceModel.id.in_(db.query(RelUserServiceQueueModel.user_service_id).filter(RelUserServiceQueueModel.user_queue_id==user_queue.id))).update(
            {UserServiceModel.status: status},
                synchronize_session="fetch",
        )
        db.query(RelUserServiceQueueModel).filter(RelUserServiceQueueModel.user_queue_id==user_queue.id).update(
            {RelUserServiceQueueModel.status: rel_user_queue_status},
                synchronize_session="fetch",
            )
    except MutationError as e:
        logger.exception(e)
        raise MutationError(e.message)
    except Exception as ex:
        logger.exception("Exception in add user queue")
        raise MutationError(ex)
        
def exit_user_queue(db: Session, queue_id: int, qr_details: QRDetail, force_exit=None,cmpltd_user_service_ids: Optional[List[int]]=None,staff_user:Optional[int]=None):
    with RedisLock(client_lock, "queue_"+str(queue_id), blocking_timeout=180):
        try:
            user_token = db.query(UserTokenModel).filter(
                UserTokenModel.id == qr_details.token_id).one()
            logger.info('EXIT')
            logger.info(queue_id)
            logger.info(cmpltd_user_service_ids)
            alloted_counter= None
            user_queue = db.query(UserQueueModel).filter(UserQueueModel.queue_id == queue_id, UserQueueModel.token_id == qr_details.token_id,
                                                        UserQueueModel.date == date.today(), UserQueueModel.status == UserQueueStatusEnum.ENTRY).one_or_none()
            queue : QueueModel = db.query(QueueModel).filter(QueueModel.id == queue_id).one()
            if user_queue is None:
                raise MutationError("Invalid action")
            else:
                counter=user_queue.counter
                logger.info(f"counter:{counter}")
                user_service_ids = db.scalars(db.query(RelUserServiceQueueModel.user_service_id).filter(
                    RelUserServiceQueueModel.user_queue_id == user_queue.id)).all()
                pending_services = set(user_service_ids) - set(cmpltd_user_service_ids) if cmpltd_user_service_ids is not None else []
                completed_services = cmpltd_user_service_ids if cmpltd_user_service_ids is not None else user_service_ids
                db.query(UserServiceModel).filter(UserServiceModel.id.in_(pending_services)).update({
                    UserServiceModel.status: ServiceStatusEnum.PENDING
                }, synchronize_session="fetch")
                db.query(UserServiceModel).filter(UserServiceModel.id.in_(completed_services)).update({
                    UserServiceModel.status: ServiceStatusEnum.COMPLETED
                }, synchronize_session="fetch")
                db.query(RelUserServiceQueueModel).filter(RelUserServiceQueueModel.user_service_id.in_(completed_services)).update({
                    RelUserServiceQueueModel.status: ServiceStatusEnum.COMPLETED
                }, synchronize_session="fetch",)
                db.query(RelUserServiceQueueModel).filter(RelUserServiceQueueModel.user_service_id.in_(pending_services)).update({
                    RelUserServiceQueueModel.status: ServiceStatusEnum.CANCELLED
                }, synchronize_session="fetch",)
                db.query(UserServicePrerequisiteModel).filter(UserServicePrerequisiteModel.pre_req_user_service_id.in_(user_service_ids)).delete()
                # user_queue.status = UserQueueStatusEnum.EXIT
                # user_queue.end_time = func.now()                
                queue_step_id= None
                if queue.queue_type==QueueTypeEnum.AUTO:
                    if counter is not None:
                        db.query(QueueCounterModel).filter(QueueCounterModel.id == counter).update(
                            {
                                QueueCounterModel.status:QueueCounterStatusEnum.UNALLOTED
                            },synchronize_session="fetch",
                        )
                    else:
                        alloted_counter= db.query(QueueCounterModel).filter(QueueCounterModel.queue_id == user_queue.queue_id).filter(
                            QueueCounterModel.status==QueueCounterStatusEnum.ALLOTED
                        ).first()
                        db.query(QueueCounterModel).filter(QueueCounterModel.id == alloted_counter.id).update(
                            {
                                QueueCounterModel.status:QueueCounterStatusEnum.UNALLOTED
                            },synchronize_session="fetch",
                        )
                queue_step_id,queue_location_id = add_user_queue_step(db,None,user_queue.id,"Exited", "CHECKOUT",UserQueueStatusEnum.EXIT,queue, None,None,None,staff_user)
                db.query(UserQueueModel).filter(UserQueueModel.id==user_queue.id).update(
                    {
                        UserQueueModel.status:UserQueueStatusEnum.EXIT,
                        UserQueueModel.end_time: func.now(),
                        UserQueueModel.queue_step_id: queue_step_id,
                        UserQueueModel.force_exit: force_exit,
                        UserQueueModel.location_id: queue_location_id,
                        UserQueueModel.step_start_time: None,
                        UserQueueModel.next_step_start_time: func.now(),
                        UserQueueModel.updated_by: staff_user
                    }
                )
                pseudo_capacity = 0
                send_service_completion_msg(db,user_queue, user_service_ids)
                db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                    {QueueModel.ongoing_count: QueueModel.ongoing_count-1,QueueModel.pseudo_capacity: QueueModel.pseudo_capacity+pseudo_capacity,
                       QueueModel.completed_count: QueueModel.completed_count+1
                        },
                    synchronize_session="fetch",
                )
                if queue.queue_type==QueueTypeEnum.AUTO:
                    ongoing_count, freezed_count=entry_or_freeze_next_users(db, queue,counter)
                    db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                        {QueueModel.ongoing_count: QueueModel.ongoing_count+ongoing_count,
                            QueueModel.freezed_count: QueueModel.freezed_count+freezed_count
                            },
                        synchronize_session="fetch",
                    )
            try:
                services= db.scalars(db.query(UserServiceModel.service_id).filter(UserServiceModel.token_id==user_token.id).filter(UserServiceModel.status==ServiceStatusEnum.PENDING)).all()
                if len(services)>0:
                    logger.info(services)
                    queue1, services=get_opt_queue(db,services,user_token.id)
                    if queue1 is not None:
                        user_queue, time = checkin_user_into_queue(db,queue1,user_token.user_id,user_token.token_no,qr_details.token_id, services, user_queue.user.phone_number,None,None)
                        update_rel_device_queue(db,queue1.id, True)
            except Exception as ex:
                logger.exception("Error checking into other queue")
            update_rel_device_queue(db,queue.id, True)
            db.commit()
            return get_user_queue(db, user_queue.queue_id, None)
        except MutationError as e:
            logger.exception(e)
            raise MutationError(e.message)
        except Exception as ex:
            logger.exception("Exception in Exit user queue")
            raise MutationError(ex)

def update_user_queue_service(db: Session, user_queue_id, user_service_ids,status:Optional[str]=None):
    try:
        user_queue=db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id).update({
                UserQueueModel.pre_check_status:UserQueuePreCheckStatusEnum.COMPLETED
            }
        )
        all_services=db.scalars(db.query(RelUserServiceQueueModel.user_service_id).filter(RelUserServiceQueueModel.user_queue_id==user_queue_id)).all()
        if len(set(all_services)-set(user_service_ids))>0:
            db.query(UserServiceModel).filter(UserServiceModel.id.in_(set(all_services)-set(user_service_ids))).update(
                {
                UserServiceModel.status: ServiceStatusEnum.PENDING
                }, synchronize_session="fetch"
            )
            db.query(RelUserServiceQueueModel).filter(RelUserServiceQueueModel.user_service_id.in_(set(all_services)-set(user_service_ids))).update(
            {RelUserServiceQueueModel.status: ServiceStatusEnum.CANCELLED},
                synchronize_session="fetch",
            )
            # db.query(RelUserServiceQueueModel).filter(RelUserServiceQueueModel.user_queue_id==user_queue_id).filter(RelUserServiceQueueModel.user_service_id.not_in(user_service_ids)).delete()
        user_queue=db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id).one()
        queue = db.query(QueueModel).filter(
                    QueueModel.id == user_queue.queue_id,QueueModel.status==StatusEnum.ACTIVE).one_or_none()
        status,queue_counter = freeze_user(db,queue, user_queue,None,None)
        logger.info(status)
        if status != None:
            if status==UserQueueStatusEnum.FREEZED:
                db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                                {QueueModel.freezed_count: QueueModel.freezed_count+1},
                                synchronize_session="fetch",
                        )
            elif status==UserQueueStatusEnum.ENTRY:
                db.query(QueueModel).filter(QueueModel.id==queue.id).update(
                    {QueueModel.ongoing_count: QueueModel.ongoing_count+1},
                    synchronize_session="fetch",
                )
        db.commit()
        return get_user_queue(db, queue.id, None)
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while updating user queue")



def activate_or_deactivate_queue(db: Session, queue_id: int, status: str):
    try:
        if status == 'ACTIVE':
            db.query(QueueModel).filter(QueueModel.id == queue_id).update(
                {QueueModel.status: StatusEnum.ACTIVE},)
            msg = "Queue Activated Sucessfully"
        else:
            db.query(QueueModel).filter(QueueModel.id == queue_id).update(
                {QueueModel.status: StatusEnum.INACTIVE},)
            msg = "Queue Deactivated Sucessfully"
        db.commit()
        return msg,get_queues(db)
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to Activate Queue")


def get_queue_weightage(db: Session,codes):
    query=db.query(QueueWeightageActionModel)
    if len(codes)>0:
        query=query.filter(QueueWeightageActionModel.code.in_(codes))
    query = query.order_by(case(
                [
                    (QueueWeightageActionModel.code == 'VIP', 0),
                    (QueueWeightageActionModel.code == 'PREGNANT_WOMEN', 1),
                    (QueueWeightageActionModel.code == 'WHC', 2),
                    (QueueWeightageActionModel.code == 'SENIOR_CITIZEN', 3),
                    (QueueWeightageActionModel.code == 'SPECIALLY_ABLED', 4),
                    (QueueWeightageActionModel.code == 'NORMAL', 5),
                    (QueueWeightageActionModel.code == 'HOLD', 6),
                    (QueueWeightageActionModel.code == 'PURGED', 7),
                ],
                else_=6,
            ))
    return query.all()


def get_staff_user_allocated_queues(db: Session, staff_user):
    logger.info(staff_user)
    return db.query(QueueModel).join(RelStaffUserQueueModel, QueueModel.id == RelStaffUserQueueModel.queue_id).filter(RelStaffUserQueueModel.staff_user_id == staff_user).filter(QueueModel.deleted_at == None).order_by(QueueModel.queue_name).all()

def get_queue_count(db: Session):
    try:
        today = date.today()
        return db.query(UserQueueModel).filter(UserQueueModel.date == today).all()
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error while fetching data")

def get_services(db: Session,staff_user,service_type):
    try:
        if staff_user is not None:
            query=db.query(ServiceModel).join(ServiceModel.queues).join(RelStaffUserQueueModel, QueueModel.id == RelStaffUserQueueModel.queue_id).filter(RelStaffUserQueueModel.staff_user_id == staff_user).filter(QueueModel.deleted_at == None)
            if service_type is not None:
                query=query.filter(ServiceModel.service_type==service_type)
            return query.all()
        else:
            return db.query(ServiceModel).all()
    except Exception as e:
        logger.exception(e)
        raise MutationError("failed to fetch the data")

def test_exit_user_from_queue(db: Session):
    today = date.today()
    user_queues = db.query(UserQueueModel).join(UserQueueModel.queue).filter(UserQueueModel.date == today).filter(QueueModel.queue_type==QueueTypeEnum.AUTO).filter(
        UserQueueModel.status == UserQueueStatusEnum.ENTRY).filter(
        QueueModel.avg_procedure_time*60<func.extract("epoch", (func.now()-UserQueueModel.start_time))).all()
    for user in user_queues:
        try:
            exit_user_queue(db,user.queue.id,QRDetail(token_id=user.token_id), False,None)
        except Exception as ex:
            text = f"""Exception : {ex}"""
            send_email([os.environ["DIALY_REPORT_MAIL"]],"Entry User",text,os.environ["MAIL_ID"],os.environ["PASSWORD"])
            logger.exception(ex)

        
def force_exit_user_from_queue(db: Session):
    try:
        today = date.today()
        user_queues = (
            db.query(UserQueueModel)
            .join(UserQueueModel.queue)
            .filter(UserQueueModel.date == today)
            .filter(UserQueueModel.status == UserQueueStatusEnum.ENTRY)
            .filter(func.extract("epoch", func.now() - UserQueueModel.start_time)/60 > QueueModel.avg_procedure_time*3)
            .all()
        )
        logger.info(user_queues)
        if len(user_queues) > 0:
            for user in user_queues:
                try:
                    exit_user_queue(db,user.queue.id,QRDetail(token_id=user.token_id), True,None)
                except Exception as ex:
                    logger.exception("exception in exit")     
    except Exception as e:
        logger.exception(e)

def reset_queues_user_queues(db: Session):
    try:
        today = date.today()
        user_queues = (
            db.query(UserQueueModel)
        .filter(or_(UserQueueModel.status == UserQueueStatusEnum.CHECKIN,
                UserQueueModel.status == UserQueueStatusEnum.ARRIVED,
                UserQueueModel.status == UserQueueStatusEnum.FREEZED,
                UserQueueModel.status == UserQueueStatusEnum.HOLD))
        .all()
        )
        if len(user_queues)> 0:
            for user in user_queues:
                # if user.status == UserQueueStatusEnum.ENTRY:
                #     user.status = UserQueueStatusEnum.EXIT
                #     user.force_exit = True
                #     # db.query(QueueCounterModel).filter(QueueCounterModel.id == user.counter).update({
                #     # QueueCounterModel.status: QueueCounterStatusEnum.UNALLOTED
                #     # })
                # else:
                user.status = UserQueueStatusEnum.PURGED
                user.force_exit = True
                user.location_id = None
                user.tag_id=None
                user.next_location_id=None
                user_service_ids = db.scalars(db.query(RelUserServiceQueueModel.user_service_id).filter(
                    RelUserServiceQueueModel.user_queue_id == user.id).filter(RelUserServiceQueueModel.status == ServiceStatusEnum.PENDING)).all()
                db.query(UserServiceModel).filter(UserServiceModel.id.in_(user_service_ids)).update({
                    UserServiceModel.status: ServiceStatusEnum.PENDING
                }, synchronize_session="fetch")
                db.query(RelUserServiceQueueModel).filter(RelUserServiceQueueModel.user_service_id.in_(user_service_ids)).update({
                    RelUserServiceQueueModel.status: ServiceStatusEnum.CANCELLED
                }, synchronize_session="fetch",)
        db.query(QueueCounterModel).update({
            QueueCounterModel.status: QueueCounterStatusEnum.UNALLOTED
        })
        queues = db.query(QueueModel).update({
            QueueModel.completed_count: 0,QueueModel.ongoing_count: 0, QueueModel.cancelled_count: 0,
            QueueModel.freezed_count: 0, QueueModel.total_count: 0,
            QueueModel.last_token_called_at: None,
            QueueModel.latest_token_id: None,
        }, synchronize_session="fetch",)
        queues = db.query(QueueCounterModel).update({
            QueueCounterModel.freeze_count: 0,
            QueueCounterModel.status: QueueCounterStatusEnum.UNALLOTED
        }, synchronize_session="fetch",)
        location_beds = db.query(LocationModel).filter(LocationModel.parent_location_id.isnot(None)).update({
            LocationModel.status: LocationBedStatusEnum.AVAILABLE
        }, synchronize_session="fetch",)
        location_beds = db.query(LocationModel).update({
            LocationModel.alloted_count:0, LocationModel.occupied_count:0
        }, synchronize_session="fetch",)
        remove_assigned_tokens_list()
        db.commit()
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to save changes")    

def entry_user(db: Session, queue, user_queue,counter):
    try:
        queue_counter=get_queue_counter(db,queue.id,counter)
        logger.info(queue_counter)
        if queue_counter !=None:
            # queue_counter.status=QueueCounterStatusEnum.ALLOTED
            # queue.ongoing_count = queue.ongoing_count+1
            status = UserQueueStatusEnum.ENTRY
            # user_queue.counter=queue_counter.id
            user_queue.start_time = func.now()
            return status
        return None
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while entering user")

def freeze_user(db: Session, queue, user_queue,condition,counter):
    try:
        queue_counter, status=get_queue_counter_freeze(db,queue,user_queue,condition,counter)
        if queue_counter !=None:
            # queue_counter.status=QueueCounterStatusEnum.ALLOTED
            return status,queue_counter
        return None,None
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error occured while entering user")

def staff_station_device_login(db: Session,queue_id:int, device_id:str, pin:int):
    try:
        data=db.query(DeviceModel.id).filter(and_(DeviceModel.login_pin==pin,DeviceModel.device_code == device_id)).first()
        if data is None:
            raise MutationError("Invalid Login credentials")
        existing_record = db.query(RelDeviceQueueModel).filter_by(device_id=data.id).one_or_none()
        if existing_record is None:
            device_queue = RelDeviceQueueModel(device_id = data.id, queue_id = queue_id, subscription_name = "getQueueCounters", is_updated = False)
            db.add(device_queue)
        else:
            existing_record.queue_id = queue_id
        db.commit()
        return "Device Login successfully"
    except Exception as e:
        logger.info(f"Error occured: {e}")
        raise MutationError("Invalid Login credentials")

def get_device_queue(db:Session, queue_id: int):
    try:
        data = (
            db.query(RelDeviceQueueModel.queue_id, RelDeviceQueueModel.is_updated)
            .filter(RelDeviceQueueModel.queue_id == queue_id).first()
        )
        return data.queue_id, data.is_updated
    except Exception as e:
        logger.exception(e)
        raise MutationError("Error while fetching data")

def update_rel_device_queue(db: Session, queue_id:int, is_updated: bool):
    db.query(RelDeviceQueueModel).filter(RelDeviceQueueModel.queue_id == queue_id).update(
        {RelDeviceQueueModel.is_updated : is_updated}
    )
    db.commit()

def entry_or_freeze_next_users(db: Session, queue:QueueModel,counter):
    freezed_count=0
    ongoing_count=0
    logger.info(f"Counter: {counter}")
    query = db.query(UserQueueModel).filter(UserQueueModel.queue_id == queue.id, UserQueueModel.date == date.today(), UserQueueModel.status == UserQueueStatusEnum.FREEZED)
    if counter is not None:
        query= query.filter(UserQueueModel.counter==counter)
    entry_user_queue=query.order_by(asc(UserQueueModel.freezed_at)).first()
    logger.info('EXIT: ENTRY next user')
    logger.info(entry_user_queue)
    if entry_user_queue is not None:
        status,queue_counter = freeze_user(db,queue, entry_user_queue,"ENTRY",counter)
        if status !=None:
            if status==UserQueueStatusEnum.ENTRY:
                ongoing_count=1
                freezed_count=-1
            else:
                raise MutationError("Error Freezing data") 
    flag=True
    while flag:
        freeze_user_queue = db.query(UserQueueModel).join(UserQueueModel.queue_weightage_action).filter(or_(UserQueueModel.pre_check_status==UserQueuePreCheckStatusEnum.COMPLETED, UserQueueModel.pre_check_status==None)).filter(UserQueueModel.queue_id == queue.id, UserQueueModel.date == date.today(), UserQueueModel.status == UserQueueStatusEnum.ARRIVED).order_by(
                desc(QueueWeightageActionModel.weightage+(func.extract("epoch",
                    (func.now() - UserQueueModel.estimated_time))/60)),
                asc(UserQueueModel.created_at))
        logger.info('EXIT: FREEZE next user')
        logger.info(freeze_user_queue)
        freeze_user_queue=freeze_user_queue.first()
        logger.info(freeze_user_queue)
        if freeze_user_queue is not None :
            status,queue_counter = freeze_user(db,queue, freeze_user_queue,"FREEZED",counter)
            logger.info(status)
            if status != None:
                if status==UserQueueStatusEnum.FREEZED:
                    freezed_count=freezed_count+1
                else:
                    raise MutationError("Error Freezing data")
            if queue_counter is None or queue_counter.upcoming_capacity-queue_counter.freeze_count>=0:
                flag=False
        else:
            flag=False
    return ongoing_count,freezed_count

def get_queue_counters(db: Session, queue_id: int):
    today = date.today()
    # list1 = db.query(UserQueueModel).join(UserQueueModel.queue_weightage_action).filter(
    #     UserQueueModel.queue_id == queue_id).filter(UserQueueModel.date == today).filter(
    #         or_(
    #             UserQueueModel.status == UserQueueStatusEnum.FREEZED,
    #             UserQueueModel.status == UserQueueStatusEnum.ENTRY)).order_by(
    #     text("user_queue.status='FREEZED' desc"),
    #     desc(QueueWeightageActionModel.weightage+(func.extract("epoch",
    #          (func.now() - UserQueueModel.estimated_time))/60)),
    #     asc(UserQueueModel.created_at)).group_by(UserQueueModel.id, QueueWeightageActionModel.weightage)
    list1 = db.query(UserQueueModel).join(UserQueueModel.queue).filter(QueueModel.latest_token_id==UserQueueModel.token_id).limit(1)
    return list1.all()

def checkin_msg(db:Session, prerequisites, queue:QueueModel, time, phone_number):
    try:
        tmp_name = get_template_name(db, SMSTypeEnum.WHATSAPP.name,EventCodeEnum.PATIENT_QUEUE_GUIDANCE.name)      
        prerequisites1=", ".join(data for data in prerequisites)
        params = [{"type":"text","text":f"Tower {queue.cluster.tower}->Floor {queue.cluster.floor}->Location {queue.cluster.cluster}"},
                {"type":"text","text":f"{queue.queue_name}"},
                {"type":"text","text":f"{0 if math.ceil(time)<0 else math.ceil(time)} minutes"},{"type":"text","text":"Please follow the below instructions before the test."},
                {"type":"text","text":prerequisites1}]
        send_msg = send_whatsapp_msg(db, tmp_name, phone_number, params,None)
    except Exception as ex:
        logger.exception(ex)
        
def checkin_user_into_queue(db: Session, queue: QueueModel, user_id:int, token_no: str, token_id: int, services, phone_number,weightage_id,staff_user: Optional[int]=None):
    try:
        if weightage_id is None:
            weightage = db.query(QueueWeightageActionModel).filter(
                        QueueWeightageActionModel.code == 'NORMAL').one()
            weightage_id= weightage.id
        time = (float((queue.avg_procedure_time*(queue.total_count-(queue.cancelled_count +
                queue.completed_count)))/queue.capacity)*queue.deviation_rate)+queue.buffer_time
        user_queue = UserQueueModel(
            queue_id=queue.id,
            user_id=user_id,
            status=UserQueueStatusEnum.CHECKIN,
            date=date.today(),
            pre_check_status= UserQueuePreCheckStatusEnum.PENDING if queue.pre_check_req== True else None,
            weightage_id=weightage_id,
            token_no=token_no,
            token_id=token_id,
            estimated_time=datetime.now(pytz.timezone('asia/kolkata')) + timedelta(minutes=math.ceil(time)),
            created_by=staff_user
        )
        queue.total_count = queue.total_count+1
        db.commit()
        db.add(user_queue)
        db.flush()
        logger.info(user_queue)
        rows = db.query(UserServiceModel).join(UserServiceModel.service).join(ServiceModel.queues).filter(
                UserServiceModel.token_id == token_id,
                UserServiceModel.user_id == user_id,
                UserServiceModel.status == ServiceStatusEnum.PENDING).filter(ServiceModel.id.in_(services)).filter(QueueModel.id==queue.id).order_by(desc(ServiceModel.priority)).all()
        prerequisites = []
        for row in rows:
            rel_user_service = RelUserServiceQueueModel(
                user_service_id=row.id,
                queue_id=queue.id,
                user_queue_id=user_queue.id,
                status = ServiceStatusEnum.PENDING
            )
            db.add(rel_user_service)
            if row.service.prerequisites is not None and row.service.prerequisites.strip()!="":
                prerequisites.append(row.service.prerequisites)
            row.status = ServiceStatusEnum.ON_PROGRESS
        prerequisites=set(prerequisites)
        logger.info(prerequisites)
        # checkin_msg(db, prerequisites, queue, time, phone_number)
        queue_step_id= db.query(QueueStepModel.id).filter(QueueStepModel.queue_id==queue.id).filter(QueueStepModel.checkin_status =='CHECKIN').first()
        logger.info(queue_step_id)
        if queue_step_id is not None:
            add_user_queue_step(db,queue_step_id.id,user_queue.id,"Patient Added to Queue","CHECKIN", None, None, None,None,None,staff_user)
        user_queue.step_start_time=None
        user_queue.next_step_start_time=func.now()
        logger.info("CHECKED_IN")
        return user_queue , time
    except Exception as e:
        logger.exception(e)

def get_user_services(db: Session,user_queue_id: int):
    data = db.query(RelUserServiceQueueModel).filter(RelUserServiceQueueModel.status!=ServiceStatusEnum.CANCELLED, 
                RelUserServiceQueueModel.user_queue_id == user_queue_id).all()
    return data

def update_counter_priority(db:Session):
    db.query(QueueCounterModel).update({
        "priority": db.query(func.count('*')).filter(UserQueueModel.counter == QueueCounterModel.id).filter(UserQueueModel.date==func.date(func.now())).filter(
            func.extract("epoch",(func.now() - UserQueueModel.start_time))/60<=60).scalar_subquery()
        },synchronize_session="fetch",
    )
    db.commit()

def get_counter_details(db: Session, device_id:str):
    try:
        today = date.today()
        data={}
        counter = (db.query(QueueCounterModel)
                    .join(DeviceModel, QueueCounterModel.id == DeviceModel.queue_counter)
                    .filter(DeviceModel.device_code == device_id)
                    .first())
        if counter is None:
            raise MutationError("No counter is linked to this device")
        data["counter"] = counter
        counter_user_queue=(db.query(UserQueueModel,UserServiceModel)
                            .join(UserServiceModel, UserServiceModel.token_id == UserQueueModel.token_id)
                            .join(RelUserServiceQueueModel, UserServiceModel.id == RelUserServiceQueueModel.user_service_id)
                            .filter(RelUserServiceQueueModel.user_queue_id == UserQueueModel.id)
                           .filter(UserQueueModel.counter == counter.id).filter(and_(RelUserServiceQueueModel.status!=ServiceStatusEnum.CANCELLED,RelUserServiceQueueModel.status!=ServiceStatusEnum.COMPLETED))
                           .filter(UserQueueModel.date == today).filter(or_(UserQueueModel.status == UserQueueStatusEnum.ENTRY,UserQueueModel.status == UserQueueStatusEnum.FREEZED))
                           .order_by(UserQueueModel.status)
                           .all())
        # logger.info(counter_user_queue)
        user_queue_service = defaultdict(list)
        for user_queue, user_service in counter_user_queue:
            user_queue_service[user_queue].append(user_service)       
        if user_queue_service is not None and len(user_queue_service)>0:
            data["counter_users"] = user_queue_service
        else:
            data["counter_users"]=[]
        return data
    except MutationError as ex:
        logger.info(ex)
        raise MutationError(ex.message)
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to get counter details")


def update_counter(db: Session, counter_id:str, status:str):
    try:
        queue_counter= db.query(QueueCounterModel).filter(QueueCounterModel.id==counter_id).one()
        counter_status= None
        with RedisLock(client_lock, "queue_"+str(0 if queue_counter.queue_id is None else queue_counter.queue_id), blocking_timeout=60):
            if status== StatusEnum.ACTIVE.name:
                counter_status= StatusEnum.ACTIVE
            elif status== StatusEnum.INACTIVE.name:
                counter_status= StatusEnum.INACTIVE
            db.query(QueueCounterModel).filter(QueueCounterModel.id==counter_id).update(
                    {
                        QueueCounterModel.counter_status:counter_status,
                    }, synchronize_session="fetch",
                )
            if counter_status==StatusEnum.ACTIVE:
                ongoing_count,freezed_count=entry_or_freeze_next_users(db, queue_counter.queue,queue_counter.id)
                db.query(QueueModel).filter(QueueModel.id==queue_counter.queue_id).update(
                        {QueueModel.ongoing_count: QueueModel.ongoing_count+ongoing_count,
                            QueueModel.freezed_count: QueueModel.freezed_count+freezed_count
                            },
                        synchronize_session="fetch",
                    )
            db.commit()
        return counter_status.name if counter_status is not None else None
    except MutationError as ex:
        logger.info(ex)
        raise MutationError(ex.message)
    except Exception as e:
        logger.exception(e)
        raise MutationError("Failed to update counter")
    
def create_tag(tag,tag_id,umr_no):
    res1 = handle_request1(
        os.environ["TAG_BASE_URL"] + UPDATE_TAG +"?uhId="+umr_no,
        None,
        {
            "tagId": tag,
            "tagName": umr_no,
            "num":tag_id,
            "groupId":"1238549258702848"
        })
    print("Create tag response")
    print(res1.json())
    if res1.status_code == 200:
        return "tag created successfully"
    else:
        raise MutationError("Error Updating Tag")
    
# def upload_all_tags(db:Session):
#     tag_list=db.query(TagModel).all()
#     for tag in tag_list:
#         try:
#             delete_tag(tag.id, tag.code)
#         except Exception as  ex:
#             logger.exception(ex)
# def create_tag_1(tag,tag_id,umr_no):
#     # res1 = requests.get(os.environ["TAG_BASE_URL"] + DELETE_TAG + f"?nums={tag_id}&tagId={tag_id}")
#     res1 = handle_request1(
#         os.environ["TAG_BASE_URL"] + UPDATE_TAG,
#         None,
#         {
#             "tagId": tag,
#             "tagName": umr_no,
#             "num":tag_id,
#         })
#     print("Create tag response")
#     print(res1.json())
#     if res1.status_code == 200:
#         return "tag created successfully"
#     else:
#         raise MutationError("Error Updating Tag")

def delete_tag(tag_id,tag_code, uhid):
    res1 = handle_request1(
        os.environ["TAG_BASE_URL"] + UPDATE_TAG +"?uhId="+uhid,
        None,
        {
            "tagId": tag_code,
            "tagName": tag_code,
            "num":tag_id,
            "groupId":"-1"
        })
    # res1 = requests.get(os.environ["TAG_BASE_URL"] + DELETE_TAG + f"?nums={tag_id}&tagId={tag_code}")
    print("Delete tag response")
    print(res1.json())
    if res1.status_code == 200:
        return "tag successfully deleted"
    else:
        return "issue while deleting tag"

def update_user_queue_step(db:Session, user_queue_id:str, queue_step_id, remarks: str, type,queue_location_id: int,tag:Optional[str]=None, queue_step_code: Optional[str]= None, staff_user: Optional[int]= None, tag_type: Optional[str] = None):
    if queue_step_code is None or queue_step_code=='':
        queue_step: QueueStepModel= db.query(QueueStepModel).filter(QueueStepModel.id==queue_step_id).one()
    else:
        queue_step: QueueStepModel= db.query(QueueStepModel).filter(QueueStepModel.code==queue_step_code).one()
    queue_step_id=queue_step.id
    logger.info(queue_step)
    with RedisLock(client_lock, "queue_manual_"+str(0 if queue_step.queue_id is None else queue_step.queue_id), blocking_timeout=180):
        try:
            user_queue: UserQueueModel = db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id).one()
            status= None
            logger.info(type)
            if type=='CHECKIN':
                status= queue_step.checkin_status
            elif type== 'CHECKOUT':
                status= queue_step.checkout_status
            else:
                raise MutationError("Invalid Type")
            logger.info(status)
            previous_location_id = user_queue.location_id
            if tag is not None:
                if status==UserQueueStatusEnum.FREEZED:
                    tag_data= get_tag_or_scan_id(db, tag,tag_type)
                else:
                    if tag_type=="RFID":
                        tag_data = db.query(TagModel).filter(func.upper(TagModel.rfid_code)==tag.upper()).one_or_none()
                    else:
                        tag_data = db.query(TagModel).filter(func.upper(TagModel.code)==tag.upper()).one_or_none()
                tag_id = tag_data.id
                tag_code = tag_data.code
            else:
                tag_code = user_queue.tag.code
                tag_id = user_queue.tag_id   
            if queue_step.queue.queue_type == QueueTypeEnum.MANUAL:
                if status==UserQueueStatusEnum.EXIT:
                    location_ids =[]
                    if user_queue.location_id is not None:
                        location_ids.append(user_queue.location_id)
                    if user_queue.next_location_id is not None:
                        location_ids.append(user_queue.next_location_id)
                    db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id, UserQueueModel.queue_id==queue_step.queue_id).update({
                                    UserQueueModel.location_id:None, UserQueueModel.next_location_id:None,UserQueueModel.tag_id:None}, synchronize_session="fetch",)
                    db.query(LocationModel).filter(LocationModel.id.in_(location_ids)).update({
                                    LocationModel.occupied_count: LocationModel.occupied_count - 1}, synchronize_session="fetch",)
                    exit_user_queue(db, user_queue.queue_id, QRDetail(token_id=user_queue.token_id), None,None,staff_user)

                    deleting_tag = delete_tag(tag_id,tag_code,user_queue.user.umr_no)
                    print(deleting_tag)
                else:
                    if status==UserQueueStatusEnum.ENTRY and (user_queue.status==UserQueueStatusEnum.ARRIVED or user_queue.status==UserQueueStatusEnum.CHECKIN or user_queue.status==UserQueueStatusEnum.FREEZED):
                        db.query(QueueModel).filter(QueueModel.id==user_queue.queue_id).update({
                            QueueModel.ongoing_count:QueueModel.ongoing_count+1
                        },synchronize_session="fetch"
                        )
                        if user_queue.tag_id is None:
                            creating_tag = create_tag(tag_code,tag_id,user_queue.user.umr_no)
                            print(creating_tag)
                        else:
                            raise MutationError("Tag is already assigned to this patient")
                    start_time_case= case(
                        [
                            (
                                (status == UserQueueStatusEnum.ENTRY) and 
                                (UserQueueModel.start_time.is_(None)), 
                                func.now()
                            )
                        ], 
                        else_=UserQueueModel.start_time
                    )
                    freezed_time_case= case(
                        [
                            (
                                (status == UserQueueStatusEnum.FREEZED) and 
                                (UserQueueModel.freezed_at.is_(None)), 
                                func.now()
                            )
                        ], 
                        else_=UserQueueModel.freezed_at
                    )
                    arrived_time_case= case(
                        [
                            (
                                (status == UserQueueStatusEnum.ARRIVED) and 
                                (UserQueueModel.arrived_at.is_(None)), 
                                func.now()
                            )
                        ], 
                        else_=UserQueueModel.arrived_at
                    )
                    value= db.query(UserQueueModel).filter(UserQueueModel.status!=UserQueueStatusEnum.EXIT, UserQueueModel.status!=UserQueueStatusEnum.PURGED).filter(UserQueueModel.id==user_queue_id, UserQueueModel.queue_id==queue_step.queue_id).update({
                            UserQueueModel.queue_step_id: queue_step_id,
                            UserQueueModel.location_id:queue_location_id if type == 'CHECKIN' else UserQueueModel.location_id,
                            UserQueueModel.status:status,
                            UserQueueModel.freezed_at: freezed_time_case,
                            UserQueueModel.arrived_at: arrived_time_case,
                            UserQueueModel.start_time: start_time_case,
                            UserQueueModel.end_time: func.now() if status==UserQueueStatusEnum.EXIT else UserQueueModel.end_time,
                            UserQueueModel.tag_id:tag_id if tag_id is not None else UserQueueModel.tag_id,
                            UserQueueModel.next_location_id:queue_location_id if type == 'CHECKOUT' else UserQueueModel.next_location_id,
                            UserQueueModel.step_start_time:None if type == 'CHECKOUT' else func.now(),
                            UserQueueModel.next_step_start_time: func.now() if type == 'CHECKOUT' else None,
                        }, synchronize_session="fetch",
                    )
                    if previous_location_id is not None and type == 'CHECKIN':
                        previous_location = db.query(LocationModel).filter(LocationModel.id == previous_location_id).first()
                        previous_location_ids=list(filter(None,[previous_location.id,previous_location.parent_location_id]))
                        db.query(LocationModel).filter(LocationModel.id.in_(previous_location_ids)).update({
                                    LocationModel.occupied_count: LocationModel.occupied_count - 1}, synchronize_session="fetch",)
                    if queue_location_id is not None:
                        queue_location = db.query(LocationModel).filter(LocationModel.id == queue_location_id).first()
                        location_ids=list(filter(None,[queue_location.id,queue_location.parent_location_id]))
                        if (type == "CHECKOUT"):
                            db.query(LocationModel).filter(LocationModel.id.in_(location_ids)).update({
                                    LocationModel.alloted_count: LocationModel.alloted_count + 1}, synchronize_session="fetch",)
                        elif (type== 'CHECKIN'):
                            if user_queue.next_location_id is not None:
                                alloted_location = db.query(LocationModel).filter(LocationModel.id == user_queue.next_location_id).first()
                                alloted_location_ids=list(filter(None,[alloted_location.id,alloted_location.parent_location_id]))
                                db.query(LocationModel).filter(LocationModel.id.in_(alloted_location_ids)).update({
                                    LocationModel.alloted_count: LocationModel.alloted_count - 1}, synchronize_session="fetch",)
                            db.query(LocationModel).filter(LocationModel.id.in_(location_ids)).update({
                                    LocationModel.occupied_count: LocationModel.occupied_count + 1}, synchronize_session="fetch",)
                            db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id, UserQueueModel.queue_id==queue_step.queue_id).update({
                                UserQueueModel.next_location_id:None}, synchronize_session="fetch",)
                    # if (type == "CHECKOUT"):
                    #     if previous_location_id is not None:
                    #         db.query(LocationModel).filter(LocationModel.id == previous_location_id,LocationModel.parent_location_id.isnot(None)).update({
                    #             LocationModel.status: LocationBedStatusEnum.AVAILABLE}, synchronize_session="fetch",)
                    #         db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id, UserQueueModel.queue_id==queue_step.queue_id).update({
                    #                 UserQueueModel.location_id:None}, synchronize_session="fetch",)
                    logger.info(queue_step_id)
                    add_user_queue_step(db,queue_step_id,user_queue_id,remarks,type, None, None, queue_location_id,previous_location_id,tag_id,staff_user)
                    logger.info(value)
                    if value== 0:
                        raise MutationError("Invalid Action")
            else:
                raise MutationError("Queue update not permitted")
            db.commit()
        except MutationError as ex:
            raise MutationError(ex.message)
        except Exception as ex:
            logger.exception(ex)
            raise MutationError("Error occured while checkin")
    
def add_user_queue_step(db,queue_step_id,user_queue_id,remarks,type, status,queue, input_location_id,assigned_location_id,tag_id,staff_user):
    if status is not None:
        queue_step: QueueStepModel= db.query(QueueStepModel).filter(QueueStepModel.queue_id==queue.id)
        if type=='CHECKIN':
           queue_step=queue_step.filter(QueueStepModel.checkin_status==status).first()
        else:
           queue_step=queue_step.filter(QueueStepModel.checkout_status==status).first()
        if queue_step is None:
            return None, None
        queue_step_id = queue_step.id
    queue_step: QueueStepModel= db.query(QueueStepModel).filter(QueueStepModel.id==queue_step_id).first()
    db.query(UserQueueStepModel).filter(UserQueueStepModel.user_queue_id==user_queue_id).filter(
        UserQueueStepModel.updated_at == None
        ).update(
        {
            UserQueueStepModel.updated_by:"SYSTEM",
            UserQueueStepModel.updated_at:func.now(),
            UserQueueStepModel.status: UserQueueStepStatusEnum.COMPLETED
        },synchronize_session="fetch",
    )
    location=None
    location_id = input_location_id if input_location_id is not None else assigned_location_id
    location: LocationModel = db.query(LocationModel).filter(LocationModel.id == location_id).first()
    if input_location_id is not None and type == "CHECKOUT":
        remarks = f"Assigned to {location.name}" if ((remarks is None or remarks =="") and location.parent_location is not None) else f"Assigned to {location.name}" if (remarks is None or remarks =="") else remarks
    if location is not None and (type !="CHECKOUT" or assigned_location_id is not None):
        created_location = location.name if location.parent_location is not None else location.name
    elif type == 'CHECKIN':
        created_location = queue_step.checkin_name
    else:
        created_location=queue_step.checkout_name
    logger.info(f"type:{type}")
    if staff_user is not None:
        staff_user_1: StaffUserModel = get_staff_user(db,staff_user)
    user_queue_step= UserQueueStepModel(
        queue_step_id = queue_step_id,
        user_queue_id = user_queue_id,
        remarks = remarks,
        action_type = type, 
        status = UserQueueStepStatusEnum.IN_PROGRESS if type=='CHECKIN' else UserQueueStepStatusEnum.COMPLETED,
        updated_at = None if type=='CHECKIN' else func.now(),
        location_id = location_id,
        created_by = "SYSTEM" if staff_user is None else staff_user_1.name,
        created_location= created_location,
        description = remarks if remarks is not None and remarks !="" else queue_step.checkin_description if type == 'CHECKIN' else queue_step.checkout_description,
        tag_id = tag_id,
    )
    db.add(user_queue_step)
    return queue_step_id,location_id
    
def add_to_queue(db:Session, uhid:str, queue_step_id, remarks: str, type,staff_user):
    queue_step: QueueStepModel= db.query(QueueStepModel).filter(QueueStepModel.id==queue_step_id).one()
    if queue_step is None:
        raise MutationError("Invalid Action")
    status= None
    if type=='CHECKIN':
        status= queue_step.checkin_status
    elif type== 'CHECKOUT':
        status= queue_step.checkout_status
    else:
        MutationError("Invalid Type")
    user_token= db.query(UserTokenModel).join(UserTokenModel.user).join(UserTokenModel.user_services).filter(or_(UserServiceModel.status==ServiceStatusEnum.PENDING, UserServiceModel.status==ServiceStatusEnum.ON_PROGRESS)).filter(UserModel.umr_no==uhid).first()
    if status==UserQueueStatusEnum.ARRIVED:
        add_user_queue(db, queue_step.queue_id, QRDetail(uhid=user_token.user.umr_no),staff_user)
    elif status ==UserQueueStatusEnum.CHECKIN:
       all_services_ids, user_id, token_id, phone_number,token_no = get_all_services(db, user_token.id, queue_step.queue_id)
       user_queue, time = checkin_user_into_queue(db, queue_step.queue, user_token.user_id, user_token.token_no, user_token.id, all_services_ids,phone_number, None,staff_user)
    db.commit()

def get_all_services(db, token_id, queue_id):
    services1= db.scalars(db.query(UserServiceModel.service_id).join(UserServicePrerequisiteModel,UserServiceModel.id==UserServicePrerequisiteModel.user_service_id).filter(
                            UserServiceModel.token_id==token_id,UserServicePrerequisiteModel.status==UserPreReqStatusEnum.HOLD)).all()
    all_services = db.query(UserServiceModel,UserTokenModel.token_no).join(UserTokenModel).join(UserServiceModel.service).join(ServiceModel.queues).filter(QueueModel.id==queue_id).filter(UserServiceModel.service_id.not_in(services1), UserServiceModel.token_id==token_id).filter(UserServiceModel.status==ServiceStatusEnum.PENDING).filter(UserTokenModel.user_id == UserServiceModel.user_id).all()
    user_services = [user_service[0] for user_service in all_services]
    token_no = list(set(map(lambda x: x[1],all_services)))
    return (
        list(set(map(attr, user_services))) for attr in (lambda x: x.service_id, lambda x: x.user_id, lambda x: x.token_id, lambda x: x.user.phone_number, lambda x: x.user_token.token_no)
    )

def get_queue_steps(db:Session,queue_id):
    return db.query(QueueStepModel).filter(QueueStepModel.queue_id==queue_id).order_by(QueueStepModel.priority).all()

def call_next(db:Session, queue_id: int, staff_user:Optional[int]= None):
    queue: QueueModel= db.query(QueueModel).filter(QueueModel.id== queue_id).one()
    with RedisLock(client_lock, "queue_manual_"+str(0 if queue.id is None else queue.id), blocking_timeout=180):
            if queue.queue_type!=QueueTypeEnum.MANUAL:
                raise MutationError("Invalid Action")
            else:
                if db.query(UserQueueModel.id).filter(UserQueueModel.queue_id == queue_id, UserQueueModel.date == date.today(), UserQueueModel.status == UserQueueStatusEnum.FREEZED).count()>=queue.upcoming_patients:
                    raise MutationError("Max calling count reached")
            freeze_user_queue = db.query(UserQueueModel).join(UserQueueModel.queue_weightage_action).filter(or_(UserQueueModel.pre_check_status==UserQueuePreCheckStatusEnum.COMPLETED, UserQueueModel.pre_check_status==None)).filter(UserQueueModel.queue_id == queue_id, UserQueueModel.date == date.today()).filter(or_(UserQueueModel.status == UserQueueStatusEnum.ARRIVED,UserQueueModel.status == UserQueueStatusEnum.CHECKIN)).order_by(
                    desc(QueueWeightageActionModel.weightage),
                    asc(UserQueueModel.created_at))
            freeze_user_queue=freeze_user_queue.first()
            logger.info(freeze_user_queue)
            if freeze_user_queue is not None:
                # queue_step_id= db.query(QueueStepModel.id).filter(QueueStepModel.code == 'TOKEN_ISSUED').first()
                # add_user_queue_step(db,queue_step_id.id,freeze_user_queue.id,"Token called","CHECKOUT", None, None, None,None,None)
                queue_step_id,location_id = add_user_queue_step(db,None,freeze_user_queue.id," Token called", "CHECKIN",UserQueueStatusEnum.FREEZED,queue, None,None,None,staff_user)
                db.query(UserQueueModel).filter(
                        UserQueueModel.id==freeze_user_queue.id
                    ).update({
                        UserQueueModel.status:UserQueueStatusEnum.FREEZED,
                        UserQueueModel.freezed_at:func.now(),
                        UserQueueModel.location_id:location_id,
                        UserQueueModel.queue_step_id:queue_step_id,
                        UserQueueModel.step_start_time: func.now(),
                        UserQueueModel.next_step_start_time: None,
                    }, synchronize_session="fetch",
                    )
                queue.latest_token_id=freeze_user_queue.token_id
                queue.last_token_called_at=func.now()
            else:
                raise MutationError("No Patients In Waiting")
            db.commit()

def update_user_queue_manual(db: Session, user_queue_id, queue_weightage_action_id, staff_user: Optional[int]= None,remarks:Optional[str]= None):
    logger.info(f'start time {time.time()}')
    user_queue= db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id).one()
    location_id=user_queue.location_id
    next_location_id= user_queue.next_location_id
    with RedisLock(client_lock, "queue_manual_"+str(0 if user_queue.queue_id is None else user_queue.queue_id), blocking_timeout=60):
        try:
            action:QueueWeightageActionModel=db.query(QueueWeightageActionModel).filter(QueueWeightageActionModel.id==queue_weightage_action_id).one()
            logger.info(f"user_queue_id:{user_queue_id}")
            if action.code=='HOLD' or action.code=='PURGED':
                if user_queue.status==UserQueueStatusEnum.ENTRY:
                    db.query(QueueModel).filter(QueueModel.id==user_queue.queue_id).update(
                        {
                            QueueModel.ongoing_count:QueueModel.ongoing_count-1,
                            QueueModel.cancelled_count: QueueModel.cancelled_count+1 if action.code=='PURGED' else QueueModel.cancelled_count
                        }, synchronize_session="fetch",
                    )
                if user_queue.tag_id is not None:
                    deleting_tag = delete_tag(user_queue.tag_id,user_queue.tag.code,user_queue.user.umr_no)
                    print(deleting_tag)
                value= db.query(UserQueueModel).filter(UserQueueModel.status!=UserQueueStatusEnum.EXIT, UserQueueModel.status!=UserQueueStatusEnum.PURGED).filter(UserQueueModel.id==user_queue_id).update({
                            UserQueueModel.status:action.code,
                            # UserQueueModel.weightage_id:queue_weightage_action_id,
                            UserQueueModel.location_id:None, 
                            UserQueueModel.next_location_id:None,
                            UserQueueModel.tag_id:None,
                            UserQueueModel.queue_step_id: None
                        }, synchronize_session="fetch",
                    )
            else:
                value= db.query(UserQueueModel).filter(UserQueueModel.status!=UserQueueStatusEnum.EXIT, UserQueueModel.status!=UserQueueStatusEnum.PURGED).filter(UserQueueModel.id==user_queue_id).update({
                            UserQueueModel.status:UserQueueStatusEnum.ARRIVED,
                            UserQueueModel.weightage_id:queue_weightage_action_id,
                        }, synchronize_session="fetch",
                    )
            if action.code=='PURGED':
                user_service_ids = db.scalars(db.query(RelUserServiceQueueModel.user_service_id).filter(
                            RelUserServiceQueueModel.user_queue_id == user_queue_id)).all()
                db.query(UserServiceModel).filter(UserServiceModel.id.in_(user_service_ids)).update({
                            UserServiceModel.status: ServiceStatusEnum.CANCELLED
                        }, synchronize_session="fetch")
                if location_id is not None:
                    db.query(LocationModel).filter(LocationModel.id == user_queue.location_id).update({
                                    LocationModel.occupied_count: LocationModel.occupied_count - 1}, synchronize_session="fetch",)
                if next_location_id is not None:
                    db.query(LocationModel).filter(LocationModel.id == user_queue.next_location_id).update({
                                    LocationModel.alloted_count: LocationModel.alloted_count - 1}, synchronize_session="fetch",)
            staff_user_1 = None
            if staff_user is not None:
                staff_user_1: StaffUserModel = get_staff_user(db,staff_user)
            db.query(UserQueueStepModel).filter(UserQueueStepModel.user_queue_id==user_queue_id).filter(
                UserQueueStepModel.updated_at == None
                ).update(
                {
                    UserQueueStepModel.updated_by:"SYSTEM",
                    UserQueueStepModel.updated_at:func.now(),
                    UserQueueStepModel.status: UserQueueStepStatusEnum.CANCELLED
                },synchronize_session="fetch",
            )
            user_queue_step= UserQueueStepModel(
                    queue_step_id = None,
                    user_queue_id = user_queue_id,
                    remarks = f"user action {action.code}",
                    action_type = "CHECKOUT" if action.code=="PURGED" else"CHECKIN", 
                    status = UserQueueStepStatusEnum.CANCELLED if action.code=="PURGED" else UserQueueStepStatusEnum.IN_PROGRESS,
                    location_id = location_id,
                    created_by = "SYSTEM" if staff_user is None else staff_user_1.name,
                    created_location= None,
                    description = remarks if remarks is not None and remarks !="" else f"Status changed to {action.code}",
                    tag_id = None,
                )  
            db.add(user_queue_step)
            logger.info(value)
            if value== 0:
                raise MutationError("Invalid Action")
            db.commit()
        except Exception as ex:
            logger.exception(ex)
            raise MutationError("Error updating queue")
    
    
def get_queue_step_locations(db:Session, queue_step_id: int,condition: str):
    queue_step= db.query(QueueStepModel).filter(QueueStepModel.id==queue_step_id).one()
    queue_step_id = None
    if condition == "CURRENT":
        queue_step_id = queue_step.id
    elif condition == "NEXT":
        queue_step_id_1=db.query(QueueStepModel).filter(QueueStepModel.queue_id==queue_step.queue_id).filter(QueueStepModel.priority>queue_step.priority).order_by(QueueStepModel.priority,QueueStepModel.id).first()
        queue_step_id = queue_step_id_1.id
    if queue_step_id is None:
        return []
    else:
        # logger.info(queue_step_id_1)
        return db.query(LocationModel).join(LocationModel.queue_step).filter(QueueStepModel.id==queue_step_id).filter(LocationModel.parent_location_id.is_(None)).order_by(LocationModel.priority).all()

def get_locations(db:Session):
    return db.query(LocationModel).all()

def get_procedure_rooms(db:Session):
    now = datetime.now(pytz.timezone('Asia/Kolkata'))
    start_of_day = now.replace(hour=0, minute=0, second=0, microsecond=0)
    return db.query(
        LocationModel.code,
        LocationModel.alloted_count,
        LocationModel.status,
        LocationModel.occupied_count,
        LocationModel.total_count,
        (func.avg(func.extract("epoch", UserQueueStepModel.updated_at - UserQueueStepModel.created_at)) / 60).label('avg_time_mins')
    ).join(
        UserQueueStepModel, UserQueueStepModel.location_id == LocationModel.id
    ).filter(
        UserQueueStepModel.created_at > start_of_day
    ).group_by(
        LocationModel.id, LocationModel.code, LocationModel.alloted_count, LocationModel.status, LocationModel.occupied_count, LocationModel.total_count
    ).all()

def get_censes(db:Session):
    now = datetime.now(pytz.timezone('Asia/Kolkata'))
    start_of_day = now.replace(hour=0, minute=0, second=0, microsecond=0)
    return db.query(
        func.count(UserQueueModel.id).label('total_count'),
        func.sum(case([(UserQueueModel.status == 'EXIT', 1)], else_=0)).label('completed_count'),
        func.sum(case([(UserQueueModel.status.in_(['FREEZED', 'HOLD', 'ARRIVED', 'CHECKIN']), 1)], else_=0)).label('waiting_count'),
        func.sum(case([(UserQueueModel.status == 'ENTRY', 1)], else_=0)).label('inprogress_count')
    ).filter(
        UserQueueModel.created_at > start_of_day
    ).one()

def get_queue_step_logs(db:Session,token_id):
    return db.query(UserQueueStepModel).join(UserQueueStepModel.user_queue).filter(UserQueueModel.token_id==token_id).order_by(
        desc(UserQueueStepModel.created_at)
    ).all()
def get_user_queue_logs(db:Session, token_id):
    return db.query(UserQueueLogsModel).join(UserQueueLogsModel.user_queue).filter(UserQueueModel.token_id==token_id).order_by(
        desc(UserQueueLogsModel.created_at)
    ).all()
def update_loc_1(db:Session,tag_id, location_code):
    with RedisLock(client_lock, "tag_id"+tag_id, blocking_timeout=60):
        location: LocationModel= db.query(LocationModel).filter(LocationModel.iot_code==location_code).first()
        user_queue: UserQueueModel= (db.query(UserQueueModel)
                    .join(TagModel, UserQueueModel.tag_id == TagModel.id)
                    .filter(TagModel.code == tag_id,UserQueueModel.status !=UserQueueStatusEnum.EXIT,UserQueueModel.status != UserQueueStatusEnum.PURGED).first())
        if user_queue is None or location is None:
            if user_queue is not None:
                logger.info(f"user_queue: {user_queue}")
            elif location is not None:
                logger.info(f"location: {location}")
            else:
                logger.info("both user_queue and location id is not found")
            return None
        else:
            priority=0
            if user_queue.queue_step_id is not None:
                queue_step_1=db.query(QueueStepModel).filter(QueueStepModel.id==user_queue.queue_step_id).first()
                priority=queue_step_1.priority
            location_id = location.id if location.parent_location_id is None else location.parent_location_id
            queue_step= db.query(QueueStepModel).join(QueueStepModel.locations).filter(QueueStepModel.queue_id==user_queue.queue_id).filter(QueueStepModel.priority>=priority).filter(LocationModel.id == location_id).order_by(asc(QueueStepModel.priority)).first()
            if queue_step is not None:
                if location.id!=user_queue.location_id:
                    data = update_user_queue_step(db, user_queue.id, queue_step.id,"updated from iot device","CHECKIN",location.id,tag_id, None,None, None)
            db.commit()
            return True
def get_available_tags(db: Session):
    try:
        tags = db.query(TagModel).join(UserQueueModel, UserQueueModel.tag_id == TagModel.id,isouter=True).filter(UserQueueModel.tag_id.is_(None)).filter(TagModel.status == StatusEnum.ACTIVE).all()
        return tags
    except Exception as ex:
        logger.exception(ex)
        raise MutationError("Error While Fteching data")

def get_all_tags(db: Session):
    try:
        tags = db.query(TagModel).all()
        return tags
    except Exception as ex:
        logger.exception(ex)
        raise MutationError("Error While Fetching data")
    
def get_tag_or_scan_id(db: Session, ref_id: str, type: str):
    try:
        query= db.query(TagModel).join(UserQueueModel, UserQueueModel.tag_id == TagModel.id,isouter=True).filter(UserQueueModel.tag_id.is_(None))
        if type=="RFID":
            query=query.filter(func.upper(TagModel.rfid_code)==ref_id.upper())
        else:
            query=query.filter(func.upper(TagModel.code)==ref_id.upper())
        tag = query.filter(TagModel.status == StatusEnum.ACTIVE).first()
        if tag is None:
            raise MutationError("Tag is not available")
        return tag
    except MutationError as ex:
        raise MutationError(ex.message)
    except Exception as ex:
        logger.exception(ex)
        raise MutationError("Error While Fetching data")
    
def checkout_user(db: Session, staff_user: str, ref_id: Optional[str]=None, uhid: Optional[str]=None):
    try:
        if ref_id is not None and ref_id != "":
            data = db.query(TagModel.code,UserQueueModel.token_no,UserQueueModel.id).join(UserQueueModel, UserQueueModel.tag_id == TagModel.id,isouter=True).filter(or_(UserQueueModel.status == UserQueueStatusEnum.ENTRY,UserQueueModel.status == UserQueueStatusEnum.PAUSED)).filter(func.upper(TagModel.rfid_code)==ref_id.upper()).one_or_none()
        else:
            data = db.query(TagModel.code,UserQueueModel.token_no,UserQueueModel.id).join(UserQueueModel, UserQueueModel.tag_id == TagModel.id,isouter=True).join(UserModel, UserModel.id == UserQueueModel.user_id,isouter=True).filter(or_(UserQueueModel.status == UserQueueStatusEnum.ENTRY,UserQueueModel.status == UserQueueStatusEnum.PAUSED)).filter(func.upper(UserModel.umr_no) == uhid.upper()).one_or_none()
        print(data)
        if data is None:
            tag= db.query(TagModel).filter(func.upper(TagModel.rfid_code)==ref_id.upper()).first()
            if tag is None:
                raise MutationError("Invalid RfId code")
            raise MutationError("Already checked out")
        update_user_queue_step(db, data.id, None,"Check out user","CHECKOUT",None,data.code, "DISCHARGE",staff_user, "TAGID")
        return data.token_no
    except MutationError as ex:
        raise MutationError(ex.message)
    except Exception as ex:
        logger.exception(ex)
        raise MutationError("Error While Fetching data")

def pause_unpause_user(db: Session,staff_user: str, user_queue_id: str, type: str):
    try:
        user_queue: UserQueueModel = db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id).one()
        if type == "PAUSED":
            update_user_queue_step(db, user_queue_id, user_queue.queue_step_id, "Tracking paused", "CHECKOUT", None, None, None, staff_user, None)
            user_queue.status = UserQueueStatusEnum.PAUSED.name
            db.commit()
            return "successfully paused"
        if type == "RESUMED":
            update_user_queue_step(db, user_queue_id, None, "Tracking resumed","CHECKOUT", None, None, "TRANSITION",staff_user, None)
            return "successfully unpaused"
        raise MutationError("Invalid type")
    except MutationError as ex:
        raise MutationError(ex.message)
    except Exception as ex:
        logger.exception(ex)
        raise MutationError("Error While Fetching data")

def check_tag_avaible_or_not(db :Session,ref_id):
    tag_code = db.query(TagModel.code).filter(TagModel.rfid_code == ref_id).one_or_none()
    scan_id = db.query(TagModel.rfid_code).filter(TagModel.code == ref_id).one_or_none()
    if tag_code is not None:
        Avaible_tags = db.query(TagModel.code).join(UserQueueModel, UserQueueModel.tag_id == TagModel.id,isouter=True).filter(UserQueueModel.tag_id.is_(None)).filter(TagModel.status == StatusEnum.ACTIVE).all()
        if tag_code not in Avaible_tags:
            # raise MutationError("rfid is already assigned")
            return "rfid is already assigned"
        return tag_code[0]
    if scan_id is not None:
        Avaible_rfid_codes = db.query(TagModel.rfid_code).join(UserQueueModel, UserQueueModel.tag_id == TagModel.id,isouter=True).filter(UserQueueModel.tag_id.is_(None)).filter(TagModel.status == StatusEnum.ACTIVE).all()
        if scan_id not in Avaible_rfid_codes:
            # raise MutationError("tagId is already assigned")
            return "tagid is already assigned"
        return scan_id[0]
    else:
        raise MutationError("refId is not found")
    
def update_user_queue_step_dynamically(db: Session):
    user_queues = (
        db.query(UserQueueModel).join(UserQueueModel.queue).join(QueueStepModel, UserQueueModel.queue_step_id == QueueStepModel.id)
        .filter(UserQueueModel.status == UserQueueStatusEnum.ENTRY,QueueModel.queue_type==QueueTypeEnum.MANUAL)
        .filter(or_(
            (func.extract("epoch", func.now() - UserQueueModel.step_start_time)/60) > QueueStepModel.avg_step_time,
            UserQueueModel.step_start_time.is_(None)))
        .order_by(desc(QueueStepModel.priority))
        .all()
    )
    logger.info(f'locations update {len(user_queues)}')
    if len(user_queues) >0:
        for user in user_queues:
            start_time=time.time()
            logger.info(f'locations update start time : {time.time()}')
            with RedisLock(client_lock, "queue_location_"+str(0 if user.queue_id is None else user.queue_id), blocking_timeout=60):
                try:
                    priority=0
                    if user.queue_step_id is not None:
                        queue_step_1=db.query(QueueStepModel).filter(QueueStepModel.id==user.queue_step_id).first()
                        priority=queue_step_1.priority
                        queue_step= (db.query(QueueStepModel).filter(QueueStepModel.queue_id==user.queue_id)
                        .filter(QueueStepModel.priority>priority).order_by(asc(QueueStepModel.priority)).first())
                        tag=user.tag.code if user.tag is not None else None
                        if queue_step is not None:
                            if queue_step.code == 'PROCEDURE':
                                locations = queue_step.locations[0].child_locations
                            else :
                                locations = queue_step.locations
                            available_locations = list(filter(lambda loc: (loc.alloted_count+ loc.occupied_count) < loc.total_count, locations))
                            assigned_location = random.choice(available_locations) if len(available_locations )>0 else None
                            location_id = assigned_location.id if assigned_location is not None else None
                            if queue_step.code =='DISCHARGE':
                                update_user_queue_step(db, user.id, queue_step.id,"From Cron Server","CHECKOUT",user.location_id,tag, None,None, None)
                            elif location_id is not None:
                                update_user_queue_step(db, user.id, queue_step.id,"From Cron Server","CHECKIN",location_id,tag, None,None,None)
                        else:
                            update_user_queue_step(db, user.id, user.queue_step_id,"From Cron Server","CHECKOUT",user.location_id,tag, None,None,None)
                except Exception as ex:
                    logger.exception(ex)
            logger.info(f'locations update end time : {time.time()}')

def get_user_queue_steps_by_user_queue_id(db:Session, user_queue_id:int):
    return db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id).one_or_none()

def update_pre_check_status(db:Session, user_queue_id:int, user_service_ids):
    user_queue= db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id).one()
    db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id).update(
        {
            UserQueueModel.pre_check_status:UserQueuePreCheckStatusEnum.COMPLETED
        },synchronize_session="fetch",
    )
    db.commit()
    if user_service_ids is not None and len(user_service_ids)>0:
        db.query(RelUserServiceQueueModel).filter(RelUserServiceQueueModel.user_queue_id==user_queue_id).filter(
            RelUserServiceQueueModel.user_service_id.not_in(
                user_service_ids
            )
        ).update(
            {
                RelUserServiceQueueModel.status:ServiceStatusEnum.CANCELLED
            },synchronize_session="fetch",
        )
        cancelled_service_ids=db.query(RelUserServiceQueueModel.user_service_id).filter(RelUserServiceQueueModel.user_queue_id==user_queue_id).filter(
            RelUserServiceQueueModel.user_service_id.not_in(
                user_service_ids
            )
        ).scalar_subquery()
        db.query(UserServiceModel).filter(
            UserServiceModel.id.in_(
                cancelled_service_ids
            )
        ).update({
            UserServiceModel.status:ServiceStatusEnum.PENDING
        },synchronize_session="fetch",
        )
    status,queue_counter = freeze_user(db,user_queue.queue, user_queue,None,None)
    logger.info(status)
    if status != None:
        if status==UserQueueStatusEnum.FREEZED:
            db.query(QueueModel).filter(QueueModel.id==user_queue.queue_id).update(
                            {QueueModel.freezed_count: QueueModel.freezed_count+1},
                            synchronize_session="fetch",
                    )
            db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id).update(
                {
                    UserQueueModel.freezed_at:func.now()
                },synchronize_session="fetch",
            )
        elif status==UserQueueStatusEnum.ENTRY:
            db.query(QueueModel).filter(QueueModel.id==user_queue.queue_id).update(
                {QueueModel.ongoing_count: QueueModel.ongoing_count+1},
                synchronize_session="fetch",
            )
            db.query(UserQueueModel).filter(UserQueueModel.id==user_queue_id).update(
                {
                    UserQueueModel.freezed_at:func.now(),
                    UserQueueModel.start_time: func.now()
                },synchronize_session="fetch",
            )        
    db.commit()

def get_service_categories(db):
    categories=db.query(func.unnest(ServiceModel.prerequisites_conditions).label('category')).distinct().all()
    categories = list(map(lambda x: x.category,categories))
    return categories

def get_average_time(db):
    now = datetime.now(pytz.timezone('Asia/Kolkata'))
    start_of_day = now.replace(hour=0, minute=0, second=0, microsecond=0)
    subquery = db.query(
    UserQueueStepModel.user_queue_id,
    UserQueueStepModel.queue_step_id,
    (func.avg((func.extract('epoch', UserQueueStepModel.updated_at) - func.extract('epoch', UserQueueStepModel.created_at)) / 60)).label('avg_duration_minutes')
    ).join(
        RelUserServiceQueueModel, UserQueueStepModel.user_queue_id == RelUserServiceQueueModel.user_queue_id
    ).join(
        UserServiceModel, RelUserServiceQueueModel.user_service_id == UserServiceModel.id
    ).join(
        ServiceModel, UserServiceModel.service_id == ServiceModel.id
    ).filter(
        UserQueueStepModel.created_at > start_of_day,
        UserQueueStepModel.updated_at.isnot(None),
        ServiceModel.code.in_(["ENDOSCOPY", "COLONOSCOPY"])
    ).group_by(
        UserQueueStepModel.user_queue_id,
        UserQueueStepModel.queue_step_id,
    ).subquery()
    result = db.query(
        QueueStepModel.code,
        subquery.c.queue_step_id,
        func.count(subquery.c.user_queue_id).label('count'),
        func.avg(subquery.c.avg_duration_minutes).label('avg_duration_per_user')
    ).join(QueueStepModel, QueueStepModel.id==subquery.c.queue_step_id).group_by(
        subquery.c.queue_step_id,QueueStepModel.code
    ).all()
    return result

def get_overall_average_time(db):
    try:
        now = datetime.now(pytz.timezone('Asia/Kolkata'))
        start_of_day = now.replace(hour=0, minute=0, second=0, microsecond=0)
        sub_query = db.query(LocationModel.id).filter(LocationModel.code == "PROCEDURE").subquery()
        one_hour_ago = now - timedelta(hours=1)
        two_hour_ago = now - timedelta(hours=2)
        data = db.query(
            func.ceil(func.abs(func.count(UserQueueStepModel.user_queue_id)/(func.extract('epoch',func.now() - func.min(UserQueueStepModel.created_at))/3600))).label('avg_procedure_per_hour'),
            func.ceil(func.abs(func.count(distinct(case([(LocationModel.code.like('EP%'), UserQueueStepModel.user_queue_id)], else_=None)))/(func.extract('epoch', func.now() - func.min(UserQueueStepModel.created_at)) / 3600))).label('endoscopy_avg_procedure_per_hour'),
            func.ceil(func.abs(func.count(distinct(case([(UserQueueStepModel.created_at >= one_hour_ago, case([(LocationModel.code.like('EP%'), UserQueueStepModel.user_queue_id)], else_=None))], else_=None))))).label('endoscopy_last_hour_avg_procedure_per_hour'),
            func.ceil(func.abs(func.count(distinct(case([(LocationModel.code.like('CP%'), UserQueueStepModel.user_queue_id)], else_=None)))/(func.extract('epoch', func.now() - func.min(UserQueueStepModel.created_at)) / 3600))).label('colonoscopy_avg_procedure_per_hour'),
            func.ceil(func.abs(func.count(distinct(case([(UserQueueStepModel.created_at >= one_hour_ago, case([(LocationModel.code.like('CP%'), UserQueueStepModel.user_queue_id)], else_=None))], else_=None))))).label('colonoscopy_last_hour_avg_procedure_per_hour'),
            # func.ceil(func.abs(func.sum(case([(UserQueueStepModel.created_at <= one_hour_ago, UserQueueStepModel.created_at >= two_hour_ago)], else_=0))/(func.extract('epoch', func.now() - one_hour_ago) / 3600))).label('last_prev_hour_avg_procedure_per_hour'),
            (func.ceil(func.abs(func.count(distinct(case([(and_(UserQueueStepModel.created_at <= one_hour_ago,UserQueueStepModel.created_at >= two_hour_ago), case([(LocationModel.code.like('CP%'), UserQueueStepModel.user_queue_id)], else_=None))], else_=None)))))+
            func.ceil(func.abs(func.count(distinct(case([(and_(UserQueueStepModel.created_at <= one_hour_ago,UserQueueStepModel.created_at >= two_hour_ago), case([(LocationModel.code.like('EP%'), UserQueueStepModel.user_queue_id)], else_=None))], else_=None)))))).label('last_prev_hour_avg_procedure_per_hour'),
            func.count(distinct(case([(LocationModel.code.like('EP%'), UserQueueStepModel.user_queue_id)], else_=None))).label('endoscopy_count'),
            func.count(distinct(case([(LocationModel.code.like('CP%'), UserQueueStepModel.user_queue_id)], else_=None))).label('colonoscopy_count'),
            func.ceil(func.abs(func.count(UserQueueStepModel.user_queue_id)/(func.extract('epoch', func.now() - func.coalesce(func.min(UserQueueStepModel.created_at), start_of_day)) / 3600))).label('avg_procedure_from_start_of_day'),
            func.ceil(func.abs(((func.sum(func.extract('epoch',UserQueueStepModel.updated_at - UserQueueStepModel.created_at)))*100)/(func.count(distinct(LocationModel.id))* (func.extract('epoch',func.now() - func.min(UserQueueStepModel.created_at)))))).label('procedure_suit_occupancy_rate')
        ).join(UserQueueStepModel.user_queue).join(UserQueueStepModel.queue_step).join(LocationModel,and_(LocationModel.status != "UNAVAILABLE", LocationModel.parent_location_id.in_(sub_query),UserQueueStepModel.location_id ==LocationModel.id)).filter(
            UserQueueStepModel.created_at > start_of_day,
            UserQueueStepModel.updated_at.isnot(None),
            QueueStepModel.code == "PROCEDURE"
        ).one_or_none()
        logger.info(data)
        return data
    except Exception as e:
        print(str(e))

def call_next_pre_check(db:Session, queue_id: int, staff_user:Optional[int]= None):
    queue= db.query(QueueModel).filter(QueueModel.id== queue_id).one()
    with RedisLock(client_lock, "queue_manual_"+str(0 if queue.id is None else queue.id), blocking_timeout=180):
        freeze_user_queue = db.query(UserQueueModel).join(UserQueueModel.queue_weightage_action).filter(UserQueueModel.pre_check_status==UserQueuePreCheckStatusEnum.PENDING).filter(UserQueueModel.queue_id == queue_id, UserQueueModel.date == date.today()).filter(UserQueueModel.status == UserQueueStatusEnum.ARRIVED,UserQueueModel.queue_step_id.is_(None)).order_by(
                desc(QueueWeightageActionModel.weightage+(func.extract("epoch",
                    (func.now() - UserQueueModel.estimated_time))/60)),
                asc(UserQueueModel.created_at))
        freeze_user_queue=freeze_user_queue.first()
        logger.info(freeze_user_queue)
        if freeze_user_queue is not None:
            queue_step_id,location_id = add_user_queue_step(db,None,freeze_user_queue.id,"vial kit preparation started", "CHECKOUT",UserQueueStatusEnum.ARRIVED,queue, None,None,None,staff_user)
            db.query(UserQueueModel).filter(
                    UserQueueModel.id==freeze_user_queue.id
                ).update({
                    UserQueueModel.queue_step_id:queue_step_id,
                }, synchronize_session="fetch",
                )
        else:
            raise MutationError("No Patients Found")
        db.commit()
        return freeze_user_queue
    
def sync_loc_count(db:Session):
    try:
        update_stmt_1 = (
        update(LocationModel)
        .values(
            occupied_count=(
                db.query(func.count(UserQueueModel.id))
                .filter(
                    UserQueueModel.status == 'ENTRY',
                    UserQueueModel.location_id == LocationModel.id
                )
                .scalar_subquery()
            )
            )
            )
            # Execute the first update statement
        db.execute(update_stmt_1)
        update_stmt_2 = (
        update(LocationModel)
        .values(
            alloted_count=(
                db.query(func.count(UserQueueModel.id))
                .filter(
                    UserQueueModel.status == 'ENTRY',
                    UserQueueModel.next_location_id == LocationModel.id
                )
                .scalar_subquery()
            )
        )
    )
        # Execute the second update statement
        db.execute(update_stmt_2)
        subquery = db.query(
            LocationModel.parent_location_id.label("parent_location_id"),
            func.sum(LocationModel.occupied_count).label("occupied_count"),
            func.sum(LocationModel.alloted_count).label("alloted_count")
        ).filter(
            LocationModel.parent_location_id.is_not(None)
        ).group_by(
            LocationModel.parent_location_id
        ).subquery()

        # Update statement
        update_stmt = update(LocationModel).values(
            alloted_count=subquery.c.alloted_count,
            occupied_count=subquery.c.occupied_count
        ).where(LocationModel.id == subquery.c.parent_location_id)

        db.execute(update_stmt)
        db.commit()
        logger.info("end")
    except Exception as ex:
        logger.exception(ex)
