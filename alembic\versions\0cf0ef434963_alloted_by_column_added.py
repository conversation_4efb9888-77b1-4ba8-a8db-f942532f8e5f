"""alloted by column added

Revision ID: 0cf0ef434963
Revises: 57828fd13599
Create Date: 2024-04-01 05:42:11.810204

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0cf0ef434963'
down_revision = '57828fd13599'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('bed_request', sa.Column('alloted_by', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('bed_request', 'alloted_by')
    # ### end Alembic commands ###
