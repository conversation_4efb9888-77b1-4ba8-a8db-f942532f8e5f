"""user queue table added

Revision ID: 87cd37ebf916
Revises: 511131ba03cf
Create Date: 2023-10-11 08:05:09.722852

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '87cd37ebf916'
down_revision = '511131ba03cf'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('queue_weightage_action',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('code', sa.String(), nullable=True),
    sa.Column('weightage', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Primary<PERSON>eyConstraint('id')
    )
    op.create_table('user_queue',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('queue_id', sa.Integer(), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('token_no', sa.String(), nullable=True),
    sa.Column('date', sa.Date(), nullable=True),
    sa.Column('status', sa.Enum('CHECKIN', 'ENTRY', 'EXIT', 'HOLD', 'ARRIVED', 'PURGED', 'FREEZED', name='userqueuestatusenum'), nullable=False),
    sa.Column('weightage_id', sa.Integer(), nullable=True),
    sa.Column('start_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('end_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('updated_by', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['user.id'], name='user_queue_created_by_fk'),
    sa.ForeignKeyConstraint(['queue_id'], ['queue.id'], name='user_queue_queue_id_fk'),
    sa.ForeignKeyConstraint(['updated_by'], ['staff_user.id'], name='user_queue_updated_by_fk'),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], name='user_queue_user_id_fk'),
    sa.ForeignKeyConstraint(['weightage_id'], ['queue_weightage_action.id'], name='user_queue_weightage_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_queue_date'), 'user_queue', ['date'], unique=False)
    op.add_column('queue', sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('queue', sa.Column('show_patient_name', sa.Boolean(), nullable=True))
    op.add_column('staff_user', sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('staff_user', 'deleted_at')
    op.drop_column('queue', 'show_patient_name')
    op.drop_column('queue', 'deleted_at')
    op.drop_index(op.f('ix_user_queue_date'), table_name='user_queue')
    op.drop_table('user_queue')
    op.drop_table('queue_weightage_action')
    # ### end Alembic commands ###
