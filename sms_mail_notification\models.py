import enum
from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, En<PERSON>, <PERSON><PERSON><PERSON>, SmallInteger, Text, String, Time, UniqueConstraint, Integer, Numeric, Boolean, Date
from sqlalchemy.sql import func
import strawberry
from sqlalchemy.orm import relationship
from database.db_conf import Base
from user.models import UserType
from sqlalchemy.dialects.postgresql import JSONB


@strawberry.enum
class SMSTypeEnum(enum.Enum):
    SMS = "SMS"
    MAIL = "MAIL"
    NOTIFICATION = "NOTIFICATION"
    WHATSAPP = "WATSAPP"


@strawberry.enum
class EventCodeEnum(enum.Enum):
    REGISTER = "REGISTER"
    CONSULTATION = "CONSULTATION"
    LOGIN = "LOGIN"
    UMR_GENERATION = "UMR_GENERATION"
    QUEUE_NO_GENERATION = "QUEUE_NO_GENERATION"
    REPORT = "REPORT"
    REGISTER_AIG = "REGISTER_AIG"
    PATIENT_WELCOME = "PATIENT_WELCO<PERSON>",
    PATIENT_EXIT = "PATIENT_EXIT"
    PATIENT_QUEUE_CHECKOUT = "PATIENT_QUEUE_CHECKOUT"
    PATIENT_QUEUE_CHECKIN = "PATIENT_QUEUE_CHECKIN"
    PATIENT_QUEUE_GUIDANCE = "PATIENT_QUEUE_GUIDANCE"
    QUEUE_TOKEN_CREATION = "QUEUE_TOKEN_CREATION"
    TESTS_COMPLETION_REPORT_COLLECTION = "TESTS_COMPLETION_REPORT_COLLECTION"
    SERVICE_COMPLETED_CONFIRMATION = "SERVICE_COMPLETED_CONFIRMATION"
    SERVICE_LOCATION_DETAILS = "SERVICE_LOCATION_DETAILS"
    SERVICE_PAYMENT_ACKNOWLEDGEMENT_V2 = "SERVICE_PAYMENT_ACKNOWLEDGEMENT_V2"
    SERVICE_PAYMENT_ACKNOWLEDGEMENT_V1 = "SERVICE_PAYMENT_ACKNOWLEDGEMENT_V1"
    PATIENT_ARRIVAL_CONFIRMATION = "PATIENT_ARRIVAL_CONFIRMATION"
    PATIENT_QUEUE_CHECKIN_V1 = "PATIENT_QUEUE_CHECKIN_V1"
    NEXT_TOKEN_CALL = "NEXT_TOKEN_CALL"
    SERVING_TOKEN_CALL = "SERVING_TOKEN_CALL"


class SMSMailNotificationConfig(Base):
    __tablename__ = "sms_mail_notification_config"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String)
    code = Column(String)
    title = Column(String)
    sub = Column(String)
    type = Column(Enum(SMSTypeEnum), nullable=False)
    sender_email_id = Column(String, nullable=True)
    sender_password = Column(String)
    receiver_type = Column(BigInteger, ForeignKey(
        "user_type.id", name="sms_mail_notification_config_user_type_id_fk"), nullable=True)
    created_by = Column(String)
    data = Column(String)

    def __repr__(self) -> str:
        return "<SMSMailNotificationConfig %r>" % self.id
