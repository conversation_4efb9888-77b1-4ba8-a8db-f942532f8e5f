"""columns added in user visa data

Revision ID: 531a482e3536
Revises: 2f6a47f13906
Create Date: 2023-12-05 05:19:38.990865

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '531a482e3536'
down_revision = '2f6a47f13906'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    attendant_type_enum = postgresql.ENUM('ATTENDANT', 'DONOR', name='attendanttypeenum')
    attendant_type_enum.create(op.get_bind(), checkfirst=True)
    op.add_column('user_visa_attendant', sa.Column('type', attendant_type_enum, nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_visa_attendant', 'type')
    # ### end Alembic commands ###
