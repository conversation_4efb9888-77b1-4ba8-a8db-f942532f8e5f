"""queue steps tables added

Revision ID: 18742d2a6464
Revises: 85a2714a4013
Create Date: 2024-02-20 09:29:23.664205

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = '18742d2a6464'
down_revision = '85a2714a4013'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('queue_step',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('code', sa.String(), nullable=True),
    sa.Column('priority', sa.Integer(), nullable=True),
    sa.Column('avg_step_time', sa.Numeric(), nullable=True),
    sa.Column('queue_id', sa.Integer(), nullable=True),
    sa.Column('checkin_status', postgresql.ENUM('CHECKIN', 'ENTRY', 'EXIT', 'HOLD', 'ARRIVED', 'PURGED', 'FREEZED', name='userqueuestatusenum', create_type=False), nullable=True),
    sa.Column('checkout_status', postgresql.ENUM('CHECKIN', 'ENTRY', 'EXIT', 'HOLD', 'ARRIVED', 'PURGED', 'FREEZED', name='userqueuestatusenum',create_type=False), nullable=True),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['queue_id'], ['queue.id'], name='queue_step_queue_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_queue_step_queue_id'), 'queue_step', ['queue_id'], unique=False)
    op.create_table('user_queue_step',
    sa.Column('id', sa.BigInteger(), autoincrement=True, nullable=False),
    sa.Column('queue_step_id', sa.Integer(), nullable=True),
    sa.Column('user_queue_id', sa.Integer(), nullable=True),
    sa.Column('remarks', sa.Text(), nullable=True),
    sa.Column('status', sa.Enum('PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'HOLD', 'SKIPPED', name='userqueuestepstatusenum'), nullable=True),
    sa.Column('created_by', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_by', sa.String(), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['queue_step_id'], ['queue_step.id'], name='user_queue_step_queue_step_id_fk'),
    sa.ForeignKeyConstraint(['user_queue_id'], ['user_queue.id'], name='user_queue_step_user_queue_id_fk'),
    sa.PrimaryKeyConstraint('id')
    )
    queue_type_enum = postgresql.ENUM('MANUAL', 'AUTO', name='queuetypeenum')
    queue_type_enum.create(op.get_bind(), checkfirst=True)
    op.create_index(op.f('ix_user_queue_step_user_queue_id'), 'user_queue_step', ['user_queue_id'], unique=False)
    op.add_column('queue', sa.Column('queue_type', sa.Enum('MANUAL', 'AUTO', name='queuetypeenum'), nullable=True))
    op.add_column('user_queue', sa.Column('queue_step_id', sa.Integer(), nullable=True))
    op.create_foreign_key('user_queue_queue_step_id_fk', 'user_queue', 'queue_step', ['queue_step_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('user_queue_queue_step_id_fk', 'user_queue', type_='foreignkey')
    op.drop_column('user_queue', 'queue_step_id')
    op.drop_column('queue', 'queue_type')
    op.drop_index(op.f('ix_user_queue_step_user_queue_id'), table_name='user_queue_step')
    op.drop_table('user_queue_step')
    op.drop_index(op.f('ix_queue_step_queue_id'), table_name='queue_step')
    op.drop_table('queue_step')
    # ### end Alembic commands ###
