import json
import logging
from datetime import datetime, date
import os
from typing import List, Optional
from assessment_vital.resolvers import get_assessment_category, save_assessement_categories, save_assessment, save_vitals, save_visit_detail, vital_user_login, get_vital_accuracy_data
from bms.resolvers import record_patient
from graphql_types import MutationResponse, QueryResponse, UserAssessmentDetailInput, UserVitalInput
import strawberry
from exceptions.exceptions import MutationError
from user.models import RegistrationTypeEnum, StatusEnum
from util.globals import format_datetime
logger = logging.getLogger()
from assessment_vital.models import AssessmentCategory as AssessmentCategoryModel, AssessmentQuestion as AssessmentQuestionModel
# from user.schema import User
from datetime import datetime, timedelta
from jose import jws
from strawberry.scalars import JSON


@strawberry.type
class AccuracyData:
    vitalname:Optional[str]
    operation:Optional[str]
    value:Optional[str]

    @classmethod
    def from_instance(cls,vitalname:Optional[str],operation:Optional[str],value:Optional[str]):
        return cls(
            vitalname=vitalname,
            operation=operation,
            value=value
        )

@strawberry.type
class Condition:
    question_code: Optional[str]=None
    value: Optional[List[Optional[str]]] = None
    operation : Optional[JSON]
    
@strawberry.type
class AssessmentValidation:
    mandatory: Optional[str] 
    minValue: Optional[float]
    maxValue: Optional[float]
    normalHigh: Optional[float]
    normalLow: Optional[float]
    hide: Optional[str]

@strawberry.type
class AssessmentOption:
    text: Optional[str] 
    score: Optional[str]
    
@strawberry.type
class AssessmentQuestion:
    id: int
    name: Optional[str]
    code:str
    type:str
    validations: AssessmentValidation
    options:Optional[List[AssessmentOption]]
    results: Optional[List[JSON]]
    conditions : Optional[Condition]
    formula: Optional[str]
    formula_variables : Optional[List[str]]
    
    instance = strawberry.Private[AssessmentQuestionModel]
    @classmethod
    def from_instance(cls,instance: AssessmentQuestionModel):
        logger.info(instance.question_conditions)
        return cls(id=instance.id, 
                name=instance.name,
                code= instance.code,
                options= [AssessmentOption(text= obj.get('localName')[0].get("text"),score= obj.get('score')) for obj in instance.question_options] if instance.type != "masterLinked" else [AssessmentOption(text= obj,score="") for obj in instance.question_options],
                type = instance.type,
                validations= AssessmentValidation(
                    mandatory=instance.question_validations.get("mandatory"),
                    minValue=instance.question_validations.get("minValue"),
                    maxValue=instance.question_validations.get("maxValue"),
                    normalHigh=instance.question_validations.get("normalHigh"),
                    normalLow=instance.question_validations.get("normalLow"),
                    hide= instance.question_validations.get("hide"),
                ),
                conditions = Condition(operation=instance.question_conditions.get('operation'),question_code= instance.question_conditions.get('conditionalFieldCode'), value=[] if instance.question_conditions.get("conditionalFieldValue") is None else list(map(lambda x: x.get("value"), instance.question_conditions.get("conditionalFieldValue")))),
                formula=instance.formula,
                formula_variables=instance.formula_variables,
                results = instance.results
                )
        
@strawberry.type
class AssessmentCategory:
    id: int
    name: str
    code: Optional[str]
    code_1: str
    description: str
    multiple: Optional[bool]
    conditions : Optional[Condition]
    questions:List[AssessmentQuestion]
        
    instance = strawberry.Private[AssessmentCategoryModel]
    @classmethod
    def from_instance(cls,instance: AssessmentCategoryModel):
        return cls(id=instance.id, 
                name=instance.name, 
                code= instance.domain_code,
                code_1 = instance.code,
                multiple = False if instance.multiple is None else instance.multiple,
                description= instance.description,
                conditions = Condition(operation=instance.category_conditions.get('operation'),question_code= instance.category_conditions.get('conditionalFieldCode'), value=[] if instance.category_conditions.get("conditionalFieldValue") is None else list(map(lambda x: x.get("value"), instance.category_conditions.get("conditionalFieldValue")))),
                questions= [AssessmentQuestion.from_instance(obj) for obj in instance.assessment_questions.filter(AssessmentQuestionModel.status==StatusEnum.ACTIVE).all()],
                )

@strawberry.type
class VisitDetail:
    encounterId:str
    practitionerName:str
    specialityName:str
    visitDate: str


@strawberry.type
class UserVitalDetail:
    patient_name: str
    patient_id:str
    gender: Optional[str]
    age: Optional[str]
    dob: Optional[str]
    access_token: str
    visit_detail: List[VisitDetail]
    
    
    
    

@strawberry.type
class Query:
    @strawberry.field
    def get_assessment_category(self, info) -> QueryResponse[List[AssessmentCategory]]:
        try:
            msg="Success"
            db = info.context["db"]
            data = get_assessment_category(db)
            if len(data)==0:
                msg="No Records Found"
            return QueryResponse.from_status_flag(True, msg, [AssessmentCategory.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    

    @strawberry.field
    def get_vital_accuracy(self,info)-> QueryResponse[List[AccuracyData]]:
        try:
            db=info.context["db"]
            device_id = info.context["device_id"]
            accuracy_data=get_vital_accuracy_data(db,device_id)
            return QueryResponse.from_status_flag(True, "Data Fetched successfully", [AccuracyData.from_instance(data.get("vitalname"),data.get("operation"),data.get("value")) for data in accuracy_data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

@strawberry.type
class Mutation:

    @strawberry.mutation
    def save_assessment(self, info,assessment_list:List[UserAssessmentDetailInput],uhid:str,encounterId:str) -> MutationResponse[None]:
        try:
            db = info.context["db"]
            user_id = info.context["auth_user"].id
            device_detail=info.context["device_detail"]
            save_assessment(db,assessment_list,user_id,device_detail,uhid,encounterId)
            return MutationResponse.from_status_flag(True, "assessment saved successfully",  None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.mutation
    def save_vital(self, info,vital_input:List[UserVitalInput]) -> MutationResponse[None]:
        try:
            db = info.context["db"]
            user_id = info.context["auth_user"].id
            save_vitals(db,vital_input,user_id)
            return MutationResponse.from_status_flag(True, "vital saved successfully",  None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.mutation
    def vital_login(self, info,uhid: str) -> MutationResponse[UserVitalDetail]:
        try:
            db = info.context["db"]
            user_id,detail = record_patient(db,uhid,RegistrationTypeEnum.VITAL)
            visit_detail,pat_details = save_visit_detail(uhid)
            if len(visit_detail) == 0:
                return MutationResponse.from_status_flag(False, "No visit details found", None)
            # filtering current day visit details commented, will use if it requires
            # visit_detail_list = [VisitDetail(encounterId=detail["encounterId"],practitionerName=detail["practitionerName"],specialityName=detail["specialityName"],visitDate=detail["visitDate"]) for detail in visit_detail if (datetime.strptime(detail["visitDate"], "%d/%m/%Y %I:%M %p")).date == date.today()]
            visit_detail_list = [VisitDetail(encounterId=detail["encounterId"],practitionerName=detail["practitionerName"],specialityName=detail["specialityName"],visitDate=detail["visitDate"]) for detail in visit_detail]
            dt = datetime.now() + \
                timedelta(minutes=float(
                    os.environ["ACCESS_EXPIRE_MINUTES"]))
            access_token = jws.sign({"sub": {"user_id": user_id ,"user_sub_type": "VITAL"}, "user_type": "PATIENT", "exp": dt.isoformat(
                ), "device_id": info.context["device_id"]}, os.environ["SECRET_KEY"], algorithm=os.environ["ALGORITHM"])
            vital_user_login(db, patient_name=detail["patientName"], uhid=uhid)
            return MutationResponse.from_status_flag(True, "login successful", UserVitalDetail(access_token=access_token, patient_name=detail["patientName"],patient_id=detail["patientID"],gender=detail["gender"],age=detail["age"],dob=pat_details["dateofBirth"],visit_detail=visit_detail_list))
        
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
     
    @strawberry.mutation
    def save_assessement_categories(self, info) -> MutationResponse[None]:
        try:
            db = info.context["db"]
            save_assessement_categories(db)
            return MutationResponse.from_status_flag(True, "vital saved successfully",  None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
        