import json
import logging
from datetime import datetime, date
import os
from typing import List, Optional
from assessment_vital.resolvers import get_assessment_category, save_assessement_categories, save_assessment, save_vitals, save_visit_detail, vital_user_login, get_vital_accuracy_data, get_prism_visit_details, get_vital_type, get_prism_visit_details
from bms.resolvers import record_patient
from graphql_types import MutationResponse, QueryResponse, UserAssessmentDetailInput, UserVitalInput, AssessmentPatientData
import strawberry
from exceptions.exceptions import MutationError
from user.models import RegistrationTypeEnum, StatusEnum
from util.globals import format_datetime
from dateutil.parser import isoparse
logger = logging.getLogger()
from assessment_vital.models import AssessmentCategory as AssessmentCategoryModel, AssessmentQuestion as AssessmentQuestionModel
# from user.schema import User
from datetime import datetime, timedelta
from jose import jws
from strawberry.scalars import JSON


@strawberry.type
class AccuracyData:
    vitalname:Optional[str]
    operation:Optional[str]
    value:Optional[str]

    @classmethod
    def from_instance(cls,vitalname:Optional[str],operation:Optional[str],value:Optional[str]):
        return cls(
            vitalname=vitalname,
            operation=operation,
            value=value
        )

@strawberry.type
class Condition:
    question_code: Optional[str]=None
    value: Optional[List[Optional[str]]] = None
    operation : Optional[JSON]
    
@strawberry.type
class AssessmentValidation:
    mandatory: Optional[str] 
    minValue: Optional[float]
    maxValue: Optional[float]
    normalHigh: Optional[float]
    normalLow: Optional[float]
    hide: Optional[str]

@strawberry.type
class AssessmentOption:
    text: Optional[str] 
    score: Optional[str]
    
@strawberry.type
class AssessmentQuestion:
    id: int
    name: Optional[str]
    code:str
    type:str
    validations: AssessmentValidation
    options:Optional[List[AssessmentOption]]
    results: Optional[List[JSON]]
    conditions : Optional[Condition]
    formula: Optional[str]
    formula_variables : Optional[List[str]]
    
    instance = strawberry.Private[AssessmentQuestionModel]
    @classmethod
    def from_instance(cls,instance: AssessmentQuestionModel):
        logger.info(instance.question_conditions)
        return cls(id=instance.id, 
                name=instance.name,
                code= instance.code,
                options= [AssessmentOption(text= obj.get('localName')[0].get("text"),score= obj.get('score')) for obj in instance.question_options] if instance.type != "masterLinked" else [AssessmentOption(text= obj,score="") for obj in instance.question_options],
                type = instance.type,
                validations= AssessmentValidation(
                    mandatory=instance.question_validations.get("mandatory"),
                    minValue=instance.question_validations.get("minValue"),
                    maxValue=instance.question_validations.get("maxValue"),
                    normalHigh=instance.question_validations.get("normalHigh"),
                    normalLow=instance.question_validations.get("normalLow"),
                    hide= instance.question_validations.get("hide"),
                ),
                conditions = Condition(operation=instance.question_conditions.get('operation'),question_code= instance.question_conditions.get('conditionalFieldCode'), value=[] if instance.question_conditions.get("conditionalFieldValue") is None else list(map(lambda x: x.get("value"), instance.question_conditions.get("conditionalFieldValue")))),
                formula=instance.formula,
                formula_variables=instance.formula_variables,
                results = instance.results
                )
        
@strawberry.type
class AssessmentCategory:
    id: int
    name: str
    code: Optional[str]
    code_1: str
    description: str
    multiple: Optional[bool]
    conditions : Optional[Condition]
    questions:List[AssessmentQuestion]
        
    instance = strawberry.Private[AssessmentCategoryModel]
    @classmethod
    def from_instance(cls,instance: AssessmentCategoryModel):
        return cls(id=instance.id, 
                name=instance.name, 
                code= instance.domain_code,
                code_1 = instance.code,
                multiple = False if instance.multiple is None else instance.multiple,
                description= instance.description,
                conditions = Condition(operation=instance.category_conditions.get('operation'),question_code= instance.category_conditions.get('conditionalFieldCode'), value=[] if instance.category_conditions.get("conditionalFieldValue") is None else list(map(lambda x: x.get("value"), instance.category_conditions.get("conditionalFieldValue")))),
                questions= [AssessmentQuestion.from_instance(obj) for obj in instance.assessment_questions.filter(AssessmentQuestionModel.status==StatusEnum.ACTIVE).all()],
                )

@strawberry.type
class VisitDetail:
    encounter_id:str
    practitioner_name:str
    speciality_name:str
    visit_date: str
    doctor_id:Optional[str]=None


@strawberry.type
class UserVitalDetail:
    patient_name: str
    patient_id:str
    gender: Optional[str]
    age: Optional[str]
    dob: Optional[str]
    access_token: str
    visit_detail: List[VisitDetail]
    
    
    
    

@strawberry.type
class Query:
    @strawberry.field
    def get_assessment_category(self, info) -> QueryResponse[List[AssessmentCategory]]:
        try:
            msg="Success"
            db = info.context["db"]
            device_id = info.context["device_id"]
            data = get_assessment_category(db,device_id)
            if len(data)==0:
                msg="No Records Found"
            return QueryResponse.from_status_flag(True, msg, [AssessmentCategory.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    

    @strawberry.field
    def get_vital_accuracy(self,info)-> QueryResponse[List[AccuracyData]]:
        try:
            db=info.context["db"]
            device_id = info.context["device_id"]
            accuracy_data=get_vital_accuracy_data(db,device_id)
            return QueryResponse.from_status_flag(True, "Data Fetched successfully", [AccuracyData.from_instance(data.get("vitalname"),data.get("operation"),data.get("value")) for data in accuracy_data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

@strawberry.type
class Mutation:

    @strawberry.mutation
    def save_assessment(self, info,assessment_list:List[UserAssessmentDetailInput],patient_data:AssessmentPatientData) -> MutationResponse[None]:
        try:
            db = info.context["db"]
            user_id = info.context["auth_user"].id
            device_detail=info.context["device_detail"]
            save_assessment(db,assessment_list,user_id,device_detail,patient_data)
            return MutationResponse.from_status_flag(True, "assessment saved successfully",  None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.mutation
    def save_vital(self, info,vital_input:List[UserVitalInput]) -> MutationResponse[None]:
        try:
            db = info.context["db"]
            user_id = info.context["auth_user"].id
            save_vitals(db,vital_input,user_id)
            return MutationResponse.from_status_flag(True, "vital saved successfully",  None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.mutation
    def vital_login(self, info,uhid: str) -> MutationResponse[UserVitalDetail]:
        try:
            db = info.context["db"]
            device_detail = info.context["device_detail"]
            uhid = uhid.upper()
            user_id,detail = record_patient(db,uhid,RegistrationTypeEnum.VITAL)
            vital_type = get_vital_type(db,device_detail)
            if vital_type == "PRISM":
                visit_detail = get_prism_visit_details(db,uhid,device_detail)
            else:
                visit_detail,pat_details = save_visit_detail(uhid)
            if len(visit_detail) == 0:
                return MutationResponse.from_status_flag(False, "No visit details found", None)
            dt = datetime.now() + timedelta(minutes=float(os.environ["ACCESS_EXPIRE_MINUTES"]))
            access_token = jws.sign({"sub": {"user_id": user_id ,"user_sub_type": "VITAL"}, "user_type": "PATIENT", "exp": dt.isoformat(), "device_id": info.context["device_id"]}, os.environ["SECRET_KEY"], algorithm=os.environ["ALGORITHM"])
            if vital_type == "PRISM":
                visit_detail_list = [VisitDetail(encounter_id=detail.get("visitId"),practitioner_name=detail.get("doctorName"),speciality_name="",visit_date=detail.get("visitDate"),doctor_id=detail.get("doctorId")) for detail in visit_detail]
                visit_detail_list.sort(key=lambda x: isoparse(x.visit_date), reverse=True)
                vital_user_login(db, patient_name=visit_detail[0].get("patientName"), uhid=uhid, encounter_id= visit_detail_list[0].encounter_id, device_code=device_detail.device_code)
                return MutationResponse.from_status_flag(True, "login successful", UserVitalDetail(access_token=access_token, patient_name=visit_detail[0].get("patientName"),patient_id=uhid,gender=visit_detail[0].get("patientGender"),age=visit_detail[0].get("patientAge"),dob=datetime.strptime(visit_detail[0].get("dateOfBirth"), '%d-%m-%Y').strftime('%d/%m/%Y'),visit_detail=visit_detail_list))
            else:
                visit_detail_list = [VisitDetail(encounter_id=detail.get("encounterId"),practitioner_name=detail.get("practitionerName"),speciality_name=detail.get("specialityName",""),visit_date=detail.get("visitDate"),doctor_id=detail.get("doctorId")) for detail in visit_detail]
                visit_detail_list.sort(key=lambda x: datetime.strptime(x.visit_date, "%d/%m/%Y %I:%M %p"), reverse=True)
                vital_user_login(db, patient_name=detail["patientName"], uhid=uhid, encounter_id= visit_detail_list[0].encounter_id, device_code=device_detail.device_code)
                return MutationResponse.from_status_flag(True, "login successful", UserVitalDetail(access_token=access_token, patient_name=detail["patientName"],patient_id=detail["patientID"],gender=detail["gender"],age=detail["age"],dob=pat_details.get("dateofBirth"),visit_detail=visit_detail_list))
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
     
    @strawberry.mutation
    def save_assessement_categories(self, info) -> MutationResponse[None]:
        try:
            db = info.context["db"]
            save_assessement_categories(db)
            return MutationResponse.from_status_flag(True, "vital saved successfully",  None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
        