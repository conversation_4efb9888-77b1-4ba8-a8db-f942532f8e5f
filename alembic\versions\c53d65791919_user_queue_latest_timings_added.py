"""user queue latest timings added

Revision ID: c53d65791919
Revises: 515f7bdb65ed
Create Date: 2024-03-12 12:53:52.646619

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c53d65791919'
down_revision = '515f7bdb65ed'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('location', sa.Column('alloted_count', sa.Integer(), nullable=True))
    op.add_column('location', sa.Column('occupied_count', sa.Integer(), nullable=True))
    op.add_column('user_queue', sa.Column('step_start_time', sa.DateTime(timezone=True), nullable=True))
    op.add_column('user_queue', sa.Column('next_step_start_time', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_queue', 'next_step_start_time')
    op.drop_column('user_queue', 'step_start_time')
    op.drop_column('location', 'occupied_count')
    op.drop_column('location', 'alloted_count')
    # ### end Alembic commands ###
