import enum
from sqlalchemy import JSON, BigInteger, Column, DateTime, Enum, ForeignKey, SmallInteger, Text, String, Time, UniqueConstraint, Integer, Numeric, Boolean,Date, Float
from sqlalchemy.sql import func
import strawberry
from sqlalchemy.orm import relationship
from database.db_conf import Base

class Config(Base):
    __tablename__="config"
    id = Column(Integer, primary_key=True, autoincrement=True)
    hospital_code=Column(String,nullable=False)
    data =Column(Text)
    created_at=Column(DateTime(timezone=True),server_default=func.now())
    updated_at= Column(DateTime(timezone=True),onupdate=func.now())
    data1 = Column(JSON)
  