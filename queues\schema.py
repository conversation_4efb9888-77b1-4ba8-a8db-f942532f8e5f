import asyncio
from datetime import datetime, timedelta
import itertools
import os
from jose import jws
from typing import AsyncGenerator, List, Optional
from database.db_conf import SessionLocal
from queues.models import (
    Location as LocationModel, Queue as QueueModel,QueueStep as QueueStepModel, Cluster as ClusterModel, QueueCounterStatusEnum, RelUserServiceQueue as RelUserServiceQueueModel, UserQueue as UserQueueModel, UserQueue as UserQueueModel, 
    QueueWeightageAction as QueueWeightageActionModel, QueueCounter as QueueCounterModel, UserQueueLogs as UserQueueLogsModel, UserQueuePreCheckStatusEnum, UserQueueStatusEnum, UserQueueStep as UserQueueStepModel, UserService as UserServiceModel,
    Tag as TagModel
)
from service.models import  Service as ServiceModel
from queues.resolvers import (
    add_to_queue, add_user_queue, call_next, call_next_pre_check, exit_user_queue, get_available_tags, get_counter_details,get_locations, get_device_queue, get_queue_counters, get_queue_step_locations, get_queue_step_logs, get_queue_steps, get_service_categories, get_services, get_queues, add_or_update_queue, delete_queue, get_user_queue_logs, get_user_services, get_user_queue,
    get_clusters, staff_station_device_login, update_counter, update_pre_check_status, update_rel_device_queue, update_user_queue, activate_or_deactivate_queue, get_queue_weightage, get_current_user_queue, 
    get_staff_user_allocated_queues, get_queue_count, update_user_queue_manual, update_user_queue_service, update_user_queue_step, get_average_time, add_or_update_tag,get_all_tags,get_procedure_rooms,get_censes,get_overall_average_time,get_tag_or_scan_id,checkout_user, pause_unpause_user
)
from staff_user.schema import StaffUser
import strawberry
from graphql_types import MutationResponse, OperationTypes, QRDetail, QueryResponse, QueueInput, TagInput
from exceptions.exceptions import MutationError
from user.schema import User, UserType
import logging
from sqlalchemy.orm.session import make_transient
logger = logging.getLogger()
from util.globals import format_datetime
from collections import Counter
@strawberry.type
class Cluster:
    id: int
    tower: str
    floor: str
    cluster: str

    instance = strawberry.Private[ClusterModel]

    @classmethod
    def from_instance(cls, instance: ClusterModel):
        return cls(
            id=instance.id,
            tower=instance.tower,
            floor=instance.floor,
            cluster=instance.cluster
        )

@strawberry.type
class QueueStaffUserDetails:
    id: int
    name: str
    emp_id : str
    
    @classmethod
    def from_instance(cls, id: int, name: str, emp_id: str):
        return cls(id = id, name = name, emp_id = emp_id)

@strawberry.type
class Services:
    id : int
    name : str
    code : str
    type : str
    categories: Optional[List[str]]=None
    
    instance = strawberry.Private[ServiceModel]
    @classmethod
    def from_instance(cls, instance: ServiceModel):
        return cls(id=instance.id, name=instance.name, code=instance.code, type=instance.type,categories=instance.prerequisites_conditions)

@strawberry.type
class Counters:
    id: int
    number: int
    code:str
    status: str

    instance = strawberry.Private[QueueCounterModel]
    @classmethod
    def from_instance(cls,instance: QueueCounterModel):
        return cls(id=instance.id, 
                code=instance.counter_name, 
                status=instance.counter_status.name, 
                number= instance.counter)
@strawberry.type
class Queue:
    id: int
    queue_name: str
    queue_code: str
    updated_at: str
    status: str
    queue_type: Optional[str]
    service_type: Optional[str]
    allocate_counter_at: Optional[str]
    cluster: Cluster
    avg_procedure_time: float
    upcoming_patients: int
    tests: List[Services]
    show_patient_name: Optional[bool]
    updated_by: Optional[StaffUser]
    supervisor: Optional[str] = None
    staff_user: Optional[List[QueueStaffUserDetails]]
    capacity: Optional[int]
    waiting_capacity: Optional[int] = None
    counters: Optional[List[Counters]] = None
    pre_requesite_conditions: Optional[List[str]]= None
    instance = strawberry.Private[QueueModel]

    @classmethod
    def from_instance(cls, instance: QueueModel, supervisor: str):
        logger.info(instance)
        return cls(
            id=instance.id,
            queue_name=instance.queue_name,
            queue_code=instance.queue_code,
            service_type = instance.service_type,
            queue_type = None if instance.queue_type is None else instance.queue_type.name,
            allocate_counter_at = None if instance.allocate_counter_at is None else instance.allocate_counter_at.name,
            avg_procedure_time=instance.avg_procedure_time,
            status=instance.status.name,
            upcoming_patients=instance.upcoming_patients,
            cluster=Cluster.from_instance(instance.cluster),
            updated_by=StaffUser.from_instance(instance.updated_by_obj) if instance.updated_by_obj is not None else (
                StaffUser.from_instance(instance.created_by_obj) if instance.created_by_obj is not None else None),
            updated_at=format_datetime(instance.updated_at) if instance.updated_at is not None else format_datetime(instance.created_at),
            supervisor=supervisor,
            show_patient_name=instance.show_patient_name,
            staff_user = [QueueStaffUserDetails.from_instance(user.id, user.name,user.emp_id) for user in instance.staff_users] if len(instance.staff_users) > 0 else [],
            capacity = instance.capacity,
            tests= [Services.from_instance(service) for service in instance.services] if len(instance.services) > 0 else [],
            waiting_capacity = instance.waiting_capacity,
            counters = [Counters.from_instance(obj) for obj in instance.counters],
            pre_requesite_conditions = None if len(instance.services)>0 else list(set(list(itertools.chain.from_iterable([arr.prerequisites_conditions for arr in instance.services if arr]))))
        )

@strawberry.type
class QueueDetails:
    id: int
    queue_name: str
    queue_code: str
    status: str
    avg_procedure_time: float
    upcoming_patients: int
    show_patient_name: Optional[bool]
    capacity: Optional[int]
    waiting_capacity: Optional[int] = None
    instance = strawberry.Private[QueueModel]

    @classmethod
    def from_instance(cls, instance: QueueModel, supervisor: str):
        return cls(
            id=instance.id,
            queue_name=instance.queue_name,
            queue_code=instance.queue_code,
            avg_procedure_time=instance.avg_procedure_time,
            status=instance.status.name,
            upcoming_patients=instance.upcoming_patients,
            show_patient_name=instance.show_patient_name,
            capacity = instance.capacity,
            waiting_capacity = instance.waiting_capacity,
        )


@strawberry.type
class UserQueue:
    id: int
    queue: QueueDetails
    user: User
    status: str
    token_no: str
    weightage: str
    weightage_id:int
    prerequisites_conditions: Optional[List[str]]= None
    counter_name : Optional[str] = None
    start_time: Optional[str] = None
    token_id:int
    queue_step_id: Optional[str] = None

    instance = strawberry.Private[UserQueueModel]

    @classmethod
    def from_instance(cls, instance: UserQueueModel):
        return cls(
            id=instance.id,
            queue=QueueDetails.from_instance(instance.queue, ", ".join(
                staff_user.name for staff_user in instance.queue.staff_users)),
            user=User.from_instance(instance.user),
            status=instance.status.name,
            token_no=instance.token_no.upper(),
            weightage=instance.queue_weightage_action.name,
            counter_name = instance.counter_obj.counter_name if instance.counter is not None else None,
            start_time = format_datetime(instance.start_time),
            weightage_id = instance.weightage_id,
            token_id=instance.token_id,
            prerequisites_conditions= instance.prerequisites_conditions,
            queue_step_id= instance.queue_step_id
        )


@strawberry.type
class QueueAudit:
    id: int
    queue: str
    queue_code: str
    updated_at: str
    status: str
    avg_procedure_time: float
    upcoming_patients: int
    cluster: Cluster
    updated_by: Optional[StaffUser]
    instance = strawberry.Private[QueueModel]

    @classmethod
    def from_instance(cls, instance: QueueModel):
        return cls(
            id=instance.id,
            queue_name=instance.queue_name,
            queue_code=instance.queue_code,
            avg_procedure_time=instance.avg_procedure_time,
            status=instance.status.name,
            upcoming_patients=instance.upcoming_patients,
            cluster=Cluster.from_instance(instance.cluster),
            updated_by=StaffUser.from_instance(instance.updated_by_obj) if instance.updated_by_obj is not None else (
                StaffUser.from_instance(instance.created_by_obj) if instance.created_by_obj is not None else None),
            updated_at=format_datetime(instance.updated_at) if instance.updated_at is not None else format_datetime(instance.created_at)
        )


@strawberry.type
class QueueWeightage:
    id: int
    name: str
    code: str
    weightage: int

    instance = strawberry.Private[QueueWeightageActionModel]

    @classmethod
    def from_instance(cls, instance: QueueWeightageActionModel):
        return cls(
            id=instance.id,
            name=instance.name,
            code=instance.code,
            weightage=instance.weightage
        )


@strawberry.type
class QueueCount:
    queue_name: str
    capacity: int
    status: str
    weightage: str
    cluster: str
    created_at: str 
    estimated_time: str
    id: int
    token: str
    patient_name: str
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    force_exit: Optional[str] = None
    arrived_at: Optional[str] = None
    waiting_capacity: Optional[int] = None
    phone_number: Optional[str] = None
    instance = strawberry.Private[UserQueueModel]
    @classmethod
    def from_instance(cls, instance: UserQueueModel):
        return cls(
            queue_name = instance.queue.queue_name,
            capacity = instance.queue.capacity,
            status = instance.status.name,
            weightage = instance.queue_weightage_action.code,
            cluster = instance.queue.cluster.cluster,
            created_at = format_datetime(instance.created_at),
            estimated_time = format_datetime(instance.estimated_time),
            start_time = format_datetime(instance.start_time),
            end_time = format_datetime(instance.end_time),
            force_exit = instance.force_exit,
            arrived_at = format_datetime(instance.arrived_at),
            id = instance.id,
            waiting_capacity = instance.queue.waiting_capacity,
            token=instance.token_no.upper(),
            patient_name= instance.user.name,
            phone_number= instance.user.phone_number
        )

@strawberry.type
class CounterUserService:
    user_service_id:int
    service_name: str
    instance = strawberry.Private[UserServiceModel]
    @classmethod
    def from_instance(cls, instance: UserServiceModel):
        return cls( user_service_id = instance.id, service_name = instance.service.name)

@strawberry.type
class CounterUserQueue:
    user_queue_id:int
    token_no: str
    user_queue_status: str
    user: Optional[User] = None
    user_service: Optional[List[CounterUserService]] = None
    token_id: str

    instance = strawberry.Private[UserQueueModel]
    @classmethod
    def from_instance(cls, instance: UserQueueModel,services):
        return cls(user_queue_id = instance.id,
                token_no = instance.token_no,
                    user_queue_status=instance.status.name,
                   user= User.from_instance(instance.user) if instance.status == UserQueueStatusEnum.ENTRY else None,
                   user_service= [CounterUserService.from_instance(obj) for obj in services] if instance.status == UserQueueStatusEnum.ENTRY and len(services) > 0 else None,
                   token_id = instance.token_id,
                )
@strawberry.type
class CounterDetails:
    counter_id: int
    counter_name: str
    counter_status: str
    queue_name: str
    queue_id: int
    user_queue: Optional[List[CounterUserQueue]] = None
    instance = strawberry.Private[QueueCounterModel]
    @classmethod
    def from_instance(cls,instance: QueueCounterModel,user_queues):
        return cls(
            counter_id = instance.id, 
            counter_name= instance.counter_name,
            counter_status=instance.counter_status.name,
            queue_name = instance.queue.queue_name,
            user_queue=[CounterUserQueue.from_instance(user_queue,services) for user_queue,services in user_queues.items()] if user_queues is not None and len(user_queues)>0 else [],
            queue_id = instance.queue.id,
        )


@strawberry.type
class LocationBed:
    id: int
    name: str
    code: str
    status: str
    
    instance = strawberry.Private[LocationModel]

    @classmethod
    def from_instance(cls,instance: LocationModel):
        return cls(id=instance.id, 
                name=instance.name,
                code = instance.code,
                status = instance.status.name
            )
@strawberry.type
class Location:
    id: int
    name: str
    code: str
    beds : Optional[List[LocationBed]] = None
    available_count:Optional[int] = None
    occupied_count:Optional[int] = None
    alloted_count:Optional[int] = None
    total_count: Optional[int] = None
    instance = strawberry.Private[LocationModel]

    @classmethod
    def from_instance(cls,instance: LocationModel):
        status_counts = Counter([bed.status.name for bed in instance.child_locations])        
        return cls(id=instance.id, 
                name=instance.name,
                code = instance.code,
                beds=[LocationBed.from_instance(child_location) for child_location in instance.child_locations] if instance.child_locations is not None else [],
                available_count=status_counts['AVAILABLE'],
                occupied_count=instance.occupied_count,
                alloted_count=instance.alloted_count,
                total_count = instance.total_count
            )

@strawberry.type
class AllLocations:
    id: int
    name: str
    code: str
    available_count:Optional[int] = None
    occupied_count:Optional[int] = None
    alloted_count:Optional[int] = None
    total_count: Optional[int] = None
    instance = strawberry.Private[LocationModel]

    @classmethod
    def from_instance(cls,instance: LocationModel):
        status_counts = Counter([bed.status.name for bed in instance.child_locations])        
        return cls(id=instance.id, 
                name=instance.name,
                code = instance.code,
                available_count=status_counts['AVAILABLE'],
                occupied_count=instance.occupied_count,
                alloted_count=instance.alloted_count,
                total_count = instance.total_count
            )
@strawberry.type
class QueueStepLocation:
    id: int
    name: str
    code: str
    locations : Optional[List[Location]]
    
    instance = strawberry.Private[QueueStepModel]

    @classmethod
    def from_instance(cls,instance: QueueStepModel):
        return cls(id=instance.id, 
                name=instance.name,
                code = instance.code,
                locations = [Location.from_instance(obj) for obj in instance.locations],

            )
    

@strawberry.type
class ProcedureRoomsList:
    code: str
    status:Optional[str] = None
    occupied_count:Optional[int] = None
    alloted_count:Optional[int] = None
    total_count: Optional[int] = None
    avg_time_mins: Optional[str] = None

@strawberry.type
class GetCensusData:
    total_count: Optional[int] = None
    completed_count:Optional[int] = None
    waiting_count:Optional[int] = None
    inprogress_count:Optional[int] = None

@strawberry.type
class EndoscopyAvgTime:
    queue_step_id: int
    count: int
    code: str
    avg_duration_per_user: Optional[str] = "None"

@strawberry.type
class OverallAvgTime:
    avg_procedure_per_hour: Optional[int] = None
    es_avg_procedure_per_hour: Optional[int] = None
    cs_avg_procedure_per_hour: Optional[int] = None
    es_last_hour_avg_procedure_per_hour: Optional[int] = None
    cs_last_hour_avg_procedure_per_hour: Optional[int] = None
    endoscopy_count: Optional[int] = None
    colonoscopy_count: Optional[int] = None
    avg_procedure_from_start_of_day: Optional[int] = None
    procedure_suit_occupancy_rate: Optional[int] = None
    last_prev_hour_avg_procedure_per_hour: Optional[int] = None


@strawberry.type
class Tag:
    id: int
    name: str
    code: str
    status: str
    created_at: str
    rfid_code: str

    instance = strawberry.Private[TagModel]
    @classmethod
    def from_instance(cls,instance: TagModel):
        return cls(
                id=instance.id, 
                name=instance.name,
                code=instance.code,
                status=instance.status.name if instance.status else None,
                created_at=instance.created_at,
                rfid_code=instance.rfid_code
                )

@strawberry.type
class userQueueStep:
    id: int
    step_name: Optional[str] = None
    created_at: Optional[str]
    completed_at: Optional[str]
    status: Optional[str]
    action_type: Optional[str]
    next_location : Optional[Location]
    location: Optional[str]=None
    description : Optional[str]=None
    remarks: Optional[str]= None
    tag : Optional[Tag] = None
    queue_name : Optional[str] = None
    source : Optional[str] = None
    
    instance = strawberry.Private[UserQueueStepModel]

    @classmethod
    def from_instance(cls,instance: UserQueueStepModel):
        return cls(id=instance.id, 
                step_name=  None if instance.queue_step is None else instance.queue_step.name,
                created_at= instance.created_at,
                completed_at= instance.updated_at,
                status = instance.status.name,
                action_type = instance.action_type,
                next_location = None if instance.location is None else Location.from_instance(instance.location),
                location=instance.created_location,
                description=instance.description,
                remarks=instance.remarks,
                tag = Tag.from_instance(instance.tag) if instance.tag is not None else None,
                queue_name = None if instance.queue_step is None else instance.queue_step.queue.queue_name,
                source = instance.created_by
            )
@strawberry.type
class userQueueLog:
    id: int
    created_at: Optional[str]
    status: Optional[str]
    queue_name : Optional[str] = None
    weightage : Optional[QueueWeightage] = None
    counter_name: Optional[str]= None           
    source: Optional[str] = None 
    instance = strawberry.Private[UserQueueLogsModel]

    @classmethod
    def from_instance(cls,instance: UserQueueLogsModel):
        return cls(id=instance.id, 
                created_at= instance.created_at,
                status = "Vial kit prepared" if instance.pre_check_status==UserQueuePreCheckStatusEnum.COMPLETED and instance.status==UserQueueStatusEnum.ARRIVED  else instance.status.name,
                weightage = QueueWeightage.from_instance(instance.queue_weightage_action) if instance.queue_weightage_action is not None else None,
                queue_name = instance.user_queue.queue.queue_name,
                counter_name= None if instance.queue_counter is None else instance.queue_counter.counter_name,
                source = instance.created_by_obj.name if instance.created_by_obj is not None else 'System'
            )

@strawberry.type
class UserService1:
    id: int
    name: Optional[str]
    status: str
    categories: Optional[List[str]]=None
        
    instance = strawberry.Private[RelUserServiceQueueModel]

    @classmethod
    def from_instance(cls,instance: RelUserServiceQueueModel):
        return cls(id=instance.user_service_id, 
                name= instance.user_service.service.name,
                status= instance.status.name,
                categories = instance.user_service.service.prerequisites_conditions,
            )

@strawberry.type
class Query:
    @strawberry.field
    def get_queues(self, info) -> QueryResponse[List[Queue]]:
        try:
            db = info.context["db"]
            data = get_queues(db)
            return QueryResponse.from_status_flag(True, "Success", [Queue.from_instance(obj, ", ".join(staff_user.name for staff_user in obj.staff_users)) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

    @strawberry.field
    def get_queues_audit(self, info) -> QueryResponse[List[Queue]]:
        try:
            db = info.context["db"]
            data = get_queues(db)
            return QueryResponse.from_status_flag(True, "Success", [Queue.from_instance(obj, ", ".join(staff_user.name for staff_user in obj.staff_users)) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

    @strawberry.field
    def get_user_queue(self, info, queue_id: Optional[int]= None, pre_check_status:Optional[str]= None) -> QueryResponse[List[UserQueue]]:
        try:
            db = info.context["db"]
            data = get_user_queue(db, queue_id,pre_check_status)
            # logger.info(data)
            return QueryResponse.from_status_flag(True, "Success", [UserQueue.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

    @strawberry.field
    def get_current_user_queue(self, info, queue_id: int) -> QueryResponse[List[UserQueue]]:
        try:
            db = info.context["db"]
            data = get_current_user_queue(db, queue_id)
            return QueryResponse.from_status_flag(True, "Success", [UserQueue.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

    @strawberry.field
    def get_clusters(self, info) -> QueryResponse[List[Cluster]]:
        try:
            db = info.context["db"]
            data = get_clusters(db)
            return QueryResponse.from_status_flag(True, "Success", [Cluster.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

    @strawberry.field
    def get_queue_weightage(self, info, codes:List[str]=[]) -> QueryResponse[List[QueueWeightage]]:
        try:
            db = info.context["db"]
            data = get_queue_weightage(db,codes)
            return QueryResponse.from_status_flag(True, "Success", [QueueWeightage.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

    @strawberry.field
    def get_staff_user_allocated_queues(self, info) -> QueryResponse[List[Queue]]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            data = get_staff_user_allocated_queues(db, staff_user)
            return QueryResponse.from_status_flag(True, "Success", [Queue.from_instance(obj, ", ".join(staff_user.name for staff_user in obj.staff_users)) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_queue_count(self, info) -> QueryResponse[List[QueueCount]]:
        try:
            db = info.context["db"]
            data = get_queue_count(db)
            return QueryResponse.from_status_flag(True, "Success", [QueueCount.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

    @strawberry.field
    def get_services(self, info, service_type:Optional[str] = None) -> QueryResponse[List[Services]]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            data = get_services(db,staff_user,service_type)
            return QueryResponse.from_status_flag(True, "Success", [Services.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_user_services(self, info, user_queue_id: int) -> QueryResponse[List[UserService1]]:
        try:
            db = info.context["db"]
            data = get_user_services(db,user_queue_id)
            return QueryResponse.from_status_flag(True, "Success", [UserService1.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_counter_details(self, info) -> QueryResponse[CounterDetails]:
        try:
            db = info.context["db"]
            device_id=info.context["device_id"]
            data = get_counter_details(db,device_id)
            return QueryResponse.from_status_flag(True, "Success", CounterDetails.from_instance(data["counter"],data["counter_users"]))
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    @strawberry.field
    def get_queue_steps(self, info, queue_id:int) -> QueryResponse[List[QueueStepLocation]]:
        try:
            db = info.context["db"]
            data = get_queue_steps(db,queue_id)
            return QueryResponse.from_status_flag(True, "Success", [QueueStepLocation.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_queue_step_locations(self, info, queue_step_id:int,condition: str) -> QueryResponse[List[Location]]:
        try:
            db = info.context["db"]
            data = get_queue_step_locations(db,queue_step_id,condition)
            return QueryResponse.from_status_flag(True, "Success", [Location.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_locations(self, info) -> QueryResponse[List[AllLocations]]:
        try:
            db = info.context["db"]
            data = get_locations(db)
            return QueryResponse.from_status_flag(True, "Success", [AllLocations.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_queue_step_logs(self, info, token_id:int) -> QueryResponse[List[userQueueStep]]:
        try:
            db = info.context["db"]
            data = get_queue_step_logs(db,token_id)
            logger.info(data)
            return QueryResponse.from_status_flag(True, "Success", [userQueueStep.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_user_queue_logs(self, info, token_id:int) -> QueryResponse[List[userQueueLog]]:
        try:
            db = info.context["db"]
            data = get_user_queue_logs(db,token_id)
            logger.info(data)
            return QueryResponse.from_status_flag(True, "Success", [userQueueLog.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_available_tags(self, info) -> QueryResponse[List[Tag]]:
        try:
            db = info.context["db"]
            data = get_available_tags(db)
            return QueryResponse.from_status_flag(True, "Success", [Tag.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

    @strawberry.field
    def get_all_tags(self, info) -> QueryResponse[List[Tag]]:
        try:
            db = info.context["db"]
            data = get_all_tags(db)
            return QueryResponse.from_status_flag(True, "Success", [Tag.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

    @strawberry.field
    def get_service_categories(self, info) -> QueryResponse[List[str]]:
        try:
            db = info.context["db"]
            data = get_service_categories(db)
            return QueryResponse.from_status_flag(True, "Success", data)
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_endoscopy_avg_time(self, info) -> QueryResponse[List[EndoscopyAvgTime]]:
        try:
            db = info.context["db"]
            data = get_average_time(db)
            return QueryResponse.from_status_flag(True, "Success", [EndoscopyAvgTime(queue_step_id=obj.queue_step_id,count=obj.count,code=obj.code,avg_duration_per_user=obj.avg_duration_per_user) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_overall_avg_time(self, info) -> QueryResponse[OverallAvgTime]:
        try:
            db = info.context["db"]
            data = get_overall_average_time(db)
            return QueryResponse.from_status_flag(True, "Success", OverallAvgTime(es_avg_procedure_per_hour=data.endoscopy_avg_procedure_per_hour,cs_avg_procedure_per_hour=data.colonoscopy_avg_procedure_per_hour,procedure_suit_occupancy_rate=data.procedure_suit_occupancy_rate,avg_procedure_from_start_of_day=data.avg_procedure_from_start_of_day,es_last_hour_avg_procedure_per_hour=data.endoscopy_last_hour_avg_procedure_per_hour,cs_last_hour_avg_procedure_per_hour=data.colonoscopy_last_hour_avg_procedure_per_hour,endoscopy_count=data.endoscopy_count,colonoscopy_count=data.colonoscopy_count,avg_procedure_per_hour=data.avg_procedure_per_hour, last_prev_hour_avg_procedure_per_hour= data.last_prev_hour_avg_procedure_per_hour))
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.field
    def get_procedure_rooms(self, info) -> QueryResponse[List[ProcedureRoomsList]]:
        try:
            db = info.context["db"]
            data = get_procedure_rooms(db)
            return QueryResponse.from_status_flag(True, "Success", [ProcedureRoomsList(code=obj.code,status=(obj.status).name if obj.status else None,occupied_count=obj.occupied_count,alloted_count=obj.alloted_count,total_count=obj.total_count,avg_time_mins=obj.avg_time_mins) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_censes(self, info) -> QueryResponse[GetCensusData]:
        try:
            db = info.context["db"]
            data = get_censes(db)
            return QueryResponse.from_status_flag(True, "Success", GetCensusData(total_count=data.total_count,completed_count=data.completed_count,waiting_count=data.waiting_count,inprogress_count=data.inprogress_count))
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

@strawberry.type
class Mutation:
    @strawberry.mutation
    def add_or_update_tag(self,info,tagInput : TagInput) -> MutationResponse[str]:
        try:
            db = info.context["db"]
            msg = add_or_update_tag(db,tagInput)
            return MutationResponse.from_status_flag(True, msg, None) 
        except MutationError as ex:
            return MutationResponse.from_status_flag(False,ex.message,None)

    @strawberry.mutation
    def add_or_edit_queue(self, info, queueInput: QueueInput) -> MutationResponse[List[Queue]]:
        try:
            db = info.context["db"]
            data = add_or_update_queue(db, queueInput)
            return MutationResponse.from_status_flag(True, "Sucess", [Queue.from_instance(obj, ", ".join(staff_user.name for staff_user in obj.staff_users)) for obj in data])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)

    @strawberry.mutation
    def delete_queue(self, info, queue_id: int) -> MutationResponse[str]:
        try:
            db = info.context["db"]
            res = delete_queue(db, queue_id)
            return MutationResponse.from_status_flag(True, "Queue Deleted Successfully", None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)

    @strawberry.mutation
    def add_user_queue(self, info, qr_details: QRDetail, queue_id: int) -> MutationResponse[List[UserQueue]]:
        try:
            db = info.context["db"]
            staff_user=info.context["staff_user"]
            data = add_user_queue(db, queue_id, qr_details,staff_user)
            return MutationResponse.from_status_flag(True, "User Added To Queue", [UserQueue.from_instance(obj) for obj in data])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)

    @strawberry.mutation
    def update_user_queue(self, info, user_queue_id: int, queue_weightage_action_id: Optional[int] = None, queue_weightage_action: Optional[str]=None) -> MutationResponse[List[UserQueue]]:
        try:
            db = info.context["db"]
            staff_user=info.context["staff_user"]
            data = update_user_queue(db, user_queue_id, queue_weightage_action_id,queue_weightage_action,staff_user)
            return MutationResponse.from_status_flag(True, "Queue Updated", [UserQueue.from_instance(obj) for obj in data])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)

    @strawberry.mutation
    def update_pre_check_status(self, info, user_queue_id: int, user_service_ids: Optional[List[int]] = None) -> MutationResponse[None]:
        try:
            db = info.context["db"]
            data = update_pre_check_status(db, user_queue_id, user_service_ids)
            return MutationResponse.from_status_flag(True, "Queue Updated", None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)

    @strawberry.mutation
    def exit_user_queue(self, info, queue_id: int, qr_details: QRDetail,user_service_ids: Optional[List[int]] = None) -> MutationResponse[List[UserQueue]]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            data = exit_user_queue(db, queue_id, qr_details,False,user_service_ids,staff_user)
            return MutationResponse.from_status_flag(True, "User Exited from Queue", [UserQueue.from_instance(obj) for obj in data])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)

    @strawberry.mutation
    def update_counter(self, info, counter_id: int,status:str) -> MutationResponse[str]:
        try:
            db = info.context["db"]
            counter_status = update_counter(db, counter_id,status)
            return MutationResponse.from_status_flag(True, "Counter Updated", counter_status)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)

    @strawberry.mutation
    def activate_or_deactivate_queue(self, info, queue_id: int, status: str) -> MutationResponse[List[Queue]]:
        try:
            db = info.context["db"]
            msg,data = activate_or_deactivate_queue(db, queue_id, status)
            return MutationResponse.from_status_flag(True, msg, [Queue.from_instance(obj, ", ".join(staff_user.name for staff_user in obj.staff_users)) for obj in data])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.mutation
    def staff_station_device_login(self, info, queue_id:int, login_pin: int) -> MutationResponse[str]:
        try:
            db= info.context['db']
            device_id = info.context["device_id"]
            result = staff_station_device_login(db, queue_id,device_id, login_pin)
            dt = datetime.now() + \
                timedelta(minutes=float(
                    os.environ["ACCESS_EXPIRE_MINUTES"]))
            access_token = jws.sign({"sub": {"device_id":device_id}, "user_type": "HOSPITAL", "exp": dt.isoformat(
                )}, os.environ["SECRET_KEY"], algorithm=os.environ["ALGORITHM"])
            logger.info("Staff user logged in suscessfully")
            return MutationResponse.from_status_flag(True,"Login Successfull",access_token)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message,None)
        
    @strawberry.mutation
    def update_user_queue_service(self, info, user_queue_id: int, user_service_ids: Optional[List[int]],status:Optional[str]=None) -> MutationResponse[List[UserQueue]]:
        try:
            db = info.context["db"]
            data = update_user_queue_service(db, user_queue_id, user_service_ids,status)
            return MutationResponse.from_status_flag(True, "User Queue Updated", [UserQueue.from_instance(obj) for obj in data])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.mutation
    def update_user_queue_step(self, info, user_queue_id: int, queue_step_id:int, remarks: Optional[str]=None,type:Optional[str]='CHECKIN', queue_location_id:Optional[int]=None,tag_id:Optional[str]=None, queue_step_code:Optional[str]= None) -> MutationResponse[None]:
        try:
            db = info.context["db"]
            staff_user=info.context["staff_user"]
            data = update_user_queue_step(db, user_queue_id, queue_step_id,remarks,type,queue_location_id,tag_id, queue_step_code,staff_user, "TAGID")
            return MutationResponse.from_status_flag(True, "User Queue Updated", None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.mutation
    def add_user_to_queue(self, info, uhid: str, queue_step_id:int, remarks: str,type:Optional[str]='CHECKIN') -> MutationResponse[None]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            data = add_to_queue(db, uhid, queue_step_id,remarks,type,staff_user)
            return MutationResponse.from_status_flag(True, "User Queue Updated", None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.mutation
    def call_next(self, info, queue_id:int) -> MutationResponse[None]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            data = call_next(db, queue_id, staff_user)
            return MutationResponse.from_status_flag(True, "User Queue Updated", None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.mutation
    def call_next_pre_check(self, info, queue_id:int) -> MutationResponse[UserQueue]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            data = call_next_pre_check(db, queue_id,staff_user)
            return MutationResponse.from_status_flag(True, "User Queue Updated", UserQueue.from_instance(data))
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    
    @strawberry.mutation
    def update_user_queue_manual(self, info, user_queue_id:int, queue_weightage_action_id: int, remarks: Optional[str]= None) -> MutationResponse[None]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            data = update_user_queue_manual(db, user_queue_id, queue_weightage_action_id,staff_user,remarks)
            return MutationResponse.from_status_flag(True, "User Queue Updated", None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)

    @strawberry.mutation
    def get_tag_or_scan_id(self, info, ref_id: str) -> MutationResponse[str]:
        print(f"ref id is {ref_id}")
        try:
            if not ref_id:
                raise MutationError("ScanId must be provided")
            db = info.context["db"]
            if ref_id:
                data = get_tag_or_scan_id(db,ref_id,"RFID")
                return MutationResponse.from_status_flag(True, "Success", data.code)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.mutation
    def checkout_user(self, info, ref_id: Optional[str]=None, uhid: Optional[str]=None) -> MutationResponse[str]:
        try:
            if not ref_id and not uhid:
                raise MutationError("ScanId or UHID must be provided")
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            msg = checkout_user(db,staff_user,ref_id,uhid)
            return MutationResponse.from_status_flag(True, "Success", msg)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.mutation
    def pause_unpause_user(self, info, user_queue_id: str, type: str) -> MutationResponse[str]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            msg = pause_unpause_user(db, staff_user, user_queue_id, type)
            return MutationResponse.from_status_flag(True, "Success", msg)     
        except MutationError as ex:
            return MutationResponse.from_status_flag(False,ex.message,None)
    
@strawberry.type
class Subscription:
    @strawberry.subscription
    async def get_queue_counters(self,info,queue_id: int) -> AsyncGenerator[List[UserQueue], None]:
        try:
            db = SessionLocal()
            # queue_id, is_updated = get_device_queue(db,queue_id)
            # data = get_user_queue(db, queue_id)
            db.close()
            while(True):
                db = SessionLocal()
                # if is_updated:
                #     data = get_user_queue(db, queue_id)
                #     yield [UserQueue.from_instance(obj) for obj in data]
                #     await asyncio.sleep(0.5)
                # else:
                #     new_data = [UserQueue.from_instance(obj) for obj in data]
                #     yield new_data
                # update_rel_device_queue(db,queue_id, False)
                data = get_queue_counters(db, queue_id)
                yield [UserQueue.from_instance(obj) for obj in data]
                if db is not None: db.close()
                await asyncio.sleep(0.25)
        except Exception as e:
            logger.exception(e)
