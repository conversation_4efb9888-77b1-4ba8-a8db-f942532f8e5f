import asyncio
from datetime import datetime, timedelta
import itertools
import os
import io
# from gtts import gTTS
from jose import jws
from typing import AsyncGenerator, List, Optional
from bill.models import UserToken as UserTokenModel
from database.db_conf import SessionLocal
from queues.models import (
    Location as LocationModel, Queue as QueueModel,QueueStep as QueueStepModel, Cluster as ClusterModel, QueueCounterStatusEnum, RelUserServiceQueue as RelUserServiceQueueModel, UserQueue as UserQueueModel, UserQueue as UserQueueModel, 
    QueueWeightageAction as QueueWeightageActionModel, QueueCounter as QueueCounterModel, UserQueueLogs as UserQueueLogsModel, UserQueuePreCheckStatusEnum, UserQueueStatusEnum, UserQueueStep as UserQueueStepModel, UserService as UserServiceModel,
    Tag as TagModel
)
from bill.resolvers import save_user_service_1
from service.models import  Service as ServiceModel
from queues.resolvers import (
    add_to_queue, add_user_queue, bar_code_generation, call_next, call_next_pre_check, exit_user_queue, get_acknowledge_test_details, get_available_tags, get_counter_details,get_locations, get_device_queue, get_queue_counters, get_queue_step_locations, get_queue_step_logs, get_queue_steps, get_queues_details, get_service_categories, get_services, get_queues,get_missed_patients, add_or_update_queue, delete_queue, get_user_queue_all, get_user_queue_logs, get_user_queue_msg, get_user_services, get_user_queue,
    get_clusters, sample_collection_update, staff_station_device_login, update_counter, update_pre_check_status, update_rel_device_queue, update_user_queue, activate_or_deactivate_queue, get_queue_weightage, get_current_user_queue, 
    get_staff_user_allocated_queues, get_queue_count, update_user_queue_manual, update_user_queue_service, update_user_queue_step, get_average_time, add_or_update_tag,get_all_tags,get_procedure_rooms,get_censes,get_overall_average_time,get_tag_or_scan_id,checkout_user, pause_unpause_user,update_patient_type,get_queue_counters_by_user,get_user_pending_services,cancel_pending_services,get_user_queue_vitals,update_user_queue_timestamp,get_queue_counters_by_queue_id,get_patient_queues,get_employee_details,add_token_prefix,get_sample_collection_type
)
from staff_user.schema import StaffUser
import strawberry
from graphql_types import AcknowledgeDetailsInput, MutationResponse, OperationTypes, QRDetail, QueryResponse, QueueInput, SampleCollectionInput, TagInput, UserDetail,BarCodeDetails
from exceptions.exceptions import MutationError, QueryError
from user.schema import User, UserType
from user.models import EntityTypeEnum
import json
import logging
from sqlalchemy.orm.session import make_transient
logger = logging.getLogger()
from util.globals import format_datetime, format_datetime_ist
from collections import Counter
import base64
from dateutil.parser import isoparse

@strawberry.type
class Cluster:
    id: int
    tower: str
    floor: str
    cluster: str

    instance = strawberry.Private[ClusterModel]

    @classmethod
    def from_instance(cls, instance: ClusterModel):
        return cls(
            id=instance.id,
            tower=instance.tower,
            floor=instance.floor,
            cluster=instance.cluster
        )

@strawberry.type
class QueueStaffUserDetails:
    id: int
    name: str
    emp_id : str
    
    @classmethod
    def from_instance(cls, id: int, name: str, emp_id: str):
        return cls(id = id, name = name, emp_id = emp_id)

@strawberry.type
class Services:
    id : int
    name : str
    code : str
    type : str
    categories: Optional[List[str]]=None
    
    instance = strawberry.Private[ServiceModel]
    @classmethod
    def from_instance(cls, instance: ServiceModel):
        return cls(id=instance.id, name=instance.name, code=instance.code, type=instance.type,categories=instance.prerequisites_conditions)

@strawberry.type
class Counters:
    id: int
    number: int
    code:str
    status: str
    queue_counter_name: str
    image_path: Optional[str] = None

    instance = strawberry.Private[QueueCounterModel]
    @classmethod
    def from_instance(cls,instance: QueueCounterModel):
        return cls(id=instance.id, 
                code=instance.counter_name, 
                status=instance.counter_status.name, 
                number= instance.counter,
                queue_counter_name= instance.counter_name,
                image_path=instance.doctor_image_name if instance.doctor_image_name else None
                )
@strawberry.type
class Queue:
    id: int
    queue_name: str
    queue_code: str
    updated_at: str
    status: str
    queue_type: Optional[str]
    service_type: Optional[str]
    completed_count: Optional[str]
    last_token_called_at:Optional[str]
    last_called_token_no:Optional[str]
    allocate_counter_at: Optional[str]
    cluster: Cluster
    avg_procedure_time: float
    upcoming_patients: int
    tests: List[Services]
    show_patient_name: Optional[bool]
    updated_by: Optional[StaffUser]
    supervisor: Optional[str] = None
    staff_user: Optional[List[QueueStaffUserDetails]]
    capacity: Optional[int]
    waiting_capacity: Optional[int] = None
    counters: Optional[List[Counters]] = None
    pre_requesite_conditions: Optional[List[str]]= None
    assignment: Optional[List[str]] = None
    expected_queue_count: Optional[int] = None
    waiting_queue_count: Optional[int] = None
    serving_queue_count: Optional[int] = None
    queue_status: Optional[str] = None
    serving_token: Optional[str] = None
    next_token: Optional[str] = None

    instance = strawberry.Private[QueueModel]

    @classmethod
    def from_instance(cls, instance: QueueModel, supervisor: str, expected_queue_count=0, waiting_queue_count=0, serving_count=0, serving_token=None, next_token=None):
        # queue_status = "Serving" if serving_count > 0 else "IDLE"
        queue_status = "Serving" if any(counter.counter_status.name == 'ACTIVE' for counter in instance.counters) else "Paused"
        return cls(
            id=instance.id,
            queue_name=instance.queue_name,
            queue_code=instance.queue_code,
            completed_count= instance.completed_count,
            last_token_called_at=instance.last_token_called_at,
            last_called_token_no=getattr(instance.user_token, "token_no", None),
            service_type = instance.service_type,
            queue_type = None if instance.queue_type is None else instance.queue_type.name,
            allocate_counter_at = None if instance.allocate_counter_at is None else instance.allocate_counter_at.name,
            avg_procedure_time=instance.avg_procedure_time,
            status=instance.status.name,
            upcoming_patients=instance.upcoming_patients,
            cluster=Cluster.from_instance(instance.cluster),
            updated_by=StaffUser.from_instance(instance.updated_by_obj) if instance.updated_by_obj else (
                StaffUser.from_instance(instance.created_by_obj) if instance.created_by_obj else None
            ),
            updated_at=format_datetime(instance.updated_at) if instance.updated_at else format_datetime(instance.created_at),
            supervisor=supervisor,
            show_patient_name=instance.show_patient_name,
            staff_user=[QueueStaffUserDetails.from_instance(user.id, user.name, user.emp_id) for user in instance.staff_users] if instance.staff_users else [],
            capacity=instance.capacity,
            tests=[Services.from_instance(service) for service in instance.services] if instance.services else [],
            waiting_capacity=instance.waiting_capacity,
            counters=[Counters.from_instance(obj) for obj in instance.counters],
            assignment=instance.assignment,
            pre_requesite_conditions=list(set(itertools.chain.from_iterable([arr.prerequisites_conditions for arr in instance.services if arr]))) if instance.services else None,
            expected_queue_count=expected_queue_count,
            waiting_queue_count=waiting_queue_count,
            serving_queue_count=serving_count,
            queue_status=queue_status,
            serving_token=serving_token if serving_token else "---",
            next_token=next_token if next_token else "---"
        )

@strawberry.type
class QueueDetails:
    id: int
    queue_name: str
    queue_code: str
    status: str
    avg_procedure_time: float
    upcoming_patients: int
    show_patient_name: Optional[bool]
    capacity: Optional[int]
    waiting_capacity: Optional[int] = None
    instance = strawberry.Private[QueueModel]

    @classmethod
    def from_instance(cls, instance: QueueModel, supervisor: str):
        return cls(
            id=instance.id,
            queue_name=instance.queue_name,
            queue_code=instance.queue_code,
            avg_procedure_time=instance.avg_procedure_time,
            status=instance.status.name,
            upcoming_patients=instance.upcoming_patients,
            show_patient_name=instance.show_patient_name,
            capacity = instance.capacity,
            waiting_capacity = instance.waiting_capacity,
        )
@strawberry.type
class UserQueue1:
    id: int
    uhid: str
    status: str
    token_no: str
    weightage: str
    user_queue_id: str
    queue_id:str
    token_id:str
    vitals_datetime: Optional[str]
    phy_ass_datetime: Optional[str]
    billId: Optional[int]
    appointment_date_time: Optional[str]
    pre_check_status: Optional[str]
    instance = strawberry.Private[UserQueueModel]

    @classmethod
    def from_instance(cls, instance: UserQueueModel, billId: int= None):
        return cls(
            id=instance.id,
            uhid=instance.user.umr_no,
            status=instance.status.name,
            token_no=add_token_prefix(instance.token_no,instance.appointment_date_time,instance.user.is_platinum_user),
            weightage=instance.queue_weightage_action.name,
            vitals_datetime= format_datetime_ist(instance.vitals_datetime),
            phy_ass_datetime= format_datetime_ist(instance.phy_ass_datetime),
            user_queue_id= instance.id,
            token_id=instance.token_id,
            queue_id=instance.queue_id,
            billId= billId,
            pre_check_status= instance.pre_check_status.name if instance.pre_check_status else None,
            appointment_date_time=instance.appointment_date_time
        )

@strawberry.type
class UserQueue:
    id: int
    queue: QueueDetails
    user: User
    status: str
    token_no: str
    weightage: str
    weightage_id:int
    prerequisites_conditions: Optional[List[str]]= None
    counter_name : Optional[str] = None
    start_time: Optional[str] = None
    token_id:int
    queue_step_id: Optional[str] = None
    vitals_datetime: Optional[str]
    phy_ass_datetime: Optional[str]
    pre_check_status: Optional[str]
    appointment_date_time: Optional[str]

    instance = strawberry.Private[UserQueueModel]

    @classmethod
    def from_instance(cls, instance: UserQueueModel):
        return cls(
            id=instance.id,
            queue=QueueDetails.from_instance(instance.queue, ", ".join(
                staff_user.name for staff_user in instance.queue.staff_users)),
            user=User.from_instance(instance.user),
            status=instance.status.name,
            token_no=add_token_prefix(instance.token_no,instance.appointment_date_time,instance.user.is_platinum_user),
            weightage=instance.queue_weightage_action.name,
            counter_name = instance.counter_obj.counter_name if instance.counter is not None else None,
            start_time = format_datetime(instance.start_time),
            weightage_id = instance.weightage_id,
            token_id=instance.token_id,
            prerequisites_conditions= instance.prerequisites_conditions,
            queue_step_id= instance.queue_step_id,
            vitals_datetime=instance.vitals_datetime,
            phy_ass_datetime=instance.phy_ass_datetime,
            pre_check_status=None if instance.pre_check_status is None else instance.pre_check_status.value,
            appointment_date_time=instance.appointment_date_time
        )

@strawberry.type
class QueueCounterType:
    counter_name : Optional[str]
    tokens : Optional[List[str]]
    doctor_image: Optional[str]
    serving_token: Optional[str]
    patient_name: Optional[str]
    mobile_number: Optional[str]

@strawberry.type
class CounterList:
    counter_name : Optional[str]
    counter_id : Optional[int]
    counter_status : Optional[str]

@strawberry.type
class StaffQueueCounters:
    queue_id : Optional[int]
    queue_name: Optional[str]
    show_patient_name: Optional[bool]
    phy_token_no: Optional[str]
    counters : Optional[List[QueueCounterType]]


@strawberry.type
class StaffQueueCountersObj:
    staff_queue_counters : List[StaffQueueCounters]
    current_date : Optional[str]
    current_time : Optional[str]
@strawberry.type
class QueueAudit:
    id: int
    queue: str
    queue_code: str
    updated_at: str
    status: str
    avg_procedure_time: float
    upcoming_patients: int
    cluster: Cluster
    updated_by: Optional[StaffUser]
    instance = strawberry.Private[QueueModel]

    @classmethod
    def from_instance(cls, instance: QueueModel):
        return cls(
            id=instance.id,
            queue_name=instance.queue_name,
            queue_code=instance.queue_code,
            avg_procedure_time=instance.avg_procedure_time,
            status=instance.status.name,
            upcoming_patients=instance.upcoming_patients,
            cluster=Cluster.from_instance(instance.cluster),
            updated_by=StaffUser.from_instance(instance.updated_by_obj) if instance.updated_by_obj is not None else (
                StaffUser.from_instance(instance.created_by_obj) if instance.created_by_obj is not None else None),
            updated_at=format_datetime(instance.updated_at) if instance.updated_at is not None else format_datetime(instance.created_at)
        )


@strawberry.type
class QueueWeightage:
    id: int
    name: str
    code: str
    weightage: int

    instance = strawberry.Private[QueueWeightageActionModel]

    @classmethod
    def from_instance(cls, instance: QueueWeightageActionModel):
        return cls(
            id=instance.id,
            name=instance.name,
            code=instance.code,
            weightage=instance.weightage
        )


@strawberry.type
class QueueCount:
    queue_name: str
    capacity: int
    status: str
    weightage: str
    cluster: str
    created_at: str 
    estimated_time: str
    id: int
    token: str
    patient_name: str
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    force_exit: Optional[str] = None
    arrived_at: Optional[str] = None
    waiting_capacity: Optional[int] = None
    phone_number: Optional[str] = None
    instance = strawberry.Private[UserQueueModel]
    @classmethod
    def from_instance(cls, instance: UserQueueModel):
        return cls(
            queue_name = instance.queue.queue_name,
            capacity = instance.queue.capacity,
            status = instance.status.name,
            weightage = instance.queue_weightage_action.code,
            cluster = instance.queue.cluster.cluster,
            created_at = format_datetime(instance.created_at),
            estimated_time = format_datetime(instance.estimated_time),
            start_time = format_datetime(instance.start_time),
            end_time = format_datetime(instance.end_time),
            force_exit = instance.force_exit,
            arrived_at = format_datetime(instance.arrived_at),
            id = instance.id,
            waiting_capacity = instance.queue.waiting_capacity,
            token=add_token_prefix(instance.token_no,instance.appointment_date_time,instance.user.is_platinum_user),
            patient_name= instance.user.name,
            phone_number= instance.user.phone_number
        )

@strawberry.type
class CounterUserService:
    user_service_id: int 
    service_name: str
    bill: Optional[str]
    test_id: Optional[str]
    sample_no: Optional[str]

    # Fields from order_details JSON
    order_id: Optional[str]
    order_date: Optional[str]
    patient_name: Optional[str]
    test_name: Optional[str]
    test_status: Optional[str]
    department: Optional[str]
    sub_department_name: Optional[str]
    test_priority: Optional[str]
    gender: Optional[str]
    age: Optional[str]
    uhid: Optional[str]
    bill_no: Optional[str]
    sample_type: Optional[str]
    sample_quantity: Optional[str]
    tube_top_colour_code: Optional[str]
    package_or_non_package: Optional[str]
    required_unique_sample_no: Optional[str]
    sub_department_short_name: Optional[str]
    test_short_name: Optional[str]
    instructions_for_phlebotomist: Optional[str]

    instance = strawberry.Private[UserServiceModel]

    @classmethod
    def from_instance(cls, instance: UserServiceModel):
        order_details = instance.order_details or {}

        obj = cls(
            user_service_id=instance.id,
            service_name=instance.service.name,
            bill=instance.bill,
            test_id=instance.test_id,
            sample_no=instance.sample_no,

            order_id=order_details.get("orderId"),
            order_date=order_details.get("orderDate"),
            patient_name=order_details.get("patientName"),
            test_name=order_details.get("testName"),
            test_status=order_details.get("testStatus"),
            department=order_details.get("department"),
            sub_department_name=order_details.get("subDepartmentName"),
            test_priority=order_details.get("testPriority"),
            gender=order_details.get("gender"),
            age=order_details.get("age"),
            uhid=order_details.get("uhid"),
            bill_no=order_details.get("billNo"),
            sample_type=order_details.get("sampleType"),
            sample_quantity=order_details.get("sampleQuantity"),
            tube_top_colour_code=order_details.get("tubeTopColourCode"),
            package_or_non_package=order_details.get("packageOrNonPackage"),
            required_unique_sample_no=order_details.get("requiredUniqueSampleNo"),
            sub_department_short_name=order_details.get("subDepartmentShortName"),
            test_short_name=order_details.get("testShortName"),
            instructions_for_phlebotomist=order_details.get("instructionsForPhlebotomist"),
        )

        obj.instance = instance  
        return obj


@strawberry.type
class CounterUserQueue:
    user_queue_id:int
    token_no: str
    user_queue_status: str
    user: Optional[User] = None
    user_service: Optional[List[CounterUserService]] = None
    token_id: str

    instance = strawberry.Private[UserQueueModel]
    @classmethod
    def from_instance(cls, instance: UserQueueModel,services):
        return cls(user_queue_id = instance.id,
                token_no = add_token_prefix(instance.token_no,instance.appointment_date_time,instance.user.is_platinum_user),
                    user_queue_status=instance.status.name,
                   user= User.from_instance(instance.user) if instance.status == UserQueueStatusEnum.ENTRY else None,
                   user_service= [CounterUserService.from_instance(obj) for obj in services] if instance.status == UserQueueStatusEnum.ENTRY and len(services) > 0 else None,
                   token_id = instance.token_id,
                )
@strawberry.type
class CounterDetails:
    counter_id: int
    counter_name: str
    counter_status: str
    queue_name: str
    queue_id: int
    user_queue: Optional[List[CounterUserQueue]] = None
    instance = strawberry.Private[QueueCounterModel]
    @classmethod
    def from_instance(cls,instance: QueueCounterModel,user_queues):
        return cls(
            counter_id = instance.id, 
            counter_name= instance.counter_name,
            counter_status=instance.counter_status.name,
            queue_name = instance.queue.queue_name,
            user_queue=[CounterUserQueue.from_instance(user_queue,services) for user_queue,services in user_queues.items()] if user_queues is not None and len(user_queues)>0 else [],
            queue_id = instance.queue.id,
        )


    
@strawberry.type
class LocationBed:
    id: int
    name: str
    code: str
    status: str
    
    instance = strawberry.Private[LocationModel]

    @classmethod
    def from_instance(cls,instance: LocationModel):
        return cls(id=instance.id, 
                name=instance.name,
                code = instance.code,
                status = instance.status.name
            )
@strawberry.type
class Location:
    id: int
    name: str
    code: str
    beds : Optional[List[LocationBed]] = None
    available_count:Optional[int] = None
    occupied_count:Optional[int] = None
    alloted_count:Optional[int] = None
    total_count: Optional[int] = None
    instance = strawberry.Private[LocationModel]

    @classmethod
    def from_instance(cls,instance: LocationModel):
        status_counts = Counter([bed.status.name for bed in instance.child_locations])        
        return cls(id=instance.id, 
                name=instance.name,
                code = instance.code,
                beds=[LocationBed.from_instance(child_location) for child_location in instance.child_locations] if instance.child_locations is not None else [],
                available_count=status_counts['AVAILABLE'],
                occupied_count=instance.occupied_count,
                alloted_count=instance.alloted_count,
                total_count = instance.total_count
            )

@strawberry.type
class AllLocations:
    id: int
    name: str
    code: str
    available_count:Optional[int] = None
    occupied_count:Optional[int] = None
    alloted_count:Optional[int] = None
    total_count: Optional[int] = None
    instance = strawberry.Private[LocationModel]

    @classmethod
    def from_instance(cls,instance: LocationModel):
        status_counts = Counter([bed.status.name for bed in instance.child_locations])        
        return cls(id=instance.id, 
                name=instance.name,
                code = instance.code,
                available_count=status_counts['AVAILABLE'],
                occupied_count=instance.occupied_count,
                alloted_count=instance.alloted_count,
                total_count = instance.total_count
            )
@strawberry.type
class QueueStepLocation:
    id: int
    name: str
    code: str
    locations : Optional[List[Location]]
    
    instance = strawberry.Private[QueueStepModel]

    @classmethod
    def from_instance(cls,instance: QueueStepModel):
        return cls(id=instance.id, 
                name=instance.name,
                code = instance.code,
                locations = [Location.from_instance(obj) for obj in instance.locations],

            )
    

@strawberry.type
class ProcedureRoomsList:
    code: str
    status:Optional[str] = None
    occupied_count:Optional[int] = None
    alloted_count:Optional[int] = None
    total_count: Optional[int] = None
    avg_time_mins: Optional[str] = None
    total_patients_served: Optional[int] = None
    first_arrived_at: Optional[str] = None

@strawberry.type
class GetCensusData:
    total_count: Optional[int] = None
    completed_count:Optional[int] = None
    waiting_count:Optional[int] = None
    inprogress_count:Optional[int] = None

@strawberry.type
class EndoscopyAvgTime:
    queue_step_id: int
    count: int
    code: str
    avg_duration_per_user: Optional[str] = "None"

@strawberry.type
class OverallAvgTime:
    avg_procedure_per_hour: Optional[int] = None
    es_avg_procedure_per_hour: Optional[int] = None
    cs_avg_procedure_per_hour: Optional[int] = None
    es_last_hour_avg_procedure_per_hour: Optional[int] = None
    cs_last_hour_avg_procedure_per_hour: Optional[int] = None
    endoscopy_count: Optional[int] = None
    colonoscopy_count: Optional[int] = None
    avg_procedure_from_start_of_day: Optional[int] = None
    procedure_suit_occupancy_rate: Optional[int] = None
    last_prev_hour_avg_procedure_per_hour: Optional[int] = None


@strawberry.type
class Tag:
    id: int
    name: str
    code: str
    status: str
    created_at: str
    rfid_code: str

    instance = strawberry.Private[TagModel]
    @classmethod
    def from_instance(cls,instance: TagModel):
        return cls(
                id=instance.id, 
                name=instance.name,
                code=instance.code,
                status=instance.status.name if instance.status else None,
                created_at=instance.created_at,
                rfid_code=instance.rfid_code
                )

@strawberry.type
class userQueueStep:
    id: int
    step_name: Optional[str] = None
    created_at: Optional[str]
    completed_at: Optional[str]
    status: Optional[str]
    action_type: Optional[str]
    next_location : Optional[Location]
    location: Optional[str]=None
    description : Optional[str]=None
    remarks: Optional[str]= None
    tag : Optional[Tag] = None
    queue_name : Optional[str] = None
    source : Optional[str] = None
    
    instance = strawberry.Private[UserQueueStepModel]

    @classmethod
    def from_instance(cls,instance: UserQueueStepModel):
        return cls(id=instance.id, 
                step_name=  None if instance.queue_step is None else instance.queue_step.name,
                created_at= instance.created_at,
                completed_at= instance.updated_at,
                status = instance.status.name,
                action_type = instance.action_type,
                next_location = None if instance.location is None else Location.from_instance(instance.location),
                location=instance.created_location,
                description=instance.description,
                remarks=instance.remarks,
                tag = Tag.from_instance(instance.tag) if instance.tag is not None else None,
                queue_name = None if instance.queue_step is None else instance.queue_step.queue.queue_name,
                source = instance.created_by
            )
@strawberry.type
class userQueueLog:
    id: int
    created_at: Optional[str]
    status: Optional[str]
    queue_name : Optional[str] = None
    weightage : Optional[QueueWeightage] = None
    counter_name: Optional[str]= None           
    source: Optional[str] = None 
    instance = strawberry.Private[UserQueueLogsModel]

    @classmethod
    def from_instance(cls,instance: UserQueueLogsModel):
        return cls(id=instance.id, 
                created_at= instance.created_at,
                status = "Vial kit prepared" if instance.pre_check_status==UserQueuePreCheckStatusEnum.COMPLETED and instance.status==UserQueueStatusEnum.ARRIVED  else instance.status.name,
                weightage = QueueWeightage.from_instance(instance.queue_weightage_action) if instance.queue_weightage_action is not None else None,
                queue_name = instance.user_queue.queue.queue_name,
                counter_name= None if instance.queue_counter is None else instance.queue_counter.counter_name,
                source = instance.created_by_obj.name if instance.created_by_obj is not None else 'System'
            )

@strawberry.type
class UserService1:
    id: int
    name: Optional[str]
    status: str
    categories: Optional[List[str]]=None
        
    instance = strawberry.Private[RelUserServiceQueueModel]

    @classmethod
    def from_instance(cls,instance: RelUserServiceQueueModel):
        return cls(id=instance.user_service_id, 
                name= instance.user_service.service.name,
                status= instance.status.name,
                categories = instance.user_service.service.prerequisites_conditions,
            )


@strawberry.type
class ServiceObj:
    service_id: int
    service_code: str
    service_name: str
    service_type: str
    bill_no: Optional[str]
    timestamp: str
    status: str

status_order = {
    "PENDING": 0,
    "COMPLETED": 1
}

@strawberry.type
class UserPendingService:
    token_id: int
    visit_date: str
    overall_status: str
    service_types: List[str]
    service: List[ServiceObj]

    @classmethod
    def from_instance(cls,token_id:int,visit_date:str,overall_status:str,service_types:List[str],service:List[ServiceObj]):
        return cls(
            token_id=token_id, 
            visit_date=visit_date,
            overall_status=overall_status,
            service_types=service_types,
            service=[
            ServiceObj(
                service_id=obj.get("service_id"),
                service_code=obj.get("service_code"),
                service_name=obj.get("service_name"),
                service_type=obj.get("service_type"),
                bill_no=obj.get("bill_no"),
                timestamp=isoparse(obj.get("timestamp")).replace(tzinfo=None).strftime("%Y-%m-%d %H:%M:%S"),
                status=obj.get("status"),
            )
            for obj in sorted(service, key=lambda obj: status_order.get(obj.get("status"), 2))
        ]
            )

@strawberry.type
class BreakJourneyOutput:
    uhid: str
    patient_name: str
    phone_number: str
    weightage_id: int
    doctor_name: Optional[str] = None
    token_details: List[UserPendingService]


@strawberry.type
class MissedPatients:
    user_id: str
    user_name: str
    umr_no: str
    phone_number: str
    token_no: str

@strawberry.type
class PatientQueue:
    user_queue_id: int
    queue_id: int
    token_no: str
    prerequisites_conditions: List[str]

@strawberry.type
class MsgService:
    id: int
    status: str
    service_name:str
    service_id: int
    service_type: str
    completed_time:str
    
    instance = strawberry.Private[UserServiceModel]
    @classmethod
    def from_instance(cls,instance: UserServiceModel):
        logger.info(instance)
        return cls(id=instance.id, 
                status=instance.status.name, 
                service_name= instance.service.name,
                service_id= instance.service.id,
                service_type = instance.service.service_type,
                completed_time = instance.updated_at if instance.status.name == 'COMPLETED' else ""
                )

@strawberry.type
class QueueMsg:
    msg:str
    services :Optional[List[MsgService]] =None
    token_no: Optional[str] = None
    token_created_at: Optional[str] = None


    @classmethod
    def from_instance(cls,msg,instance:UserTokenModel):
        logger.info(instance)
        return cls(
            msg = "Token not found" if instance is None else msg,
            services=sorted(
                [MsgService.from_instance(obj) for obj in instance.user_services],
                key=lambda x: (x.completed_time == "", x.completed_time),
                reverse=True
            ) if instance is not None else [],
            token_no = None if instance is None else instance.token_no,
            token_created_at = None if instance is None else instance.created_at
        )
    
@strawberry.type
class DoctorEmployeeDetail:
    name:str
    off_mail:Optional[str]=None
    per_mail:Optional[str]=None
    phone_no:str
    mapped_queues:Optional[List[int]]=None

@strawberry.type
class BarCodeResponse:
    user_service_id:int
    sample_no: Optional[str] = None

    
@strawberry.type
class SampleCollection:
    hosp_code: str
    episode: str
    uhid: str
    bill_no: str
    order_id: str
    test_id: str
    sample_no: str
    sample_status: str

@strawberry.type
class AcknowledgeDetails:
    hosp_code: str
    order_id: str
    order_date: str
    uhid: str
    patient_name: str
    gender: str
    age: str
    test_id: str
    test_short_code: str
    test_priority: str
    department_short_name: str

@strawberry.type
class Query:
    @strawberry.field
    def get_queues(self, info) -> QueryResponse[List[Queue]]:
        try:
            db = info.context["db"]
            data = get_queues(db)
            return QueryResponse.from_status_flag(True, "Success", [Queue.from_instance(obj, ", ".join(staff_user.name for staff_user in obj.staff_users)) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.field
    def get_patient_queue(self, info, uhid:str) -> QueryResponse[PatientQueue]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            data = get_patient_queues(db,staff_user,uhid)
            return QueryResponse.from_status_flag(True, "Success", PatientQueue(user_queue_id=data.id,queue_id=data.queue_id,token_no=data.token_no,prerequisites_conditions=data.all_prerequisites if data.all_prerequisites else []) if data else None)
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
        
        
    @strawberry.field
    def get_missed_patients(self, info, start_date: Optional[str] = None, end_date: Optional[str]= None) -> QueryResponse[List[MissedPatients]]:
        try:
          db = info.context["db"]
          data = get_missed_patients(db,start_date, end_date)  
          return QueryResponse.from_status_flag(True,"Success",[MissedPatients(user_id=obj.user_id, user_name=obj.name, umr_no=obj.umr_no, phone_number=obj.phone_number, token_no=obj.token_no) for obj in data] if data is not None else [])  
        except QueryError as ex:
          return QueryResponse.from_status_flag(False,ex.message,None) 

    @strawberry.field
    def get_queues_audit(self, info) -> QueryResponse[List[Queue]]:
        try:
            db = info.context["db"]
            data = get_queues(db)
            return QueryResponse.from_status_flag(True, "Success", [Queue.from_instance(obj, ", ".join(staff_user.name for staff_user in obj.staff_users)) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

    @strawberry.field
    def get_user_queue(self, info, queue_id: Optional[int]= None, pre_check_status:Optional[str]= None, assignment_option: Optional[str]= None, queue_ids: Optional[List[int]]= None, queue_code: Optional[str]= None) -> QueryResponse[List[UserQueue]]:
        try:
            db = info.context["db"]
            if assignment_option == "VITALS" or assignment_option == "PHYSICIANASSISTANT":
                if queue_ids is None:
                    queue_ids=[queue_id]
                data = get_user_queue_vitals(db, queue_ids,pre_check_status,assignment_option)
            else:
                data = get_user_queue(db, queue_id,pre_check_status,assignment_option, queue_code)
            # logger.info(data)
            return QueryResponse.from_status_flag(True, "Success", [UserQueue.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

    @strawberry.field
    def get_user_queue_all(self, info, queue_code: Optional[str]= None) -> QueryResponse[List[UserQueue1]]:
        try:
            db = info.context["db"]
            data = get_user_queue_all(db, queue_code)
            # logger.info(data)
            return QueryResponse.from_status_flag(True, "Success", [UserQueue1.from_instance(obj[0], obj[1]) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)


    @strawberry.field
    def get_current_user_queue(self, info, queue_id: int) -> QueryResponse[List[UserQueue]]:
        try:
            db = info.context["db"]
            data = get_current_user_queue(db, queue_id)
            return QueryResponse.from_status_flag(True, "Success", [UserQueue.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

    @strawberry.field
    def get_clusters(self, info) -> QueryResponse[List[Cluster]]:
        try:
            db = info.context["db"]
            data = get_clusters(db)
            return QueryResponse.from_status_flag(True, "Success", [Cluster.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

    @strawberry.field
    def get_queue_weightage(self, info, codes:List[str]=[]) -> QueryResponse[List[QueueWeightage]]:
        try:
            db = info.context["db"]
            data = get_queue_weightage(db,codes)
            return QueryResponse.from_status_flag(True, "Success", [QueueWeightage.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

    @strawberry.field
    def get_staff_user_allocated_queues(self, info) -> QueryResponse[List[Queue]]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            data = get_staff_user_allocated_queues(db, staff_user)
            queue_list = [
                Queue.from_instance(
                    queue_obj[0],
                    ", ".join(staff_user.name for staff_user in queue_obj[0].staff_users),
                    queue_obj[3] or 0,
                    queue_obj[2] or 0,
                    queue_obj[1] or 0,
                    queue_obj[5][0] if queue_obj[5] is not None else None,
                    queue_obj[4][0] if queue_obj[4] is not None else None,
                )
                for queue_obj in data
            ]
            return QueryResponse.from_status_flag(True, "Success", queue_list)
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_queue_details(self, info, queue_code:str) -> QueryResponse[Queue]:
        try:
            db = info.context["db"]
            queue_obj = get_queues_details(db, queue_code)
            return QueryResponse.from_status_flag(True, "Success", Queue.from_instance(
                queue_obj[0],
                ", ".join(staff_user.name for staff_user in queue_obj[0].staff_users),
                queue_obj[3] or 0,
                queue_obj[2] or 0,
                queue_obj[1] or 0,
                queue_obj[5][0] if queue_obj[5] is not None else None,
                queue_obj[4][0] if queue_obj[4] is not None else None,
            ))
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.field
    def get_queue_count(self, info) -> QueryResponse[List[QueueCount]]:
        try:
            db = info.context["db"]
            data = get_queue_count(db)
            return QueryResponse.from_status_flag(True, "Success", [QueueCount.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

    @strawberry.field
    def get_services(self, info, service_type:Optional[str] = None, include_all:Optional[bool]=False) -> QueryResponse[List[Services]]:
        try:
            db = info.context["db"]
            if include_all :
                staff_user= None
            else:
                staff_user = info.context["staff_user"]
            data = get_services(db,staff_user,service_type)
            return QueryResponse.from_status_flag(True, "Success", [Services.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_user_services(self, info, user_queue_id: int) -> QueryResponse[List[UserService1]]:
        try:
            db = info.context["db"]
            data = get_user_services(db,user_queue_id)
            return QueryResponse.from_status_flag(True, "Success", [UserService1.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_counter_details(self, info, queue_counter_id: Optional[int] = None) -> QueryResponse[CounterDetails]:
        try:
            db = info.context["db"]
            device_id=info.context["device_id"]
            data = get_counter_details(db,device_id,queue_counter_id)
            return QueryResponse.from_status_flag(True, "Success", CounterDetails.from_instance(data["counter"],data["counter_users"]))
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.field
    async def get_user_queue_counters(self,info,queue_id:int) -> QueryResponse[List[CounterList]]:
        try:
            db = info.context["db"]
            response = get_queue_counters_by_queue_id(db, queue_id)
            return QueryResponse.from_status_flag(
                True, 
                "Success", 
                [CounterList(counter_name=obj.counter_name,counter_id=obj.counter_id,counter_status=obj.counter_status.value if obj.counter_status else "") for obj in response] if response else []
            )
        except Exception as e:
            logger.exception(e)
            return QueryResponse.from_status_flag(False, str(e), None)

    @strawberry.field
    def get_queue_steps(self, info, queue_id:int) -> QueryResponse[List[QueueStepLocation]]:
        try:
            db = info.context["db"]
            data = get_queue_steps(db,queue_id)
            return QueryResponse.from_status_flag(True, "Success", [QueueStepLocation.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_queue_step_locations(self, info, queue_step_id:int,condition: str) -> QueryResponse[List[Location]]:
        try:
            db = info.context["db"]
            data = get_queue_step_locations(db,queue_step_id,condition)
            return QueryResponse.from_status_flag(True, "Success", [Location.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_locations(self, info) -> QueryResponse[List[AllLocations]]:
        try:
            db = info.context["db"]
            data = get_locations(db)
            return QueryResponse.from_status_flag(True, "Success", [AllLocations.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_queue_step_logs(self, info, token_id:int) -> QueryResponse[List[userQueueStep]]:
        try:
            db = info.context["db"]
            data = get_queue_step_logs(db,token_id)
            logger.info(data)
            return QueryResponse.from_status_flag(True, "Success", [userQueueStep.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_user_queue_logs(self, info, token_id:int) -> QueryResponse[List[userQueueLog]]:
        try:
            db = info.context["db"]
            data = get_user_queue_logs(db,token_id)
            logger.info(data)
            return QueryResponse.from_status_flag(True, "Success", [userQueueLog.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_available_tags(self, info) -> QueryResponse[List[Tag]]:
        try:
            db = info.context["db"]
            data = get_available_tags(db)
            return QueryResponse.from_status_flag(True, "Success", [Tag.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

    @strawberry.field
    def get_all_tags(self, info) -> QueryResponse[List[Tag]]:
        try:
            db = info.context["db"]
            data = get_all_tags(db)
            return QueryResponse.from_status_flag(True, "Success", [Tag.from_instance(obj) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

    @strawberry.field
    def get_service_categories(self, info) -> QueryResponse[List[str]]:
        try:
            db = info.context["db"]
            data = get_service_categories(db)
            return QueryResponse.from_status_flag(True, "Success", data)
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_endoscopy_avg_time(self, info) -> QueryResponse[List[EndoscopyAvgTime]]:
        try:
            db = info.context["db"]
            data = get_average_time(db)
            return QueryResponse.from_status_flag(True, "Success", [EndoscopyAvgTime(queue_step_id=obj.queue_step_id,count=obj.count,code=obj.code,avg_duration_per_user=obj.avg_duration_per_user) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_overall_avg_time(self, info) -> QueryResponse[OverallAvgTime]:
        try:
            db = info.context["db"]
            data = get_overall_average_time(db)
            return QueryResponse.from_status_flag(True, "Success", OverallAvgTime(es_avg_procedure_per_hour=data.endoscopy_avg_procedure_per_hour,cs_avg_procedure_per_hour=data.colonoscopy_avg_procedure_per_hour,procedure_suit_occupancy_rate=data.procedure_suit_occupancy_rate,avg_procedure_from_start_of_day=data.avg_procedure_from_start_of_day,es_last_hour_avg_procedure_per_hour=data.endoscopy_last_hour_avg_procedure_per_hour,cs_last_hour_avg_procedure_per_hour=data.colonoscopy_last_hour_avg_procedure_per_hour,endoscopy_count=data.endoscopy_count,colonoscopy_count=data.colonoscopy_count,avg_procedure_per_hour=data.avg_procedure_per_hour, last_prev_hour_avg_procedure_per_hour= data.last_prev_hour_avg_procedure_per_hour) if data is not None else None)
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.field
    def get_procedure_rooms(self, info) -> QueryResponse[List[ProcedureRoomsList]]:
        try:
            db = info.context["db"]
            data = get_procedure_rooms(db)
            return QueryResponse.from_status_flag(True, "Success", [ProcedureRoomsList(code=obj.code,status=(obj.status).name if obj.status else None,occupied_count=obj.occupied_count,alloted_count=obj.alloted_count,total_count=obj.total_count,avg_time_mins=obj.avg_time_mins,total_patients_served=obj.total_patients_served,first_arrived_at=obj.first_arrived_at) for obj in data])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_censes(self, info) -> QueryResponse[GetCensusData]:
        try:
            db = info.context["db"]
            data = get_censes(db)
            return QueryResponse.from_status_flag(True, "Success", GetCensusData(total_count=data.total_count,completed_count=data.completed_count,waiting_count=data.waiting_count,inprogress_count=data.inprogress_count))
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
        
    
    @strawberry.field
    def get_user_pending_services(self, info, uhid: str) -> QueryResponse[BreakJourneyOutput]:
        try:
            db = info.context["db"]
            user_data, data = get_user_pending_services(db,uhid)
            return QueryResponse.from_status_flag(True, "Success", BreakJourneyOutput(uhid=uhid,patient_name=user_data.name,phone_number=user_data.phone_number,weightage_id=user_data.weightage_id,doctor_name=user_data.doctor_name,token_details=[UserPendingService.from_instance(token_id=obj.token_id,visit_date=str(obj.visit_date),overall_status=obj.overall_status,service_types=obj.service_types,service=obj.service) for obj in data] if data else []))
        except QueryError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.field
    def get_user_queue_msg(self, info,uhid:str) -> QueryResponse[QueueMsg]:
        try:
            db = info.context["db"]
            msg,user_token = get_user_queue_msg(db,uhid)
            return QueryResponse.from_status_flag(True, "Success", QueueMsg.from_instance(msg,user_token))
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)

    
    # @strawberry.field
    # def get_base64_from_text(self, info, text: str) -> QueryResponse[str]:
    #     try:
    #         tts = gTTS(text)
    #         buffer = io.BytesIO()
    #         tts.write_to_fp(buffer)
    #         buffer.seek(0)
    #         base64_text = base64.b64encode(buffer.read()).decode('utf-8')
    #         return QueryResponse.from_status_flag(True, "Success", base64_text)
    #     except QueryError as ex:
    #         return QueryResponse.from_status_flag(False, ex.message, None)

    
    @strawberry.field
    def get_doctor_employee_details(self,info,employee_id:str) ->QueryResponse[DoctorEmployeeDetail]:
        try:
            db= info.context["db"]
            data=get_employee_details(db,employee_id)
            return QueryResponse.from_status_flag(True, "Success", DoctorEmployeeDetail(name=data.get("name"),off_mail=data.get("offMailId"),per_mail=data.get("perMailId"),phone_no=data.get("phoneNo"),mapped_queues=data.get("mapped_queues",[])) if data is not None else [])
        except MutationError as ex:
            return QueryResponse.from_status_flag(False, ex.message, None)
        
    
    @strawberry.field
    def get_queue_counters_by_staff_user(self,info) -> QueryResponse[StaffQueueCountersObj]:
        try:
            db= info.context["db"]
            staff_user = info.context["staff_user"]
            response = get_queue_counters_by_user(db, staff_user)
            current_date = datetime.now().strftime("%A, %B %d, %Y")
            current_time = datetime.now().strftime("%I:%M:%S %p")
            return QueryResponse.from_status_flag(
                True,
                "Success",
                StaffQueueCountersObj(current_date=current_date,current_time=current_time,staff_queue_counters=[StaffQueueCounters(
                    queue_id = obj.get("queueId"),
                    queue_name = obj.get("queueName"),
                    show_patient_name = obj.get("showPatientName"),
                    phy_token_no= obj.get("phyTokenNo"),
                    counters= [QueueCounterType(counter_name=obj1.get("counterName"),tokens=obj1.get("tokens"),doctor_image=obj1.get("doctorImage"),serving_token=obj1.get("servingToken"),patient_name=obj1.get("patientName"),mobile_number=obj1.get("mobileNumber")) for obj1 in obj.get("counters")] if obj.get("counters") else []
                ) for obj in response])
            )
        except Exception as e:
            logger.exception(e)

@strawberry.type
class Mutation:
    @strawberry.mutation
    def add_or_update_tag(self,info,tagInput : TagInput) -> MutationResponse[str]:
        try:
            db = info.context["db"]
            msg = add_or_update_tag(db,tagInput)
            return MutationResponse.from_status_flag(True, msg, None) 
        except MutationError as ex:
            return MutationResponse.from_status_flag(False,ex.message,None)
        
    @strawberry.mutation
    def update_patient_type(self,info,id:int,patient_type:str)->MutationResponse[None]:
        try:
            db=info.context["db"]
            message="Updated successfully"
            update_patient_type(db,id,patient_type)
            return  MutationResponse.from_status_flag(True, message,None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False,ex.message,None)
         
            
    @strawberry.mutation
    async def add_or_edit_queue(self, info, queueInput: QueueInput) -> MutationResponse[List[Queue]]:
        try:
            db = info.context["db"]
            data = await add_or_update_queue(db, queueInput)
            return MutationResponse.from_status_flag(True, "Sucess", [Queue.from_instance(obj, ", ".join(staff_user.name for staff_user in obj.staff_users)) for obj in data])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)

    @strawberry.mutation
    def delete_queue(self, info, queue_id: int) -> MutationResponse[str]:
        try:
            db = info.context["db"]
            res = delete_queue(db, queue_id)
            return MutationResponse.from_status_flag(True, "Queue Deleted Successfully", None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)

    @strawberry.mutation
    def add_user_queue(self, info, qr_details: QRDetail, queue_id: int) -> MutationResponse[List[UserQueue]]:
        try:
            db = info.context["db"]
            staff_user=info.context["staff_user"]
            data = add_user_queue(db, queue_id, qr_details,staff_user)
            return MutationResponse.from_status_flag(True, "User Added To Queue", [UserQueue.from_instance(obj) for obj in data])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)

    @strawberry.mutation
    def update_user_queue(self, info, user_queue_id: int, queue_weightage_action_id: Optional[int] = None, queue_weightage_action: Optional[str]=None, remarks: Optional[str]= None) -> MutationResponse[List[UserQueue]]:
        try:
            db = info.context["db"]
            staff_user=info.context["staff_user"]
            data = update_user_queue(db, user_queue_id, queue_weightage_action_id,queue_weightage_action,staff_user,"PENDING", remarks)
            return MutationResponse.from_status_flag(True, "Queue Updated", [UserQueue.from_instance(obj) for obj in data])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.mutation
    def update_user_queue_timestamp(self, info, user_queue_id: int, update_type: str) -> MutationResponse[None]:
        try:
            db = info.context["db"]
            staff_user=info.context["staff_user"]
            if update_type == "PHYSCIAN_ASSISTANT":
                update_type = "PHYSICIANASSISTANT"
            update_user_queue_timestamp(db, user_queue_id, update_type, staff_user)
            return MutationResponse.from_status_flag(True, "Queue Updated", None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)

    @strawberry.mutation
    def update_pre_check_status(self, info, user_queue_id: int, user_service_ids: Optional[List[int]] = None) -> MutationResponse[str]:
        try:
            db = info.context["db"]
            staff_user=info.context["staff_user"]
            data = update_pre_check_status(db, user_queue_id, user_service_ids, "PRECHECK", staff_user, None)
            return MutationResponse.from_status_flag(True, "Queue Updated", None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)

    @strawberry.mutation
    def exit_user_queue(self, info, queue_id: int, qr_details: QRDetail,user_service_ids: Optional[List[int]] = None) -> MutationResponse[List[UserQueue]]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            data = exit_user_queue(db, queue_id, qr_details,False,user_service_ids,staff_user)
            return MutationResponse.from_status_flag(True, "Queue updated", [UserQueue.from_instance(obj) for obj in data])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)

    @strawberry.mutation
    def update_counter(self, info, counter_id: int,status:str) -> MutationResponse[str]:
        try:
            db = info.context["db"]
            counter_status = update_counter(db, counter_id,status)
            return MutationResponse.from_status_flag(True, "Counter Updated", counter_status)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)

    @strawberry.mutation
    def activate_or_deactivate_queue(self, info, queue_id: int, status: str) -> MutationResponse[List[Queue]]:
        try:
            db = info.context["db"]
            msg,data = activate_or_deactivate_queue(db, queue_id, status)
            return MutationResponse.from_status_flag(True, msg, [Queue.from_instance(obj, ", ".join(staff_user.name for staff_user in obj.staff_users)) for obj in data])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.mutation
    def staff_station_device_login(self, info, queue_id:int, login_pin: int) -> MutationResponse[str]:
        try:
            db= info.context['db']
            device_id = info.context["device_id"]
            result = staff_station_device_login(db, queue_id,device_id, login_pin)
            dt = datetime.now() + \
                timedelta(minutes=float(
                    os.environ["ACCESS_EXPIRE_MINUTES"]))
            access_token = jws.sign({"sub": {"device_id":device_id}, "user_type": "HOSPITAL", "exp": dt.isoformat(
                )}, os.environ["SECRET_KEY"], algorithm=os.environ["ALGORITHM"])
            logger.info("Staff user logged in suscessfully")
            return MutationResponse.from_status_flag(True,"Login Successfull",access_token)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message,None)
        
    @strawberry.mutation
    def update_user_queue_service(self, info, user_queue_id: int, user_service_ids: Optional[List[int]],status:Optional[str]=None) -> MutationResponse[List[UserQueue]]:
        try:
            db = info.context["db"]
            data = update_user_queue_service(db, user_queue_id, user_service_ids,status)
            return MutationResponse.from_status_flag(True, "User Queue Updated", [UserQueue.from_instance(obj) for obj in data])
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.mutation
    def update_user_queue_step(self, info, user_queue_id: int, queue_step_id:int, remarks: Optional[str]=None,type:Optional[str]='CHECKIN', queue_location_id:Optional[int]=None,tag_id:Optional[str]=None, queue_step_code:Optional[str]= None) -> MutationResponse[None]:
        try:
            db = info.context["db"]
            staff_user=info.context["staff_user"]
            data = update_user_queue_step(db, user_queue_id, queue_step_id,remarks,type,queue_location_id,tag_id, queue_step_code,staff_user, "TAGID")
            return MutationResponse.from_status_flag(True, "User Queue Updated", None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.mutation
    def add_user_to_queue(self, info, uhid: str, queue_step_id:int, remarks: str,type:Optional[str]='CHECKIN') -> MutationResponse[None]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            data = add_to_queue(db, uhid, queue_step_id,remarks,type,staff_user)
            return MutationResponse.from_status_flag(True, "User Queue Updated", None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.mutation
    def call_next(self, info, queue_id:int) -> MutationResponse[None]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            data = call_next(db, queue_id, staff_user)
            return MutationResponse.from_status_flag(True, "User Queue Updated", None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    @strawberry.mutation
    def call_next_pre_check(self, info, queue_id:int) -> MutationResponse[UserQueue]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            data = call_next_pre_check(db, queue_id,staff_user)
            return MutationResponse.from_status_flag(True, "User Queue Updated", UserQueue.from_instance(data))
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
    
    
    @strawberry.mutation
    def update_user_queue_manual(self, info, user_queue_id:int, queue_weightage_action_id: int, remarks: Optional[str]= None) -> MutationResponse[None]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            data = update_user_queue_manual(db, user_queue_id, queue_weightage_action_id,staff_user,remarks)
            return MutationResponse.from_status_flag(True, "User Queue Updated", None)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)

    @strawberry.mutation
    def get_tag_or_scan_id(self, info, ref_id: str) -> MutationResponse[str]:
        print(f"ref id is {ref_id}")
        try:
            if not ref_id:
                raise MutationError("ScanId must be provided")
            db = info.context["db"]
            if ref_id:
                data = get_tag_or_scan_id(db,ref_id,"RFID")
                return MutationResponse.from_status_flag(True, "Success", data.code)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.mutation
    def checkout_user(self, info, ref_id: Optional[str]=None, uhid: Optional[str]=None) -> MutationResponse[str]:
        try:
            if not ref_id and not uhid:
                raise MutationError("ScanId or UHID must be provided")
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            msg = checkout_user(db,staff_user,ref_id,uhid)
            return MutationResponse.from_status_flag(True, "Success", msg)
        except MutationError as ex:
            return MutationResponse.from_status_flag(False, ex.message, None)
        
    @strawberry.mutation
    def pause_unpause_user(self, info, user_queue_id: str, type: str) -> MutationResponse[str]:
        try:
            db = info.context["db"]
            staff_user = info.context["staff_user"]
            msg = pause_unpause_user(db, staff_user, user_queue_id, type)
            return MutationResponse.from_status_flag(True, "Success", msg)     
        except MutationError as ex:
            return MutationResponse.from_status_flag(False,ex.message,None)
    
    @strawberry.mutation
    def continue_break_journey(self,info,user_detail: UserDetail, token_id: int, is_direct_checkin: Optional[bool]=False, tag_code: Optional[str]=None) -> MutationResponse[str]:
        try:
            db = info.context["db"]
            staff_user =info.context["staff_user"]
            obj = save_user_service_1(db,staff_user,user_detail=user_detail, is_direct_checkin=is_direct_checkin, tag=tag_code,send_sms=True,old_token=token_id,is_from_billing=True)
            if obj:
                cancel_pending_services(db,token_id)
            return MutationResponse.from_status_flag(True, "bill linked successfully",  obj)
        except Exception as e:
            return MutationResponse.from_status_flag(False, str(e), None)
        
    @strawberry.mutation
    def generate_bar_code(self,info,user_service_ids: List[int]) -> MutationResponse[List[BarCodeResponse]]:
        try:
            db = info.context["db"]
            hosp_code = info.context["device_detail"].hospital_code
            bar_code_result,msg,service_id_map = bar_code_generation(db, hosp_code, user_service_ids)
            print(bar_code_result,"result")
            if bar_code_result:
                return MutationResponse.from_status_flag(
                    True, 
                    "Bar code generated successfully", 
                    [
                        BarCodeResponse(
                            user_service_id = service_id_map.get(obj.get("testId")),
                            sample_no=obj.get("sampleNo")
                        )
                        for obj in bar_code_result
                    ]
                )
            else:
                return MutationResponse.from_status_flag(False,msg,None)
        except MutationError as ex:
            logger.exception(ex)
            return MutationResponse.from_status_flag(False, ex.message, None)

    @strawberry.mutation
    def update_sample_collection(self, info, user_service_ids: List[int], update_time: str) -> MutationResponse[None]:
        try:
            if not user_service_ids:
                return MutationResponse.from_status_flag(False, "No service IDs provided", None)

            sample_body, ack_body = get_sample_collection_type(
                info.context["db"], info.context["device_detail"].hospital_code, user_service_ids, update_time)

            operations = [(n, f) for n, f in [
                ("sample_collection", lambda: sample_collection_update(sample_body)) if sample_body else (None, None),
                ("acknowledge_test", lambda: get_acknowledge_test_details(ack_body)) if ack_body else (None, None)
            ] if n]

            if not operations:
                return MutationResponse.from_status_flag(False, "No valid records to process", None)

            results = [(lambda: op_func())() if True else (False, str(e), op_name)
                      for op_name, op_func in operations
                      for e in [None]
                      for success, msg in [op_func() if not e else (False, str(e))]]

            successful = sum(1 for r in results if r[0])
            return MutationResponse.from_status_flag(
                successful > 0,
                f"Processed {len(sample_body) + len(ack_body)} records" if successful else
                f"Failed: {'; '.join(f'{r[2]}: {r[1]}' for r in results)}",
                None
            )

        except Exception as ex:
            logger.exception(f"Error in update_sample_collection: {ex}")
            return MutationResponse.from_status_flag(False, "Internal server error", None)

@strawberry.type
class Subscription:
    @strawberry.subscription
    async def get_queue_counters(self,info,queue_id: int) -> AsyncGenerator[List[UserQueue], None]:
        try:
            db = SessionLocal()
            # queue_id, is_updated = get_device_queue(db,queue_id)
            # data = get_user_queue(db, queue_id)
            db.close()
            while(True):
                db = SessionLocal()
                # if is_updated:
                #     data = get_user_queue(db, queue_id)
                #     yield [UserQueue.from_instance(obj) for obj in data]
                #     await asyncio.sleep(0.5)
                # else:
                #     new_data = [UserQueue.from_instance(obj) for obj in data]
                #     yield new_data
                # update_rel_device_queue(db,queue_id, False)
                data = get_queue_counters(db, queue_id)
                yield [UserQueue.from_instance(obj) for obj in data]
                if db is not None: db.close()
                await asyncio.sleep(1)
        except Exception as e:
            logger.exception(e)

    @strawberry.subscription
    async def get_queue_counters_by_user(self,info, access_token:str) -> AsyncGenerator[StaffQueueCountersObj,None]:
        try:
            auth=access_token.split(" ")
            if(auth[0]=='Bearer'):
                token = auth[1]
                payload = jws.verify(token, key=os.environ["SECRET_KEY"], algorithms=[os.environ["ALGORITHM"]])
                content=json.loads(payload)
                if content["exp"]< datetime.now().isoformat() :
                    raise MutationError("Un Authorised")
                if(content["user_type"]==EntityTypeEnum.STAFF.name):
                    staff_user = content["sub"]["user_id"] if content["sub"]["user_id"] is not None else None
                while(True):
                    db = SessionLocal()
                    response = get_queue_counters_by_user(db, staff_user)
                    current_date = datetime.now().strftime("%A, %B %d, %Y")
                    current_time = datetime.now().strftime("%I:%M:%S %p")
                    staff_queue_counter = StaffQueueCountersObj(current_date=current_date,current_time=current_time,staff_queue_counters=[StaffQueueCounters(
                        queue_id = obj.get("queueId"),
                        queue_name = obj.get("queueName"),
                        show_patient_name = obj.get("showPatientName"),
                        phy_token_no= obj.get("phyTokenNo"),
                        counters= [QueueCounterType(counter_name=obj1.get("counterName"),tokens=obj1.get("tokens"),doctor_image=obj1.get("doctorImage"),serving_token=obj1.get("servingToken"),patient_name=obj1.get("patientName"),mobile_number=obj1.get("mobileNumber")) for obj1 in obj.get("counters")] if obj.get("counters") else []
                    ) for obj in response])
                    yield staff_queue_counter
                    if db is not None: db.close()
                    await asyncio.sleep(0.5)
        except Exception as e:
            logger.exception(e)