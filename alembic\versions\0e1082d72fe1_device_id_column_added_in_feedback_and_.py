"""device_id column added in feedback and removed rel_module_resource

Revision ID: 0e1082d72fe1
Revises: d4a8fae6a2ce
Create Date: 2024-02-02 13:21:45.109800

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0e1082d72fe1'
down_revision = 'd4a8fae6a2ce'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('rel_module_resource')
    op.add_column('user_feedback', sa.Column('device_id', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_feedback', 'device_id')
    op.create_table('rel_module_resource',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('module_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('resource_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['module_id'], ['module.id'], name='rel_module_resource_module_id_fk'),
    sa.ForeignKeyConstraint(['resource_id'], ['resource.id'], name='rel_module_resource_resource_id_fk'),
    sa.PrimaryKeyConstraint('id', name='rel_module_resource_pkey')
    )
    # ### end Alembic commands ###
