"""columns added in service model

Revision ID: 33aecb7c6533
Revises: 1762b07a333f
Create Date: 2023-10-18 06:54:04.638772

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '33aecb7c6533'
down_revision = '1762b07a333f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('service', sa.Column('reports_collection', sa.Text(), nullable=True))
    op.add_column('service', sa.Column('source', sa.String(), nullable=True))
    op.add_column('service', sa.Column('reports_completion_time', sa.Float(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('service', 'reports_completion_time')
    op.drop_column('service', 'source')
    op.drop_column('service', 'reports_collection')
    # ### end Alembic commands ###
