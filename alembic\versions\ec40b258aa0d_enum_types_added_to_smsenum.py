"""enum types added to smsenum

Revision ID: ec40b258aa0d
Revises: 42b6ec9a9df1
Create Date: 2023-10-16 07:48:55.363230

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'ec40b258aa0d'
down_revision = '42b6ec9a9df1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("ALTER TYPE smstypeenum ADD VALUE 'WHATSAPP'")

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        "ALTER TYPE smstypeenum RENAME TO smstypeenum")
    op.execute("CREATE TYPE smstypeenum AS ENUM('SMS')")
    op.execute((
        "ALTER TABLE sms_mail_notification_config ALTER COLUMN  TYPE smstypeenum USING "
        "type::text::smstypeenum"
    ))
    op.execute("DROP TYPE smstypeenum")
    # ### end Alembic commands ###
