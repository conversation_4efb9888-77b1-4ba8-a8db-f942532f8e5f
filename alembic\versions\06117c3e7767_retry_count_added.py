"""retry count added

Revision ID: 06117c3e7767
Revises: c30763c88df3
Create Date: 2023-12-08 12:39:36.157839

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '06117c3e7767'
down_revision = 'c30763c88df3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('printer', sa.Column('retry_count', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('printer', 'retry_count')
    # ### end Alembic commands ###
